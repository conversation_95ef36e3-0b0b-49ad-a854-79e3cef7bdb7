using Microsoft.Extensions.Logging;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Documents.FileDocuments;
using Sleekflow.IntelligentHub.Documents.WebpageDocuments;
using Sleekflow.IntelligentHub.Documents.WebsiteDocuments;
using Sleekflow.IntelligentHub.LightRags;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.Models.LightRag;

namespace Sleekflow.IntelligentHub.Workers.Services;

public interface IKnowledgeBaseUploadService
{
    Task UploadFileDocumentToAgentKnowledgeBaseAsync(
        string sleekflowCompanyId,
        string fileDocumentId,
        string chunkId,
        string agentId);
}

public class KnowledgeBaseUploadService : IKnowledgeBaseUploadService, IScopedService
{
    private readonly ILogger<KnowledgeBaseUploadService> _logger;
    private readonly ILightRagClient _lightRagClient;
    private readonly IKbDocumentService _kbDocumentService;
    private readonly IFileDocumentChunkService _fileDocumentChunkService;
    private readonly IWebsiteDocumentChunkService _websiteDocumentChunkService;
    private readonly IWebpageDocumentChunkService _webpageDocumentChunkService;

    public KnowledgeBaseUploadService(
        ILogger<KnowledgeBaseUploadService> logger,
        ILightRagClient lightRagClient,
        IKbDocumentService kbDocumentService,
        IFileDocumentChunkService fileDocumentChunkService,
        IWebsiteDocumentChunkService websiteDocumentChunkService,
        IWebpageDocumentChunkService webpageDocumentChunkService)
    {
        _logger = logger;
        _lightRagClient = lightRagClient;
        _kbDocumentService = kbDocumentService;
        _fileDocumentChunkService = fileDocumentChunkService;
        _websiteDocumentChunkService = websiteDocumentChunkService;
        _webpageDocumentChunkService = webpageDocumentChunkService;
    }

    public async Task UploadFileDocumentToAgentKnowledgeBaseAsync(
        string sleekflowCompanyId,
        string fileDocumentId,
        string chunkId,
        string agentId)
    {
        _logger.LogInformation(
            "Start inserting {ChunkId} chunks from document {DocumentId} into {KnowledgeBaseId} {AgentId}",
            chunkId,
            fileDocumentId,
            sleekflowCompanyId,
            agentId);

        var kbDocument = await _kbDocumentService.GetDocumentAsync(sleekflowCompanyId, fileDocumentId);

        string chunkContent;
        if (kbDocument is FileDocument fileDocument)
        {
            var chunk = await _fileDocumentChunkService.GetFileDocumentChunkAsync(sleekflowCompanyId, chunkId);
            chunkContent = chunk.Content;
        }
        else if (kbDocument is WebsiteDocument websiteDocument)
        {
            var chunk = await _websiteDocumentChunkService.GetWebsiteDocumentChunkAsync(sleekflowCompanyId, chunkId);
            chunkContent = chunk.Content;
        }
        else if (kbDocument is WebpageDocument webpageDocument)
        {
            var chunk = await _webpageDocumentChunkService.GetWebpageDocumentChunkAsync(sleekflowCompanyId, chunkId);
            chunkContent = chunk.Content;
        }
        else
        {
            throw new Exception("Unknown KbDocument type");
        }

        // TODO please check if we need to pass knowledgeBaseId as header and body
        var insertRequest = new InsertLightRagRequest(
            chunkContent,
            LightRagClient.GetAgentKnowledgeBaseId(sleekflowCompanyId, agentId));

        var insertResponse =
            await _lightRagClient.ExecuteAsync<InsertLightRagResponse>(
                LightRagPaths.Insert,
                sleekflowCompanyId,
                agentId,
                insertRequest);

        if (insertResponse == null || insertResponse.Status != InsertLightRagResponse.STATUS_SUCCESS)
        {
            throw new Exception("Insert failed");
        }
    }
}