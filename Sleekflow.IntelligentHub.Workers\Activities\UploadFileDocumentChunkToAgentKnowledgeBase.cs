using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Workers.Services;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Workers.Activities;

public class UploadFileDocumentChunkToAgentKnowledgeBase
{
    private readonly ILogger<UploadFileDocumentChunkToAgentKnowledgeBase> _logger;
    private readonly IKnowledgeBaseUploadService _knowledgeBaseUploadService;

    public UploadFileDocumentChunkToAgentKnowledgeBase(
        ILogger<UploadFileDocumentChunkToAgentKnowledgeBase> logger,
        IKnowledgeBaseUploadService knowledgeBaseUploadService)
    {
        _logger = logger;
        _knowledgeBaseUploadService = knowledgeBaseUploadService;
    }

    public class UploadFileDocumentChunkToAgentKnowledgeBaseInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("document_id")]
        [Required]
        public string DocumentId { get; set; }

        [JsonProperty("chunk_id")]
        [Required]
        public string ChunkId { get; set; }

        [JsonProperty("agent_id")]
        [Required]
        public string AgentId { get; set; }

        [JsonConstructor]
        public UploadFileDocumentChunkToAgentKnowledgeBaseInput(
            string sleekflowCompanyId,
            string documentId,
            string chunkId,
            string agentId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DocumentId = documentId;
            ChunkId = chunkId;
            AgentId = agentId;
        }
    }

    public class UploadFileDocumentChunkToAgentKnowledgeBaseOutput
    {
        [JsonConstructor]
        public UploadFileDocumentChunkToAgentKnowledgeBaseOutput()
        {
        }
    }

    [Function(nameof(UploadFileDocumentChunkToAgentKnowledgeBase))]
    public async Task<UploadFileDocumentChunkToAgentKnowledgeBaseOutput> Run(
        [ActivityTrigger]
        UploadFileDocumentChunkToAgentKnowledgeBaseInput input)
    {
        _logger.LogInformation(
            "Uploading file document {DocumentId} chunk {ChunkId} to agent knowledge base {AgentId} for company {CompanyId}",
            input.DocumentId,
            input.AgentId,
            input.ChunkId,
            input.SleekflowCompanyId);

        await _knowledgeBaseUploadService.UploadFileDocumentToAgentKnowledgeBaseAsync(
            input.SleekflowCompanyId,
            input.DocumentId,
            input.ChunkId,
            input.AgentId);

        return new UploadFileDocumentChunkToAgentKnowledgeBaseOutput();
    }
}