using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Microsoft.SemanticKernel.Agents.Chat;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Companies.CompanyConfigs;
using Sleekflow.IntelligentHub.FaqAgents.Chats.Enrichers;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.IntelligentHub.Plugins.Knowledges;
using Sleekflow.IntelligentHub.Utils;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.Longs;

public interface ILongAgentCollaborationDefinition : IAgentCollaborationDefinition
{
}

public class LongAgentCollaborationDefinition
    : BaseAgentCollaborationDefinition, ILongAgentCollaborationDefinition, IScopedService
{
    private const string HistoryVariable = "history";

    private readonly IAgentReviewerToolsPlugin _agentReviewerToolsPlugin;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;
    private readonly ILongAgentCollaborationChatCacheService _longAgentCollaborationChatCacheService;
    private readonly ILongAgentKnowledgePlugin _longAgentKnowledgePlugin;

    public LongAgentCollaborationDefinition(
        ILogger<LongAgentCollaborationDefinition> logger,
        Kernel kernel,
        ISummaryPlugin summaryPlugin,
        ILanguagePlugin languagePlugin,
        IAgentDurationTracker agentDurationTracker,
        ICompanyConfigService companyConfigService,
        IAgentReviewerToolsPlugin agentReviewerToolsPlugin,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        ILongAgentCollaborationChatCacheService longAgentCollaborationChatCacheService,
        IChatHistoryEnricherFactory enricherFactory,
        IFileContentExtractionPlugin fileContentExtractionPlugin,
        ILongAgentKnowledgePlugin longAgentKnowledgePlugin)
        : base(
            logger,
            kernel,
            summaryPlugin,
            languagePlugin,
            agentDurationTracker,
            companyConfigService,
            enricherFactory,
            fileContentExtractionPlugin)
    {
        _agentReviewerToolsPlugin = agentReviewerToolsPlugin;
        _promptExecutionSettingsService = promptExecutionSettingsService;
        _longAgentCollaborationChatCacheService = longAgentCollaborationChatCacheService;
        _longAgentKnowledgePlugin = longAgentKnowledgePlugin;
    }

    public override Task<List<Agent>> CreateAgents(
        Kernel kernel,
        List<SfChatEntry> chatEntries,
        string sleekflowCompanyId,
        AgentCollaborationConfig agentCollaborationConfig,
        BaseInternalAgentCreationConfiguration? internalAgentCreationConfiguration = null)
    {
        var salesStrategyAgent = LongAgentDefinitions.GetSalesStrategyAgent(
            kernel,
            "SalesStrategyAgent",
            GetPromptExecutionSettings("SalesStrategyAgent"),
            agentCollaborationConfig.Tone);
        var salesAgent = LongAgentDefinitions.GetSalesAgent(
            kernel,
            "SalesAgent",
            agentCollaborationConfig.DetectedResponseLanguage,
            GetPromptExecutionSettings("SalesAgent"));
        var knowledgeRetrievalAgent = LongAgentDefinitions.GetKnowledgeRetrievalAgent(
            kernel,
            _longAgentKnowledgePlugin,
            "KnowledgeRetrievalAgent",
            sleekflowCompanyId,
            GetPromptExecutionSettings("KnowledgeRetrievalAgent"),
            agentCollaborationConfig.RestrictivenessLevel);
        var reviewerAgent = LongAgentDefinitions.GetReviewerAgent(
            kernel,
            "ReviewerAgent",
            GetPromptExecutionSettings("ReviewerAgent"),
            _agentReviewerToolsPlugin);
        return Task.FromResult(
            new List<Agent>
            {
                salesStrategyAgent, salesAgent, knowledgeRetrievalAgent, reviewerAgent
            });
    }

    public override SelectionStrategy? CreateSelectionStrategy(Kernel kernel)
    {
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, true);

        return new KernelFunctionSelectionStrategy(CreateCoordinatingFunction(), kernel)
        {
            HistoryReducer = new ChatHistoryTruncationReducer(1),
            HistoryVariableName = "history",
            ResultParser = (result) =>
            {
                try
                {
                    var agentName = result.GetValue<string>();
                    return !string.IsNullOrEmpty(agentName) ? agentName : LongAgentDefinitions.SalesStrategyAgentName;
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                    Console.WriteLine(result);
                    return LongAgentDefinitions.SalesStrategyAgentName;
                }
            },
            Arguments = new KernelArguments(promptExecutionSettings),
            UseInitialAgentAsFallback = true
        };
    }

    public override RegexTerminationStrategy? CreateTerminationStrategy(List<Agent> agents)
    {
        return new RegexTerminationStrategy("\"customer_facing_reply_approved\"\\s*:\\s*\"[^\"]+\"")
        {
            MaximumIterations = 15,
            AutomaticReset = true,
            Agents = agents.Where(a => a.Name == LongAgentDefinitions.ReviewerAgentName).ToList()
        };
    }

    public override async Task<(string ChatHistoryStr, string Context)> InitializeChatHistoryAsync(
        AgentGroupChat? agentGroupChat,
        string groupChatIdStr,
        List<SfChatEntry> chatEntries,
        ReplyGenerationContext replyGenerationContext,
        AgentCollaborationConfig agentCollaborationConfig,
        CompanyAgentConfig? agentConfig)
    {
        var (chatHistoryStr, context) = await base.InitializeChatHistoryAsync(
            agentGroupChat,
            groupChatIdStr,
            chatEntries,
            replyGenerationContext,
            agentCollaborationConfig,
            agentConfig);

        await _longAgentCollaborationChatCacheService.SetCustomerInquiryAsync(groupChatIdStr, chatHistoryStr);

        return (chatHistoryStr, context);
    }

    public override async Task<bool> InterceptAgentReplyAsync(
        ChatMessageContent response,
        string groupChatIdStr)
    {
        await base.InterceptAgentReplyAsync(response, groupChatIdStr);

        // Parse JSON response and cache relevant fields
        if (!string.IsNullOrEmpty(response.Content))
        {
            try
            {
                var jsonResponse = JsonConvert.DeserializeObject<Dictionary<string, object>>(response.Content);
                if (jsonResponse != null)
                {
                    // Cache strategy for AgentReviewerToolsPlugin
                    if (jsonResponse.TryGetValue("strategy", out var strategy) && strategy != null)
                    {
                        await _longAgentCollaborationChatCacheService.SetStrategyAsync(
                            groupChatIdStr,
                            strategy.ToString());
                    }

                    // Cache proposed reply to customer for AgentReviewerToolsPlugin
                    if (jsonResponse.TryGetValue("proposed_reply_to_customer", out var proposedReply) && proposedReply != null)
                    {
                        await _longAgentCollaborationChatCacheService.AppendProposedReplyToCustomerAsync(
                            groupChatIdStr,
                            proposedReply.ToString());
                    }
                }
            }
            catch (JsonException)
            {
                // If JSON parsing fails, log and continue
                // This is expected for Context messages and other non-agent responses
            }
        }

        /* additional_insights is cached in KnowledgePlugin */

        return true;
    }

    public override string GetFinalReplyTag() => "proposed_reply_to_customer";

    public override string GetSourceTag() => "CONFIRMED_KNOWLEDGE";

    public override async Task<string> GetFinalReplyAsync(ChatHistory chatHistory)
    {
        var finalChatMessage = chatHistory
            .FirstOrDefault(x => x.Content != null && x.Content.Contains(GetFinalReplyTag()));

        if (finalChatMessage?.Content == null)
        {
            throw new InvalidOperationException("No final reply found in chat history");
        }

        var jsonResponse = JsonConvert.DeserializeObject<Dictionary<string, object>>(finalChatMessage.Content);
        if (jsonResponse != null && jsonResponse.TryGetValue(GetFinalReplyTag(), out var finalReply) && finalReply != null)
        {
            var finalReplyToCustomer = finalReply.ToString().Trim();

            // Decode HTML entities and convert to WhatsApp markdown
            finalReplyToCustomer = System.Net.WebUtility.HtmlDecode(finalReplyToCustomer);
            finalReplyToCustomer = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(finalReplyToCustomer);

            return finalReplyToCustomer;
        }

        throw new InvalidOperationException($"Could not extract {GetFinalReplyTag()} from final message");
    }

    private PromptExecutionSettings GetPromptExecutionSettings(string name)
    {
        // SalesAgent needs to use a more advanced model because it has a more complex role
        if (name is LongAgentDefinitions.SalesAgentName)
        {
            return _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1, true);
        }

        return _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1_MINI, true);
    }

    private record ChatHistoryRecord(string Role, string Name, string Content);

    private static KernelFunction CreateCoordinatingFunction()
    {
        return KernelFunctionFactory.CreateFromMethod(
            (string history) =>
            {
                var messages = JsonConvert.DeserializeObject<ChatHistoryRecord[]>(history);

                if (messages == null || messages.Length == 0)
                {
                    return LongAgentDefinitions.SalesStrategyAgentName;
                }

                var latestMessage = messages[^1];
                if (!JsonUtils.TryParseJson<Dictionary<string, object?>>(latestMessage.Content, out var message))
                {
                    return LongAgentDefinitions.SalesStrategyAgentName;
                }

                if (message == null)
                {
                    return LongAgentDefinitions.SalesStrategyAgentName;
                }

                // Process fields in priority order - stop at first match with non-null, non-empty value
                // 1. "proposed_reply_to_customer" (not null/empty) -> ReviewerAgent
                if (message.TryGetValue("proposed_reply_to_customer", out var proposedReply)
                    && proposedReply != null
                    && !string.IsNullOrWhiteSpace(proposedReply.ToString()))
                {
                    return LongAgentDefinitions.ReviewerAgentName;
                }

                // 2. "customer_facing_reply_approved" (not null/empty) -> complete (handled by termination strategy)
                if (message.TryGetValue("customer_facing_reply_approved", out var approved)
                    && approved != null
                    && !string.IsNullOrWhiteSpace(approved.ToString()))
                {
                    // This will be handled by termination strategy
                    return LongAgentDefinitions.ReviewerAgentName;
                }

                // 3. "customer_facing_reply_rejected" (not null/empty) -> SalesAgent
                if (message.TryGetValue("customer_facing_reply_rejected", out var rejected)
                    && rejected != null
                    && !string.IsNullOrWhiteSpace(rejected.ToString()))
                {
                    return LongAgentDefinitions.SalesAgentName;
                }

                // 4. "suggest_need_knowledge" (not null/empty) -> SalesAgent
                if (message.TryGetValue("suggest_need_knowledge", out var suggestKnowledge)
                    && suggestKnowledge != null
                    && !string.IsNullOrWhiteSpace(suggestKnowledge.ToString()))
                {
                    return LongAgentDefinitions.SalesAgentName;
                }

                // 5. "need_knowledge" (not null/empty) -> KnowledgeRetrievalAgent
                if (message.TryGetValue("need_knowledge", out var needKnowledge)
                    && needKnowledge != null
                    && !string.IsNullOrWhiteSpace(needKnowledge.ToString()))
                {
                    return LongAgentDefinitions.KnowledgeRetrievalAgentName;
                }

                // 6. "additional_insights" (not null/empty) -> SalesAgent
                if (message.TryGetValue("additional_insights", out var insights)
                    && insights != null
                    && !string.IsNullOrWhiteSpace(insights.ToString()))
                {
                    return LongAgentDefinitions.SalesAgentName;
                }

                // 7. "strategy" (not null/empty) -> SalesAgent
                if (message.TryGetValue("strategy", out var strategy)
                    && strategy != null
                    && !string.IsNullOrWhiteSpace(strategy.ToString()))
                {
                    return LongAgentDefinitions.SalesAgentName;
                }

                // Default route: SalesStrategyAgent
                return LongAgentDefinitions.SalesStrategyAgentName;
            },
            "LongCoordinatingFunction");
    }
}