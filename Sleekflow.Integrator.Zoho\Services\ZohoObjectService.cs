﻿using System.Net;
using System.Net.Http.Headers;
using System.Text;
using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.CrmHub.Models.InflowActions;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.Zoho;
using Sleekflow.Integrator.Zoho.Services.Models;
using Sleekflow.Integrator.Zoho.Utils;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;

namespace Sleekflow.Integrator.Zoho.Services;

public interface IZohoObjectService
{
    Task<string> GetOrganizationNameAsync(ZohoAuthentication authentication);

    Task<string> GetOrganizationIdAsync(ZohoAuthentication authentication);

    Task<ZohoObjectService.GetFieldsOutput> GetFieldsAsync(
        ZohoAuthentication authentication,
        string entityTypeName);

    Task CreateAsync(
        ZohoAuthentication authentication,
        Dictionary<string, object?> dict,
        string entityTypeName);

    Task<Dictionary<string, object?>> GetObjectAsync(
        ZohoAuthentication authentication,
        string objectId,
        string entityTypeName);

    Task UpdateAsync(
        ZohoAuthentication authentication,
        Dictionary<string, object?> dict,
        string objectId,
        string entityTypeName);

    Task<List<Dictionary<string, object?>>> SearchObjectsAsync(
        ZohoAuthentication authentication,
        string entityTypeName,
        List<SearchObjectCondition> conditions);

    Task<List<CustomObjectType>> GetCustomObjectTypesAsync(
        ZohoAuthentication authentication);

    Task<(List<Dictionary<string, object?>> Objects, int? NextPage)> GetRecentlyUpdatedObjectsAsync(
        ZohoAuthentication authentication,
        string entityTypeName,
        DateTimeOffset lastModificationTime,
        int page = 1,
        int pageSize = 50);

    Task<(List<Dictionary<string, object?>> Objects, int? NextPageSize, int? NextPage)> GetObjectsAsync(
        ZohoAuthentication authentication,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups,
        List<SyncConfigFieldFilter>? fieldFilters,
        int? pageSize = 200,
        int? page = 1);
}

public class ZohoObjectService : ISingletonService, IZohoObjectService
{
    private readonly HttpClient _httpClient;
    private readonly ICacheService _cacheService;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;

    public ZohoObjectService(
        HttpClient httpClient,
        ICacheService cacheService,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer)
    {
        _httpClient = httpClient;
        _cacheService = cacheService;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
    }

    public class GetFieldsOutput
    {
        [JsonProperty("updatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> UpdatableFields { get; set; }

        [JsonProperty("creatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> CreatableFields { get; set; }

        [JsonProperty("viewable_fields")]
        public List<GetTypeFieldsOutputFieldDto> ViewableFields { get; set; }

        [JsonConstructor]
        public GetFieldsOutput(
            List<GetTypeFieldsOutputFieldDto> updatableFields,
            List<GetTypeFieldsOutputFieldDto> creatableFields,
            List<GetTypeFieldsOutputFieldDto> viewableFields)
        {
            UpdatableFields = updatableFields;
            CreatableFields = creatableFields;
            ViewableFields = viewableFields;
        }
    }

    public async Task<string> GetOrganizationNameAsync(ZohoAuthentication authentication)
    {
        var zohoGetOrganizationsOutput = await GetZohoOrganizationsOutputAsync(authentication);
        return zohoGetOrganizationsOutput.Organizations?.FirstOrDefault()?.CompanyName ?? string.Empty;
    }

    public async Task<string> GetOrganizationIdAsync(ZohoAuthentication authentication)
    {
        var zohoGetOrganizationsOutput = await GetZohoOrganizationsOutputAsync(authentication);
        return zohoGetOrganizationsOutput.Organizations?.FirstOrDefault()?.Id ?? string.Empty;
    }

    public async Task<GetFieldsOutput> GetFieldsAsync(
        ZohoAuthentication authentication,
        string entityTypeName)
    {
        return await _cacheService.CacheAsync(
            $"{nameof(ZohoObjectService)}-{authentication.Id}-{entityTypeName}",
            async () =>
            {
                var url = $"{authentication.ApiDomain}/crm/v7/settings/fields?module={entityTypeName}";

                var (_, zohoGetFieldsOutput) =
                    await ZohoGetAsync<ZohoGetFieldsOutput>(authentication, entityTypeName, url);

                var updatableFields = new List<GetTypeFieldsOutputFieldDto>();
                var creatableFields = new List<GetTypeFieldsOutputFieldDto>();
                var viewableFields = new List<GetTypeFieldsOutputFieldDto>();

                if (zohoGetFieldsOutput.Fields == null)
                {
                    return new GetFieldsOutput(updatableFields, creatableFields, viewableFields);
                }

                foreach (var field in zohoGetFieldsOutput.Fields)
                {
                    var fieldDto = new GetTypeFieldsOutputFieldDto(
                        calculated: false,
                        compoundFieldName: string.Empty,
                        createable: field.ReadOnly != true,
                        custom: field.CustomField == true,
                        encrypted: false,
                        label: field.FieldLabel ?? string.Empty,
                        length: 0,
                        name: field.ApiName ?? string.Empty,
                        picklistValues: new List<GetTypeFieldsOutputPicklistValue>(),
                        soapType: string.Empty,
                        type: field.DataType == null ? "string" : MapFieldType(field.DataType),
                        unique: false,
                        updateable: field.ReadOnly != true,
                        mandatory: field.SystemMandatory == true || field.Required == true);

                    if (field.ReadOnly != true)
                    {
                        creatableFields.Add(fieldDto);
                        updatableFields.Add(fieldDto);
                    }
                    else
                    {
                        viewableFields.Add(fieldDto);
                    }
                }

                var output = new GetFieldsOutput(updatableFields, creatableFields, viewableFields);
                ZohoDateTimeUtils.UpdateDateTimeFieldsCache(entityTypeName, output);
                return output;
            });
    }

    public async Task CreateAsync(
        ZohoAuthentication authentication,
        Dictionary<string, object?> dict,
        string entityTypeName)
    {
        // Ensure datetime fields are in the correct format before sending
        ZohoDateTimeUtils.ConvertDateTimeFields(dict, entityTypeName);

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(
                JsonConvert.SerializeObject(new { data = new[] { dict } }),
                Encoding.UTF8,
                "application/json"),
            RequestUri = new Uri(
                $"{authentication.ApiDomain}/crm/v7/{entityTypeName}")
        };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        var str = await httpResponseMsg.Content.ReadAsStringAsync();
        if (!httpResponseMsg.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ZohoDataApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new SfZObjectOperationException(str, entityTypeName, httpResponseMsg);
        }
    }

    public async Task<Dictionary<string, object?>> GetObjectAsync(
        ZohoAuthentication authentication,
        string objectId,
        string entityTypeName)
    {
        var getFieldsOutput = await GetFieldsAsync(authentication, entityTypeName);
        var creatableFields = getFieldsOutput.CreatableFields;
        var updatableFields = getFieldsOutput.UpdatableFields;
        var viewableFields = getFieldsOutput.ViewableFields;

        var fields = updatableFields.Concat(creatableFields).Concat(viewableFields).ToList();
        var fieldNames = string.Join(",", fields.Select(f => f.Name));

        var url = $"{authentication.ApiDomain}/crm/v7/{entityTypeName}/{objectId}?fields={fieldNames}";

        var (_, zohoGetOutput) = await ZohoGetAsync<ZohoGetObjectOutput>(authentication, entityTypeName, url);

        if (zohoGetOutput.Data == null || !zohoGetOutput.Data.Any())
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ZohoDataApiInvalidOutputReceived,
                new Dictionary<string, string>
                {
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new SfZObjectQueryOperationException($"No data found for {entityTypeName} with ID {objectId}", entityTypeName);
        }

        var result = zohoGetOutput.Data[0];
        ZohoDateTimeUtils.ConvertDateTimeFields(result, entityTypeName);
        return result;
    }

    public async Task UpdateAsync(
        ZohoAuthentication authentication,
        Dictionary<string, object?> dict,
        string objectId,
        string entityTypeName)
    {
        // Ensure datetime fields are in the correct format before sending
        ZohoDateTimeUtils.ConvertDateTimeFields(dict, entityTypeName);

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Put,
            Content = new StringContent(
                JsonConvert.SerializeObject(new { data = new[] { dict } }),
                Encoding.UTF8,
                "application/json"),
            RequestUri = new Uri(
                $"{authentication.ApiDomain}/crm/v7/{entityTypeName}/{objectId}")
        };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        var str = await httpResponseMsg.Content.ReadAsStringAsync();
        if (!httpResponseMsg.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ZohoDataApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new SfZObjectOperationException(str, entityTypeName, httpResponseMsg);
        }
    }

    public async Task<List<Dictionary<string, object?>>> SearchObjectsAsync(
        ZohoAuthentication authentication,
        string entityTypeName,
        List<SearchObjectCondition> conditions)
    {
        var result = await SearchObjectsAsync(
            authentication,
            entityTypeName,
            ZohoSearchObjectUtils.BuildCriteria(conditions));

        // Convert datetime fields in the results
        ZohoDateTimeUtils.ConvertDateTimeFields(result.Objects, entityTypeName);
        return result.Objects;
    }

    public async Task<List<CustomObjectType>> GetCustomObjectTypesAsync(ZohoAuthentication authentication)
    {
        var url = $"{authentication.ApiDomain}/crm/v7/settings/modules";
        var (_, zohoGetModulesOutput) = await ZohoGetAsync<ZohoGetModulesOutput>(authentication, "Modules", url);

        return zohoGetModulesOutput.Modules?
            .Where(m => m.CustomModule == true || m.ModuleName!.Contains("CustomModule"))
            .Select(m => new CustomObjectType(m.ApiName!, m.PluralLabel!))
            .ToList() ?? new List<CustomObjectType>();
    }

    public async Task<(List<Dictionary<string, object?>> Objects, int? NextPage)> GetRecentlyUpdatedObjectsAsync(
        ZohoAuthentication authentication,
        string entityTypeName,
        DateTimeOffset lastModificationTime,
        int page = 1,
        int pageSize = 50)
    {
        // Special handling for Zoho users (different endpoint casing)
        if (entityTypeName == "User")
        {
            entityTypeName = "users";
        }

        var getFieldsOutput = await GetFieldsAsync(authentication, entityTypeName);
        var allFields = getFieldsOutput.UpdatableFields
            .Concat(getFieldsOutput.CreatableFields)
            .Concat(getFieldsOutput.ViewableFields)
            .ToList();

        // Zoho caps 50 fields per request – batch them
        var fieldNameBatches = allFields.Select(f => f.Name)
            .Select((name, idx) => new { name, idx })
            .GroupBy(x => x.idx / 50)
            .Select(g => string.Join(",", g.Select(x => x.name)))
            .ToList();

        // Convert to Zoho's local time format
        var lastModificationTimeStr = ZohoDateTimeUtils.ToZohoDateTimeString(lastModificationTime);
        var criteria = $"(Modified_Time:greater_than:{lastModificationTimeStr})";

        var objectsById = new Dictionary<string, Dictionary<string, object?>>();
        var hasMore = false;

        foreach (var fieldNames in fieldNameBatches)
        {
            var url =
                $"{authentication.ApiDomain}/crm/v7/{entityTypeName}/search?criteria={Uri.EscapeDataString(criteria)}&fields={fieldNames}&page={page}&per_page={pageSize}";

            var (_, resp) = await ZohoGetAsync<ZohoGetObjectsOutput>(authentication, entityTypeName, url);

            if (resp.Data != null)
            {
                ZohoDateTimeUtils.ConvertDateTimeFields(resp.Data, entityTypeName);

                foreach (var obj in resp.Data)
                {
                    var id = obj["id"]?.ToString();
                    if (string.IsNullOrEmpty(id))
                    {
                        continue;
                    }

                    if (!objectsById.TryGetValue(id, out var merged))
                    {
                        merged = new Dictionary<string, object?>();
                        objectsById[id] = merged;
                    }

                    foreach (var kvp in obj)
                    {
                        merged[kvp.Key] = kvp.Value;
                    }
                }
            }

            hasMore |= resp.Info?.MoreRecords ?? false;
        }

        var nextPage = hasMore ? page + 1 : (int?) null;
        return (objectsById.Values.ToList(), nextPage);
    }

    public async Task<(List<Dictionary<string, object?>> Objects, int? NextPageSize, int? NextPage)> GetObjectsAsync(
        ZohoAuthentication authentication,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups,
        List<SyncConfigFieldFilter>? fieldFilters,
        int? pageSize = 200,
        int? page = 1)
    {
        // Special handling for Zoho users
        if (entityTypeName == "User")
        {
            entityTypeName = "users";
        }

        var getFieldsOutput = await GetFieldsAsync(authentication, entityTypeName);
        var creatableFields = getFieldsOutput.CreatableFields;
        var updatableFields = getFieldsOutput.UpdatableFields;
        var viewableFields = getFieldsOutput.ViewableFields;

        var fields = updatableFields.Concat(creatableFields).Concat(viewableFields).ToList();
        if (fieldFilters != null && fieldFilters.Any())
        {
            var filteredFieldNames = fieldFilters.Select(f => f.Name).ToList();

            fields = fields
                .Where(f => filteredFieldNames.Contains(f.Name))
                .ToList();
        }

        var fieldNamesBatches = fields.Select(f => f.Name).Select((name, index) => new { name, index })
            .GroupBy(x => x.index / 50)
            .Select(g => string.Join(",", g.Select(x => x.name)))
            .ToList();

        var objectsById = new Dictionary<string, Dictionary<string, object?>>();
        var hasMore = true;

        foreach (var fieldNames in fieldNamesBatches)
        {
            var url = $"{authentication.ApiDomain}/crm/v7/{entityTypeName}?fields={fieldNames}&page={page}&per_page={pageSize}";

            if (entityTypeName == "users")
            {
                var (_, zohoGetUsersOutput) = await ZohoGetAsync<ZohoGetUsersOutput>(authentication, entityTypeName, url);

                if (zohoGetUsersOutput.Users != null)
                {
                    // Convert all datetime fields in the results
                    ZohoDateTimeUtils.ConvertDateTimeFields(zohoGetUsersOutput.Users, entityTypeName);

                    foreach (var user in zohoGetUsersOutput.Users)
                    {
                        var id = user["id"]?.ToString();
                        if (string.IsNullOrEmpty(id))
                        {
                            continue;
                        }

                        if (!objectsById.ContainsKey(id))
                        {
                            objectsById[id] = new Dictionary<string, object?>();
                        }

                        foreach (var kvp in user)
                        {
                            objectsById[id][kvp.Key] = kvp.Value;
                        }
                    }
                }

                hasMore = zohoGetUsersOutput.Info?.MoreRecords ?? false;
            }
            else
            {
                var (_, zohoGetObjectsOutput) = await ZohoGetAsync<ZohoGetObjectsOutput>(authentication, entityTypeName, url);

                if (zohoGetObjectsOutput.Data != null)
                {
                    // Convert all datetime fields in the results
                    ZohoDateTimeUtils.ConvertDateTimeFields(zohoGetObjectsOutput.Data, entityTypeName);

                    foreach (var obj in zohoGetObjectsOutput.Data)
                    {
                        var id = obj["id"]?.ToString();
                        if (string.IsNullOrEmpty(id))
                        {
                            continue;
                        }

                        if (!objectsById.ContainsKey(id))
                        {
                            objectsById[id] = new Dictionary<string, object?>();
                        }

                        foreach (var kvp in obj)
                        {
                            objectsById[id][kvp.Key] = kvp.Value;
                        }
                    }
                }

                hasMore = zohoGetObjectsOutput.Info?.MoreRecords ?? false;
            }
        }

        var objects = objectsById.Values.ToList();

        if (filterGroups.Any())
        {
            var filters = filterGroups.SelectMany(fg => fg.Filters).ToList();
            var filteredObjects = new List<Dictionary<string, object?>>();
            foreach (var filter in filters)
            {
                var filtered = objects
                    .Where(obj => $"'{obj[filter.FieldName]}'" == filter.Value)
                    .ToList();
                filteredObjects.AddRange(filtered);
            }

            objects = filteredObjects;
        }

        return hasMore ? (objects, pageSize, page + 1) : (objects, null, null);
    }

    private string MapFieldType(string zohoType)
    {
        return zohoType.ToLower() switch
        {
            "text" => "string",
            "textarea" => "textarea",
            "email" => "email",
            "phone" => "phone",
            "website" => "url",
            "boolean" => "boolean",
            "date" => "date",
            "datetime" => "datetime",
            "bigint" => "double",
            "currency" => "currency",
            "picklist" => "picklist",
            _ => "string"
        };
    }

    private async Task<ZohoGetOrganizationsOutput> GetZohoOrganizationsOutputAsync(ZohoAuthentication authentication)
    {
        return await _cacheService.CacheAsync(
            $"{nameof(ZohoObjectService)}-{authentication.Id}-Organization",
            async () =>
            {
                var url = $"{authentication.ApiDomain}/crm/v7/org";
                var (_, zohoGetOrganizationsOutput) =
                    await ZohoGetAsync<ZohoGetOrganizationsOutput>(authentication, "Organization", url);
                return zohoGetOrganizationsOutput;
            });
    }

    private async Task<(List<Dictionary<string, object?>> Objects, bool HasMore)> SearchObjectsAsync(
        ZohoAuthentication authentication,
        string entityTypeName,
        string criteria,
        int page = 1,
        int pageSize = 200)
    {
        var url = $"{authentication.ApiDomain}/crm/v7/{entityTypeName}/search?criteria={Uri.EscapeDataString(criteria)}&page={page}&per_page={pageSize}";

        var (_, zohoSearchOutput) = await ZohoGetAsync<ZohoSearchOutput>(authentication, entityTypeName, url);

        return (zohoSearchOutput.Data ?? new List<Dictionary<string, object?>>(), zohoSearchOutput.Info?.MoreRecords ?? false);
    }

    private async Task<(string Str, T Obj)> ZohoGetAsync<T>(
        ZohoAuthentication authentication,
        string typeName,
        string url)
        where T : class, new()
    {
        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Get, RequestUri = new Uri(url),
        };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        var str = await httpResponseMsg.Content.ReadAsStringAsync();

        // Special handling for Zoho
        if (httpResponseMsg.StatusCode == HttpStatusCode.NoContent)
        {
            return (str, new T());
        }

        if (!httpResponseMsg.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ZohoDataApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new SfZObjectOperationException(str, typeName, httpResponseMsg);
        }

        T? obj;
        try
        {
            obj = JsonConvert.DeserializeObject<T>(str);
        }
        catch (Exception e)
        {
            throw new SfZObjectOperationException(e, str, typeName, httpResponseMsg);
        }

        if (obj == null)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ZohoDataApiInvalidOutputReceived,
                new Dictionary<string, string>
                {
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new SfZObjectOperationException(str, typeName, httpResponseMsg);
        }

        return (str, obj);
    }
}