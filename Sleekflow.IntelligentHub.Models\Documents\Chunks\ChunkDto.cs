using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Documents.Chunks;

public class ChunkDto
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("content")]
    public string Content { get; set; }

    [JsonConstructor]
    public ChunkDto(string id, string content)
    {
        Id = id;
        Content = content;
    }

    public ChunkDto(Chunk chunk)
    {
        Id = chunk.Id;
        Content = chunk.Content;
    }
}