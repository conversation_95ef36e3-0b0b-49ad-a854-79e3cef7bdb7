using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Documents.WebCrawlingSessions;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.IntelligentHub.Workers.Activities;

namespace Sleekflow.IntelligentHub.Workers.Orchestrators;

public class StartWebCrawlingSessionOrchestrator
{
    private readonly ILogger<StartWebCrawlingSessionOrchestrator> _logger;

    public StartWebCrawlingSessionOrchestrator(ILogger<StartWebCrawlingSessionOrchestrator> logger)
    {
        _logger = logger;
    }

    public class StartWebCrawlingSessionOrchestratorOutput
    {
        [JsonConstructor]
        public StartWebCrawlingSessionOrchestratorOutput()
        {
        }
    }

    [Function(nameof(StartWebCrawlingSessionOrchestrator))]
    public async Task<StartWebCrawlingSessionOrchestratorOutput> RunOrchestrator(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var input = context.GetInput<StartWebCrawlingSessionEvent>();

        _logger.LogInformation(
            "StartWebCrawlingSessionOrchestrator started for Company: {CompanyId}, URL: {Url}, SessionId: {SessionId}",
            input.SleekflowCompanyId,
            input.Url,
            input.SessionId);

        var taskOptions = new TaskOptions(new TaskRetryOptions(new RetryPolicy(5, TimeSpan.FromSeconds(60), 1)));

        try
        {
            while (true)
            {
                _logger.LogInformation(
                    "Starting crawling for SessionId: {SessionId}",
                    input.SessionId);

                // Step 1: Call CrawlWebsite activity function
                var crawlResult = await context.CallActivityAsync<CrawlWebsite.CrawlWebsiteOutput>(
                    nameof(CrawlWebsite),
                    new CrawlWebsite.CrawlWebsiteInput(input.SleekflowCompanyId, input.SessionId),
                    taskOptions);

                // Step 2: Update the WebCrawlingSession based on the crawlResult
                // we handle terminal conditions immediately
                if (crawlResult.Session.Status == WebCrawlingSessionStatuses.LimitReached ||
                    crawlResult.Session.Status == WebCrawlingSessionStatuses.Finished ||
                    crawlResult.Session.Status == WebCrawlingSessionStatuses.Failed)
                {
                    await context.CallActivityAsync<PatchWebCrawlingSession.PatchWebCrawlingSessionOutput>(
                        nameof(PatchWebCrawlingSession),
                        new PatchWebCrawlingSession.PatchWebCrawlingSessionInput(
                            input.SleekflowCompanyId,
                            input.SessionId,
                            crawlResult.Session.CrawlingResults,
                            crawlResult.Session.UrlsToProcess,
                            crawlResult.Session.Status),
                        taskOptions);

                    _logger.LogInformation(
                        "Completed crawling for SessionId: {SessionId}. " +
                        "Status: {Status}, TotalResults: {TotalResults}, RemainingUrls: {RemainingUrls}",
                        input.SessionId,
                        crawlResult.Session.Status,
                        crawlResult.Session.CrawlingResults.Count,
                        crawlResult.Session.UrlsToProcess.Length);
                    break;
                }

                // it is possible for the user to have paused the crawling, in that case we discard this iteration and finish immediately
                var currentWebCrawlingSession =
                    await context.CallActivityAsync<GetWebCrawlingSession.GetWebCrawlingSessionOutput>(
                        nameof(GetWebCrawlingSession),
                        new GetWebCrawlingSession.GetWebCrawlingSessionInput(
                            input.SleekflowCompanyId,
                            input.SessionId),
                        taskOptions);

                if (currentWebCrawlingSession.WebCrawlingSession.IsSessionPaused())
                {
                    _logger.LogInformation(
                        "Paused crawling for SessionId: {SessionId}. " +
                        "Status: {Status}, TotalResults: {TotalResults}, RemainingUrls: {RemainingUrls}",
                        input.SessionId,
                        currentWebCrawlingSession.WebCrawlingSession.Status,
                        currentWebCrawlingSession.WebCrawlingSession.CrawlingResults.Count,
                        currentWebCrawlingSession.WebCrawlingSession.UrlsToProcess.Length);
                    break;
                }

                // by now the status must be in-progress, we update and proceed to the next iteration
                await context.CallActivityAsync<PatchWebCrawlingSession.PatchWebCrawlingSessionOutput>(
                    nameof(PatchWebCrawlingSession),
                    new PatchWebCrawlingSession.PatchWebCrawlingSessionInput(
                        input.SleekflowCompanyId,
                        input.SessionId,
                        crawlResult.Session.CrawlingResults,
                        crawlResult.Session.UrlsToProcess,
                        crawlResult.Session.Status),
                    taskOptions);

                _logger.LogInformation(
                    "Crawling for SessionId: {SessionId}. " +
                    "Status: {Status}, TotalResults: {TotalResults}, RemainingUrls: {RemainingUrls}",
                    input.SessionId,
                    crawlResult.Session.Status,
                    crawlResult.Session.CrawlingResults.Count,
                    crawlResult.Session.UrlsToProcess.Length);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error in web crawling orchestration for SessionId: {SessionId}",
                input.SessionId);
            throw;
        }

        return new StartWebCrawlingSessionOrchestratorOutput();
    }
}