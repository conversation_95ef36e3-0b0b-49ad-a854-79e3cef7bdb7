using Newtonsoft.Json;
using Pulumi;
using Pulumi.Auth0.Inputs;
using Action = Pulumi.Auth0.Action;
using Logger = Pulumi.Log;
using PulumiAuth0 = Pulumi.Auth0;

namespace Sleekflow.Infras.Components.Auth0s;

public class Auth0s
{
    private const string ResourcesPath = "Resources/auth0";

    public Dictionary<string, PulumiAuth0.Client> InitAuth0(Auth0Config auth0Config)
    {
        #region Tenant

        var sleekflowTenant = new PulumiAuth0.Tenant(
            "sleekflow-tenant",
            new PulumiAuth0.TenantArgs
            {
                Flags = new PulumiAuth0.Inputs.TenantFlagsArgs
                {
                    EnablePublicSignupUserExistsError = true,
                    EnableCustomDomainInEmails = auth0Config.EnableCustomDomainInEmails
                },
                SessionCookie = new PulumiAuth0.Inputs.TenantSessionCookieArgs
                {
                    Mode = "persistent"
                },
                EnabledLocales = new[]
                {
                    "en",
                    "zh-CN",
                    "zh-TW",
                    "id",
                    "pt",
                    "it",
                    "de"
                },
                SupportEmail = "<EMAIL>",
                SupportUrl = "https://sleekflow.io/contact",
                PictureUrl = "https://app.sleekflow.io/static/media/logo-solid.3c58cf66.svg",
                FriendlyName = "SleekFlow",
                IdleSessionLifetime = 720,
                SessionLifetime = 2880
            });

        #endregion

        #region APIs resoucre

        var resourceApiIdentifier = auth0Config.Name switch
        {
            "production" => "https://api.sleekflow.io",
            _ => $"https://api-{auth0Config.Name}.sleekflow.io"
        };

        var resourceServer = new PulumiAuth0.ResourceServer(
            "sleekflow-resource-server-api",
            new PulumiAuth0.ResourceServerArgs()
            {
                AllowOfflineAccess = true,
                SigningAlg = "RS256",
                SkipConsentForVerifiableFirstPartyClients = true,
                Identifier = resourceApiIdentifier,
                TokenLifetime = auth0Config.Name is "dev" or "staging" ? 900 : 86400, // 1 day
                TokenLifetimeForWeb = auth0Config.Name is "dev" or "staging" ? 900 : 86400, // Cannot be greater than TokenLifeTime
            });

        #endregion

        #region Email and Email templates

        var emailProvider = new PulumiAuth0.EmailProvider(
            "sleekflow-email-provider",
            new ()
            {
                Enabled = true,
                Name = "sendgrid",
                DefaultFromAddress = "SleekFlow Team <<EMAIL>>",
                Credentials = new PulumiAuth0.Inputs.EmailProviderCredentialsArgs()
                {
                    ApiKey = "*********************************************************************"
                }
            },
            new CustomResourceOptions()
            {
                RetainOnDelete = true
            });

        var welcomeEmailTemplate = new PulumiAuth0.EmailTemplate(
            "sleekflow-welcome-email",
            new PulumiAuth0.EmailTemplateArgs()
            {
                Template = "welcome_email",
                Syntax = "liquid",
                Subject = "Welcome to Sleekflow !",
                From = "SleekFlow Team <<EMAIL>>",
                Body = File.ReadAllText($"{ResourcesPath}/emails/welcome_email.html"),
                Enabled = false
            },
            new CustomResourceOptions
            {
                DependsOn = new[]
                {
                    emailProvider,
                },
            });

        var verifyEmailTemplate = new PulumiAuth0.EmailTemplate(
            "sleekflow-verify-email",
            new PulumiAuth0.EmailTemplateArgs()
            {
                Template = "verify_email",
                Syntax = "liquid",
                Subject = "Verify your email",
                From = "SleekFlow Team <<EMAIL>>",
                Body = File.ReadAllText($"{ResourcesPath}/emails/verify_email.html"),
                Enabled = true
            },
            new CustomResourceOptions
            {
                DependsOn = new[]
                {
                    emailProvider,
                },
            });

        var resetEmailTemplate = new PulumiAuth0.EmailTemplate(
            "sleekflow-reset-email",
            new PulumiAuth0.EmailTemplateArgs()
            {
                Template = "reset_email",
                Syntax = "liquid",
                Subject = "Account reset",
                From = "SleekFlow Team <<EMAIL>>",
                Body = File.ReadAllText($"{ResourcesPath}/emails/reset_email.html"),
                Enabled = true
            });

        var mfaEmailTemplate = new PulumiAuth0.EmailTemplate(
            "sleekflow-mfa-email",
            new PulumiAuth0.EmailTemplateArgs()
            {
                Template = "enrollment_email",
                Syntax = "liquid",
                Subject = "Invite to two-factor authentication (2FA)",
                From = "SleekFlow Team <<EMAIL>>",
                Body = File.ReadAllText($"{ResourcesPath}/emails/enrollment_email.html"),
                Enabled = true
            });

        #endregion

        #region Auth0 Prompts

        var prompts = new PulumiAuth0.Prompt(
            "sleekflow-prompt",
            new PulumiAuth0.PromptArgs()
            {
                IdentifierFirst = true,
                UniversalLoginExperience = "new",
            });

        var customTextJson = File.ReadAllText($"{ResourcesPath}/prompts/custom-text.json");
        var languageToPromptConfigsDict =
            JsonConvert.DeserializeObject<Dictionary<string, PromptsConfigs>>(customTextJson);

        if (languageToPromptConfigsDict != null && languageToPromptConfigsDict.Any())
        {
            foreach (var (language, promptsConfigs) in languageToPromptConfigsDict)
            {
                var loginPromptCustomText = new PulumiAuth0.PromptCustomText(
                    $"sleekflow-prompt-custom-text-login-{language}",
                    new PulumiAuth0.PromptCustomTextArgs()
                    {
                        Language = language,
                        Prompt = "login",
                        Body = JsonConvert.SerializeObject(
                            new Dictionary<string, Dictionary<string, string>>()
                            {
                                {
                                    "login", promptsConfigs.Login
                                }
                            })
                    });


                var loginIdPromptCustomText = new PulumiAuth0.PromptCustomText(
                    $"sleekflow-prompt-custom-text-login-id-{language}",
                    new PulumiAuth0.PromptCustomTextArgs()
                    {
                        Language = language,
                        Prompt = "login-id",
                        Body = JsonConvert.SerializeObject(
                            new Dictionary<string, Dictionary<string, string>>()
                            {
                                {
                                    "login-id", promptsConfigs.LoginId
                                }
                            })
                    });

                var loginPasswordPromptCustomText = new PulumiAuth0.PromptCustomText(
                    $"sleekflow-prompt-custom-text-login-password-{language}",
                    new PulumiAuth0.PromptCustomTextArgs()
                    {
                        Language = language,
                        Prompt = "login-password",
                        Body = JsonConvert.SerializeObject(
                            new Dictionary<string, Dictionary<string, string>>()
                            {
                                {
                                    "login-password", promptsConfigs.LoginPassword
                                }
                            })
                    });


                var signupPromptCustomText = new PulumiAuth0.PromptCustomText(
                    $"sleekflow-prompt-custom-text-signup-{language}",
                    new PulumiAuth0.PromptCustomTextArgs()
                    {
                        Language = language,
                        Prompt = "signup",
                        Body = JsonConvert.SerializeObject(
                            new Dictionary<string, Dictionary<string, string>>()
                            {
                                {
                                    "signup", promptsConfigs.Signup
                                }
                            })
                    });

                var signupIdPromptCustomText = new PulumiAuth0.PromptCustomText(
                    $"sleekflow-prompt-custom-text-signup-id-{language}",
                    new PulumiAuth0.PromptCustomTextArgs()
                    {
                        Language = language,
                        Prompt = "signup-id",
                        Body = JsonConvert.SerializeObject(
                            new Dictionary<string, Dictionary<string, string>>()
                            {
                                {
                                    "signup-id", promptsConfigs.SignupId
                                }
                            })
                    });


                var signupPasswordPromptCustomText = new PulumiAuth0.PromptCustomText(
                    $"sleekflow-prompt-custom-text-signup-password-{language}",
                    new PulumiAuth0.PromptCustomTextArgs()
                    {
                        Language = language,
                        Prompt = "signup-password",
                        Body = JsonConvert.SerializeObject(
                            new Dictionary<string, Dictionary<string, string>>()
                            {
                                {
                                    "signup-password", promptsConfigs.SignupPassword
                                }
                            })
                    });


                var resetPasswordPromptCustomText = new PulumiAuth0.PromptCustomText(
                    $"sleekflow-prompt-custom-text-reset-password-{language}",
                    new PulumiAuth0.PromptCustomTextArgs()
                    {
                        Language = language,
                        Prompt = "reset-password",
                        Body = JsonConvert.SerializeObject(promptsConfigs.ResetPassword)
                    });

                var mfaOtpPromptCustomText = new PulumiAuth0.PromptCustomText(
                    $"sleekflow-prompt-custom-text-mfa-otp-{language}",
                    new PulumiAuth0.PromptCustomTextArgs()
                    {
                        Language = language,
                        Prompt = "mfa-otp",
                        Body = JsonConvert.SerializeObject(promptsConfigs.MfaOtp)
                    });

                var emailVerificationPromptCustomText = new PulumiAuth0.PromptCustomText(
                    $"sleekflow-prompt-custom-text-email-verification-{language}",
                    new PulumiAuth0.PromptCustomTextArgs()
                    {
                        Language = language,
                        Prompt = "email-verification",
                        Body = JsonConvert.SerializeObject(promptsConfigs.EmailVerification)
                    });
            }
        }

        #endregion

        #region Auth0 Prompt Partials

        var promptPartials = new PulumiAuth0.PromptPartials(
            "sleekflow-prompt-partial-signup-id",
            new PulumiAuth0.PromptPartialsArgs()
            {
                Prompt = "signup-id",
                FormContentEnd =
                    "<div class='ulp-field'><input type='checkbox' name='ulp-terms-of-service' id='terms-of-service'>" +
                    "<label for='terms-of-service'>{{ prompt.screen.texts.varTos }}</label>" +
                    "<div class='ulp-error-info' data-ulp-validation-function='requiredFunction' data-ulp-validation-event-listeners='change'>" +
                    "{{ prompt.screen.texts.varRequiredTos }}</div></div><script>function requiredFunction(element, formSubmitted)" +
                    "{ if(!formSubmitted){ return true; } return element.checked;}</script>"
            });
        var promptLoginPasswordPartials = new PulumiAuth0.PromptPartials(
            "sleekflow-prompt-partial-login-password",
            new PulumiAuth0.PromptPartialsArgs()
            {
                Prompt = "login-password",
                FormContentEnd =
                    "<div class='ulp-field'><input type='checkbox' name='ulp-keep-me-sign-in' id='keep-me-sign-in' checked>" +
                    "<label for='keep-me-sign-in'>{{ prompt.screen.texts.varTos }}</label></div>"
            });

        #endregion

        #region Application Client

        var clients = new Dictionary<string, (PulumiAuth0.Client Client, PulumiAuth0.ClientCredentials Credential)>();
        var nonInteractiveClients = new Dictionary<string, (PulumiAuth0.Client Client, PulumiAuth0.ClientCredentials Credential)>();
        var nativeClients = new Dictionary<string, (PulumiAuth0.Client Client, PulumiAuth0.ClientCredentials Credential)>();

        // Get the Management Client to grant permissions to all application.
        var apiExplorerApplicationClientId = PulumiAuth0.GetClient
            .Invoke(
                new ()
                {
                    Name = "API Explorer Application"
                })
            .Apply(
                c =>
                {
                    Logger.Info($"Fetched {c.Name} client id {c.Id}");

                    return c.Id;
                });

        if (auth0Config.ClientConfigs.Any())
        {
            foreach (var client in auth0Config.ClientConfigs)
            {
                Logger.Info($"Setting up the client [{client.Name}]..");
                var refreshTokenLifeTime =
                    (client.Name == "sleekflow-client-mobile-app")
                        ? 31536000 // 365 days
                        : 2592000; // 30 days

                var clientGrantTypes = client.GrantTypes ??
                                       new List<string>
                                       {
                                           "authorization_code", "implicit", "refresh_token"
                                       };

                var auth0Client = new PulumiAuth0.Client(
                    client.Name,
                    new PulumiAuth0.ClientArgs
                    {
                        Name = client.Name,
                        InitiateLoginUri = client.InitiateLoginUri ?? string.Empty,
                        AllowedLogoutUrls = client.AllowedLogoutUrls,
                        AllowedOrigins = client.AllowedOrigins,
                        WebOrigins = client.WebOrigins,
                        AppType = client.AppType,
                        Callbacks = client.Callbacks,
                        OidcConformant = true,
                        IsFirstParty = true,
                        RefreshToken = new PulumiAuth0.Inputs.ClientRefreshTokenArgs
                        {
                            ExpirationType = "expiring",
                            RotationType = clientGrantTypes.Contains("refresh_token") ? "rotating" : "non-rotating",
                            TokenLifetime = refreshTokenLifeTime,
                            IdleTokenLifetime = 1296000,
                            InfiniteTokenLifetime = false,
                            InfiniteIdleTokenLifetime = true,
                            Leeway = 30
                        },
                        CustomLoginPageOn = true,
                        JwtConfiguration = new PulumiAuth0.Inputs.ClientJwtConfigurationArgs
                        {
                            Alg = "RS256", LifetimeInSeconds = 1209600, SecretEncoded = false
                        },
                        GrantTypes = clientGrantTypes,
                        NativeSocialLogin = (client.AppType == "native"
                            ? new PulumiAuth0.Inputs.ClientNativeSocialLoginArgs()
                            {
                                Apple = new PulumiAuth0.Inputs.ClientNativeSocialLoginAppleArgs()
                                {
                                    Enabled = true
                                }
                            }
                            : null)!,
                        Mobile = (client.AppType == "native")
                            ? new PulumiAuth0.Inputs.ClientMobileArgs()
                            {
                                Ios = new PulumiAuth0.Inputs.ClientMobileIosArgs()
                                {
                                    TeamId = client.NativeClientOptions?.Ios?.TeamId,
                                    AppBundleIdentifier = client.NativeClientOptions?.Ios?.AppBundleIdentifier
                                },
                                Android = new PulumiAuth0.Inputs.ClientMobileAndroidArgs()
                                {
                                    AppPackageName = client.NativeClientOptions?.Android?.AppPackageName,
                                    Sha256CertFingerprints = client.NativeClientOptions?.Android?.KeyHashes
                                }
                            }
                            : null
                    });

                var auth0ClientCredentials = new PulumiAuth0.ClientCredentials(
                    client.Name,
                    new PulumiAuth0.ClientCredentialsArgs()
                    {
                        ClientId = auth0Client.Id, AuthenticationMethod = client.TokenEndpointAuthMethod ?? "none"
                    });

                clients.Add(client.Name, (auth0Client, auth0ClientCredentials));

                if (client.AppType == "non_interactive")
                {
                    nonInteractiveClients.Add(client.Name, (auth0Client, auth0ClientCredentials));

                    Logger.Info(
                        $"{client.Name} is non_interactive. Added it into the machine to machine list, nonInteractiveClients. It will be granted many administrative permissions.");
                }

                if (client.AppType == "native")
                {
                    nativeClients.Add(client.Name, (auth0Client, auth0ClientCredentials));

                    Logger.Info(
                        $"{client.Name} is native. Added it into the machine to machine list, nativeClients. It will be granted many administrative permissions.");
                }
            }
        }

        #endregion

        #region Database Connection

        var sleekflowUsernamePasswordDatabase = new PulumiAuth0.Connection(
            "sleekflow-username-password-database",
            new PulumiAuth0.ConnectionArgs
            {
                Name = "Sleekflow-Username-Password-Authentication",
                Strategy = "auth0",
                Realms = new[]
                {
                    "Sleekflow-Username-Password-Authentication"
                },
                IsDomainConnection = false,
                Options = new PulumiAuth0.Inputs.ConnectionOptionsArgs
                {
                    Mfa = new PulumiAuth0.Inputs.ConnectionOptionsMfaArgs
                    {
                        Active = true, ReturnEnrollSettings = true
                    },
                    RequiresUsername = true,
                    Validation = new PulumiAuth0.Inputs.ConnectionOptionsValidationArgs
                    {
                        Username = new PulumiAuth0.Inputs.ConnectionOptionsValidationUsernameArgs
                        {
                            Min = 5, Max = 128
                        }
                    },
                    PasswordComplexityOptions = new PulumiAuth0.Inputs.ConnectionOptionsPasswordComplexityOptionsArgs
                    {
                        MinLength = 8
                    },
                    PasswordHistories = new PulumiAuth0.Inputs.ConnectionOptionsPasswordHistoryArgs
                    {
                        Enable = false, Size = 5
                    },
                    StrategyVersion = 2,
                    DisableSignup = false,
                    PasswordPolicy = "good",
                    BruteForceProtection = true
                }
            },
            new CustomResourceOptions
            {
                Protect = true, DeleteBeforeReplace = false
            });

        foreach (var client in clients.Values)
        {
            Output
                .Tuple(
                    client.Client.Id,
                    client.Client.Name)
                .Apply(
                    t => new PulumiAuth0.ConnectionClient(
                        $"sleekflow-username-password-database-{t.Item2}",
                        new PulumiAuth0.ConnectionClientArgs
                        {
                            ConnectionId = sleekflowUsernamePasswordDatabase.Id, ClientId = t.Item1,
                        }));
        }

        var _ = new PulumiAuth0.ConnectionClient(
            "sleekflow-username-password-database-api-explorer-application",
            new PulumiAuth0.ConnectionClientArgs
            {
                ConnectionId = sleekflowUsernamePasswordDatabase.Id, ClientId = apiExplorerApplicationClientId,
            });

        #endregion

        #region Social / Enterprise Connections

        if (auth0Config.ConnectionConfigs.Any())
        {
            foreach (var connectionConfig in auth0Config.ConnectionConfigs)
            {
                Logger.Info($"Setting up the connection [{connectionConfig.Name}]..");
                Logger.Info(
                    $"name: {connectionConfig.Name}, \n" + $"strategy: {connectionConfig.Strategy},\n " +
                    $"scopes: {connectionConfig.Options.Scopes},\n " +
                    $"client_id: {connectionConfig.Options.ClientId},\n " +
                    $"client_secret: {connectionConfig.Options.ClientSecret},\n " +
                    $"kid: {connectionConfig.Options.KeyId},\n " + $"team_id: {connectionConfig.Options.TeamId}");

                var connection = new PulumiAuth0.Connection(
                    connectionConfig.Name,
                    new PulumiAuth0.ConnectionArgs
                    {
                        Name = connectionConfig.Name,
                        Strategy = connectionConfig.Strategy,
                        Options = new PulumiAuth0.Inputs.ConnectionOptionsArgs
                        {
                            Scopes = connectionConfig.Options.Scopes,
                            ClientId = connectionConfig.Options.ClientId,
                            ClientSecret = connectionConfig.Options.ClientSecret,
                            KeyId = connectionConfig.Options.KeyId!,
                            TeamId = connectionConfig.Options.TeamId!, }
                    },
                    new CustomResourceOptions
                    {
                        Protect = !connectionConfig.ForceReplace, DeleteBeforeReplace = connectionConfig.ForceReplace
                    });

                foreach (var client in clients.Values)
                {
                    Output.Tuple(client.Client.Id, client.Client.Name)
                        .Apply(
                            t =>
                            {
                                if (connectionConfig.EnableClients.Contains(t.Item2))
                                {
                                    return new PulumiAuth0.ConnectionClient(
                                        $"{connectionConfig.Name}-{t.Item2}",
                                        new PulumiAuth0.ConnectionClientArgs
                                        {
                                            ConnectionId = connection.Id, ClientId = t.Item1,
                                        });
                                }

                                return null;
                            });
                }

                var ___ = new PulumiAuth0.ConnectionClient(
                    $"{connectionConfig.Name}-api-explorer-application",
                    new PulumiAuth0.ConnectionClientArgs
                    {
                        ConnectionId = connection.Id, ClientId = apiExplorerApplicationClientId,
                    });
            }

            // Will move to production when the dev environment is stable
            if (auth0Config.Name == "production" || auth0Config.Name == "staging")
            {
                #region SHKP ADFS Connection

                var shkpAdfsConnection = new PulumiAuth0.Connection(
                    "shkp-adfs",
                    new PulumiAuth0.ConnectionArgs
                    {
                        Name = "shkp",
                        Strategy = "adfs",
                        IsDomainConnection = false,
                        ShowAsButton = false,
                        DisplayName = "SHKP",
                        Options = new PulumiAuth0.Inputs.ConnectionOptionsArgs
                        {
                            TenantDomain = "shkp.com",
                            DomainAliases = new InputList<string>()
                            {
                                "shkp.com"
                            },
                            FedMetadataXml = File.ReadAllText($"{ResourcesPath}/connections/FederationMetadata.xml"),
                            SignInEndpoint = "https://adfs.shkp.com/adfs/ls/",
                            ApiEnableUsers = false,
                            ShouldTrustEmailVerifiedConnection = "always_set_emails_as_verified",
                            UpstreamParams = """
                                             {"wreply": {"value":"https://sso.sleekflow.io/login/callback"}}
                                             """
                        }
                    });

                var shkpEnabledClients = clients
                    .Where(
                        c => c.Key is "sleekflow-client-web-app" or "sleekflow-client-web-v2-app"
                            or "sleekflow-client-mobile-app")
                    .Select(c => c.Value.Client.Id)
                    .ToList();

                var shkpAdfsConnectionClient = new PulumiAuth0.ConnectionClients(
                    "shkp-adfs-enabled-clients",
                    new ()
                    {
                        ConnectionId = shkpAdfsConnection.Id, EnabledClients = shkpEnabledClients
                    });

                #endregion

                #region SHKP OpenID Connection

                var shkpOIDCConnection = new PulumiAuth0.Connection(
                    "hongyip-oidc",
                    new PulumiAuth0.ConnectionArgs
                    {
                        Name = "hongyip",
                        Strategy = "oidc",
                        IsDomainConnection = false,
                        ShowAsButton = false,
                        DisplayName = "HongYip",
                        Options = new PulumiAuth0.Inputs.ConnectionOptionsArgs
                        {
                            Type = "back_channel",
                            TenantDomain = "hongyip.com",
                            Scopes = "openid email profile",
                            Issuer = "https://sso2.hongyip.com/auth/realms/hy-sso-prd01",
                            JwksUri = "https://sso2.hongyip.com/auth/realms/hy-sso-prd01/protocol/openid-connect/certs",
                            ClientId = "shkp-wtsapp-bus",
                            ClientSecret = "rFCo7G6TVIL4l5l34Z2kOocUk1i8aSGw",
                            DiscoveryUrl =
                                "https://sso2.hongyip.com/auth/realms/hy-sso-prd01/.well-known/openid-configuration",
                            TokenEndpoint =
                                "https://sso2.hongyip.com/auth/realms/hy-sso-prd01/protocol/openid-connect/token",
                            UserinfoEndpoint =
                                "https://sso2.hongyip.com/auth/realms/hy-sso-prd01/protocol/openid-connect/userinfo",
                            AuthorizationEndpoint =
                                "https://sso2.hongyip.com/auth/realms/hy-sso-prd01/protocol/openid-connect/auth",
                            SetUserRootAttributes = "on_first_login",
                            ConnectionSettings = new ConnectionOptionsConnectionSettingsArgs
                            {
                                Pkce = "auto"
                            },
                            DomainAliases = new InputList<string>()
                            {
                                "hongyip.com", "hongyip3.com"
                            },
                            AttributeMap = new ConnectionOptionsAttributeMapArgs
                            {
                                MappingMode = "use_map",
                                Attributes = File.ReadAllText(
                                    $"{ResourcesPath}/connections/hongyip-oidc-attribute-map.json"),
                            },
                        }
                    });

                var hongyipOidcConnectionClient = new PulumiAuth0.ConnectionClients(
                    "hongyip-oidc-enabled-clients",
                    new ()
                    {
                        ConnectionId = shkpOIDCConnection.Id, EnabledClients = shkpEnabledClients
                    });

                #endregion

                #region HKBN Azure AD Connection

                var hkbnAzureAdConnection = new PulumiAuth0.Connection(
                    "hkbn-azure-ad",
                    new PulumiAuth0.ConnectionArgs
                    {
                        Name = "HKBN",
                        Strategy = "waad",
                        IsDomainConnection = false,
                        ShowAsButton = false,
                        DisplayName = "HKBN",
                        Options = new PulumiAuth0.Inputs.ConnectionOptionsArgs
                        {
                            TenantDomain = "hkbn.com.hk",
                            Domain = "hkbn.com.hk",
                            DomainAliases = new InputList<string>()
                            {
                                "hkbn.com.hk", "cs.hkbn.com.hk"
                            },
                            IdentityApi = "azure-active-directory-v1.0",
                            ClientId = "a70ba81f-7c1c-45b9-9935-d56e92137b24",
                            ClientSecret = "****************************************",
                            ApiEnableUsers = true,
                            StrategyVersion = 2,
                            UserIdAttribute = "oid",
                            Scopes = new InputList<string>()
                            {
                                "basic_profile", "ext_groups", "ext_profile",
                            },
                            SetUserRootAttributes = "on_first_login",
                            ShouldTrustEmailVerifiedConnection = "always_set_emails_as_verified",
                            UpstreamParams = JsonConvert.SerializeObject(
                                new Dictionary<string, object?>
                                {
                                    ["screen_name"] = new Dictionary<string, object?>
                                    {
                                        ["alias"] = "login_hint",
                                    },
                                })
                        }
                    });

                var hkbnEnabledClients = clients
                    .Where(
                        c => c.Key is "sleekflow-client-web-app" or "sleekflow-client-web-v2-app"
                            or "sleekflow-client-mobile-app")
                    .Select(c => c.Value.Client.Id)
                    .ToList();

                var hkbnAzureADConnectionClient = new PulumiAuth0.ConnectionClients(
                    "hkbn-azure-ad-enabled-clients",
                    new ()
                    {
                        ConnectionId = hkbnAzureAdConnection.Id, EnabledClients = hkbnEnabledClients
                    });

                #endregion
            }
        }

        #endregion

        #region Grant all API permissions to the machine to machine applications

        if (nonInteractiveClients.Any())
        {
            foreach (var client in nonInteractiveClients)
            {
                var unused = new PulumiAuth0.ClientGrant(
                    $"sleekflow-client-grant-{client.Key}",
                    new ()
                    {
                        ClientId = client.Value.Client.Id,
                        Audience = $"https://{auth0Config.Domain}/api/v2/",
                        Scopes = new[]
                        {
                            "read:client_grants",
                            "create:client_grants",
                            "delete:client_grants",
                            "update:client_grants",
                            "read:users",
                            "update:users",
                            "delete:users",
                            "create:users",
                            "read:users_app_metadata",
                            "update:users_app_metadata",
                            "delete:users_app_metadata",
                            "create:users_app_metadata",
                            "read:user_custom_blocks",
                            "create:user_custom_blocks",
                            "delete:user_custom_blocks",
                            "create:user_tickets",
                            "read:clients",
                            "update:clients",
                            "delete:clients",
                            "create:clients",
                            "read:client_keys",
                            "update:client_keys",
                            "delete:client_keys",
                            "create:client_keys",
                            "read:connections",
                            "update:connections",
                            "delete:connections",
                            "create:connections",
                            "read:resource_servers",
                            "update:resource_servers",
                            "delete:resource_servers",
                            "create:resource_servers",
                            "read:device_credentials",
                            "update:device_credentials",
                            "delete:device_credentials",
                            "create:device_credentials",
                            "read:rules",
                            "update:rules",
                            "delete:rules",
                            "create:rules",
                            "read:rules_configs",
                            "update:rules_configs",
                            "delete:rules_configs",
                            "read:hooks",
                            "update:hooks",
                            "delete:hooks",
                            "create:hooks",
                            "read:actions",
                            "update:actions",
                            "delete:actions",
                            "create:actions",
                            "read:email_provider",
                            "update:email_provider",
                            "delete:email_provider",
                            "create:email_provider",
                            "blacklist:tokens",
                            "read:stats",
                            "read:insights",
                            "read:tenant_settings",
                            "update:tenant_settings",
                            "read:logs",
                            "read:logs_users",
                            "read:shields",
                            "create:shields",
                            "update:shields",
                            "delete:shields",
                            "read:anomaly_blocks",
                            "delete:anomaly_blocks",
                            "update:triggers",
                            "read:triggers",
                            "read:grants",
                            "delete:grants",
                            "read:guardian_factors",
                            "update:guardian_factors",
                            "read:guardian_enrollments",
                            "delete:guardian_enrollments",
                            "create:guardian_enrollment_tickets",
                            "read:user_idp_tokens",
                            "create:passwords_checking_job",
                            "delete:passwords_checking_job",
                            "read:custom_domains",
                            "delete:custom_domains",
                            "create:custom_domains",
                            "update:custom_domains",
                            "read:email_templates",
                            "create:email_templates",
                            "update:email_templates",
                            "read:mfa_policies",
                            "update:mfa_policies",
                            "read:roles",
                            "create:roles",
                            "delete:roles",
                            "update:roles",
                            "read:prompts",
                            "update:prompts",
                            "read:branding",
                            "update:branding",
                            "delete:branding",
                            "read:log_streams",
                            "create:log_streams",
                            "delete:log_streams",
                            "update:log_streams",
                            "create:signing_keys",
                            "read:signing_keys",
                            "update:signing_keys",
                            "read:limits",
                            "update:limits",
                            "create:role_members",
                            "read:role_members",
                            "delete:role_members",
                            "read:entitlements",
                            "read:attack_protection",
                            "update:attack_protection",
                            "read:organizations_summary",
                            "read:organizations",
                            "update:organizations",
                            "create:organizations",
                            "delete:organizations",
                            "create:organization_members",
                            "read:organization_members",
                            "delete:organization_members",
                            "create:organization_connections",
                            "read:organization_connections",
                            "update:organization_connections",
                            "delete:organization_connections",
                            "create:organization_member_roles",
                            "read:organization_member_roles",
                            "delete:organization_member_roles",
                            "create:organization_invitations",
                            "read:organization_invitations",
                            "delete:organization_invitations",
                            "read:authentication_methods",
                            "update:authentication_methods",
                            "delete:authentication_methods"
                        }
                    });
            }
        }

        #endregion

        #region Auth0 Actions

        var sleekflowActionRestrictRegistrationAction =
            InitSleekflowActionRestrictRegistrationAction(auth0Config);
        var sleekflowActionPreUserRegistrationAction =
            InitSleekflowActionPreUserRegistrationAction(auth0Config, clients);
        var sleekflowActionRequiresMfaAction =
            InitSleekflowActionRequiresMfaAction(auth0Config, clients);
        var sleekflowActionPostLoginAction =
            InitSleekflowActionPostLoginAction(auth0Config, clients);
        var sleekflowActionPostChangePasswordAction =
            InitSleekflowActionPostChangePasswordAction(auth0Config, clients);
        var sleekflowActionEnrichJwtAction =
            InitSleekflowActionEnrichJwtAction();

        // Apply the action scripts into the action flow
        InitPreUserRegistrationActions(
            sleekflowActionRestrictRegistrationAction,
            sleekflowActionPreUserRegistrationAction);
        InitPostChangePasswordActions(
            sleekflowActionPostChangePasswordAction,
            sleekflowActionPostLoginAction);
        InitPostLoginActions(
            sleekflowActionPostLoginAction,
            sleekflowActionEnrichJwtAction,
            sleekflowActionRequiresMfaAction);

        #endregion

        if (auth0Config.CustomDomainConfig.IsEnabled)
        {
            #region Custom Domain

            var customDomain = new PulumiAuth0.CustomDomain(
                "sleekflow-custom-domain",
                new PulumiAuth0.CustomDomainArgs
                {
                    Domain = auth0Config.CustomDomainConfig.Domain, Type = auth0Config.CustomDomainConfig.Type,
                });

            #endregion

            #region Branding

            var branding = new PulumiAuth0.Branding(
                "sleekflow-branding",
                new PulumiAuth0.BrandingArgs()
                {
                    LogoUrl = "https://app.sleekflow.io/static/media/logo-solid.3c58cf66.svg",
                    UniversalLogin = new PulumiAuth0.Inputs.BrandingUniversalLoginArgs()
                    {
                        Body = File.ReadAllText($"{ResourcesPath}/branding/universal_login.html")
                    }
                },
                new CustomResourceOptions()
                {
                    DependsOn = customDomain
                });

            #endregion
        }

        #region Powerflow Roles

        var cmsAdmin =
            new PulumiAuth0.Role(
                "sleekflow-role-InternalCmsAdmin",
                new PulumiAuth0.RoleArgs
                {
                    Name = "InternalCmsAdmin"
                });
        var cmsCustomerSuccess =
            new PulumiAuth0.Role(
                "sleekflow-role-InternalCmsCustomerSuccessUser",
                new PulumiAuth0.RoleArgs
                {
                    Name = "InternalCmsCustomerSuccessUser"
                });
        var cmsSalesUser =
            new PulumiAuth0.Role(
                "sleekflow-role-InternalCmsSalesUser",
                new PulumiAuth0.RoleArgs
                {
                    Name = "InternalCmsSalesUser"
                });
        var cmsSuperUser =
            new PulumiAuth0.Role(
                "sleekflow-role-InternalCmsSuperUser",
                new PulumiAuth0.RoleArgs
                {
                    Name = "InternalCmsSuperUser"
                });
        var cmsUser =
            new PulumiAuth0.Role(
                "sleekflow-role-InternalCmsUser",
                new PulumiAuth0.RoleArgs
                {
                    Name = "InternalCmsUser"
                });
        var resellerPortalUser =
            new PulumiAuth0.Role(
                "sleekflow-role-ResellerPortalUser",
                new PulumiAuth0.RoleArgs
                {
                    Name = "ResellerPortalUser"
                });
        var resellerPortalSuperUser =
            new PulumiAuth0.Role(
                "sleekflow-role-ResellerPortalSuperUser",
                new PulumiAuth0.RoleArgs
                {
                    Name = "ResellerPortalSuperUser"
                });
        var resellerPortalAdmin =
            new PulumiAuth0.Role(
                "sleekflow-role-ResellerPortalAdmin",
                new PulumiAuth0.RoleArgs
                {
                    Name = "ResellerPortalAdmin"
                });

        #endregion

        #region Attack Protection

        var protection = new PulumiAuth0.AttackProtection(
            "sleekflow-attack-protection",
            new PulumiAuth0.AttackProtectionArgs()
            {
                // Caution: the maximum number of allowed IP addresses is 100
                SuspiciousIpThrottling = new PulumiAuth0.Inputs.AttackProtectionSuspiciousIpThrottlingArgs
                {
                    Enabled = true,
                    Allowlists = new List<string>()
                    {
                        // SleekFlow HK office ip address
                        "**************",
                        "**************",

                        // Self hosted github runner for playwright testing
                        "*************",

                        "**********/14",
                        "***********/20",
                        "************/20",
                        "***********/20",
                        "**********/16",
                        "***********/16",
                        "***********/15",
                        "************/20",
                        "*************/21",
                        "************/21",
                        "************/28",
                        "*************/28",
                        "************/28",
                        "************/28",
                        "************/28",
                        "************/19",
                        "*************/17",
                        "***********/18",
                        "************/17",
                        "************/17",
                        "***********/22",
                        "202.129.242.0/23",
                        "13.113.196.48/28",
                        "13.228.64.80/28",
                        "13.124.145.0/28",
                        "13.126.23.64/28",
                        "13.210.3.208/28",
                        "13.215.171.240/28",
                        "35.73.89.117/32",
                        "18.181.43.11/32",
                        "54.95.206.252/32",
                        "54.254.118.123/32",
                        "13.251.9.241/32",
                        "13.250.175.171/32",
                        "43.201.151.176/28",
                        "54.233.205.0/28",
                        "177.71.229.247/32",
                        "18.228.66.156/32",
                        "18.228.207.180/32",
                        // surfshark vpn for playwright automation CI to bypass captcha
                        "146.70.186.198",

                        // staging eas
                        "20.2.57.209",
                        "20.2.59.117",
                        "20.2.60.251",
                        "20.2.62.72",
                        "20.2.62.109",
                        "20.2.62.214",
                        "20.205.69.86",

                        // staging eus
                        "172.212.46.54",
                        "172.212.46.83",
                        "172.212.46.89",
                        "172.212.46.108",
                        "172.212.46.130",
                        "172.212.46.132",
                        "20.119.8.51",

                        // eas
                        "52.184.91.234",
                        "52.184.92.50",
                        "52.184.94.48",
                        "52.184.95.34",
                        "52.184.95.54",
                        "52.184.95.113",
                        "20.189.104.97",

                        // eus
                        "20.121.238.179",
                        "20.121.238.186",
                        "20.121.238.209",
                        "20.121.238.214",
                        "20.121.238.229",
                        "20.121.238.255",
                        "20.119.16.46",

                        // seas
                        "20.198.191.30",
                        "20.198.191.224",
                        "20.205.240.50",
                        "20.205.240.61",
                        "20.205.240.253",
                        "20.205.241.1",
                        "20.212.64.16",

                        // uaen
                        "20.174.40.113",
                        "20.174.41.216",
                        "20.174.41.217",
                        "20.174.40.96",
                        "20.174.41.218",
                        "20.174.41.219",
                        "***********",

                        // weu
                        "**********",
                        "***********",
                        "***********",
                        "***********",
                        "***********",
                        "***********",
                        "*************",

                        // HKBN ES & RS
                        "*************"
                    },
                    PreLogin = new PulumiAuth0.Inputs.AttackProtectionSuspiciousIpThrottlingPreLoginArgs()
                    {
                        MaxAttempts = 100, Rate = 864000
                    },
                    PreUserRegistration =
                        new PulumiAuth0.Inputs.AttackProtectionSuspiciousIpThrottlingPreUserRegistrationArgs()
                        {
                            MaxAttempts = 50, Rate = 1200
                        },
                    Shields = new[]
                    {
                        "admin_notification",
                        "block"
                    }
                },
                BruteForceProtection = new PulumiAuth0.Inputs.AttackProtectionBruteForceProtectionArgs()
                {
                    // Caution: the maximum number of allowed IP addresses is 100
                    Enabled = true,
                    Allowlists = new List<string>()
                    {
                        // SleekFlow HK office ip address
                        "**************",
                        "**************",

                        // Self hosted github runner for playwright testing
                        "*************",

                        "**********/14",
                        "***********/20",
                        "************/20",
                        "***********/20",
                        "**********/16",
                        "***********/16",
                        "***********/15",
                        "************/20",
                        "*************/21",
                        "************/21",
                        "************/28",
                        "*************/28",
                        "************/28",
                        "************/28",
                        "************/28",
                        "************/19",
                        "*************/17",
                        "***********/18",
                        "************/17",
                        "************/17",
                        "***********/22",
                        "202.129.242.0/23",
                        "13.113.196.48/28",
                        "13.228.64.80/28",
                        "13.124.145.0/28",
                        "13.126.23.64/28",
                        "13.210.3.208/28",
                        "13.215.171.240/28",
                        "35.73.89.117/32",
                        "18.181.43.11/32",
                        "54.95.206.252/32",
                        "54.254.118.123/32",
                        "13.251.9.241/32",
                        "13.250.175.171/32",
                        "43.201.151.176/28",
                        "54.233.205.0/28",
                        "177.71.229.247/32",
                        "18.228.66.156/32",
                        "18.228.207.180/32",
                        // surfshark vpn for playwright automation CI to bypass captcha
                        "146.70.186.198",

                        // staging eas
                        "20.2.57.209",
                        "20.2.59.117",
                        "20.2.60.251",
                        "20.2.62.72",
                        "20.2.62.109",
                        "20.2.62.214",
                        "20.205.69.86",

                        // staging eus
                        "172.212.46.54",
                        "172.212.46.83",
                        "172.212.46.89",
                        "172.212.46.108",
                        "172.212.46.130",
                        "172.212.46.132",
                        "20.119.8.51",

                        // eas
                        "52.184.91.234",
                        "52.184.92.50",
                        "52.184.94.48",
                        "52.184.95.34",
                        "52.184.95.54",
                        "52.184.95.113",
                        "20.189.104.97",

                        // eus
                        "20.121.238.179",
                        "20.121.238.186",
                        "20.121.238.209",
                        "20.121.238.214",
                        "20.121.238.229",
                        "20.121.238.255",
                        "20.119.16.46",

                        // seas
                        "20.198.191.30",
                        "20.198.191.224",
                        "20.205.240.50",
                        "20.205.240.61",
                        "20.205.240.253",
                        "20.205.241.1",
                        "20.212.64.16",

                        // uaen
                        "20.174.40.113",
                        "20.174.41.216",
                        "20.174.41.217",
                        "20.174.40.96",
                        "20.174.41.218",
                        "20.174.41.219",
                        "***********",

                        // weu
                        "**********",
                        "***********",
                        "***********",
                        "***********",
                        "***********",
                        "***********",
                        "*************",

                        // HKBN ES & RS
                        "*************"
                    },
                    Mode = "count_per_identifier_and_ip",
                    MaxAttempts = 10,
                    Shields = new[]
                    {
                        "user_notification",
                        "block"
                    }
                },
                BreachedPasswordDetection = new PulumiAuth0.Inputs.AttackProtectionBreachedPasswordDetectionArgs()
                {
                    Enabled = true,
                    Method = "standard",
                    AdminNotificationFrequencies = new List<string>(),
                    Shields = new List<string>(),
                    PreUserRegistration =
                        new PulumiAuth0.Inputs.AttackProtectionBreachedPasswordDetectionPreUserRegistrationArgs()
                        {
                            Shields = new List<string>()
                        }
                }
            });

        #endregion

        #region MFA Multi-factor Auth

        var myGuardian = new PulumiAuth0.Guardian(
            "sleekflow-mfa",
            new ()
            {
                Duo = null,
                Email = false,
                Otp = true,
                Phone = new PulumiAuth0.Inputs.GuardianPhoneArgs()
                {
                    Enabled = false,
                    Provider = "twilio",
                    MessageTypes = new List<string>()
                    {
                        "sms"
                    },
                    Options = new PulumiAuth0.Inputs.GuardianPhoneOptionsArgs()
                    {
                        Sid = "**********************************",
                        AuthToken = "9897aa9855d1e192755d73143107cbe2",
                        EnrollmentMessage =
                            "{{code}} is your verification code for {{tenant.friendly_name}}. Please enter this code to verify your enrollment.",
                        VerificationMessage = "{{code}} is your verification code for {{tenant.friendly_name}}."
                    }
                },
                Policy = "never",
                Push = null,
                RecoveryCode = null,
                WebauthnPlatform = new PulumiAuth0.Inputs.GuardianWebauthnPlatformArgs
                {
                    Enabled = false,
                },
                WebauthnRoaming = null,
            });

        #endregion

        return clients.ToDictionary(c => c.Key, c => c.Value.Client);
    }

    private static void InitPreUserRegistrationActions(
        Action sleekflowActionRestrictRegistrationAction,
        Action sleekflowActionPreUserRegistrationAction)
    {
        var sleekflowTriggerActionsPreUserRegistration = new PulumiAuth0.TriggerActions(
            "sleekflow-trigger-binding-pre-user-registration",
            new PulumiAuth0.TriggerActionsArgs
            {
                Trigger = "pre-user-registration",
                Actions = new[]
                {
                    new PulumiAuth0.Inputs.TriggerActionsActionArgs
                    {
                        Id = Output
                            .Tuple(
                                sleekflowActionRestrictRegistrationAction.Id,
                                sleekflowActionRestrictRegistrationAction.VersionId)
                            .Apply(t => t.Item1),
                        DisplayName = sleekflowActionRestrictRegistrationAction.Name
                    },
                    new PulumiAuth0.Inputs.TriggerActionsActionArgs
                    {
                        Id = Output
                            .Tuple(
                                sleekflowActionPreUserRegistrationAction.Id,
                                sleekflowActionPreUserRegistrationAction.VersionId)
                            .Apply(t => t.Item1),
                        DisplayName = sleekflowActionPreUserRegistrationAction.Name
                    }
                }
            });
    }

    private static void InitPostChangePasswordActions(
        Action sleekflowActionPostChangePasswordAction,
        Action sleekflowActionPostLoginAction)
    {
        var sleekflowTriggerActionsPostChangePassword = new PulumiAuth0.TriggerActions(
            "sleekflow-trigger-binding-post-change-password",
            new PulumiAuth0.TriggerActionsArgs
            {
                Trigger = "post-change-password",
                Actions = new[]
                {
                    new PulumiAuth0.Inputs.TriggerActionsActionArgs
                    {
                        Id = Output
                            .Tuple(
                                sleekflowActionPostChangePasswordAction.Id,
                                sleekflowActionPostLoginAction.VersionId)
                            .Apply(t => t.Item1),
                        DisplayName = sleekflowActionPostChangePasswordAction.Name
                    }
                }
            });
    }

    private static void InitPostLoginActions(
        Action sleekflowActionPostLoginAction,
        Action sleekflowActionEnrichJwtAction,
        Action sleekflowActionRequiresMfaAction)
    {
        var sleekflowTriggerActionsPostLoginActions = new List<PulumiAuth0.Inputs.TriggerActionsActionArgs>()
        {
            new PulumiAuth0.Inputs.TriggerActionsActionArgs
            {
                Id = Output
                    .Tuple(
                        sleekflowActionPostLoginAction.Id,
                        sleekflowActionPostLoginAction.VersionId)
                    .Apply(t => t.Item1),
                DisplayName = sleekflowActionPostLoginAction.Name
            },
            new PulumiAuth0.Inputs.TriggerActionsActionArgs
            {
                Id = Output
                    .Tuple(
                        sleekflowActionEnrichJwtAction.Id,
                        sleekflowActionEnrichJwtAction.VersionId)
                    .Apply(t => t.Item1),
                DisplayName = sleekflowActionEnrichJwtAction.Name,
            },
            new PulumiAuth0.Inputs.TriggerActionsActionArgs
            {
                Id = Output
                    .Tuple(
                        sleekflowActionRequiresMfaAction.Id,
                        sleekflowActionRequiresMfaAction.VersionId)
                    .Apply(t => t.Item1),
                DisplayName = sleekflowActionRequiresMfaAction.Name
            }
        };

        var sleekflowTriggerActionsPostLogin = new PulumiAuth0.TriggerActions(
            "sleekflow-trigger-binding-post-login",
            new PulumiAuth0.TriggerActionsArgs
            {
                Trigger = "post-login", Actions = sleekflowTriggerActionsPostLoginActions.ToArray()
            },
            new CustomResourceOptions()
            {
                DependsOn = new List<Resource>()
                {
                    sleekflowActionPostLoginAction,
                    sleekflowActionEnrichJwtAction,
                    sleekflowActionRequiresMfaAction
                }
            });
    }

    private static Action InitSleekflowActionEnrichJwtAction()
    {
        var sleekflowActionEnrichJwt = new Action(
            "sleekflow-action-enrich-jwt",
            new PulumiAuth0.ActionArgs
            {
                Code = File.ReadAllText($"{ResourcesPath}/actions/sleekflow-jwt.js"),
                Name = "Enrich Jwt",
                Dependencies = new InputList<PulumiAuth0.Inputs.ActionDependencyArgs>
                {
                    new PulumiAuth0.Inputs.ActionDependencyArgs
                    {
                        Name = "jwt-decode", Version = "3.1.2"
                    }
                },
                SupportedTriggers = new PulumiAuth0.Inputs.ActionSupportedTriggersArgs
                {
                    Id = "post-login", Version = "v3"
                },
                Deploy = true,
                Runtime = "node16",
            },
            new CustomResourceOptions()
            {
                RetainOnDelete = true
            });
        return sleekflowActionEnrichJwt;
    }

    private static Action InitSleekflowActionPostChangePasswordAction(
        Auth0Config auth0Config,
        Dictionary<string, (PulumiAuth0.Client Client, PulumiAuth0.ClientCredentials Credential)> clients)
    {
        var actionPath =
            $"{ResourcesPath}/actions/global-deployment-update/sleekflow-post-change-password.js";

        var sleekflowActionPostChangePassword = new Action(
            "sleekflow-action-post-change-password",
            new PulumiAuth0.ActionArgs
            {
                Code = File.ReadAllText(actionPath),
                Name = "Post Change Password",
                Dependencies = new InputList<PulumiAuth0.Inputs.ActionDependencyArgs>
                {
                    new PulumiAuth0.Inputs.ActionDependencyArgs
                    {
                        Name = "jwt-encode", Version = "1.0.1"
                    },
                    new PulumiAuth0.Inputs.ActionDependencyArgs
                    {
                        Name = "lodash", Version = "4.17.21"
                    },
                    new PulumiAuth0.Inputs.ActionDependencyArgs
                    {
                        Name = "jwt-decode", Version = "3.1.2"
                    },
                    new PulumiAuth0.Inputs.ActionDependencyArgs
                    {
                        Name = "auth0", Version = "2.44.0"
                    }
                },
                Secrets = new InputList<PulumiAuth0.Inputs.ActionSecretArgs>
                {
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "domain", Value = auth0Config.Domain
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "client_id", Value = clients["sleekflow-client-auth0-actions"].Client.ClientId
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "client_secret",
                        Value = clients["sleekflow-client-auth0-actions"].Credential.ClientSecret
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "post_change_password_webhook", Value = auth0Config.PostChangePasswordWebhook
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "issuer", Value = auth0Config.ActionIssuer
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "audience", Value = auth0Config.ActionAudience
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "show_debug", Value = "false"
                    }
                },
                SupportedTriggers = new PulumiAuth0.Inputs.ActionSupportedTriggersArgs
                {
                    Id = "post-change-password", Version = "v2"
                },
                Deploy = true,
                Runtime = "node16",
            });
        return sleekflowActionPostChangePassword;
    }

    private static Action InitSleekflowActionPostLoginAction(
        Auth0Config auth0Config,
        Dictionary<string, (PulumiAuth0.Client Client, PulumiAuth0.ClientCredentials Credential)> clients)
    {
        var actionPath =
                $"{ResourcesPath}/actions/global-deployment-update/sleekflow-post-login.js";

        var sleekflowActionPostLogin = new Action(
            "sleekflow-action-post-login",
            new PulumiAuth0.ActionArgs
            {
                Code = File.ReadAllText(actionPath),
                Name = "Post Login",
                Dependencies = new InputList<PulumiAuth0.Inputs.ActionDependencyArgs>
                {
                    new PulumiAuth0.Inputs.ActionDependencyArgs
                    {
                        Name = "jwt-encode", Version = "1.0.1"
                    },
                    new PulumiAuth0.Inputs.ActionDependencyArgs
                    {
                        Name = "lodash", Version = "4.17.21"
                    },
                    new PulumiAuth0.Inputs.ActionDependencyArgs
                    {
                        Name = "jwt-decode", Version = "3.1.2"
                    },
                    new PulumiAuth0.Inputs.ActionDependencyArgs
                    {
                        Name = "auth0", Version = "2.44.0"
                    }
                },
                Secrets = new InputList<PulumiAuth0.Inputs.ActionSecretArgs>
                {
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "domain", Value = auth0Config.Domain
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "client_id", Value = clients["sleekflow-client-auth0-actions"].Client.ClientId
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "client_secret",
                        Value = clients["sleekflow-client-auth0-actions"].Credential.ClientSecret
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "post_login_webhook", Value = auth0Config.PostLoginWebhook
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "issuer", Value = auth0Config.ActionIssuer
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "audience", Value = auth0Config.ActionAudience
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "show_debug", Value = "false"
                    }
                },
                SupportedTriggers = new PulumiAuth0.Inputs.ActionSupportedTriggersArgs
                {
                    Id = "post-login", Version = "v3"
                },
                Deploy = true,
                Runtime = "node16",
            });
        return sleekflowActionPostLogin;
    }

    private static Action InitSleekflowActionRequiresMfaAction(
        Auth0Config auth0Config,
        Dictionary<string, (PulumiAuth0.Client Client, PulumiAuth0.ClientCredentials Credential)> clients)
    {
        var actionPath =
                $"{ResourcesPath}/actions/global-deployment-update/sleekflow-requires-mfa.js";

        var sleekflowActionRequiresMfa = new Action(
            "sleekflow-action-requires-mfa",
            new PulumiAuth0.ActionArgs
            {
                Code = File.ReadAllText(actionPath),
                Name = "Requires Mfa",
                Dependencies = new InputList<PulumiAuth0.Inputs.ActionDependencyArgs>
                {
                    new PulumiAuth0.Inputs.ActionDependencyArgs
                    {
                        Name = "jwt-encode", Version = "1.0.1"
                    },
                    new PulumiAuth0.Inputs.ActionDependencyArgs
                    {
                        Name = "axios", Version = "1.4.0"
                    }
                },
                Secrets = new InputList<PulumiAuth0.Inputs.ActionSecretArgs>
                {
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "domain", Value = auth0Config.Domain
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "client_id", Value = clients["sleekflow-client-auth0-actions"].Client.ClientId
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "issuer", Value = auth0Config.ActionIssuer
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "audience", Value = auth0Config.ActionAudience
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "requires_mfa_webhook", Value = auth0Config.RequiresMfaWebhook!
                    },
                },
                SupportedTriggers = new PulumiAuth0.Inputs.ActionSupportedTriggersArgs
                {
                    Id = "post-login", Version = "v3"
                },
                Deploy = true,
                Runtime = "node16",
            });
        return sleekflowActionRequiresMfa;
    }

    private static Action InitSleekflowActionPreUserRegistrationAction(
        Auth0Config auth0Config,
        Dictionary<string, (PulumiAuth0.Client Client, PulumiAuth0.ClientCredentials Credential)> clients)
    {
        var actionPath =
            $"{ResourcesPath}/actions/global-deployment-update/sleekflow-pre-user-registration.js";

        var sleekflowActionPreUserRegistration = new Action(
            "sleekflow-action-pre-user-registration",
            new PulumiAuth0.ActionArgs
            {
                Code = File.ReadAllText(actionPath),
                Name = "Pre User Registration",
                Dependencies = new InputList<PulumiAuth0.Inputs.ActionDependencyArgs>
                {
                    new PulumiAuth0.Inputs.ActionDependencyArgs
                    {
                        Name = "jwt-encode", Version = "1.0.1"
                    },
                    new PulumiAuth0.Inputs.ActionDependencyArgs
                    {
                        Name = "lodash", Version = "4.17.21"
                    },
                    new PulumiAuth0.Inputs.ActionDependencyArgs
                    {
                        Name = "jwt-decode", Version = "3.1.2"
                    },
                    new PulumiAuth0.Inputs.ActionDependencyArgs
                    {
                        Name = "auth0", Version = "2.44.0"
                    }
                },
                Secrets = new InputList<PulumiAuth0.Inputs.ActionSecretArgs>
                {
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "domain", Value = auth0Config.Domain
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "client_id", Value = clients["sleekflow-client-auth0-actions"].Client.ClientId
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "client_secret",
                        Value = clients["sleekflow-client-auth0-actions"].Credential.ClientSecret
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "pre_user_registration_webhook", Value = auth0Config.PreUserRegistrationWebhook
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "issuer", Value = auth0Config.ActionIssuer
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "audience", Value = auth0Config.ActionAudience
                    },
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "show_debug", Value = "false"
                    }
                },
                SupportedTriggers = new PulumiAuth0.Inputs.ActionSupportedTriggersArgs
                {
                    Id = "pre-user-registration", Version = "v2"
                },
                Deploy = true,
                Runtime = "node16",
            });
        return sleekflowActionPreUserRegistration;
    }

    private static Action InitSleekflowActionRestrictRegistrationAction(Auth0Config auth0Config)
    {
        var sleekflowActionRestrictRegistration = new Action(
            "sleekflow-action-restrict-registration",
            new PulumiAuth0.ActionArgs
            {
                Code = File.ReadAllText($"{ResourcesPath}/actions/sleekflow-restrict-registration.js"),
                Name = "Restrict Registration",
                SupportedTriggers = new PulumiAuth0.Inputs.ActionSupportedTriggersArgs
                {
                    Id = "pre-user-registration", Version = "v2"
                },
                Secrets = new InputList<PulumiAuth0.Inputs.ActionSecretArgs>
                {
                    new PulumiAuth0.Inputs.ActionSecretArgs
                    {
                        Name = "user_email_check_secret_key", Value = auth0Config.UserEmailCheckSecretKey
                    },
                },
                Deploy = true,
                Runtime = "node16",
            });
        return sleekflowActionRestrictRegistration;
    }
}