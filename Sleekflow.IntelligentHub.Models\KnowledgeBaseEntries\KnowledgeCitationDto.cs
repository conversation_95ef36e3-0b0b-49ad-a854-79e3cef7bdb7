﻿using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.KnowledgeBaseEntries;

public class KnowledgeCitationDto
{
    [JsonProperty("chunk")]
    public string? Chunk { get; set; }

    [JsonConstructor]
    public KnowledgeCitationDto(string? chunk)
    {
        Chunk = chunk;
    }

    public KnowledgeCitationDto(KnowledgeCitation knowledgeCitation)
    {
        Chunk = knowledgeCitation?.Chunk;
    }
}