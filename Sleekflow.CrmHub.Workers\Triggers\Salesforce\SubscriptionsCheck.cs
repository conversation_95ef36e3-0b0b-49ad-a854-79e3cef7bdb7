﻿using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.CrmHub.Workers.ProviderConfigs;
using Sleekflow.CrmHub.Workers.Subscriptions;

namespace Sleekflow.CrmHub.Workers.Triggers.Salesforce;

public class SubscriptionsCheck
{
    private readonly ILogger<SubscriptionsCheck> _logger;
    private readonly ISalesforceSubscriptionRepository _salesforceSubscriptionRepository;
    private readonly IProviderConfigService _providerConfigService;
    private readonly ICustomSyncConfigService _customSyncConfigService;

    public SubscriptionsCheck(
        ILogger<SubscriptionsCheck> logger,
        ISalesforceSubscriptionRepository salesforceSubscriptionRepository,
        IProviderConfigService providerConfigService,
        ICustomSyncConfigService customSyncConfigService)
    {
        _logger = logger;
        _salesforceSubscriptionRepository = salesforceSubscriptionRepository;
        _providerConfigService = providerConfigService;
        _customSyncConfigService = customSyncConfigService;
    }

    public class SubscriptionsCheckInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("subscription")]
        [Required]
        public SalesforceSubscription Subscription { get; set; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<SyncConfigFilterGroup> FilterGroups { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckInput(
            string sleekflowCompanyId,
            string entityTypeName,
            SalesforceSubscription subscription,
            List<SyncConfigFilterGroup> filterGroups,
            List<SyncConfigFieldFilter>? fieldFilters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            Subscription = subscription;
            FilterGroups = filterGroups;
            FieldFilters = fieldFilters;
        }
    }

    [Function("Salesforce_SubscriptionsCheck")]
    public async Task RunAsync(
        [TimerTrigger("0 */1 * * * *")]
        TimerInfo timerInfo,
        [DurableClient]
        DurableTaskClient starter)
    {
        var queryDefinition =
            new QueryDefinition(
                    "SELECT * "
                    + "FROM %%CONTAINER_NAME%% c "
                    + "WHERE (DateTimeDiff('second', c.last_execution_start_time, @now) > c.interval AND c.durable_payload = null) "
                    + "OR (DateTimeDiff('second', c.last_execution_start_time, @now) > 300 AND c.durable_payload != null)")
                .WithParameter("@now", DateTimeOffset.UtcNow);
        var objects =
            await _salesforceSubscriptionRepository.GetObjectsAsync(queryDefinition);

        foreach (var subscription in objects)
        {
            string instanceId;

            if (subscription.IsFlowsBased is not true)
            {
                var providerConfig =
                    await _providerConfigService.GetProviderConfigOrDefaultAsync(
                        subscription.SleekflowCompanyId,
                        "salesforce-integrator");
                if (providerConfig == null || providerConfig.IsAuthenticated == false)
                {
                    _logger.LogWarning(
                        "The provider salesforce-integrator for sleekflowCompanyId {SleekflowCompanyId} is not initialized",
                        subscription.SleekflowCompanyId);

                    continue;
                }

                var syncConfig =
                    providerConfig.EntityTypeNameToSyncConfigDict.GetValueOrDefault(subscription.EntityTypeName);
                if (syncConfig == null)
                {
                    _logger.LogWarning(
                        "The entityTypeName {EntityTypeName} for sleekflowCompanyId {SleekflowCompanyId} is not initialized",
                        subscription.EntityTypeName,
                        subscription.SleekflowCompanyId);

                    continue;
                }

                // Hardcoded conditions for certain companies.
                _customSyncConfigService.OverrideSyncConfig(
                    syncConfig,
                    subscription.SleekflowCompanyId,
                    "salesforce-integrator",
                    subscription.EntityTypeName);

                instanceId = await starter.ScheduleNewOrchestrationInstanceAsync(
                    "Salesforce_SubscriptionsCheck_Orchestrator",
                    input: new SubscriptionsCheckInput(
                        subscription.SleekflowCompanyId,
                        subscription.EntityTypeName,
                        subscription,
                        syncConfig.FilterGroups,
                        syncConfig.FieldFilters));
            }
            else
            {
                instanceId = await starter.ScheduleNewOrchestrationInstanceAsync(
                    "Salesforce_SubscriptionsCheck_Orchestrator",
                    input: new SubscriptionsCheckInput(
                        subscription.SleekflowCompanyId,
                        subscription.EntityTypeName,
                        subscription,
                        new List<SyncConfigFilterGroup>(),
                        null));
            }

            var httpManagementPayload = starter.CreateHttpManagementPayload(instanceId);
            if (httpManagementPayload == null)
            {
                _logger.LogInformation(
                    "Unable to get Salesforce_SubscriptionsCheck_Orchestrator httpManagementPayload");
            }

            await _salesforceSubscriptionRepository.PatchAsync(
                subscription.Id,
                subscription.SleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Replace(
                        $"/{SalesforceSubscription.PropertyNameDurablePayload}",
                        httpManagementPayload)
                });
        }
    }
}