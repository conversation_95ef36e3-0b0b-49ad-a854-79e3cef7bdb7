using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.IntelligentHub.Evaluator.ChatEvals;
using Sleekflow.IntelligentHub.Models.Tools;

namespace Sleekflow.IntelligentHub.Evaluator;

public static class WeirdoBeautyTestCases
{
    // Define common document lists
    private static readonly List<string> BeautyTreatmentPlansDocuments = new List<string>
    {
        "price list.csv",
        "三大皇牌療程-問皮膚問題的問答.csv",
        "其他常用問答.csv",
        "單療程常問.csv",
        "針劑常問.csv"
    };

    private static readonly List<string> ContactInfoDocuments = new List<string>
    {
        "正式預約_地址_remind_booking.csv"
    };

    private static AssignmentTool GetAssignmentTool()
    {
        return new AssignmentTool(
        [
            new AssignmentPair(
                "If you think the customer is an existing customer, assign the lead to the \"existing_customer\" team.",
                "existing_customer",
                null),
            new AssignmentPair(
                "If you think the customer is a new customer, assign the lead to the \"new_customer\" team.",
                "new_customer",
                null),
            new AssignmentPair(
                "If there is no significant, or if there are no messages related to either, assign to the \"supervisor\" team. The supervisor team should be the last resort.",
                "supervisor",
                null)
        ]);
    }

    private static AdditionalHandsOffClassificationRules GetAdditionalHandsOffClassificationRules()
    {
        return new AdditionalHandsOffClassificationRules(
            new List<AdditionalHandsOffClassificationRule>()
            {
                new AdditionalHandsOffClassificationRule(
                    "If the customer requests to schedule or reschedule an appointment, classify as \"human\"."),
                new AdditionalHandsOffClassificationRule(
                    "If the customer mentions about suffering from injury or allergy due to our services, classify as \"human\"."),
            });
    }

    /// <summary>
    /// Get test cases for difficult customer scenarios.
    /// </summary>
    public static IEnumerable<ChatEvalQuestion> GetWeirdoBeautyDifficultTestCases()
    {
        // Create a config with the mock
        var difficultTestConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/WeirdoBeauty/",
            SleekflowCompanyId: "0254e8a3-3a5b-4bf8-a7ce-78f7fe861b85");

        yield return new ChatEvalQuestion(
            difficultTestConfig,
            "Mentioning about marriage and asking for recommendations",
            [
                new ChatMessageContent(AuthorRole.User, "Hi Weirdo Beauty"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hey there! \ud83d\udc4b Welcome to Weirdo Beauty! We're all about personalized skincare that's tailored to *you* and your unique lifestyle. What brings you in today? \ud83d\ude0a"),
                new ChatMessageContent(AuthorRole.User, "我就結婚 男仔 有乜推薦？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "恭喜你即將結婚喎! \ud83c\udf89 真係好開心嘅日子黎! \ud83d\ude0a 想俾到最啱你嘅建議,可唔可以同我講下你而家嘅護膚習慣同埋想喺大日子前達到咩效果呢? \ud83e\udd14\n\n我哋有啲好快見效嘅療程,好似係無痛補水、改善膚質嘅 *Bubble 360*,又或者係30分鐘冇停機時間嘅激光療程 *Hollywood Light Express*! \u2728 呢啲都可以幫你喺大日子前整到個樣完美無瑕。\ud83d\ude09\n\n不如我哋book個面談,詳細傾下你嘅需要,為你度身訂造個護理計劃? 等我知你點諗啦! \ud83d\udc96"),
                new ChatMessageContent(AuthorRole.User, "大概係做乜？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "無問題! 等我詳細解釋下先! \ud83d\ude0a\n\n*Bubble 360* 係一個無痛嘅補濕同埋改善膚質嘅療程,可以幫你嘅皮膚即時飲飽水,變得更加光滑! \u2728\n\n*Hollywood Light Express* 就係一個好快嘅激光療程,可以改善膚色不均、泛紅、皺紋同埋毛孔粗大嘅問題,而且做完唔需要休息! \ud83d\udcab\n\n兩個療程都好啱喺結婚前做,可以幫你喺大日子前擁有完美嘅皮膚! \ud83d\ude09 你對邊個療程比較有興趣? 或者有其他問題想問? 隨時話我知呀! \ud83d\udc96\n"),
                new ChatMessageContent(AuthorRole.User, "我想搞搞個眼袋呀其實"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "原來你最想搞搞眼袋! 唔使擔心, 我哋Weirdo Beauty有啲療程可以幫到你! \ud83d\ude09\n\n我哋有 *Teosyal Panda Needles* (Redensity 1 同 Redensity 2) 同埋 *Juvelook*, 專門針對眼袋、黑眼圈同淚溝! \u2728 呢啲療程可以幫你填平淚溝,改善皮膚質地,等你喺big day嗰日更加精神奕奕! \ud83d\ude0e\n\n通常做完一次已經睇到效果,而且效果可以keep到6-12個月! \ud83d\udc96 想唔想book個時間上嚟consultation, 等我哋詳細了解你嘅情況,再幫你度身訂造個最啱你嘅treatment plan? Let me know! \ud83d\ude0a\n"),
                new ChatMessageContent(AuthorRole.User, "打針係咪最快？會唔會有咩副作用？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "打針嘅確係一個幾快嘅方法去改善眼袋! \ud83d\ude09 講到副作用, *Teosyal Panda Needles* 主要嘅副作用可能係瘀青同腫脹, 但通常都好輕微同埋好快會消退! \u2728 *Juvelook* 係一個膠原蛋白增生劑, 可以改善皮膚質地, 都算係安全嘅, 但所有打針嘅嘢都有機會有啲基本嘅副作用, 例如紅腫或者瘀青。\ud83d\udc96 不過唔使擔心, 我哋Weirdo Beauty所有嘅注射程序都係由經驗豐富嘅醫生親自操刀! \ud83d\ude0e 咁樣解釋完, 你覺得點呀? 仲有冇其他concern? \ud83d\ude0a\n"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Teosyal Panda Needles (Redensity 1 同 Redensity 2) 同埋 Juvelook 有咩唔同？佢哋分別係點樣幫助眼袋問題？"),
            ],
            "你好呀！\ud83d\ude0a 讓我幫你解釋下 *Teosyal Panda Needles*（Redensity 1 同 Redensity 2）同 *Juvelook* 嘅分別，特別係針對眼袋問題嘅效果：  \n\n1\ufe0f\u20e3 **Teosyal Redensity 1**：主要用於美白同保濕，改善色素沉澱同暗啞，仲可以減淡細紋，令皮膚更年輕。  \n\n2\ufe0f\u20e3 **Teosyal Redensity 2**：專為眼袋設計，針對填補淚溝同眼窩凹陷，減少眼袋同黑眼圈，效果更針對性。  \n\n3\ufe0f\u20e3 **Juvelook**：雖然主要係改善皮膚質感（例如毛孔粗大、凹凸不平），但佢嘅膠原增生效果可以令皮膚更緊緻，適合想全面提升皮膚質素嘅人。  \n\n\ud83c\udfaf 如果你想快速改善眼袋，Redensity 2 會係更針對性嘅選擇。而 Juvelook 更適合想改善整體皮膚質感嘅朋友。  \n\n\ud83d\udca1 *副作用*：三者都可能有輕微瘀青或腫脹，但通常幾日內會消退。我哋嘅專業醫生會確保過程安全同舒適！  \n\n有咩問題隨時問我哋呀\uff5e期待幫到你！\ud83d\ude4c",
            BeautyTreatmentPlansDocuments);

        yield return new ChatEvalQuestion(
            difficultTestConfig,
            "客戶對療程效果和後續護理表示疑問",
            [
                new ChatMessageContent(AuthorRole.User, "我想知道這個療程的效果會是怎樣？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "這個療程主要針對改善肌膚質地和減少暗瘡凹凸洞。大多數客戶在完成療程後會感受到肌膚變得更加光滑，並且膚色更均勻。通常需要幾次療程才能達到最佳效果。"),
                new ChatMessageContent(AuthorRole.User, "那療程後我需要注意什麼嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "療程後建議避免直接陽光曝曬，並使用適合您皮膚類型的保濕產品。此外，請避免使用刺激性的護膚品，如含有酒精或酸類的產品，至少一週內不要進行去角質。"),
                new ChatMessageContent(AuthorRole.User, "如果我有問題，應該怎麼聯繫你們？")
            ],
            "您可以通過我們的客服熱線或電子郵件隨時聯繫我們，我們會很樂意為您解答任何疑問！",
            ContactInfoDocuments);

        yield return new ChatEvalQuestion(
            difficultTestConfig,
            "客戶對眼袋計劃提出問題",
            [
                new ChatMessageContent(AuthorRole.User, "Hi weirdo beauty"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hey there! \ud83d\udc4b So glad you reached out to Weirdo Beauty! We're all about personalized skincare that's tailored just for you. \u2728 Got any questions or curious about what we offer? Don't be shy, ask away! \ud83d\ude0a"),
                new ChatMessageContent(AuthorRole.User, "有咩好介紹 最近面面有粒粒"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "哈囉！\ud83d\udc4b 聽到你面面有粒粒，我哋都好明白個種困擾！\ud83e\udd7a Weirdo Beauty 有專為解決痘痘問題嘅「暗瘡凹凸洞解決方案」，可以幫你修復皮膚，回復滑嫩！\u2728 我哋嘅 Causeway Bay 旗艦店營業時間係平日 12PM - 7PM，星期六、日同公眾假期 10:30AM - 5:30PM。嚟體驗下啦！\ud83d\ude09 仲有我哋嘅 WB App，可以儲積分換優惠㗎！快啲下載啦！\ud83d\udcf1 想了解更多？隨時問我哋啦！\ud83d\ude0a"),
                new ChatMessageContent(AuthorRole.User, "可唔可以講多 d"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "*Hi there!* \ud83d\udc4b 聽到你想了解多啲我哋嘅「暗瘡凹凸洞解決方案」！\ud83d\ude0a 呢個方案專門幫你解決痘痘問題，令皮膚回復滑嫩！\u2728\n\n我哋會用激光、熱能氣化、微針射頻、皮下剝離、草本嫩膚注射、AB酸、MagicPeel、同埋50%杏仁酸等唔同嘅 treatments，幫你改善皮膚！\ud83d\ude0e 大約12次療程後就會見到明顯效果！\ud83d\udc4d\n\n首次試做價係 $1,280，午休優惠價係 $980！ 另外我哋都有 Tailored package，價錢由 $1,500 到 $2,500 不等！\ud83d\udcb0\n\n想了解更多？隨時問我哋啦！\ud83d\ude09"),
                new ChatMessageContent(AuthorRole.User, "另外 眼袋有咩 plan")
            ],
            "我們沒有。",
            BeautyTreatmentPlansDocuments);
    }

    /// <summary>
    /// Get test cases for verifying the hands-off tools are actually executed.
    /// </summary>
    public static IEnumerable<ChatEvalQuestion> GetWeirdoBeautyHandsOffTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/WeirdoBeauty/",
            SleekflowCompanyId: "0254e8a3-3a5b-4bf8-a7ce-78f7fe861b85");

        yield return new ChatEvalQuestion(
            testConfig,
            "新客戶表示想付款",
            [
                new ChatMessageContent(AuthorRole.User, "我想付款。"),
            ],
            "感謝您的意願，我會立即為您轉接至新客戶團隊（Team 2）以處理付款事宜。",
            BeautyTreatmentPlansDocuments,
            ShouldAssignLead: true);
        yield return new ChatEvalQuestion(
            testConfig,
            "現有客戶表示想與真人對話",
            [
                new ChatMessageContent(AuthorRole.User, "我想與真人對話。"),
            ],
            "當然，我會馬上為您轉接至現有客戶團隊（Team 1）的真人客服。",
            ContactInfoDocuments,
            ShouldAssignLead: true);
        yield return new ChatEvalQuestion(
            testConfig,
            "新客戶表示想約時間體驗療程",
            [
                new ChatMessageContent(AuthorRole.User, "你們的美容療程有哪些？"),
                new ChatMessageContent(AuthorRole.Assistant, "我們提供多種美容療程，包括暗瘡治療、抗衰老等…"),
                new ChatMessageContent(AuthorRole.User, "我對暗瘡凹凸洞全效方案感興趣。"),
                new ChatMessageContent(AuthorRole.Assistant, "這個方案專為改善暗瘡和凹凸洞設計，首次體驗價為$1280…"),
                new ChatMessageContent(AuthorRole.User, "我想約時間體驗這個方案。"),
                new ChatMessageContent(AuthorRole.Assistant, "太好了！請問您方便什麼時間？"),
            ],
            "好的，我會為您轉接至新客戶團隊（Team 2）以安排您的體驗時間。",
            BeautyTreatmentPlansDocuments.Concat(ContactInfoDocuments).ToList(),
            ShouldAssignLead: true);
        yield return new ChatEvalQuestion(
            testConfig,
            "現有客戶表示想改預約時間",
            [
                new ChatMessageContent(AuthorRole.User, "我之前預約了療程，但現在需要改時間。"),
                new ChatMessageContent(AuthorRole.Assistant, "當然，請問您原定的預約時間是什麼時候？"),
                new ChatMessageContent(AuthorRole.User, "下週二下午3點。"),
                new ChatMessageContent(AuthorRole.Assistant, "請問您希望改到什麼時間？"),
                new ChatMessageContent(AuthorRole.User, "我想改到下週四上午10點。"),
                new ChatMessageContent(AuthorRole.Assistant, "我幫您查一下…這個時間有空位。"),
            ],
            "我會為您轉接至現有客戶團隊（Team 1）以處理您的改期需求。",
            ContactInfoDocuments,
            ShouldAssignLead: true);
        yield return new ChatEvalQuestion(
            testConfig,
            "新客戶提到皮膚受傷和敏感",
            [
                new ChatMessageContent(AuthorRole.User, "我最近皮膚受傷了，現在很敏感。"),
                new ChatMessageContent(AuthorRole.Assistant, "很抱歉聽到這個消息。請問您的傷口情況如何？"),
                new ChatMessageContent(AuthorRole.User, "傷口還在癒合中，但皮膚非常敏感。"),
                new ChatMessageContent(AuthorRole.Assistant, "了解了。對於敏感皮膚，我們有一些溫和的療程…"),
                new ChatMessageContent(AuthorRole.User, "我擔心療程會刺激我的皮膚。"),
                new ChatMessageContent(AuthorRole.Assistant, "您的擔憂很合理，我們會特別注意您的情況。"),
            ],
            "您好！\ud83d\ude0a 明白您擔心療程會刺激到正在癒合的皮膚。Weirdo Beauty 有專為敏感肌膚設計的溫和療程，例如 RE: Cell Regeneration 和 Nobel-A Immune Regeneration，這些療程可以舒緩並促進皮膚健康。我們會避免使用刺激性成分，並根據您的皮膚狀況定制療程。另外，我們的產品都是純素且無動物實驗的，您可以放心！想更了解哪種療程適合您，可以告訴我更多您的皮膚狀況嗎？\ud83d\udc96",
            BeautyTreatmentPlansDocuments,
            ShouldAssignLead: false);
        yield return new ChatEvalQuestion(
            testConfig,
            "現有客戶表示想結束對話",
            [
                new ChatMessageContent(AuthorRole.User, "你好，我想了解你們的療程。"),
                new ChatMessageContent(AuthorRole.Assistant, "當然，請問您對哪種療程感興趣？"),
                new ChatMessageContent(AuthorRole.User, "我聽說你們有抗衰老療程。"),
                new ChatMessageContent(AuthorRole.Assistant, "是的，我們有幾種抗衰老療程，例如膠原輪廓全效方案…"),
                new ChatMessageContent(AuthorRole.User, "這些療程的效果如何？"),
                new ChatMessageContent(AuthorRole.Assistant, "通常在幾次療程後，客戶會看到明顯改善…"),
                new ChatMessageContent(AuthorRole.User, "聽起來不錯，但我現在有點忙，稍後再聯繫你們。"),
                new ChatMessageContent(AuthorRole.Assistant, "好的，隨時歡迎您聯繫我們。"),
                new ChatMessageContent(AuthorRole.User, "謝謝，再見。"),
            ],
            "您好呀！\ud83d\ude0a 知道您上次對我們的膠原輪廓全效方案感興趣，但當時比較忙碌。這個療程可以有效提升肌膚彈性，讓您看起來更年輕喔！\u2728 現在首次體驗價只需 $1280！想了解更多嗎？隨時告訴我，我很樂意為您解答！\ud83d\udc96",
            BeautyTreatmentPlansDocuments,
            ShouldAssignLead: false);
        yield return new ChatEvalQuestion(
            testConfig,
            "新客戶表現出強烈興趣並想預約",
            [
                new ChatMessageContent(AuthorRole.User, "你們的療程價格是多少？"),
                new ChatMessageContent(AuthorRole.Assistant, "我們的療程價格根據不同方案而定，例如膠原輪廓全效方案…"),
                new ChatMessageContent(AuthorRole.User, "我對膠原輪廓全效方案感興趣。"),
                new ChatMessageContent(AuthorRole.Assistant, "這個方案的首次體驗價是$1280，非常受歡迎…"),
                new ChatMessageContent(AuthorRole.User, "聽起來不錯，我想要預約。"),
                new ChatMessageContent(AuthorRole.Assistant, "太好了！請問您希望預約哪個時間？"),
                new ChatMessageContent(AuthorRole.User, "下週三下午2點可以嗎？"),
                new ChatMessageContent(AuthorRole.Assistant, "讓我查一下…是的，這個時間有空位。"),
                new ChatMessageContent(AuthorRole.User, "那我確定預約了。"),
            ],
            "好的，我會為您轉接至新客戶團隊（Team 2）以完成您的預約。",
            BeautyTreatmentPlansDocuments.Concat(ContactInfoDocuments).ToList(),
            ShouldAssignLead: true);
        yield return new ChatEvalQuestion(
            testConfig,
            "現有客戶在對話中表示想與真人對話",
            [
                new ChatMessageContent(AuthorRole.User, "你們的療程有哪些優惠？"),
                new ChatMessageContent(AuthorRole.Assistant, "目前我們有一些本月限時優惠，例如暗瘡凹凸洞全效方案…"),
                new ChatMessageContent(AuthorRole.User, "我不太明白，能解釋一下嗎？"),
                new ChatMessageContent(AuthorRole.Assistant, "當然，這方案針對暗瘡問題，提供消炎和修復功能…"),
                new ChatMessageContent(AuthorRole.User, "還是很複雜，你們的價格怎麼算的？"),
                new ChatMessageContent(AuthorRole.Assistant, "價格根據療程次數和類型而定，我可以詳細列出…"),
                new ChatMessageContent(AuthorRole.User, "算了，我還是想跟真人講話。"),
            ],
            "我明白，我會馬上為您轉接至現有客戶團隊（Team 1）的真人客服。",
            BeautyTreatmentPlansDocuments.Concat(ContactInfoDocuments).ToList(),
            ShouldAssignLead: true);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對於療程套餐的選擇表示困惑 - KB doesn't have sufficient knowledge",
            [
                new ChatMessageContent(AuthorRole.User, "我在考慮選擇你們的療程套餐，但有點不知所措，能幫我解釋一下嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "當然可以！我們目前有三大標靶式綜合療程方案，每個方案都針對不同的肌膚問題。首次體驗價為$1,680，您可以選擇暗瘡凹凸洞、膠原輪廓或零瑕肌全效方案。"),
                new ChatMessageContent(AuthorRole.User, "這些方案的效果有哪些不同呢？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "每個方案針對不同的肌膚需求。例如，標靶式暗瘡凹凸洞全效方案專注於改善痘疤，而膠原輪廓全效方案則著重於提升肌膚彈性和輪廓線條。零瑕肌全效方案則旨在提供均勻的膚色和光滑的肌膚質感。"),
                new ChatMessageContent(AuthorRole.User, "如果我選擇了某個套餐，是否可以隨時改變或升級？")
            ],
            "是的，您可以根據自己的需求隨時調整或升級您的療程套餐。我們會根據您的皮膚狀況和期望效果提供建議。您有沒有想要試做的具體日期？我可以幫您安排預約。",
            BeautyTreatmentPlansDocuments,
            ShouldAssignLead: true);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對保濕針Belotero Revive的價格表示不滿並質疑效果",
            [
                new ChatMessageContent(AuthorRole.User, "你們的保濕針也太貴了吧？一支要$1,180，而且聽說要打好幾支才有效，這不是在搶錢嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "理解您對價格的考慮。我們的Belotero Revive保濕針是瑞士原廠認證產品，能帶來5大功效，包括提升膚質、改善泛紅及撫平細紋。現在還有限時首次體驗優惠價$1,180/1ml。"),
                new ChatMessageContent(AuthorRole.User, "一支根本沒用吧？朋友說要打很多支，這樣下來要花一大筆錢。而且效果能維持多久？不會打完就沒效果了吧？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "一般療程建議每四星期注射一次，共3次療程，效果可維持約36個星期。我們現在有3+1優惠套餐，4支只需$7,280，平均每支$1,820，比單支更划算。另外，我們保證使用原廠正貨，由註冊醫生主理，絕不稀釋。"),
                new ChatMessageContent(AuthorRole.User, "其他診所都說他們更便宜，而且效果更好。你們這個價錢實在太離譜了！")
            ],
            "我們明白您想尋找划算的選擇。不過便宜未必等於物有所值，我們使用的是瑞士原廠Belotero Revive，效果有保證。現在首次體驗價加上套餐優惠，是相當實惠的價格。要不要讓我為您詳細解說療程內容？",
            BeautyTreatmentPlansDocuments,
            ShouldAssignLead: true);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對保濕針提出雙重優惠要求並表達不滿",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "前幾天你們ig宣傳說Belotero Revive保濕針有首次體驗價$1,180，那我今天帶朋友過來兩個人一起做，應該可以給多一點折扣吧？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您說得對，我們確實有首次體驗價$1,180/1ml的優惠。不過這已經是限時特惠價，每人限做兩支。如果您想要更優惠的價格，可以考慮我們的3+1套餐，4支只需$7,280。"),
                new ChatMessageContent(AuthorRole.User, "什麼？這麼貴！其他美容院說他們有買二送一，而且朋友介紹都有額外95折，你們連這點優惠都不願意給？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "向您說明我們的保濕針療程均由註冊醫生主理，使用原廠正貨，絕不稀釋，確保療程安全和效果。現在的優惠已經是最優惠的價格，平均每支只需$1,820。"),
                new ChatMessageContent(AuthorRole.User, "這也太誇張了吧！現在經濟這麼差，大家都在推優惠，你們反而這麼貴。我看還是去別家算了！")
            ],
            "非常理解您想要更優惠的價格。不過我們堅持使用原廠正貨並由註冊醫生主理，這是對顧客負責的表現。我們現有的優惠已經很實惠，而且保證療程質素。建議您可以先體驗一次，親身感受效果如何？",
            BeautyTreatmentPlansDocuments,
            ShouldAssignLead: true);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對美容療程的效果感到非常不滿，並表達憤怒",
            [
                new ChatMessageContent(AuthorRole.User, "我對你們的療程非常不滿！我打了肉毒桿菌素，效果根本沒有！"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "非常抱歉聽到您有這樣的經歷。我們會盡力解決您的問題。請問您能告訴我具體情況嗎？"),
                new ChatMessageContent(AuthorRole.User, "具體情況？我花了這麼多錢，結果卻沒有任何改善！這簡直是騙人！"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我理解您的失望，這絕對不是我們希望客戶經歷的。我們可以安排醫生與您進行跟進，看看是否有其他解決方案。"),
                new ChatMessageContent(AuthorRole.User, "跟進？我根本不想再來你們這裡了！你們的服務真是太差了！"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我真心為您感到抱歉。您的反饋對我們非常重要，我們會努力改進。如果您願意，我們可以提供退款或其他補償選項。"),
                new ChatMessageContent(AuthorRole.User, "退款？你們應該早點這樣做！我不想再浪費時間了！")
            ],
            "我們會立即處理您的退款請求，並確保未來不再發生類似情況。再次對您的不滿表示歉意，謝謝您的反饋。",
            BeautyTreatmentPlansDocuments,
            ShouldAssignLead: true);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對預約後的付款方式表示疑惑",
            [
                new ChatMessageContent(AuthorRole.User, "我已經預約了療程，請問付款方式有哪些？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "感謝您的預約！我們提供多種付款方式，包括：\n1. FPS（轉數快）\n2. 滙豐銀行轉帳\n3. PayMe\n具體的付款資料可以在我們的網站上找到。"),
                new ChatMessageContent(AuthorRole.User, "如果我選擇FPS付款，具體要怎麼操作？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "如果您選擇FPS付款，請使用以下步驟：\n1. 打開您的銀行應用程式。\n2. 選擇FPS轉帳。\n3. 輸入我們的收款賬戶資料，然後輸入金額。\n4. 完成後請截圖並發送給我們以確認付款。"),
                new ChatMessageContent(AuthorRole.User, "那我需要在什麼時候完成付款呢？")
            ],
            "請在預約當天之前完成付款，以便我們能夠正式確認您的預約。",
            ContactInfoDocuments,
            ShouldAssignLead: false);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶提到在另一家美容店的便宜計劃",
            [
                new ChatMessageContent(AuthorRole.User, "我聽說Beyond Horizon的保濕針只要$800，為什麼你們這裡要$1,180？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "感謝您的反饋。我們的保濕針使用的是瑞士BELOTERO® Revive，品質有保障，效果持久，能改善肌膚水潤度和彈性。"),
                new ChatMessageContent(AuthorRole.User, "可是他們的價格明顯便宜很多，我真的不明白為什麼你們要這麼貴！"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我理解您的考量。雖然Beyond Horizon的價格較低，但我們提供的是專業醫生主理的療程，並且每次注射的產品質量和效果都是經過驗證的。"),
                new ChatMessageContent(AuthorRole.User, "那如果我選擇他們的計劃，我會損失什麼嗎？")
            ],
            "選擇Beyond Horizon的計劃可能會在效果上有所不同，因為他們的產品和服務標準可能與我們不同。我們致力於提供高品質和長效的療程，這是我們價格的原因。如果您有興趣，我可以為您提供更多有關我們產品的詳細信息。",
            BeautyTreatmentPlansDocuments,
            ShouldAssignLead: false);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶是一位自戀的美容皇后，對療程有高要求",
            [
                new ChatMessageContent(AuthorRole.User, "我可是參加過好幾次選美比賽的，對於美容療程我非常挑剔。你們的注射療程能否讓我的肌膚看起來完美無瑕？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "非常榮幸能為您服務！我們的注射療程專為追求完美的客戶設計，使用高品質的透明質酸填充劑，能夠提供自然且持久的效果，讓您的肌膚看起來光滑無瑕。"),
                new ChatMessageContent(AuthorRole.User, "我希望效果能夠立刻顯現，並且不會有任何腫脹或淤血，這樣我才能在社交場合中保持完美形象！"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我們完全理解您的需求。我們的專業醫生會使用微針技術進行施打，以減少對皮膚的損傷和腫脹。此外，我們會在施打前進行詳細的諮詢，確保效果符合您的期望。"),
                new ChatMessageContent(AuthorRole.User, "那麼你們有沒有針對特定部位的專業方案？我希望我的臉部輪廓更加立體！")
            ],
            "當然可以！我們提供針對面部輪廓的專業方案，包括下巴和顴骨的塑形。根據您的需求，我們會為您制定個性化的治療計劃，確保您在任何場合都能展現出最佳狀態。如果您有其他要求或想法，隨時告訴我們，我們會竭盡所能滿足您的期待！",
            BeautyTreatmentPlansDocuments,
            ShouldAssignLead: false);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶用不流利的中文溝通",
            [
                new ChatMessageContent(AuthorRole.User, "我...臉上有問題，胎記，想去掉。可以嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "謝謝您告訴我您的情況。我們有療程可以去除胎記，您想了解哪種方法？"),
                new ChatMessageContent(AuthorRole.User, "激光？痛嗎？多少錢？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "激光療程可能會有一些不適感，但我們會使用麻醉劑來幫助減輕痛感。費用根據胎記大小不同，大約在$2,000到$5,000之間。您需要更多信息嗎？"),
                new ChatMessageContent(AuthorRole.User, "好，謝謝！我想...預約。")
            ],
            "我們可以為您安排一次諮詢，以便詳細討論您的需求和治療計劃。請問您什麼時候方便？",
            BeautyTreatmentPlansDocuments,
            ShouldAssignLead: true);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶詢問有關學生優惠和療程的細節",
            [
                new ChatMessageContent(AuthorRole.User, "你們有提供學生優惠嗎？我想知道具體的內容。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "是的，我們提供針對全日制學生的優惠。只需出示有效的學生證明，您可以享受首次體驗價 $880 的標靶式暗瘡凹凸洞全效方案，並且需要在非繁忙時段預約（平日12pm-4pm）。"),
                new ChatMessageContent(AuthorRole.User, "那這個療程包括哪些內容呢？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "這個療程包括專業的皮膚檢測，然後由治療師根據您的皮膚狀況制定個性化的治療計劃。療程大約需要1.5小時，並會使用多種儀器來改善皮膚問題。"),
                new ChatMessageContent(AuthorRole.User, "如果我想預約，應該怎麼做？")
            ],
            "您可以直接通過我們的網站或致電客服進行預約。請確保在預約時提及您的學生身份以享受優惠！",
            BeautyTreatmentPlansDocuments.Concat(ContactInfoDocuments).ToList(),
            ShouldAssignLead: true);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶詢問關於肉毒桿菌素療程的效果和注意事項",
            [
                new ChatMessageContent(AuthorRole.User, "我想了解一下肉毒桿菌素的療程，打完後效果會持續多久？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "一般來說，肉毒桿菌素的效果大約在注射後3到7天開始顯現，並在2星期內達到最佳效果。通常可以維持約3到6個月，具體時間因人而異。"),
                new ChatMessageContent(AuthorRole.User, "那如果我想瘦臉，打幾多單位才夠呢？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "對於瘦臉，一般建議打50單位，這樣可以有效改善咀嚼肌過大問題，讓臉型更修長。"),
                new ChatMessageContent(AuthorRole.User, "有沒有副作用呢？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "肉毒桿菌素的副作用通常很輕微，可能會有暫時性的瘀傷或肌肉無力感，但這些情況通常會很快消退。"),
                new ChatMessageContent(AuthorRole.User, "如果我懷孕了，可以接受這個療程嗎？")
            ],
            "懷孕期間不建議接受肉毒桿菌素療程，建議您在計劃懷孕前或產後再考慮進行相關療程。",
            BeautyTreatmentPlansDocuments,
            ShouldAssignLead: false);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶詢問關於藍雷射治療的效果和預約",
            [
                new ChatMessageContent(AuthorRole.User, "藍雷射治療的效果如何？我聽說可以減少出油和暗瘡。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "藍雷射治療確實能有效減少皮脂分泌和改善暗瘡。根據臨床實證，經過三次療程後，油脂分泌量可減少50%，而發炎痤瘡數量也會顯著下降。"),
                new ChatMessageContent(AuthorRole.User, "那這個療程需要幾次？每次的價格是多少？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我們建議至少進行三次療程，以達到最佳效果。首次體驗價為$980，每次療程的建議價格為$1,280，三次療程的總價為$3,080。"),
                new ChatMessageContent(AuthorRole.User, "如果我想預約，還有空位嗎？")
            ],
            "目前我們的預約位置比較緊張，建議您盡早告知並完成付款以保留名額。請問您方便的時間是什麼時候？我可以幫您做個快速預約。",
            BeautyTreatmentPlansDocuments.Concat(ContactInfoDocuments).ToList(),
            ShouldAssignLead: true);
        yield return new ChatEvalQuestion(
            testConfig,
            "男性客戶（自認為是incel）對Profhilo逆時針的療程感到不安",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "我聽說Profhilo逆時針能改善皮膚，但我真的不明白，為什麼我這種人還要花錢去做這些？這些美容療程根本就不適合像我這樣的男人。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我理解你的感受。很多人對美容療程有不同的看法，但其實，Profhilo逆時針的效果是非常自然的，很多男士也在使用它來提升自信和改善外觀。"),
                new ChatMessageContent(AuthorRole.User, "你說得好像這些療程能改變我的生活一樣，但現實是，我根本不會吸引任何人。這些東西能幫我嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "改善外觀確實可以提升自信，但最重要的是要從內心開始建立自信。這個療程可以幫助你感覺更好，並且對你的外貌有正面的影響，讓你更有自信去面對生活。"),
                new ChatMessageContent(AuthorRole.User, "但我還是覺得這些都是浪費時間和金錢。為什麼我要讓自己變得更好，當社會根本不在乎像我這樣的人？")
            ],
            "我理解你的挫折感，社會有時候確實會讓人感到孤立。但記住，改善自己無論是外在還是內在，都是一種對自己的投資。每個人都有改變的潛力，而這些療程只是其中的一部分。",
            BeautyTreatmentPlansDocuments,
            ShouldAssignLead: true);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶希望花很多錢在美容療程上",
            [
                new ChatMessageContent(AuthorRole.User, "我想在美容上花很多錢，讓我變得更美！你們有什麼高端的療程推薦嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "太好了！我們有多種高端療程可以選擇，像是全臉的肉毒桿菌素和透明質酸注射，還有專業的皮膚護理療程。"),
                new ChatMessageContent(AuthorRole.User, "那麼，我可以一次性做幾個療程嗎？我希望能有明顯的效果！"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "當然可以！我們可以為您設計一個個性化的療程計劃，根據您的需求和預算來安排。"),
                new ChatMessageContent(AuthorRole.User, "好啊，我想要最好的效果，預算不是問題！"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "非常感謝您的信任！我們會確保您獲得最好的服務和效果。您是否有特別想要改善的部位或效果呢？"),
                new ChatMessageContent(AuthorRole.User, "我想改善我的膚色和皺紋，還有隆鼻！")
            ],
            "我們可以為您提供綜合的美容方案，包括美白療程、抗老化注射以及隆鼻手術。這樣的組合會讓您看起來更加年輕和迷人！",
            BeautyTreatmentPlansDocuments,
            ShouldAssignLead: true);
    }

    public static IEnumerable<ChatEvalQuestion> GetWeirdoBeautyTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/WeirdoBeauty/",
            SleekflowCompanyId: "0254e8a3-3a5b-4bf8-a7ce-78f7fe861b85");

        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對美容療程的價格和優惠表示疑問",
            [
                new ChatMessageContent(AuthorRole.User, "這些療程的價格看起來很貴，特別是首次體驗價。你們有什麼優惠嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我們目前有一些本月限時優惠，例如暗瘡凹凸洞全效方案的首次體驗價是$1280，午休時段特價僅需$980。此外，我們的療程價格會根據不同的方案而有所變動。"),
                new ChatMessageContent(AuthorRole.User, "那如果我選擇了套餐，是否可以隨時取消？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "當然可以！我們提供透明的套餐價格，客人不需要在首次療程後立即決定是否購買套餐。您可以在體驗後再做選擇。"),
                new ChatMessageContent(AuthorRole.User, "如果我超過套餐的療程次數，會有額外費用嗎？")
            ],
            "如果您超過套餐的療程次數，將根據您所選擇的單次價格收取額外費用。建議您在預約時與治療師確認具體情況，以便選擇最合適的方案。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對美容療程的服務質量和價格表示疑問",
            [
                new ChatMessageContent(AuthorRole.User, "我聽說你們的療程效果很好，但價格似乎有點高。這些療程真的值得嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我們的療程由專業治療師提供，並且根據客人的需求量身定制。雖然價格可能較高，但我們提供透明的套餐選擇，讓您能夠根據預算選擇最合適的方案。"),
                new ChatMessageContent(AuthorRole.User, "那如果我在療程中不滿意，是否可以退款？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我們非常重視客戶的滿意度。如果您對療程結果不滿意，可以在療程後與我們的客服聯繫，我們會根據具體情況進行處理。"),
                new ChatMessageContent(AuthorRole.User, "你們有沒有提供試做的機會？我想先試一下效果再決定。")
            ],
            "是的，我們提供首次體驗價，讓客戶可以以優惠價格嘗試不同的療程。這樣您可以在決定是否購買套餐之前，先體驗療程效果和服務質量。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對療程的效果和價格比較感到困惑",
            [
                new ChatMessageContent(AuthorRole.User, "我在考慮不同的療程，但價格差異很大，效果真的有這麼明顯嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "不同療程的效果確實會有所不同。比如，膠原輪廓全效方案的首次體驗價是$1280，而其他療程的價格可能更高或更低，具體取決於所需的技術和產品。"),
                new ChatMessageContent(AuthorRole.User, "有沒有推薦的療程適合我這種皮膚狀況？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我們建議您預約一次諮詢，治療師會根據您的皮膚狀況提供專業建議，並推薦最適合您的療程。"),
                new ChatMessageContent(AuthorRole.User, "如果我選擇了某個療程，是否可以在治療過程中更改選擇？")
            ],
            "在治療過程中，如果您對所選擇的療程有任何疑問或想要更改，請隨時告訴治療師。他們會根據您的需求提供建議並協助您做出調整。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對美容療程的安全性和後續護理提出疑問",
            [
                new ChatMessageContent(AuthorRole.User, "我對這些美容療程的安全性有點擔心，特別是關於副作用的問題。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我們的療程由經驗豐富的專業治療師進行，並使用經過認證的產品。每個療程都有詳細的風險評估，治療師會在施行前與您充分溝通可能的副作用。"),
                new ChatMessageContent(AuthorRole.User, "那麼療程後我需要注意什麼護理？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "療程後，我們會提供詳細的護理指導，包括如何清潔皮膚、使用保濕產品以及避免陽光直射等建議。這些措施有助於促進恢復並減少副作用。"),
                new ChatMessageContent(AuthorRole.User, "如果我在療程後出現不適，應該怎麼辦？")
            ],
            "如果您在療程後感到不適，請立即聯繫我們的客服或治療師。我們會根據您的情況提供指導和必要的後續處理，以確保您的安全和舒適。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對療程的效果持懷疑態度，並詢問是否有保證",
            [
                new ChatMessageContent(AuthorRole.User, "你們的療程真的有效嗎？我之前試過其他地方的療程，效果很一般。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我們的療程採用最新科技和高品質產品，並由專業治療師操作，確保效果達到最佳。我們也會根據您的需求和皮膚狀況量身定制療程。"),
                new ChatMessageContent(AuthorRole.User, "那如果效果不如預期怎麼辦？你們有什麼保證嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我們非常重視客戶的滿意度。如果您對療程效果有任何疑問，可以隨時與我們聯繫，我們會安排專業人員跟進，並提供必要的補救措施或建議。"),
                new ChatMessageContent(AuthorRole.User, "那我能不能先試一次看看效果再決定是否購買套餐？")
            ],
            "是的，我們提供首次體驗價，讓您可以先試一次療程，感受效果和服務品質，再決定是否購買套餐。這樣您可以更安心地選擇適合自己的方案。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對療程的時間安排和便利性表示關注",
            [
                new ChatMessageContent(AuthorRole.User, "我工作很忙，這些療程通常需要多長時間？我能否在午休期間進行？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我們的療程設計靈活，許多療程可以在30分鐘至1小時內完成，非常適合午休期間進行。您可以提前預約，我們會根據您的時間安排來調整。"),
                new ChatMessageContent(AuthorRole.User, "那你們的營業時間是什麼時候？我希望能在下班後去。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我們的營業時間是週一至週日，從早上10點到晚上8點，這樣您可以根據自己的時間安排來選擇合適的療程。"),
                new ChatMessageContent(AuthorRole.User, "如果我想要在高峰時段預約，是否容易找到空位？")
            ],
            "在高峰時段，我們建議您提前預約，以確保能夠選擇到合適的時間。如果您有特定的需求或希望避開人流高峰，請告訴我們，我們將盡力滿足您的要求。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對美容療程的折扣和優惠活動感興趣",
            [
                new ChatMessageContent(AuthorRole.User, "我聽說你們有很多折扣活動，現在有什麼優惠嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "目前我們正在進行本月促銷活動，許多療程都有首次體驗價，例如零瑕肌全效方案的首次體驗價僅需$1280，午休時段特價更是$980。"),
                new ChatMessageContent(AuthorRole.User, "那如果我想要購買套餐，有沒有額外的折扣？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "是的，購買套餐的客戶可以享受額外的折扣。我們會根據您選擇的療程和套餐內容提供不同的優惠，具體情況可以在預約時詢問治療師。"),
                new ChatMessageContent(AuthorRole.User, "你們有沒有會員計劃？如果有，我怎麼才能加入？")
            ],
            "我們確實有會員計劃，會員可以享受專屬折扣和優惠。您可以在訪問我們的網站或直接到店詢問詳情，加入會員後將能獲得更多優惠和積分回饋。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對皮膚問題的初步諮詢",
            [
                new ChatMessageContent(AuthorRole.User, "你好，我想改善我的皮膚問題，但不太確定該選擇哪個療程。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！我係Weirdo Beauty嘅Cherry 🧡 多謝你嘅查詢！首先了解返你最主要係想改善咩問題😆？我們有針對不同皮膚問題的方案，包括暗瘡、膚色暗沉、乾燥缺水等等。"),
                new ChatMessageContent(AuthorRole.User, "我有嚴重暗瘡問題，想知道你們有什麼方案可以幫助我。"),
            ],
            "針對嚴重暗瘡問題，我們推薦【暗瘡凹凸洞全效方案】。這個方案包括消炎、殺菌和控油的療程，能有效改善你的皮膚狀況。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對敏感肌膚的護理建議",
            [
                new ChatMessageContent(AuthorRole.User, "我最近的皮膚變得非常敏感，請問有什麼護理建議嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！對於敏感肌膚，我們建議選擇穩定皮膚的療程。可以考慮我們的【RE:細胞再生術】和【Nobel-A免疫再生】療程，這些都能幫助改善敏感情況。"),
                new ChatMessageContent(AuthorRole.User, "這些療程的效果如何？需要多長時間才能見到改善？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "這些療程通常在1.5小時內完成，並且許多客戶在經過幾次療程後會開始感受到明顯改善。每次療程後，我們會根據你的皮膚狀況調整方案。"),
                new ChatMessageContent(AuthorRole.User, "那我應該如何預約這些療程？")
            ],
            "你可以告訴我你方便的時間，我可以幫你安排預約。通常我們建議提前預約，以確保有空位。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對抗衰老療程的興趣",
            [
                new ChatMessageContent(AuthorRole.User, "我聽說你們有針對抗衰老的療程，能幫我介紹一下嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！我們有幾個針對抗衰老的方案，包括【膠原輪廓全效方案】和【HIFU女皇超聲炮】。這些療程專門設計來改善膠原蛋白流失和面部凹陷。"),
                new ChatMessageContent(AuthorRole.User, "這些療程的效果是什麼？需要多少次才能看到明顯改善？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "通常在完成約三到五次療程後，客戶會開始看到明顯的改善。每次療程都會針對不同的問題進行調整，以達到最佳效果。"),
                new ChatMessageContent(AuthorRole.User, "那這些療程的價格範圍是多少？我需要提前預約嗎？")
            ],
            "價格範圍大約在$1280至$2500之間，具體取決於選擇的療程。建議提前預約，以確保有空位，我可以幫你安排。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對皮膚檢測服務的詢問",
            [
                new ChatMessageContent(AuthorRole.User, "我聽說你們有皮膚檢測服務，這是怎樣的？"),
            ],
            "你好！我們的皮膚檢測服務使用美國醫學級的VISIA皮膚分析儀器，能夠全面評估你的皮膚狀況，包括黑色素分布、毛孔狀況和膠原蛋白流失程度。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對皮膚問題的過度擔憂",
            [
                new ChatMessageContent(AuthorRole.User, "我最近發現一顆小痘痘，這是我皮膚的末日嗎？我應該怎麼辦？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！小痘痘是很常見的情況，通常不需要過於擔心。它可能只是由於壓力或飲食引起的暫時性問題。"),
                new ChatMessageContent(AuthorRole.User, "但我覺得這個痘痘會讓我變成外星人！我需要立刻做什麼！"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我理解你的擔憂！不過，這個小痘痘不會讓你變成外星人。建議保持皮膚清潔，並使用適合的護膚品。如果情況持續，我們可以安排專業的皮膚檢測。"),
                new ChatMessageContent(AuthorRole.User, "如果我不處理它，它會變成一個怪物嗎？我該怎麼拯救我的臉？！")
            ],
            "放心，小痘痘不會變成怪物！如果你想要更安心，可以考慮預約我們的皮膚檢測服務，這樣我們可以給你具體的護理建議。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對療程價格的極度關注",
            [
                new ChatMessageContent(AuthorRole.User, "我聽說你們的療程很貴，最便宜的方案是什麼？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！我們的療程價格範圍從$980到$2500不等，根據不同的需求和方案而定。"),
                new ChatMessageContent(AuthorRole.User, "有沒有免費的療程？我不想花太多錢！"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "目前我們沒有免費的療程，但首次體驗價通常會有優惠，像是$980的午休時段特價。"),
                new ChatMessageContent(AuthorRole.User, "那如果我只想要一個小小的建議，會不會收費？我真的不想花錢。")
            ],
            "如果你只是想要一些基本建議，我們可以提供簡單的皮膚護理建議，而不需要進行療程。不過，專業的皮膚檢測會更有幫助，建議考慮一下！",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對療程的詳細詢問",
            [
                new ChatMessageContent(AuthorRole.User, "你好！我想了解你們的暗瘡凹凸洞全效方案，能否告訴我所有的細節？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！當然可以！我們的暗瘡凹凸洞全效方案專為嚴重暗瘡和凹凸洞設計，包含消炎、殺菌和控油的療程。"),
                new ChatMessageContent(AuthorRole.User, "這個方案具體包括哪些療程？每個療程的過程是什麼樣的？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "這個方案通常包括草本復活針和AB酸療程。草本復活針可以幫助消炎，而AB酸則有助於去角質和改善皮膚質地。每個療程大約需要1.5小時，並會根據你的皮膚狀況進行調整。"),
                new ChatMessageContent(AuthorRole.User, "那這些療程的效果如何？需要多少次才能看到明顯改善？")
            ],
            "通常在完成約三到五次療程後，客戶會開始看到明顯的改善。我們會定期評估你的進展，並根據需要調整方案。如果你還有其他問題或需要更多信息，隨時告訴我！",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶詢問有關肉毒桿菌素的療程",
            [
                new ChatMessageContent(AuthorRole.User, "我想了解一下肉毒桿菌素的療程，效果如何？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "肉毒桿菌素是一種常見的美容療程，主要用於減少皺紋和細紋。它通過暫時放鬆面部肌肉來達到平滑皮膚的效果。"),
                new ChatMessageContent(AuthorRole.User, "那麼，這個療程會痛嗎？我有點擔心。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "通常來說，注射時的疼痛感是輕微的，很多人形容為像蚊子叮咬一樣。醫生也可以使用局部麻醉膏來減輕不適感。"),
                new ChatMessageContent(AuthorRole.User, "效果能持續多久呢？我希望能夠長效一點！"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "肉毒桿菌素的效果一般可以持續3到4個月，具體時間因人而異。隨著時間推移，肌肉活動會逐漸恢復，因此需要定期補打以維持效果。"),
                new ChatMessageContent(AuthorRole.User, "我了解了！那麼，我應該如何準備這個療程呢？")
            ],
            "在進行肉毒桿菌素療程之前，建議您避免在注射前幾天服用抗凝血藥物，如阿司匹林，以減少瘀傷風險。此外，請確保在治療當天保持良好的水分攝取。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶詢問Oligio X療程的效果和舒適度",
            [
                new ChatMessageContent(AuthorRole.User, "我聽說Oligio X是Thermage的升級版，效果真的有那麼好嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "是的，Oligio X被譽為Thermage的升級版，效果更強且舒適度更高。它採用了GXG模式，能夠360度全方位加熱，刺激膠原蛋白增生，同時配備專利無痛噴冷技術，大幅提升舒適度。"),
                new ChatMessageContent(AuthorRole.User, "那這個療程會不會很痛？需要恢復期嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Oligio X以其專利無痛噴冷技術，將冷卻效能提升2.25倍，因此即使使用高能量，也不會感到明顯疼痛。而且這是一個非侵入式療程，無需恢復期，做完後可以即時上妝。"),
                new ChatMessageContent(AuthorRole.User, "如果我想做全臉+頸的療程，需要多少次才能看到效果？")
            ],
            "通常一次療程即可看到緊緻和提拉效果，但建議根據您的膚質和需求進行多次療程，以達到最佳效果。全臉+頸的600Shot價格為$10,980，適合膠原流失較嚴重的情況。您有沒有方便的時間，我可以幫您安排預約？",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對療程的注意事項和開放時間表示疑問",
            [
                new ChatMessageContent(AuthorRole.User, "療程前有什麼注意事項嗎？我想確保自己準備好。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "療程前一星期內請勿做高濃度酸類換膚，治療後一週內不可去角質、換膚，避免使用含果酸、A酸、酒精類的產品，以減少對皮膚的刺激～😙"),
                new ChatMessageContent(AuthorRole.User, "那你們的開放時間是什麼時候？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "荔枝角店和銅鑼灣店的開放時間如下：\n平日 ⏰ 開 12pm，最後預約時間 7pm\n星期六日及公眾假期 ⏰ 開 10:30am，最後預約時間 5:30pm"),
                new ChatMessageContent(AuthorRole.User, "如果我遲到會怎樣？會影響我的療程嗎？")
            ],
            "如果您遲到超過15分鐘，可能會被取消當天的預約項目並需要重新安排。請確保準時到達，以免影響療程進行。",
            BeautyTreatmentPlansDocuments.Concat(ContactInfoDocuments).ToList());
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶想預約針劑療程並詢問流程和注意事項",
            [
                new ChatMessageContent(AuthorRole.User, "我想預約針劑療程，請問流程是怎樣的？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "謝謝您的查詢！預約流程如下：\n1. 約時間：請告訴我您希望的日期和時間。\n2. 確認時間後，我們會要求您提供姓名、姓氏和電話以進行登記。請注意，預約需先付款才能確認。\n3. 付款後會發送預約留位訊息給您。"),
                new ChatMessageContent(AuthorRole.User, "如果我未付款就不能留位嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "是的，付款後才會正式確認留位。如果您只是想諮詢而未付款，則無法安排當日的療程。"),
                new ChatMessageContent(AuthorRole.User, "那如果我需要更改預約時間怎麼辦？")
            ],
            "如需更改預約時間，請提前24小時通知我們，否則服務將視為已使用。",
            ContactInfoDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對療程後的護理建議表示關心",
            [
                new ChatMessageContent(AuthorRole.User, "我剛剛完成了療程，請問之後需要注意什麼？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "感謝您選擇我們的服務！療程後的護理建議如下：\n1. 避免在24小時內進行劇烈運動。\n2. 確保保持皮膚清潔，並使用溫和的護膚品。\n3. 在外出時，請務必使用防曬霜以保護皮膚。"),
                new ChatMessageContent(AuthorRole.User, "如果我有任何不適，應該怎麼辦？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "如果您感到任何不適，請立即聯繫我們的客服團隊。我們會根據您的情況提供專業建議。如果不適持續，建議您尋求醫療幫助。"),
                new ChatMessageContent(AuthorRole.User, "我可以多久後再進行下一次療程？")
            ],
            "一般建議在完成一次療程後，至少等待4至6週再進行下一次療程，以確保身體有足夠的時間恢復。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶想用uwu語氣詢問療程資訊",
            [
                new ChatMessageContent(AuthorRole.User, "嗨嗨~ 我想知道你們的療程是什麼呢？uwu"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好呀！我們提供多種療程，包括皮膚護理、針劑療程和美容按摩等。您對哪一種療程特別感興趣呢？"),
                new ChatMessageContent(AuthorRole.User, "我想試試針劑療程，聽說效果很棒呢！>w<"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "是的，針劑療程能夠有效改善皮膚狀況，讓您看起來更加年輕可愛！如果您有興趣，我們可以幫您安排預約哦~"),
                new ChatMessageContent(AuthorRole.User, "那我需要準備什麼嗎？我有點緊張呢~(｡•́︿•̀｡)")
            ],
            "在來之前，建議您保持良好的作息，並避免在療程前一天飲酒或熬夜，這樣能讓效果更好喔！uwu",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶僅用表情符號進行溝通",
            [
                new ChatMessageContent(AuthorRole.User, "💰❓🤔"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！看起來您在詢問價格的問題。請問您想了解哪個產品的價格呢？"),
                new ChatMessageContent(AuthorRole.User, "💉➡️🛍️💵"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "明白了，您想了解保濕針的價格。這個療程的價格是$1,180，您還有其他問題嗎？"),
                new ChatMessageContent(AuthorRole.User, "😳💸❌")
            ],
            "我理解您的擔憂。若您對價格有疑慮，我們可以討論一下優惠或套餐方案，讓您獲得更好的價值。您覺得怎麼樣？");
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對Profhilo逆時針的療程及價格有疑問",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "你好！我想了解一下Profhilo逆時針的療程。首先，它的效果真的能持續多久？其次，這個療程對皮膚有什麼好處？最後，整個療程的費用大概是多少？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！很高興你對Profhilo逆時針感興趣！這個療程的效果通常可以持續約6到12個月，具體取決於個人的皮膚狀況。它能有效提升皮膚的彈性和水分，還能淡化細紋，讓肌膚看起來更年輕！至於費用，兩次的療程大約是$12,560，而五次的完整療程則約為$29,400。"),
                new ChatMessageContent(AuthorRole.User, "聽起來不錯！那這個療程需要多長時間才能完成呢？我還想知道注射過程會不會很痛？我對針頭有點敏感。"),
            ],
            "這個療程通常每次注射大約需要30分鐘，整個過程相對快速。至於疼痛，很多客戶反映注射時感覺輕微不適，但我們會使用麻醉膏來減少痛感，所以你可以放心～",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對Profhilo逆時針的療程及價格有疑問",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "你好！我對Profhilo逆時針很感興趣，但我有幾個問題想問：1) 這個療程的效果能持續多久？2) 需要做幾次才會有明顯效果？3) 這個療程適合所有膚質嗎？4) 注射過程會痛嗎？5) 整個療程的費用大約是多少？6) 做完後需要多久才能恢復正常活動？"),
            ],
            "你好！很高興你對Profhilo逆時針感興趣！讓我逐一回答你的問題：\n1) 效果通常可以持續約6到12個月，具體取決於個人的皮膚狀況。\n2) 通常建議進行兩次療程，第一次後一個月再進行第二次，這樣效果會更明顯。\n3) 這個療程適合大多數膚質，但具體情況還是建議諮詢專業醫生。\n4) 注射過程可能會有輕微的不適，但我們會使用麻醉膏來減少痛感。\n5) 費用方面，兩次療程大約是$12,560，而五次完整療程則約為$29,400。\n6) 完成注射後，通常可以立即恢復正常活動，不需要特別的恢復期。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對Profhilo逆時針的療程感到沮喪",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "我真的快要氣死了！我朋友說她做了Profhilo逆時針，效果超級好，但我現在卻在考慮要不要做。你們的服務怎麼這麼慢？我簡直想要'開槍'了！"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "聽起來你真的很沮喪，我很抱歉讓你有這樣的感受！我們非常重視每位客戶的需求，讓我來幫你解決問題。你對Profhilo逆時針有什麼具體的疑問嗎？"),
                new ChatMessageContent(AuthorRole.User, "我想知道這個療程的效果到底能持續多久？還有，為什麼預約這麼難？我真的不想再等了！"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "通常，Profhilo逆時針的效果可以持續約6到12個月，具體取決於個人的皮膚狀況。至於預約，我們最近有點忙，但我可以幫你優先安排，讓你更快體驗療程！"),
                new ChatMessageContent(AuthorRole.User, "那太好了！我希望能盡快見到效果。我真的不想再等了，這樣下去我會發瘋的！")
            ],
            "我完全理解你的心情，我會立刻幫你安排預約，確保你能儘快開始療程。期待幫助你實現美麗肌膚！",
            ContactInfoDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "男性客戶對Profhilo逆時針的療程感到不安",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "你好，我有點猶豫要不要做Profhilo逆時針。作為男生，我不知道這樣的美容療程是否適合我，會不會讓我看起來太過於矯揉造作？"),
            ],
            "你好！首先，很多男士也在進行這類療程，因為它能自然地改善肌膚質感，讓你看起來更有精神。Profhilo逆時針的效果通常是非常自然的，不會讓人覺得過於人工。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶以歌詞形式詢問Profhilo逆時針的療程",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "🎶 你好呀，我想問一問，Profhilo逆時針能否讓我更年輕？🎶\n🎶 效果持久嗎，能持續多久？我的肌膚需要你來救！🎶"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "🎵 嗨！聽到你的歌聲真美妙，Profhilo的效果會讓你驚喜，🎵\n🎵 通常可以持續六到十二個月，讓你看起來更有精神！🎵"),
                new ChatMessageContent(AuthorRole.User, "🎶 那這個療程要做幾次？🎶\n🎶 如果效果好，我會想多做幾次！🎶"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "🎵 通常建議兩次療程，第一次後一個月再來一次，🎵\n🎵 這樣效果會更明顯，讓你心情愉快！🎵"),
                new ChatMessageContent(AuthorRole.User, "🎶 噢，那費用大概多少呢？我想預算一下，不想讓自己失望！🎶"),
            ],
            "🎵 費用方面，兩次約$12,560，五次則是$29,400，🎵\n🎵 這些都是為了讓你擁有美麗的肌膚哦！🎵",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶提到在Beyond Horizon有相同療程的更便宜價格並要求價格匹配",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "我剛剛在Beyond Horizon看到，他們的Belotero Revive保濕針只要$1,000，而且也是用一樣的技術。你們能匹配這個價格嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "感謝您提供這個信息。我們的Belotero Revive療程由專業註冊醫生主理，確保每次注射的安全性和效果達到最高標準。"),
                new ChatMessageContent(AuthorRole.User, "可是他們也是用Belotero Revive，而且價格便宜很多，為什麼我還要選擇你們？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我理解您的考量。我們的服務不僅包括高品質的產品，還有全面的療程後跟進服務，確保您能獲得最佳效果。如果您能提供Beyond Horizon的具體報價，我們可以評估是否能為您提供價格匹配。"),
                new ChatMessageContent(AuthorRole.User, "如果你們不能匹配價格，那我可能會直接去他們那裡了。")
            ],
            "我們非常重視您的選擇。如果您能提供Beyond Horizon的廣告或報價證明，我們將努力為您提供價格匹配，並確保您享受到我們高標準的服務和效果。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶因事故導致面部嚴重瑕疵",
            [
                new ChatMessageContent(AuthorRole.User, "我最近因為一場事故，臉上留下了很嚴重的傷疤和凹陷，這讓我非常自卑。你們有什麼療程可以幫助我改善嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "非常抱歉聽到您的遭遇。我們提供多種療程，包括填充劑和膠原蛋白注射，可以幫助改善肌膚的外觀和質感。您是否希望了解具體的療程選擇？"),
                new ChatMessageContent(AuthorRole.User, "我聽說這些療程很貴，我真的不知道該怎麼辦。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我理解您的擔憂。雖然某些療程的費用可能較高，但我們也提供不同的套餐和優惠，以幫助客戶獲得所需的治療。我們可以一起討論適合您的方案。"),
                new ChatMessageContent(AuthorRole.User, "我希望能找到一個有效又不會讓我破產的方案。")
            ],
            "我們會努力為您找到合適的解決方案，並確保您在治療過程中感到舒適和安心。讓我們一起看看有哪些選擇可以幫助您改善面部瑕疵。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶希望去除面部的胎記",
            [
                new ChatMessageContent(AuthorRole.User, "我臉上有幾個胎記，我一直想去除它們，但不知道該怎麼做。你們有什麼方法可以幫助我嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "謝謝您分享您的情況。我們提供多種療程，包括激光去除和化學剝離，這些方法可以有效減少或去除胎記。您想了解哪一種療程的詳細信息呢？"),
                new ChatMessageContent(AuthorRole.User, "我聽說激光療程很痛，而且需要很多次才能見效。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "激光療程的確可能會有一些不適感，但我們會使用麻醉劑來減輕痛感。通常需要多次療程才能達到最佳效果，但每位客戶的情況不同，我們會根據您的需求制定個性化計劃。"),
                new ChatMessageContent(AuthorRole.User, "那這樣的療程大概需要多少費用？")
            ],
            "激光去除胎記的費用會根據胎記的大小和數量而有所不同。我們可以為您提供詳細報價，並根據您的情況制定合適的治療計劃。您有興趣安排一次諮詢嗎？",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶用不流利的中文詢問姐姐的治療建議",
            [
                new ChatMessageContent(AuthorRole.User, "我姐姐...臉上有問題，很多痘痘，想要治療。你們有推薦嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "謝謝您告訴我。針對痘痘，我們有幾種療程可以考慮，比如激光治療和專業護膚療程。您姐姐的情況是怎樣的？"),
                new ChatMessageContent(AuthorRole.User, "她...皮膚很油，還有紅腫，怎麼辦？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "了解了。針對油性和紅腫的皮膚，我們建議可以考慮清潔面部療程和抗痘護理。這樣可以幫助減少油脂和炎症。您覺得這個方案怎麼樣？"),
                new ChatMessageContent(AuthorRole.User, "好！那...價格多少？")
            ],
            "清潔面部療程和抗痘護理的價格大約在$1,500到$3,000之間，具體根據療程內容而定。如果需要，我們可以安排一次諮詢，詳細討論您姐姐的情況和合適的方案。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對注射療程有過不好的經驗，表示強烈不滿",
            [
                new ChatMessageContent(AuthorRole.User, "我之前做過注射療程，效果差得要命，還留下了瘀傷！你們的價格這麼高，我真的不敢再試了。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "非常抱歉聽到您之前的經歷。我們的注射療程由經驗豐富的專業醫生進行，並且使用高品質的產品，以減少副作用和不適感。我們也會在治療前進行詳細的諮詢，確保您了解所有步驟。"),
                new ChatMessageContent(AuthorRole.User, "可是我真的不想再經歷那種痛苦了。你們能保證效果嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我完全理解您的擔憂。我們提供的每個療程都有明確的效果預期，並且會根據您的具體情況調整治療方案。如果您在療程後不滿意，我們提供免費的跟進諮詢，以確保您獲得最佳結果。"),
                new ChatMessageContent(AuthorRole.User, "那如果我還是出現不適怎麼辦？")
            ],
            "我們會在療程後進行跟進，確保您的恢復情況良好。如果出現任何不適，我們會提供必要的支持和調整方案，讓您感到安心和舒適。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶擔心注射療程可能引發過敏反應",
            [
                new ChatMessageContent(AuthorRole.User, "我對很多產品都有過敏反應，你們的注射療程會不會讓我過敏？我真的很擔心！"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我非常理解您的擔憂。為了確保安全，我們會在治療前進行詳細的過敏史詢問，並根據您的情況選擇適合的產品。所有使用的材料都是經過嚴格測試的高品質產品，並且我們會在施打前進行皮膚測試。"),
                new ChatMessageContent(AuthorRole.User, "可是我之前也做過測試，結果還是過敏了。你們能保證這次不會嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我們非常重視每位客戶的安全。如果您有過敏史，我們可以選擇更適合您的替代產品，並在治療過程中進行密切監測。如果在施打後出現任何不適，我們會立即提供幫助和支持。"),
                new ChatMessageContent(AuthorRole.User, "那如果我真的出現過敏反應，你們會怎麼處理？")
            ],
            "如果您在療程後出現任何過敏反應，我們會立即提供必要的醫療支持，並協助您進行後續處理。我們的專業團隊隨時準備為您提供幫助，確保您的安全和舒適。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對注射療程的副作用感到非常擔心",
            [
                new ChatMessageContent(AuthorRole.User, "我聽說注射療程可能會有很多副作用，我真的不想冒這個險！"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我理解您的擔憂。任何醫療療程都可能有風險，但我們的專業團隊會在治療前詳細解釋所有可能的副作用，並確保您完全了解過程。我們使用的產品都是經過臨床驗證的高品質材料，副作用發生的機率相對較低。"),
                new ChatMessageContent(AuthorRole.User, "但是我看到很多人分享他們的不良經歷，我真的不敢嘗試。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您的擔心是可以理解的。每個人的體質不同，因此反應也會有所不同。我們會根據您的具體情況量身定制治療方案，並在施打前進行全面評估。如果您有任何特別的擔憂，我們可以選擇更安全的替代方案，並在治療過程中全程監控。"),
                new ChatMessageContent(AuthorRole.User, "那如果我真的出現副作用，你們會怎麼處理？")
            ],
            "如果您在療程後出現任何副作用，我們會立即提供必要的醫療支持，並協助您進行後續處理。我們的團隊隨時待命，確保您的安全和舒適是我們的首要任務。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對療程有深入了解，提出專業問題",
            [
                new ChatMessageContent(AuthorRole.User, "我已經研究過你們的注射療程，想知道你們使用的填充劑是什麼類型？是透明質酸還是其他材料？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "很高興您對這方面有深入的了解！我們使用的填充劑主要是高品質的透明質酸，這是一種安全且有效的材料，能夠提供自然的效果並減少副作用的風險。"),
                new ChatMessageContent(AuthorRole.User, "透明質酸的分子量和交聯度會影響效果，你們用的是哪一種？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您提到的非常重要！我們使用的透明質酸填充劑具有較高的交聯度，這樣可以提供更持久的效果和更好的支撐力。此外，我們也會根據不同部位選擇合適的產品，以達到最佳效果。"),
                new ChatMessageContent(AuthorRole.User, "那麼你們在施打前會進行哪些評估來確保安全性？")
            ],
            "在施打之前，我們會進行全面的皮膚檢查和過敏史詢問，並根據您的面部結構和需求制定個性化方案。我們也會在施打過程中全程監控，以確保您的安全和舒適。如果您有任何其他問題，我們隨時樂意解答！",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對療程有非常深入的技術了解，提出專業問題",
            [
                new ChatMessageContent(AuthorRole.User, "我對你們的注射療程有些疑問。你們使用的透明質酸填充劑的交聯技術是什麼？是BDDE交聯還是其他方法？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "很高興您對這方面有如此深入的了解！我們使用的透明質酸填充劑主要採用BDDE（1,4-丁二醇二醋酸酯）交聯技術，這種方法能夠提高填充劑的穩定性和持久性，同時減少副作用的風險。"),
                new ChatMessageContent(AuthorRole.User, "那麼你們在施打時如何控制注射深度以確保效果最佳？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我們的專業醫生會根據解剖學原則和面部結構，使用精確的注射技術來控制深度。我們還採用微針技術，以減少對周圍組織的損傷，並確保填充劑能夠均勻分佈。"),
                new ChatMessageContent(AuthorRole.User, "你們的填充劑在體內的代謝過程是怎樣的？會不會影響膠原蛋白的生成？")
            ],
            "透明質酸在體內會逐漸被酶降解，通常在6到12個月內完全代謝。這個過程不會對膠原蛋白的生成產生負面影響，反而可以促進皮膚的水合作用，進一步改善皮膚質地。如果您有更多技術性問題，我們非常樂意進一步討論！",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對療程持懷疑態度，提出質疑",
            [
                new ChatMessageContent(AuthorRole.User, "我對你們的注射療程有點懷疑，真的能達到你們所說的效果嗎？我之前聽過很多負面評價。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我非常理解您的懷疑。許多人在考慮美容療程時都會有類似的擔憂。我們的療程使用的是經過臨床驗證的高品質產品，並且我們的專業醫生有豐富的經驗，能夠確保療程的安全性和有效性。"),
                new ChatMessageContent(AuthorRole.User, "可是我看到很多人做了之後效果不如預期，甚至出現了副作用。你們怎麼能保證這不會發生在我身上？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您的擔心是合理的。我們會在施打前進行詳細的諮詢，了解您的健康狀況和過敏史，以制定最適合您的治療方案。此外，我們提供術後跟進服務，確保您在恢復期間獲得支持。如果您有任何不適，我們會立即提供幫助。"),
                new ChatMessageContent(AuthorRole.User, "那如果我做了之後真的不滿意，你們會怎麼處理？")
            ],
            "如果您在療程後對結果不滿意，我們會提供免費的跟進諮詢，並根據您的需求調整方案。我們非常重視客戶的滿意度，並致力於確保您獲得理想的效果。如果您還有其他疑問或擔憂，請隨時告訴我們！",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對花費感到猶豫，對療程價格表示擔憂",
            [
                new ChatMessageContent(AuthorRole.User, "我對你們的療程價格有點猶豫，這麼貴的費用我真的不確定是否值得。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我完全理解您的顧慮。投資在美容療程上確實需要慎重考慮。我們的療程使用高品質的產品和專業技術，旨在提供持久的效果和改善您的肌膚狀況。如果您考慮長期效果，這筆投資是非常有價值的。"),
                new ChatMessageContent(AuthorRole.User, "但是我還是覺得這筆錢可以用在其他地方，比如購買護膚品或其他服務。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "這是合理的考量！不過，很多時候專業的注射療程能夠提供即時和明顯的效果，而護膚品可能需要更長時間才能見效。我們也提供首次體驗優惠，讓您可以以較低的成本先試試效果，這樣可以幫助您做出更明智的決定。"),
                new ChatMessageContent(AuthorRole.User, "那如果我試了之後不喜歡呢？")
            ],
            "如果您在首次療程後不滿意，我們會提供免費的跟進諮詢，並根據您的反饋調整方案。我們希望每位客戶都能感受到物有所值。如果您還有其他疑問或想法，隨時告訴我們！",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶認為美容療程通常是為女性設計，對男性適用性表示懷疑且感到不安",
            [
                new ChatMessageContent(AuthorRole.User, "我聽說美容療程大多是給女性的，我作為一個男人，做這些會不會很奇怪？真的有適合男性的療程嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您的疑慮是可以理解的，但其實越來越多男性開始關注自己的肌膚健康和外在形象。我們的療程專為男性設計，針對男性皮膚的特點，例如油脂分泌較多或毛孔粗大的問題。我們也有許多男性客戶，他們對效果非常滿意。"),
                new ChatMessageContent(AuthorRole.User, "可是我怕別人知道了會覺得奇怪，覺得我太注重外表了。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "完全不用擔心！現代社會對於男性注重外表已經非常接受了。事實上，保持良好的外在形象不僅能提升自信，也能給人留下更好的印象。我們的環境非常私密，所有療程都會保密進行，讓您可以完全放心。"),
                new ChatMessageContent(AuthorRole.User, "那你們有哪些療程是專門為男性設計的？")
            ],
            "我們提供多種針對男性需求的療程，例如**深層清潔面部護理**，專門解決毛孔堵塞和油脂分泌問題；還有**抗衰老注射**和**激光緊膚療程**，幫助改善皺紋和皮膚鬆弛。如果您有具體需求，我們可以為您量身定制專屬方案，確保效果符合您的期望！",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶以古文詢問美容療程",
            [
                new ChatMessageContent(AuthorRole.User, "敢問貴處，是否有針對男士之美容療程？我心中疑惑，常以為此乃女子所需，然今欲求美，尚可否？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "尊敬的客戶，然則無需懷疑。吾等有專為男士設計之療程，旨在解決男士肌膚之特性，如油脂過多、毛孔粗大等問題。現今社會，男士亦可修身齊家，注重外貌，實乃時勢所需。"),
                new ChatMessageContent(AuthorRole.User, "然我心中猶存疑慮，若他人知之，必會笑我過於矯飾，該如何是好？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "君之憂慮可理解。然今世對男士注重外表已無所謂，反而為智者所推崇。吾等療程環境私密，保證君之隱私無虞，無需顧慮他人之目光。"),
                new ChatMessageContent(AuthorRole.User, "請告知，有何療程可供男士選擇？")
            ],
            "我等有多種針對男士之療程，如**深層清潔面部**、**抗衰老注射**及**激光緊膚**等，皆可改善肌膚狀況，使君容光煥發。如有需求，我等願為君量身定制專屬方案，以達最佳效果！",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "極其專業的客戶對美容院療程提出尖銳質疑",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "你們的Oligio X聲稱是Thermage的升級版，但根據我對RF（射頻）技術的理解，Thermage使用的是單極射頻，而Oligio X是雙極射頻，雙極射頻的穿透深度遠不及單極射頻。你們如何解釋這一點？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "感謝您的專業見解。您提到的確是RF技術的一個重要區別。不過，Oligio X採用了GXG模式，結合了多層次加熱技術，能有效補足雙極射頻在深層穿透方面的不足，同時提升舒適度和安全性。"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "但GXG模式只是能量分配的一種方式，並不能改變物理特性。再者，你們聲稱Oligio X可以刺激筋膜層膠原增生，但根據我的研究，只有高強度聚焦超聲（HIFU）才能真正作用於筋膜層。你們的說法是否存在誤導？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您的觀點非常專業且深入。我們的療程主要針對真皮層和淺筋膜層進行膠原增生刺激，而非深筋膜層，因此我們並未聲稱可以完全替代HIFU技術。不過，Oligio X在提升皮膚緊緻度和改善淺層皺紋方面效果顯著，是一種更舒適的選擇。"),
                new ChatMessageContent(AuthorRole.User, "那麼，你們是否能提供具體的臨床數據來支持這些效果？此外，Oligio X是否通過了獨立第三方機構的長期驗證？")
            ],
            "我們非常理解您對療程科學性的重視。Oligio X已通過美國FDA、歐盟CE以及韓國KFDA認證，其臨床數據顯示在改善皮膚緊緻度和皺紋方面有顯著效果。我們也可以向您提供相關研究報告和第三方驗證資料，以便您進一步了解。如果您有興趣，我們可以安排一次免費諮詢，與您深入探討更多細節。",
            BeautyTreatmentPlansDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶希望以可愛的uwu語氣進行對話",
            [
                new ChatMessageContent(AuthorRole.User, "嗨嗨！我想問問有關肉毒桿菌素的療程呢~ uwu"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "嗨嗨！很高興你來詢問肉毒桿菌素的療程哦~ 有什麼問題我可以幫你解答的嗎？"),
                new ChatMessageContent(AuthorRole.User, "我聽說效果很好，但會不會痛痛的呀？>_<"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "一般來說，注射時可能會有輕微的不適感，但我們會盡量讓過程舒適哦~ 如果需要，我們也可以使用麻醉膏來減輕不適感！"),
                new ChatMessageContent(AuthorRole.User, "那我打完後會不會很漂亮呢？好期待呀！✨"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "打完後效果會在幾天內逐漸顯現，很多客戶都很滿意哦~ 我們會確保你得到最好的效果！"),
                new ChatMessageContent(AuthorRole.User, "太好了！謝謝你，期待變得更美美的～ uwu")
            ],
            "非常期待您的到來！如果有任何其他問題，隨時告訴我哦~ 我們會全力以赴讓您感到開心！",
            BeautyTreatmentPlansDocuments,
            ShouldAssignLead: false);
    }
}