# Scalability Practices

This document is part of [Sleekflow Project Practices](ProjectPractices.md).

## Distributed ID Generation

The system uses specialized ID generation for distributed systems with the following components:

- **IdGen** - A Snowflake-like ID generation algorithm that provides unique identifiers across distributed systems
- **Hashids.net** - Used to encode both type information and the Snowflake ID into a single string identifier
- **Azure Cosmos DB** - Used to manage and allocate machine-specific IDs (0-1023) for the Snowflake algorithm

The ID generation process follows this pattern:

```csharp
// Basic ID generation
Id = Hashids.EncodeLong(TypeId, SnowflakeId)

// For child entities with parent relationship
Id = ParentId + "-" + Hashids.EncodeLong(TypeId, SnowflakeId)
```

Key implementation features:

1. **Thread-safe initialization** using SemaphoreSlim to ensure the ID generator is safely initialized
2. **Type-based ID allocation** with a comprehensive mapping of entity types to specific type IDs
3. **Support for hierarchical IDs** with parent-child relationships
4. **ID decoding capabilities** to extract type information and the original Snowflake ID

Each service instance in the cluster can generate up to 2000 unique IDs per millisecond without coordination, ensuring high throughput in distributed environments.

### Using IdService

The `IdService` is registered as a singleton service in the DI container. To use it:

```csharp
// Inject the service
public class MyService
{
    private readonly IIdService _idService;

    public MyService(IIdService idService)
    {
        _idService = idService;
    }

    // Generate a new ID for an entity
    public string CreateNewEntity()
    {
        // Use the appropriate entity type name
        string id = _idService.GetId("Workflow");

        // Process entity with the new ID
        return id;
    }

    // Generate a child entity ID
    public string CreateChildEntity(string parentId)
    {
        // For entities with parent-child relationship
        string id = _idService.GetId("WorkflowExecution", parentId);

        return id;
    }

    // Decode an ID to get type information
    public void ProcessId(string id)
    {
        var (typeName, snowflakeId) = _idService.DecodeId(id);

        // Use the type information and snowflake ID
        Console.WriteLine($"Type: {typeName}, SnowflakeId: {snowflakeId}");
    }
}
```

Best practices:

1. Always use the correct entity type name from the predefined list in `IdService`
2. For child entities, always pass the parent ID as the second parameter
3. When decoding IDs, check if the returned type name is "Unknown" which indicates the type ID wasn't recognized
4. The IdService handles its own thread-safety and initialization, so you can use it directly where needed

## Deployment Architecture

Scalability is achieved through:

1. **Azure Container Apps** - For microservices deployment with scaling rules based on CPU, memory, and event metrics
2. **Azure Functions** - For serverless workloads that need rapid scaling
3. **Azure Service Bus** - For distributed message queuing with specialized high-traffic queues
4. **Azure Front Door** - Global routing and traffic management for multi-region deployment
5. **Redis Cache** - Distributed caching to reduce database load and improve response times
6. **Cosmos DB** - Auto-scaling document database with multi-region replication

The system supports multi-region deployment across primary (East Asia) and secondary regions (East US, Southeast Asia, UAE North, and West Europe) for high availability and disaster recovery.
