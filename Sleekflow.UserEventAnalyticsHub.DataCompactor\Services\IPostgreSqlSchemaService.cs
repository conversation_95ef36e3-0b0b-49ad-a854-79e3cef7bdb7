namespace Sleekflow.UserEventAnalyticsHub.DataCompactor.Services;

public interface IPostgreSqlSchemaService
{
    Task EnsureCompanyTablesExistAsync(string sleekflowCompanyId);
    Task<bool> ValidateTableSchemaAsync(string sleekflowCompanyId);
    Task CreateIndexesAsync(string sleekflowCompanyId);
    Task<bool> TableExistsAsync(string tableName);
    Task<bool> TestConnectionAsync();
    Task EnsureDatabaseExistsAsync();
    Task TestConnectivityAsync();
    Task EnsureCompanySchemaAsync(string sleekflowCompanyId);
}