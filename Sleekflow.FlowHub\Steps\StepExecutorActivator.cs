using Sleekflow.DependencyInjection;
using Sleekflow.DistributedInvocations;
using Sleekflow.FlowHub.Configs;
using Sleekflow.FlowHub.Counters;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workers;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Silos;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Steps;

public interface IStepExecutorActivator
{
    Task RequestStepAsync(
        string stateId,
        string stepId,
        Stack<StackEntry> stackEntries,
        string? workerInstanceId);

    Task FailStepAsync(
        string stateId,
        string stepId,
        Stack<StackEntry> stackEntries,
        string? workerInstanceId,
        Exception? exception);

    Task CompleteStepAsync(
        string stateId,
        string stepId,
        Stack<StackEntry> stackEntries,
        string activationStatus,
        EventBody? eventBody = null,
        string? workerInstanceId = null);
}

// It is responsible for managing the execution of steps within a workflow by coordinating
// the activation, completion, or failure of each step.
public class StepExecutorActivator : IStepExecutorActivator, IScopedService
{
    private readonly IStateService _stateService;
    private readonly IWorkflowService _workflowService;
    private readonly IWorkflowRuntimeService _workflowRuntimeService;
    private readonly IStepExecutionService _stepExecutionService;
    private readonly IStateAggregator _stateAggregator;
    private readonly IWorkflowStepLocator _workflowStepLocator;
    private readonly IWorkflowStepNodeLocator _workflowStepNodeLocator;
    private readonly IStepExecutorMatcher _stepExecutorMatcher;
    private readonly IStepOrchestrationService _stepOrchestrationService;
    private readonly ISlidingWindowCounter _slidingWindowCounter;
    private readonly IStateAllStepsRequestRateCountConfig _stateAllStepsRequestRateCountConfig;
    private readonly IWorkflowPotentialInfiniteLoopConfig _workflowPotentialInfiniteLoopConfig;
    private readonly ILogger<StepExecutorActivator> _logger;
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public StepExecutorActivator(
        IStateService stateService,
        IWorkflowService workflowService,
        IWorkflowRuntimeService workflowRuntimeService,
        IStepExecutionService stepExecutionService,
        IStateAggregator stateAggregator,
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowStepNodeLocator workflowStepNodeLocator,
        IStepExecutorMatcher stepExecutorMatcher,
        IStepOrchestrationService stepOrchestrationService,
        ISlidingWindowCounter slidingWindowCounter,
        IStateAllStepsRequestRateCountConfig stateAllStepsRequestRateCountConfig,
        IWorkflowPotentialInfiniteLoopConfig workflowPotentialInfiniteLoopConfig,
        ILogger<StepExecutorActivator> logger,
        IDistributedInvocationContextService distributedInvocationContextService)
    {
        _stateService = stateService;
        _workflowService = workflowService;
        _workflowRuntimeService = workflowRuntimeService;
        _stepExecutionService = stepExecutionService;
        _stateAggregator = stateAggregator;
        _workflowStepLocator = workflowStepLocator;
        _workflowStepNodeLocator = workflowStepNodeLocator;
        _stepExecutorMatcher = stepExecutorMatcher;
        _stepOrchestrationService = stepOrchestrationService;
        _slidingWindowCounter = slidingWindowCounter;
        _stateAllStepsRequestRateCountConfig = stateAllStepsRequestRateCountConfig;
        _workflowPotentialInfiniteLoopConfig = workflowPotentialInfiniteLoopConfig;
        _logger = logger;
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    public async Task RequestStepAsync(
        string stateId,
        string stepId,
        Stack<StackEntry> stackEntries,
        string? workerInstanceId)
    {
        // Orleans automatically handles graceful shutdown by:
        // 1. Stopping acceptance of new grain calls during shutdown
        // 2. Waiting for active grain calls to complete (up to ShutdownRerouteTimeout)
        // 3. Rerouting pending calls to other silos
        // No manual tracking needed here.

        try
        {
            var state = await _stateService.GetProxyStateAsync(stateId);

            if (state.StateStatus is
                StateStatuses.Failed
                or StateStatuses.Cancelled
                or StateStatuses.Abandoned
                or StateStatuses.Blocked)
            {
                return;
            }

            var targetWorkflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
                state.Identity.SleekflowCompanyId,
                state.Identity.WorkflowVersionedId);

            if (await IsVersionedWorkflowDeletedAsync(state, targetWorkflow)
                || await IsVersionedWorkflowDisabledAsync(state, targetWorkflow))
            {
                return;
            }

            try
            {
                var singleStateAllStepsRequestCount = await _slidingWindowCounter.CountAsync(
                    $"state-steps-count:{stateId}",
                    _stateAllStepsRequestRateCountConfig.StateAllStepsRequestWindowSeconds);

                if (singleStateAllStepsRequestCount >
                    _stateAllStepsRequestRateCountConfig.StateAllStepsRequestErrorMaxWithinWindow &&
                    singleStateAllStepsRequestCount % 50 == 0)
                {
                    _logger.LogError(
                        "Too many steps requested within {windowSeconds} seconds. State: {stateId}, Step Requests Count: {stepRequestCount}.",
                        _stateAllStepsRequestRateCountConfig.StateAllStepsRequestWindowSeconds,
                        stateId,
                        singleStateAllStepsRequestCount);
                }
                else if (singleStateAllStepsRequestCount >
                         _stateAllStepsRequestRateCountConfig.StateAllStepsRequestWarningMaxWithinWindow &&
                         singleStateAllStepsRequestCount % 50 == 0)
                {
                    _logger.LogWarning(
                        "Too many steps requested within {windowSeconds} seconds. State: {stateId}, Step Requests Count: {stepRequestCount}.",
                        _stateAllStepsRequestRateCountConfig.StateAllStepsRequestWindowSeconds,
                        stateId,
                        singleStateAllStepsRequestCount);
                }
            }
            catch
            {
                // ignored
            }

            await _stepOrchestrationService.NotifyStatusAsync(
                stateId,
                stepId,
                workerInstanceId,
                StepExecutionStatuses.Started);

            try
            {
                var singleStepRequestCount = await _slidingWindowCounter.CountAsync(
                    $"steps-count:{stateId}-{stepId}",
                    _workflowPotentialInfiniteLoopConfig.SpecificStepRequestRateLimitWindowSeconds);
                if (singleStepRequestCount >
                    _workflowPotentialInfiniteLoopConfig.SpecificStepRequestAllowedWithinWindow)
                {
                    var stepNodeId = _workflowStepNodeLocator.GetStepNodeId(targetWorkflow, stepId);

                    await _stepExecutionService.CreateStepExecutionAsync(
                        state.Id,
                        state.Identity,
                        stepId,
                        stepNodeId,
                        StepExecutionStatuses.Failed,
                        workerInstanceId,
                        DateTimeOffset.UtcNow,
                        new UserFriendlyError(
                            UserFriendlyErrorCodes.PotentialInfiniteLoop,
                            $"Potential infinite loop detected in step {stepId} of workflow {targetWorkflow?.WorkflowId}"));

                    await _workflowRuntimeService.FailWorkflowAsync(state);

                    return;
                }
            }
            catch
            {
                // ignored
            }

            await ActivateStepExecutorAsync(
                stateId,
                stepId,
                stackEntries,
                async (_, activationStatus) =>
                {
                    await CompleteStepAsync(
                        stateId,
                        stepId,
                        stackEntries,
                        activationStatus,
                        null,
                        workerInstanceId);
                });
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "State {StateId} failed to process requested step {StepId}, worker instance {WorkerInstanceId}",
                stateId,
                stepId,
                workerInstanceId);

            await FailStepAsync(
                stateId,
                stepId,
                stackEntries,
                workerInstanceId,
                e);

            throw;
        }
    }

    private async Task<bool> IsVersionedWorkflowDeletedAsync(
        ProxyState state,
        ProxyWorkflow? targetWorkflow)
    {
        if (targetWorkflow is null or { ActivationStatus: WorkflowActivationStatuses.Deleted })
        {
            await _workflowRuntimeService.CancelWorkflowAsync(
                state.Identity.SleekflowCompanyId,
                state.Id,
                StateReasonCodes.VersionedWorkflowDeleted);

            return true;
        }

        return false;
    }

    private async Task<bool> IsVersionedWorkflowDisabledAsync(
        ProxyState state,
        ProxyWorkflow? targetWorkflow)
    {
        if (targetWorkflow is not { ActivationStatus: WorkflowActivationStatuses.Draft }
            // allow agent flow to continue enroll
            || targetWorkflow.WorkflowType == WorkflowType.AIAgent)
        {
            return false;
        }

        var numOfActiveWorkflowVersions = await _workflowService.CountWorkflowsAsync(
            state.Identity.SleekflowCompanyId,
            state.Identity.WorkflowId,
            WorkflowActivationStatuses.Active);

        if (numOfActiveWorkflowVersions > 0)
        {
            await _workflowRuntimeService.CancelWorkflowAsync(
                state.Identity.SleekflowCompanyId,
                state.Id,
                StateReasonCodes.VersionedWorkflowOutdated);
        }
        else
        {
            await _workflowRuntimeService.CancelWorkflowAsync(
                state.Identity.SleekflowCompanyId,
                state.Id,
                StateReasonCodes.VersionedWorkflowDisabled);
        }

        return true;
    }

    public async Task CompleteStepAsync(
        string stateId,
        string stepId,
        Stack<StackEntry> stackEntries,
        string activationStatus,
        EventBody? eventBody = null,
        string? workerInstanceId = null)
    {
        if (activationStatus == StepExecutionStatuses.Failed
            || activationStatus == StepExecutionStatuses.Timeout)
        {
            await FailStepAsync(
                stateId,
                stepId,
                stackEntries,
                workerInstanceId,
                null);

            return;
        }

        await CompleteStepActivationAsync(
            stateId,
            stepId,
            workerInstanceId,
            stackEntries,
            activationStatus,
            eventBody);
    }

    public async Task FailStepAsync(
        string stateId,
        string stepId,
        Stack<StackEntry> stackEntries,
        string? workerInstanceId,
        Exception? exception)
    {
        await _stepOrchestrationService.NotifyStatusAsync(
            stateId,
            stepId,
            workerInstanceId,
            StepExecutionStatuses.Failed,
            exception);

        // If worker instance id is null,
        // it means that the step is not controlled by the worker.
        // Need to handle failure manually.
        if (workerInstanceId == null)
        {
            await CompleteStepActivationAsync(
                stateId,
                stepId,
                workerInstanceId,
                stackEntries,
                StepExecutionStatuses.Failed,
                null,
                exception);
        }
    }

    private async Task ActivateStepExecutorAsync(
        string stateId,
        string stepId,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivationComplete)
    {
        var state = await _stateService.GetProxyStateAsync(stateId);
        var workflow = state.WorkflowContext.SnapshottedWorkflow;

        var step = _workflowStepLocator.GetStep(workflow, stepId);
        _distributedInvocationContextService.SetFlowBuilderWorkflowContext(
            workflow.SleekflowCompanyId,
            workflow.WorkflowVersionedId,
            stepId);

        _distributedInvocationContextService.SetFlowBuilderWorkflowContext(
            workflow.SleekflowCompanyId,
            workflow.WorkflowVersionedId,
            stepId);

        await _stepExecutorMatcher.MatchExecutor(step).OnStepActivateAsync(
            workflow,
            state,
            step,
            stackEntries,
            async (s, activationStatus) =>
            {
                if (activationStatus == StepExecutionStatuses.Failed)
                {
                    // The exception will be caught by Sleekflow.FlowHub.Consumers.OnStepRequestedEventConsumer.Consume
                    // All steps having an exception must throw an exception
                    // OnStepRequestedEventConsumer.Consume will catch the exception and call CompleteStepActivationAsync
                    return;
                }

                await onActivationComplete(s, activationStatus);
            });
    }

    private async Task CompleteStepActivationAsync(
        string stateId,
        string stepId,
        string? workerInstanceId,
        Stack<StackEntry> stackEntries,
        string executionStatus,
        EventBody? eventBody,
        Exception? exception = null)
    {
        var state = await _stateService.GetProxyStateAsync(stateId);
        var workflow = state.WorkflowContext.SnapshottedWorkflow;

        var targetWorkflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
            state.Identity.SleekflowCompanyId,
            state.Identity.WorkflowVersionedId);

        if (await IsVersionedWorkflowDeletedAsync(state, targetWorkflow)
            || await IsVersionedWorkflowDisabledAsync(state, targetWorkflow))
        {
            return;
        }

        var step = _workflowStepLocator.GetStep(workflow, stepId);

        await CompleteStepActivationAsync(
            state,
            step,
            stackEntries,
            executionStatus,
            eventBody,
            exception,
            () => _stepOrchestrationService.NotifyStatusAsync(
                stateId,
                stepId,
                workerInstanceId,
                StepExecutionStatuses.Complete));
    }

    private async Task CompleteStepActivationAsync(
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        string executionStatus,
        EventBody? eventBody = null,
        Exception? exception = null,
        Func<Task>? onCompletionTask = null)
    {
        var workflow = state.WorkflowContext.SnapshottedWorkflow;

        if (executionStatus == StepExecutionStatuses.Failed
            || executionStatus == StepExecutionStatuses.Timeout)
        {
            if (stackEntries.Any() && stackEntries.TryPop(out var stackEntry))
            {
                await CompleteStepAsync(
                    state.Id,
                    stackEntry.StepId,
                    stackEntries,
                    StepExecutionStatuses.Failed,
                    eventBody,
                    stackEntry.WorkerInstanceId);
            }
            else if (state.StateStatus == StateStatuses.Scheduled)
            {
                await _workflowRuntimeService.AbandonWorkflowAsync(
                    state.Identity.SleekflowCompanyId,
                    state.Id,
                    StateReasonCodes.ScheduledEnrollmentInternalError);
            }
            else
            {
                await _workflowRuntimeService.FailWorkflowAsync(state);
            }

            return;
        }

        {
            var myState = state;

            if (step.Assign != null)
            {
                var updatedState = await _stateAggregator.AggregateStateAssignAsync(myState, step.Assign, eventBody);

                myState = updatedState;
            }

            if (onCompletionTask is not null)
            {
                await onCompletionTask();
            }

            Step? nextStep = null;

            try
            {
                nextStep = await _stepExecutorMatcher
                    .MatchExecutor(step)
                    .OnStepCompleteAsync(workflow, myState, step, stackEntries);
            }
            catch (ScheduledWorkflowConditionNotFulfilledException ex)
            {
                _logger.LogInformation(
                    ex,
                    "State {StateId} does not fulfill enrollment condition {ConditionExpression}",
                    ex.StateId,
                    ex.ConditionExpression);

                await _workflowRuntimeService.AbandonWorkflowAsync(
                    myState.Identity.SleekflowCompanyId,
                    myState.Id,
                    StateReasonCodes.ScheduledEnrollmentConditionNotFulfilled);

                return;
            }
            catch (FlowHubDisabledException ex)
            {
                _logger.LogError(
                    ex,
                    "State {StateId} abandoned due to FlowHub disabled",
                    ex.StateId);

                await _workflowRuntimeService.AbandonWorkflowAsync(
                    myState.Identity.SleekflowCompanyId,
                    myState.Id,
                    StateReasonCodes.ScheduledEnrollmentFlowHubDisabled);

                return;
            }
            catch (FlowHubMonthlyWorkflowExecutionLimitExceededException ex)
            {
                _logger.LogError(
                    ex,
                    "State {StateId} abandoned due to monthly workflow execution limit exceeded",
                    ex.StateId);

                await _workflowRuntimeService.RestrictWorkflowAsync(
                    myState.Identity.SleekflowCompanyId,
                    myState.Id,
                    StateReasonCodes.EnrollmentUsageLimitExceeded);

                return;
            }


            if (nextStep == null
                && stackEntries.Any()
                && stackEntries.TryPop(out var stackEntry))
            {
                await CompleteStepAsync(
                    state.Id,
                    stackEntry.StepId,
                    stackEntries,
                    StepExecutionStatuses.Complete,
                    eventBody,
                    stackEntry.WorkerInstanceId);
            }
            else if (nextStep != null)
            {
                await _stepOrchestrationService.ExecuteStepAsync(
                    state.Id,
                    nextStep.Id,
                    stackEntries);
            }
        }
    }
}