﻿using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.CrmHub.Workers.ProviderConfigs;
using Sleekflow.CrmHub.Workers.Subscriptions;

namespace Sleekflow.CrmHub.Workers.Triggers.Dynamics365;

public class SubscriptionsCheck
{
    private readonly ILogger<SubscriptionsCheck> _logger;
    private readonly IDynamics365SubscriptionRepository _dynamics365SubscriptionRepository;
    private readonly IProviderConfigService _providerConfigService;

    public SubscriptionsCheck(
        ILogger<SubscriptionsCheck> logger,
        IDynamics365SubscriptionRepository dynamics365SubscriptionRepository,
        IProviderConfigService providerConfigService)
    {
        _logger = logger;
        _dynamics365SubscriptionRepository = dynamics365SubscriptionRepository;
        _providerConfigService = providerConfigService;
    }

    public class SubscriptionsCheckInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("subscription")]
        [Required]
        public Dynamics365Subscription Subscription { get; set; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<SyncConfigFilterGroup> FilterGroups { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckInput(
            string sleekflowCompanyId,
            string entityTypeName,
            Dynamics365Subscription subscription,
            List<SyncConfigFilterGroup> filterGroups,
            List<SyncConfigFieldFilter>? fieldFilters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            Subscription = subscription;
            FilterGroups = filterGroups;
            FieldFilters = fieldFilters;
        }
    }

    [Function("Dynamics365_SubscriptionsCheck")]
    public async Task RunAsync(
        [TimerTrigger("0 */1 * * * *")]
        TimerInfo timerInfo,
        [DurableClient]
        DurableTaskClient starter)
    {
        var queryDefinition =
            new QueryDefinition(
                    "SELECT * "
                    + "FROM %%CONTAINER_NAME%% c "
                    + "WHERE (DateTimeDiff('second', c.last_execution_start_time, @now) > c.interval AND c.durable_payload = null) "
                    + "OR (DateTimeDiff('second', c.last_execution_start_time, @now) > 300 AND c.durable_payload != null)")
                .WithParameter("@now", DateTimeOffset.UtcNow);
        var objects =
            await _dynamics365SubscriptionRepository.GetObjectsAsync(queryDefinition);

        foreach (var subscription in objects)
        {
            var providerConfig =
                await _providerConfigService.GetProviderConfigOrDefaultAsync(
                    subscription.SleekflowCompanyId,
                    "d365");
            if (providerConfig == null || providerConfig.IsAuthenticated == false)
            {
                _logger.LogWarning(
                    "The provider dynamics365-integrator for sleekflowCompanyId {SleekflowCompanyId} is not initialized",
                    subscription.SleekflowCompanyId);

                continue;
            }

            var syncConfig =
                providerConfig.EntityTypeNameToSyncConfigDict.GetValueOrDefault(subscription.EntityTypeName);
            if (syncConfig == null)
            {
                _logger.LogWarning(
                    "The entityTypeName {EntityTypeName} for sleekflowCompanyId {SleekflowCompanyId} is not initialized",
                    subscription.EntityTypeName,
                    subscription.SleekflowCompanyId);

                continue;
            }

            var instanceId = await starter.ScheduleNewOrchestrationInstanceAsync(
                "Dynamics365_SubscriptionsCheck_Orchestrator",
                input: new SubscriptionsCheckInput(
                    subscription.SleekflowCompanyId,
                    subscription.EntityTypeName,
                    subscription,
                    syncConfig.FilterGroups,
                    syncConfig.FieldFilters));
            var httpManagementPayload = starter.CreateHttpManagementPayload(instanceId);
            if (httpManagementPayload == null)
            {
                _logger.LogInformation(
                    "Unable to get Dynamics365_SubscriptionsCheck_Orchestrator httpManagementPayload");
            }

            await _dynamics365SubscriptionRepository.PatchAsync(
                subscription.Id,
                subscription.SleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Replace(
                        $"/{Dynamics365Subscription.PropertyNameDurablePayload}",
                        httpManagementPayload)
                });
        }
    }
}