using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Workers.Activities;

public class UpdateDebugTimestamps
{
    private readonly ILogger<UpdateDebugTimestamps> _logger;
    private readonly IKbDocumentService _kbDocumentService;

    public UpdateDebugTimestamps(
        ILogger<UpdateDebugTimestamps> logger,
        IKbDocumentService kbDocumentService)
    {
        _logger = logger;
        _kbDocumentService = kbDocumentService;
    }

    public class UpdateDebugTimestampsInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("document_id")]
        [Required]
        public string DocumentId { get; set; }

        [JsonProperty("conversion_started")]
        public DateTimeOffset? ConversionStarted { get; set; }

        [JsonProperty("conversion_ended")]
        public DateTimeOffset? ConversionEnded { get; set; }

        [JsonProperty("upload_started")]
        public DateTimeOffset? UploadStarted { get; set; }

        [JsonProperty("upload_ended")]
        public DateTimeOffset? UploadEnded { get; set; }

        [JsonConstructor]
        public UpdateDebugTimestampsInput(
            string sleekflowCompanyId,
            string documentId,
            DateTimeOffset? conversionStarted = null,
            DateTimeOffset? conversionEnded = null,
            DateTimeOffset? uploadStarted = null,
            DateTimeOffset? uploadEnded = null)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DocumentId = documentId;
            ConversionStarted = conversionStarted;
            ConversionEnded = conversionEnded;
            UploadStarted = uploadStarted;
            UploadEnded = uploadEnded;
        }
    }

    public class UpdateDebugTimestampsOutput
    {
        [JsonConstructor]
        public UpdateDebugTimestampsOutput()
        {
        }
    }

    [Function(nameof(UpdateDebugTimestamps))]
    public async Task<UpdateDebugTimestampsOutput> Run(
        [ActivityTrigger]
        UpdateDebugTimestampsInput input)
    {
        _logger.LogInformation(
            "UpdateDebugTimestamps {SleekflowCompanyId} {DocumentId}",
            input.SleekflowCompanyId,
            input.DocumentId);

        await _kbDocumentService.UpdateDebugTimestampsAsync(
            input.SleekflowCompanyId,
            input.DocumentId,
            input.ConversionStarted,
            input.ConversionEnded,
            input.UploadStarted,
            input.UploadEnded);

        return new UpdateDebugTimestampsOutput();
    }
}