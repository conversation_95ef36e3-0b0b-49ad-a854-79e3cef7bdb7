using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.IntelligentHub.WebCrawlingSessions;

namespace Sleekflow.IntelligentHub.Documents.WebsiteDocuments;

public interface IWebsiteDocumentService
{
    Task<string> CreateWebsiteDocumentAsync(
        string sleekflowCompanyId,
        string webCrawlingSessionId,
        string baseUrl,
        string? baseUrlTitle,
        List<string> selectedUrls,
        List<string> agentIds,
        string staffId);

    Task<WebsiteDocument> UpdateUrlStatusesAsync(
        string sleekflowCompanyId,
        string documentId,
        List<string> urlsToUpdate,
        string newStatus);
}

public class WebsiteDocumentService : IWebsiteDocumentService, IScopedService
{
    private readonly ILogger<WebsiteDocumentService> _logger;
    private readonly IWebCrawlingSessionService _webCrawlingSessionService;
    private readonly IWebsiteDocumentRepository _websiteDocumentRepository;
    private readonly IIdService _idService;
    private readonly ICompanyAgentConfigService _companyAgentConfigService;
    private readonly IKnowledgeBaseIngestionService _knowledgeBaseIngestionService;
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;
    private readonly IIntelligentHubCharacterCountService _intelligentHubCharacterCountService;

    public WebsiteDocumentService(
        ILogger<WebsiteDocumentService> logger,
        IWebCrawlingSessionService webCrawlingSessionService,
        IWebsiteDocumentRepository websiteDocumentRepository,
        IIdService idService,
        ICompanyAgentConfigService companyAgentConfigService,
        IKnowledgeBaseIngestionService knowledgeBaseIngestionService,
        IIntelligentHubConfigService intelligentHubConfigService,
        IIntelligentHubUsageService intelligentHubUsageService,
        IIntelligentHubCharacterCountService intelligentHubCharacterCountService)
    {
        _logger = logger;
        _webCrawlingSessionService = webCrawlingSessionService;
        _websiteDocumentRepository = websiteDocumentRepository;
        _idService = idService;
        _companyAgentConfigService = companyAgentConfigService;
        _knowledgeBaseIngestionService = knowledgeBaseIngestionService;
        _intelligentHubConfigService = intelligentHubConfigService;
        _intelligentHubUsageService = intelligentHubUsageService;
        _intelligentHubCharacterCountService = intelligentHubCharacterCountService;
    }

    public async Task<string> CreateWebsiteDocumentAsync(
        string sleekflowCompanyId,
        string webCrawlingSessionId,
        string baseUrl,
        string? baseUrlTitle,
        List<string> selectedUrls,
        List<string> agentIds,
        string staffId)
    {
        try
        {
            _logger.LogInformation(
                "[WebCrawlingSession] Creating website document for company {CompanyId} with {UrlCount} URLs and {AgentCount} agents",
                sleekflowCompanyId,
                selectedUrls.Count,
                agentIds.Count);

            // Validate agent IDs and create agent assignments
            var agentAssignments = await CreateAgentAssignmentsAsync(sleekflowCompanyId, agentIds);

            // Convert string URLs to SelectedUrl objects with pending status
            var webCrawlingSession =
                await _webCrawlingSessionService.GetWebCrawlingSessionAsync(sleekflowCompanyId, webCrawlingSessionId);

            // Transform webCrawlingSession.CrawlingResults to a dictionary of url -> character_count
            var urlToCharacterCountMap = webCrawlingSession.CrawlingResults
                .ToDictionary(result => result.Url, result => result.CharacterCount);

            // Fix the SelectedUrl initialization by passing in the correct character_count (aka pagesize)
            var selectedUrlObjects = selectedUrls.Select(
                url =>
                {
                    var characterCount = urlToCharacterCountMap[url];
                    return new SelectedUrl(SelectedUrlStatuses.Pending, url, characterCount);
                }).ToList();

            // Create a single WebsiteDocument with all selected URLs
            var currentTime = DateTimeOffset.UtcNow;
            var websiteDocument = new WebsiteDocument(
                _idService.GetId(SysTypeNames.WebsiteDocument),
                sleekflowCompanyId,
                string.Empty,
                baseUrl,
                baseUrlTitle,
                selectedUrlObjects, // Pass the SelectedUrl objects instead of strings
                new Dictionary<string, object?>(),
                ProcessFileDocumentStatuses.NotConverted,
                0.0,
                agentAssignments,
                currentTime,
                currentTime,
                staffId,
                new DebugTimestamps());

            var intelligentHubConfig =
                await _intelligentHubConfigService.GetIntelligentHubConfigAsync(sleekflowCompanyId);

            if (intelligentHubConfig == null)
            {
                throw new Exception("Intelligent Hub config not found");
            }

            // Check character count limits for each agent
            var characterCountLimit = _intelligentHubUsageService.GetFeatureTotalUsageLimit(
                intelligentHubConfig,
                PriceableFeatures.AiAgentsKnowledgeBaseTotalCharacterCount);

            var characterCount = websiteDocument.GetCharacterCount();

            foreach (var agentId in agentIds.Where(id => id != CompanyAgentTypes.SmartReply))
            {
                var currentCharacterCount =
                    await _intelligentHubCharacterCountService.GetCharacterCountForAgent(sleekflowCompanyId, agentId);
                if (currentCharacterCount + characterCount > characterCountLimit)
                {
                    throw new SfUserFriendlyException(
                        $"Agent {agentId} would exceed the character count limit of {characterCountLimit}. Current count: {currentCharacterCount}, Adding: {characterCount}");
                }
            }

            // Create the document
            var createdDocument =
                await _websiteDocumentRepository.CreateAndGetAsync(websiteDocument, sleekflowCompanyId);

            if (agentIds.Any())
            {
                await _knowledgeBaseIngestionService.StartKnowledgeBaseIngestion(
                    sleekflowCompanyId,
                    createdDocument.Id);
            }

            _logger.LogInformation(
                "Successfully created website document for company {CompanyId}",
                sleekflowCompanyId);

            return createdDocument.Id;
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Failed to create website document! {CompanyId} {Exception}",
                sleekflowCompanyId,
                e.Message);
            throw;
        }
    }

    private async Task<List<AgentAssignment>> CreateAgentAssignmentsAsync(
        string sleekflowCompanyId,
        List<string> agentIds)
    {
        if (!agentIds.Any())
        {
            return new List<AgentAssignment>();
        }

        // Validate agent IDs exist (excluding Smart Reply which is special)
        await ValidateAgentIdsAsync(sleekflowCompanyId, agentIds);

        // Separate SmartReply from other agent IDs
        var smartReplyIds = agentIds.Where(id => id == CompanyAgentTypes.SmartReply).ToList();
        var otherAgentIds = agentIds.Where(id => id != CompanyAgentTypes.SmartReply).ToList();

        var agentAssignments = new List<AgentAssignment>();

        // Handle regular agent IDs
        if (otherAgentIds.Any())
        {
            // Fetch agent configs concurrently for regular agents
            var agentConfigTasks = otherAgentIds
                .Select(id => _companyAgentConfigService.GetOrDefaultAsync(id, sleekflowCompanyId));
            var agentConfigs = await Task.WhenAll(agentConfigTasks);

            // Map agent configs to agent assignments, initializing RAG status to Pending
            var regularAgentAssignments = agentConfigs
                .Where(config => config != null) // Filter out null configs (invalid IDs)
                .Select(config => new AgentAssignment(config!.Id, RagStatus.Pending, 0.0))
                .ToList();

            agentAssignments.AddRange(regularAgentAssignments);
        }

        // Handle SmartReply special case
        if (smartReplyIds.Any())
        {
            // Add SmartReply agent assignment directly without querying config service
            agentAssignments.Add(
                new AgentAssignment(
                    CompanyAgentTypes.SmartReply,
                    RagStatus.Pending,
                    0.0));
        }

        return agentAssignments;
    }

    private async Task ValidateAgentIdsAsync(string sleekflowCompanyId, List<string> agentIds)
    {
        // Filter out SmartReply which is a special case
        var regularAgentIds = agentIds.Where(id => id != CompanyAgentTypes.SmartReply).ToList();

        if (!regularAgentIds.Any())
        {
            return; // No regular agent IDs to validate
        }

        // Fetch agent configs concurrently to validate they exist
        var agentConfigTasks = regularAgentIds
            .Select(id => _companyAgentConfigService.GetOrDefaultAsync(id, sleekflowCompanyId));
        var agentConfigs = await Task.WhenAll(agentConfigTasks);

        // Check for invalid agent IDs
        var invalidAgentIds = new List<string>();
        for (int i = 0; i < regularAgentIds.Count; i++)
        {
            if (agentConfigs[i] == null)
            {
                invalidAgentIds.Add(regularAgentIds[i]);
            }
        }

        if (invalidAgentIds.Any())
        {
            throw new Sleekflow.Exceptions.SfNotFoundObjectException(
                $"The following agent IDs do not exist: {string.Join(", ", invalidAgentIds)}");
        }
    }

    public async Task<WebsiteDocument> UpdateUrlStatusesAsync(
        string sleekflowCompanyId,
        string documentId,
        List<string> urlsToUpdate,
        string newStatus)
    {
        // Get the current document
        var websiteDocument = await _websiteDocumentRepository.GetAsync(documentId, sleekflowCompanyId);
        if (websiteDocument == null)
        {
            throw new Exceptions.SfNotFoundObjectException($"WebsiteDocument not found: {documentId}");
        }

        // Update the status of specified URLs
        var urlsToUpdateSet = urlsToUpdate.ToHashSet();
        foreach (var selectedUrl in websiteDocument.SelectedUrls)
        {
            if (urlsToUpdateSet.Contains(selectedUrl.PageUrl))
            {
                selectedUrl.Status = newStatus;
            }
        }

        // Save the updated document
        var updatedDocument = await _websiteDocumentRepository.PatchSelectedUrlsAsync(
            sleekflowCompanyId,
            documentId,
            websiteDocument.SelectedUrls);

        _logger.LogInformation(
            "Updated URL statuses for document {DocumentId}: {UrlCount} URLs set to {Status}",
            documentId,
            urlsToUpdate.Count,
            newStatus);

        return updatedDocument;
    }
}