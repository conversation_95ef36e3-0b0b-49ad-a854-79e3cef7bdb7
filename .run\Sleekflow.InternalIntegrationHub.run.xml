<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Sleekflow.InternalIntegrationHub" type="DotNetProject" factoryName=".NET Project">
    <option name="EXE_PATH" value="$PROJECT_DIR$/Sleekflow.InternalIntegrationHub/bin/Debug/net8.0/Sleekflow.InternalIntegrationHub" />
    <option name="PROGRAM_PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Sleekflow.InternalIntegrationHub" />
    <option name="PASS_PARENT_ENVS" value="1" />
    <envs>
      <env name="ASPNETCORE_ENVIRONMENT" value="Development" />
      <env name="ASPNETCORE_URLS" value="https://localhost:7117;http://localhost:7118" />
      <env name="CACHE_PREFIX" value="Sleekflow.InternalIntegrationHub" />
      <env name="COSMOS_DATABASE_ID" value="db" />
      <env name="COSMOS_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="COSMOS_INTERNAL_INTEGRATION_HUB_DB_DATABASE_ID" value="internalIntegrationHubDb" />
      <env name="COSMOS_INTERNAL_INTEGRATION_HUB_DB_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="COSMOS_INTERNAL_INTEGRATION_HUB_DB_KEY" value="****************************************************************************************" />
      <env name="COSMOS_KEY" value="****************************************************************************************" />
      <env name="EAS_TRAVIS_DATABASE_CONNECTION_STRING" value="Server=tcp:sleekflow-core-sql-server-eas-productiond2a4d949.database.windows.net,1433;Initial Catalog=sleekflow-crm-prod;Persist Security Info=False;User ID=sf3cd3f3a;Password=*********************************************************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Pooling=true;Max Pool Size=500;Min Pool Size=200;Connection Lifetime=120;" />
      <env name="Endpoint" value="sb://sleekflowc9e40e34.servicebus.windows.net/" />
      <env name="EUS_TRAVIS_DATABASE_CONNECTION_STRING" value="Server=tcp:sleekflow-core-sql-server-eus-stagingd64d721a.database.windows.net,1433;Initial Catalog=sleekflow-core-sql-db-eus-staging;Persist Security Info=False;User ID=s64bcc8b5;Password=*********************************************************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Pooling=true;Max Pool Size=500;Min Pool Size=200;Connection Lifetime=120;" />
      <env name="EVENT_HUB_CONN_STR" value="Endpoint=sb://sleekflowc9e40e34.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=AFm8qHjyFme4dwrfbBKxWqCwDXcHd4klL58Db+FxG7c=" />
      <env name="LOGGER_AUTHENTICATION_ID" value="PxOFmtDmfRvHYCoGsuItWHqipxqn72YE0WxgLy7msPitr3TMgvFFtX1RY7yvnP6Mu+lx0HUGy+Z5Un4oshm9Lw==" />
      <env name="LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON" value="************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" />
      <env name="LOGGER_GOOGLE_CLOUD_PROJECT_ID" value="cool-phalanx-404402" />
      <env name="LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED" value="FALSE" />
      <env name="LOGGER_IS_LOG_ANALYTICS_ENABLED" value="FALSE" />
      <env name="LOGGER_WORKSPACE_ID" value="f0ea3579-8e0a-483f-81bb-62617cdd75a6" />
      <env name="MESSAGE_DATA_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=lxg8d38o3e;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="MESSAGE_DATA_CONTAINER_NAME" value="message-data" />
      <env name="NETSUITE_ACCESS_TOKEN" value="****************************************************************" />
      <env name="NETSUITE_ACCESS_TOKEN_SECRET" value="****************************************************************" />
      <env name="NETSUITE_ACCOUNT_ID" value="7498133-sb1" />
      <env name="NETSUITE_BASE_URL" value="https://7498133-sb1.suitetalk.api.netsuite.com/services/rest/record/v1" />
      <env name="NETSUITE_CONSUMER_KEY" value="****************************************************************" />
      <env name="NETSUITE_CONSUMER_SECRET" value="****************************************************************" />
      <env name="NETSUITE_REALM" value="7498133_SB1" />
      <env name="OMNIHR_BASE_URL" value="https://api.omnihr.co/api/v1" />
      <env name="OMNIHR_PASSWORD" value="fmv5fpz7hfw7XTM_fjr" />
      <env name="OMNIHR_USERNAME" value="<EMAIL>" />
      <env name="REDIS_CONN_STR" value="127.0.0.1:6379" />
      <env name="SEAS_TRAVIS_DATABASE_CONNECTION_STRING" value="" />
      <env name="SERVICE_BUS_CONN_STR" value="Endpoint=sb://sleekflow-local.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ZKDnptWyDBxBPASM36H7o+NrnyDqtK7L3+ASbPJHFzw=" />
      <env name="SharedAccessKey" value="AFm8qHjyFme4dwrfbBKxWqCwDXcHd4klL58Db+FxG7c=&quot;" />
      <env name="SharedAccessKeyName" value="RootManageSharedAccessKey" />
      <env name="UAEN_TRAVIS_DATABASE_CONNECTION_STRING" value="" />
      <env name="WEU_TRAVIS_DATABASE_CONNECTION_STRING" value="" />
    </envs>
    <option name="USE_EXTERNAL_CONSOLE" value="0" />
    <option name="USE_MONO" value="0" />
    <option name="RUNTIME_ARGUMENTS" value="" />
    <option name="PROJECT_PATH" value="$PROJECT_DIR$/Sleekflow.InternalIntegrationHub/Sleekflow.InternalIntegrationHub.csproj" />
    <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
    <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
    <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
    <option name="PROJECT_KIND" value="DotNetCore" />
    <option name="PROJECT_TFM" value="net8.0" />
    <method v="2">
      <option name="Build" />
    </method>
  </configuration>
</component>