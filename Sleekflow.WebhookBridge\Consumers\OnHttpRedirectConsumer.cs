﻿using System.Text;
using MassTransit;
using Sleekflow.Models.Events;
using Sleekflow.WebhookBridge.Services;

namespace Sleekflow.WebhookBridge.Consumers;

public class OnHttpRedirectConsumerDefinition
    : ConsumerDefinition<OnHttpRedirectConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnHttpRedirectConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16 * 10;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(1);
            serviceBusReceiveEndpointConfiguration.UseMessageRetry(r => r.<PERSON>val(6, TimeSpan.FromSeconds(30)));
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnHttpRedirectConsumer : IConsumer<OnHttpRedirectRequest>
{
    private readonly IHttpRedirectService _httpRedirectService;

    public OnHttpRedirectConsumer(IHttpRedirectService httpRedirectService)
    {
        _httpRedirectService = httpRedirectService;
    }

    public async Task Consume(ConsumeContext<OnHttpRedirectRequest> context)
    {
        var evt = context.Message;

        var url = evt.Url;
        var method = new HttpMethod(evt.Method);

        var request = new HttpRequestMessage(method, url);

        var headerDict = evt.HeaderDict;

        var content = evt.HttpContent;

        var mediaType = evt.MediaType;

        if (headerDict != null)
        {
            foreach (var (key, value) in headerDict)
            {
                request.Headers.Add(key, value as string ?? string.Empty);
            }
        }

        if (content != null && mediaType != null)
        {
            var stringContent = new StringContent(content, Encoding.UTF8, mediaType);
            request.Content = stringContent;
        }

        var (isSuccess, response) = await _httpRedirectService.Redirect(request);

        var onHttpRedirectEventResult = new OnHttpRedirectReply(isSuccess, response);

        await context.RespondAsync<OnHttpRedirectReply>(onHttpRedirectEventResult);
    }
}