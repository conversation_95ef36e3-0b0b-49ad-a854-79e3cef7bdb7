﻿using System.Net;
using System.Net.Sockets;
using Polly;

namespace Sleekflow.IntelligentHub.Https;

public static class HttpPolicies
{
    public static IAsyncPolicy<HttpResponseMessage> HttpTransientErrorRetryPolicy
        => Policy<HttpResponseMessage>
            .Handle<HttpRequestException>(
                ex =>
                    ex.StatusCode is > HttpStatusCode.InternalServerError or HttpStatusCode.RequestTimeout)
            .Or<TaskCanceledException>()
            .Or<SocketException>()
            .OrResult(
                res =>
                    res.StatusCode is > HttpStatusCode.InternalServerError or HttpStatusCode.RequestTimeout)
            .WaitAndRetryAsync(
                retryCount: 3,
                sleepDurationProvider: currentRetryCount => TimeSpan.FromSeconds(2 * currentRetryCount),
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    // We can add logging here if needed
                });

    /// <summary>
    /// A more lenient retry policy for MIME type detection that considers certain client errors as non-retryable
    /// to avoid unnecessary retries when the server explicitly blocks the request
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> MimeTypeDetectionRetryPolicy
        => Policy<HttpResponseMessage>
            .Handle<HttpRequestException>(
                ex =>
                    // Only retry on server errors and timeouts, not on client errors like 405, 403, etc.
                    ex.StatusCode is > HttpStatusCode.InternalServerError or HttpStatusCode.RequestTimeout)
            .Or<TaskCanceledException>()
            .Or<SocketException>()
            .OrResult(
                res =>

                    // Don't retry on client errors that indicate the server doesn't support the method
                    res.StatusCode is > HttpStatusCode.InternalServerError or HttpStatusCode.RequestTimeout)
            .WaitAndRetryAsync(
                retryCount: 2,
                sleepDurationProvider: currentRetryCount => TimeSpan.FromSeconds(1 * currentRetryCount));
}