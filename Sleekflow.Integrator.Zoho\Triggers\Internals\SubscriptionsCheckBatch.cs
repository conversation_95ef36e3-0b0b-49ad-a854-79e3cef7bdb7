﻿using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Zoho.Authentications;
using Sleekflow.Integrator.Zoho.Connections;
using Sleekflow.Integrator.Zoho.Services;
using Sleekflow.Integrator.Zoho.Utils;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.Integrator.Zoho.Triggers.Internals;

[TriggerGroup("Internals")]
public class SubscriptionsCheckBatch : ITrigger
{
    private readonly IZohoAuthenticationService _zohoAuthenticationService;
    private readonly IZohoObjectService _zohoObjectService;
    private readonly IBus _bus;
    private readonly ILogger<SubscriptionsCheckBatch> _logger;
    private readonly IZohoConnectionService _zohoConnectionService;

    public SubscriptionsCheckBatch(
        IZohoAuthenticationService zohoAuthenticationService,
        IZohoObjectService zohoObjectService,
        IBus bus,
        ILogger<SubscriptionsCheckBatch> logger,
        IZohoConnectionService zohoConnectionService)
    {
        _zohoAuthenticationService = zohoAuthenticationService;
        _zohoObjectService = zohoObjectService;
        _bus = bus;
        _logger = logger;
        _zohoConnectionService = zohoConnectionService;
    }

    public class SubscriptionsCheckBatchInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("subscription")]
        [Required]
        public ZohoSubscription Subscription { get; set; }

        [JsonProperty("last_object_modification_time")]
        [Required]
        public DateTimeOffset LastObjectModificationTime { get; }

        [JsonProperty("next_page")]
        public int? NextPage { get; }

        [JsonConstructor]
        public SubscriptionsCheckBatchInput(
            string sleekflowCompanyId,
            ZohoSubscription subscription,
            DateTimeOffset lastObjectModificationTime,
            int? nextPage = null)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Subscription = subscription;
            LastObjectModificationTime = lastObjectModificationTime.ToUniversalTime();
            NextPage = nextPage;
        }
    }

    public class SubscriptionsCheckBatchOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("next_last_object_modification_time")]
        public DateTimeOffset NextLastObjectModificationTime { get; }

        [JsonProperty("next_page")]
        public int? NextPage { get; }

        [JsonConstructor]
        public SubscriptionsCheckBatchOutput(
            long count,
            DateTimeOffset nextLastObjectModificationTime,
            int? nextPage)
        {
            Count = count;
            NextLastObjectModificationTime = nextLastObjectModificationTime.ToUniversalTime();
            NextPage = nextPage;
        }
    }

    public async Task<SubscriptionsCheckBatchOutput> F(
        SubscriptionsCheckBatchInput subscriptionsCheckBatchInput)
    {
        var subscription = subscriptionsCheckBatchInput.Subscription;
        var entityTypeName = subscription.EntityTypeName;
        var lastObjectModificationTime = subscriptionsCheckBatchInput.LastObjectModificationTime;

        var connection = await _zohoConnectionService.GetByIdAsync(
            subscription.ConnectionId,
            subscription.SleekflowCompanyId);

        var authentication =
            await _zohoAuthenticationService.GetAsync(connection.AuthenticationId, subscriptionsCheckBatchInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        _logger.LogInformation(
            "Started sleekflowCompanyId {SleekflowCompanyId}, subscription.Id {SubscriptionId}, entityTypeName {EntityTypeName}, lastObjectModificationTime {LastObjectModificationTime}",
            subscription.SleekflowCompanyId,
            subscription.Id,
            entityTypeName,
            lastObjectModificationTime);

        var (objects, nextPage) = await _zohoObjectService.GetRecentlyUpdatedObjectsAsync(
            authentication,
            entityTypeName,
            lastObjectModificationTime,
            subscriptionsCheckBatchInput.NextPage ?? 1);

        _logger.LogInformation(
            "Ended sleekflowCompanyId {SleekflowCompanyId}, subscription.Id {SubscriptionId}, entityTypeName {EntityTypeName}, lastObjectModificationTime {LastObjectModificationTime}, count {Count}",
            subscription.SleekflowCompanyId,
            subscription.Id,
            entityTypeName,
            lastObjectModificationTime,
            objects.Count);

        var nextLastObjectModificationTime =
            (subscription.LastObjectModificationTime ?? subscription.LastExecutionStartTime).ToUniversalTime();

        var zohoObjectCreatedEventRequests = new List<ZohoObjectCreatedEventRequest>();
        var zohoObjectUpdatedEventRequests = new List<ZohoObjectUpdatedEventRequest>();

        foreach (var dict in objects)
        {
            var modifiedTime = ZohoDateTimeUtils.GetZohoDateTimeValue(dict["Modified_Time"]);
            if (modifiedTime == null)
            {
                _logger.LogError(
                    "The modifiedTime is invalid. modifiedTime {ModifiedTime}",
                    dict["Modified_Time"]);

                continue;
            }

            // Both times are now in UTC, so comparison is safe
            if (modifiedTime.Value > nextLastObjectModificationTime)
            {
                nextLastObjectModificationTime = modifiedTime.Value;
            }

            var createdTime = ZohoDateTimeUtils.GetZohoDateTimeValue(dict["Created_Time"]);
            if (createdTime == null)
            {
                _logger.LogError(
                    "The createdTime is invalid. createdTime {CreatedTime}",
                    dict["Created_Time"]);

                continue;
            }

            if ((modifiedTime.Value - createdTime.Value).Duration().TotalSeconds <= 1)
            {
                zohoObjectCreatedEventRequests.Add(
                    new ZohoObjectCreatedEventRequest(
                        DateTimeOffset.UtcNow,
                        subscriptionsCheckBatchInput.SleekflowCompanyId,
                        subscriptionsCheckBatchInput.Subscription.ConnectionId,
                        (string) dict["id"]!,
                        entityTypeName,
                        dict));
            }
            else
            {
                zohoObjectUpdatedEventRequests.Add(
                    new ZohoObjectUpdatedEventRequest(
                        DateTimeOffset.UtcNow,
                        subscriptionsCheckBatchInput.SleekflowCompanyId,
                        subscriptionsCheckBatchInput.Subscription.ConnectionId,
                        (string) dict["id"]!,
                        entityTypeName,
                        dict));
            }
        }

        if (zohoObjectCreatedEventRequests.Count > 0)
        {
            await _bus.PublishBatch(
                zohoObjectCreatedEventRequests,
                context =>
                {
                    context.ConversationId = Guid.Parse(subscriptionsCheckBatchInput.SleekflowCompanyId);
                });
        }

        if (zohoObjectUpdatedEventRequests.Count > 0)
        {
            await _bus.PublishBatch(
                zohoObjectUpdatedEventRequests,
                context =>
                {
                    context.ConversationId = Guid.Parse(subscriptionsCheckBatchInput.SleekflowCompanyId);
                });
        }

        _logger.LogInformation(
            "Flushed sleekflowCompanyId {SleekflowCompanyId}, subscription.Id {SubscriptionId}, entityTypeName {EntityTypeName}, lastObjectModificationTime {LastObjectModificationTime}, count {Count}, nextLastObjectModificationTime {NextLastObjectModificationTime}",
            subscription.SleekflowCompanyId,
            subscription.Id,
            entityTypeName,
            lastObjectModificationTime,
            objects.Count,
            nextLastObjectModificationTime);

        return new SubscriptionsCheckBatchOutput(objects.Count, nextLastObjectModificationTime, nextPage);
    }
}