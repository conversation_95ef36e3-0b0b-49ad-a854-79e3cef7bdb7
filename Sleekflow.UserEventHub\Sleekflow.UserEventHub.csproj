<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <PropertyGroup Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <DefineConstants>SWAGGERGEN</DefineConstants>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="CsvHelper" Version="33.0.1" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.13" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />
        <PackageReference Include="Microsoft.Azure.NotificationHubs" Version="4.2.0" />
        <PackageReference Include="Microsoft.Azure.SignalR" Version="1.30.2" />
        <PackageReference Include="Microsoft.Azure.SignalR.Management" Version="1.30.2" />
        <PackageReference Include="Microsoft.Azure.SignalR.Serverless.Protocols" Version="1.11.0" />
        <PackageReference Include="Npgsql" Version="8.0.3" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.0.1" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Sleekflow.Models\Sleekflow.Models.csproj" />
        <ProjectReference Include="..\Sleekflow.Mvc\Sleekflow.Mvc.csproj" />
        <ProjectReference Include="..\Sleekflow.UserEventHub.Models\Sleekflow.UserEventHub.Models.csproj" />
    </ItemGroup>

    <ItemGroup>
        <InternalsVisibleTo Include="Sleekflow.UserEventHub.Tests" />
    </ItemGroup>

    <Target Name="SwaggerGen" AfterTargets="Build" Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <MakeDir Directories=".swagger" />
        <Exec Command="dotnet tool restore" />
        <Exec Command="dotnet swagger tofile --output ./.swagger/v1.yaml --yaml $(OutputPath)$(AssemblyName).dll v1" WorkingDirectory="$(ProjectDir)" />
    </Target>

</Project>