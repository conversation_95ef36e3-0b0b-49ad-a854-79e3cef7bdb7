using System.IdentityModel.Tokens.Jwt;
using System.Text;
using Auth0.ManagementApi;
using Auth0.ManagementApi.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.TenantHub;
using Sleekflow.Exceptions.TenantHub.Auth0;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.TenantHub.Client;
using Sleekflow.TenantHub.Companies;
using Sleekflow.TenantHub.Models.Auth0s;
using Sleekflow.TenantHub.Models.Constants;
using Sleekflow.TenantHub.Models.TravisBackend;
using Sleekflow.TenantHub.Models.Users;
using Sleekflow.TenantHub.Rbac;
using Sleekflow.TenantHub.Roles;
using Sleekflow.TenantHub.Users;
using Auth0User = Auth0.ManagementApi.Models.User;
using TenantHubUser = Sleekflow.TenantHub.Models.Users.User;

namespace Sleekflow.TenantHub.Auth0;

public interface IAuth0UserManagementService
{
    Task<(TenantHubUser ConvertedUser, Auth0AppMetadata Auth0AppMetadata)?> FindAuth0UserAsync(Auth0User auth0User);

    Task<Job?> SendVerifyEmailAsync(Auth0User auth0User);

    Task<TenantHubUser?> FindUserAsync(string userId);

    Task<TenantHubUser?> FindUserByUserNameAsync(string userName);

    Task<TenantHubUser?> FindUserByEmailAsync(string email);

    Task<(TenantHubUser? TenantHubUser, Auth0User? Auth0User)> CreateNewDbUserAsync(Auth0User auth0User);

    Task<(Auth0User? UpdatedAuth0User, TenantHubUser? TenantHubUser)> AssociateDbUserWithAuth0UserAsync(
        Auth0User auth0User,
        TenantHubUser possibleApplicationDbUser);

    Task<(Auth0User? Auth0User, TenantHubUser? TenantHubUser)> CreateNewDbUserAndAssociateWithAuth0User(
        Auth0User auth0User);

    Task<List<string>> GetRolesAsync(string userId);

    Task<List<Auth0User>> GetAllAuth0UsersByEmailAsync(string email);

    Task<bool> ValidateAuth0ActionToken(string jwt);

    Task DeleteUserAsync(string email);

    Task<bool> MigrateMetadataAsync(string email, string tenantHubUserId);
}

public class Auth0UserManagementService : IAuth0UserManagementService, IScopedService
{
    private const string SecretKeyEndStr = "+wsadz4gI_3DUXI8P";

    private readonly IUserService _userService;
    private readonly IRoleService _roleService;
    private readonly ICacheService _cacheService;
    private readonly IConfiguration _configuration;
    private readonly IManagementApiClient _managementApiClient;
    private readonly ITravisBackendClient _travisBackendClient;
    private readonly ILogger<Auth0UserManagementService> _logger;
    private readonly ISleekflowAuthorizationContext _authorizationContext;

    public Auth0UserManagementService(
        IUserService userService,
        IRoleService roleService,
        ICacheService cacheService,
        IConfiguration configuration,
        IManagementApiClient managementApiClient,
        ITravisBackendClient travisBackendClient,
        ILogger<Auth0UserManagementService> logger,
        ISleekflowAuthorizationContext authorizationContext)
    {
        _logger = logger;
        _authorizationContext = authorizationContext;
        _userService = userService;
        _roleService = roleService;
        _cacheService = cacheService;
        _configuration = configuration;
        _managementApiClient = managementApiClient;
        _travisBackendClient = travisBackendClient;
    }

    public async Task<TenantHubUser?> FindUserAsync(string userId)
    {
        var user = await _userService.GetUserAsync(userId);

        if (user == null)
        {
            /* If the user is not found in the database, try to get the user from the backend */
            var applicationUser =
                await _travisBackendClient.GetApplicationUserAsync(sleekflowUserId: userId);

            if (applicationUser == null)
            {
                return null;
            }

            return ToTenantHubUser(applicationUser);
            // This will cause 500 error when calling GetUserWorkspaces, or may need work with after user migration
            /*
            var createdUser = await _userService.CreateAndGetAsync(
                applicationUser.Id,
                applicationUser.UserName,
                applicationUser.FirstName,
                applicationUser.LastName,
                applicationUser.DisplayName,
                applicationUser.Email,
                applicationUser.PhoneNumber,
                new List<UserWorkspace>(),
                null,
                new Dictionary<string, object?>(),
                null,
                null,
                applicationUser.CreatedAt,
                applicationUser.UpdatedAt);
                */

            // return createdUser;
        }

        return user;
    }

    public async Task<TenantHubUser?> FindUserByUserNameAsync(string userName)
    {
        var user = await _userService.GetOrDefaultUserByUsernameAsync(userName);

        if (user == null)
        {
            /* If the user is not found in the database, try to get the user from the backend */
            var applicationUser =
                await _travisBackendClient.GetApplicationUserAsync(userName: userName);

            if (applicationUser == null)
            {
                return null;
            }

            return ToTenantHubUser(applicationUser);
        }

        return user;
    }

    public async Task<TenantHubUser?> FindUserByEmailAsync(string email)
    {
        var user = await _userService.GetOrDefaultUserByEmailAsync(email);

        if (user == null)
        {
            /* If the user is not found in the database, try to get the user from the backend */
            var applicationUser =
                await _travisBackendClient.GetApplicationUserAsync(email: email);

            if (applicationUser == null)
            {
                return null;
            }

            return ToTenantHubUser(applicationUser);
        }

        return user;
    }

    public async Task<(TenantHubUser ConvertedUser, Auth0AppMetadata Auth0AppMetadata)?> FindAuth0UserAsync(Auth0User auth0User)
    {
        var auth0UserAppMetadata = GetAuth0AppMetadata(auth0User);
        if (auth0UserAppMetadata == null)
        {
            return null;
        }

        var searchUserId =
            !string.IsNullOrEmpty(auth0UserAppMetadata.TenantHubUserId)
                ? auth0UserAppMetadata.TenantHubUserId
                : auth0UserAppMetadata.SleekflowId ?? string.Empty;
        if (string.IsNullOrEmpty(searchUserId))
        {
            return null;
        }

        var user = await FindUserAsync(searchUserId);
        if (user == null)
        {
            throw new SfAuth0UserManagementException(
                $"[FindAuth0User] Unable to find the user in the database with Id {searchUserId}.");
        }

        return (user, auth0UserAppMetadata);
    }

    public static Auth0AppMetadata? GetAuth0AppMetadata(Auth0User auth0User)
    {
        return (auth0User.AppMetadata is null || auth0User.AppMetadata.ToString() == "{}")
            ? null
            : JsonConvert.DeserializeObject<Auth0AppMetadata?>(JsonConvert.SerializeObject(auth0User.AppMetadata));
    }

    public async Task<Job?> SendVerifyEmailAsync(Auth0User auth0User)
    {
        var identity = auth0User.Identities.FirstOrDefault(u => u.Provider == "auth0")
                       ?? auth0User.Identities[0];

        var job = await _cacheService.CacheAsync(
            $"VerifyEmailSent:{identity.UserId}",
            async () =>
            {
                var verifyEmailJobRequest = new VerifyEmailJobRequest()
                {
                    UserId = auth0User.UserId,
                    Identity = new EmailVerificationIdentity()
                    {
                        UserId = identity.UserId, Provider = identity.Provider
                    }
                };
                return await _managementApiClient.Jobs.SendVerificationEmailAsync(verifyEmailJobRequest);
            },
            TimeSpan.FromMinutes(3));

        return job;
    }

    public async Task<(Auth0User? UpdatedAuth0User, TenantHubUser? TenantHubUser)> AssociateDbUserWithAuth0UserAsync(
        Auth0User auth0User,
        TenantHubUser possibleApplicationDbUser)
    {
        var user = await _userService.GetUserAsync(possibleApplicationDbUser.Id, null);
        var applicationUser = new TravisBackendApplicationUser(
            possibleApplicationDbUser.Id,
            possibleApplicationDbUser.Email,
            possibleApplicationDbUser.Username,
            possibleApplicationDbUser.FirstName,
            possibleApplicationDbUser.LastName,
            possibleApplicationDbUser.DisplayName,
            possibleApplicationDbUser.IsAgreeMarketingConsent);

        bool? isRbacEnabled = null;
        if (user.UserWorkspace is not null)
        {
            isRbacEnabled = _authorizationContext.IsRbacEnabled;
        }

        var roles = await GetRolesAsync(possibleApplicationDbUser.Id);
        var appMetadata = new Auth0AppMetadata(
            roles: roles,
            phoneNumber: possibleApplicationDbUser.PhoneNumber,
            sleekflowId: user.UserWorkspace?.SleekflowUserId,
            tenantHubUserId: user.TenantHubUserId,
            loginRequiresEmailVerification: true,
            loginAsUser: null,
            isRbacEnabled: isRbacEnabled);
        var updatedAuth0User = await _managementApiClient.Users.UpdateAsync(
            auth0User.UserId,
            new UserUpdateRequest()
            {
                AppMetadata = appMetadata
            });

        var result =
            await _travisBackendClient.AssociateDbUserWithAuth0UserAsync(applicationUser, auth0User, roles!);

        // auth0 user data will not be sync if we get the user by email or other fields. So we not return the auth0 user by travis backend.
        return (updatedAuth0User, ToTenantHubUser(result?.ApplicationUser!));
    }

    public async Task<(TenantHubUser? TenantHubUser, Auth0User? Auth0User)> CreateNewDbUserAsync(Auth0User auth0User)
    {
        var createdTenantHubUser = await _userService.CreateAndGetAsync(
            auth0User.UserName,
            auth0User.FirstName,
            auth0User.LastName,
            auth0User.FullName,
            auth0User.Email,
            auth0User.PhoneNumber,
            new List<UserWorkspace>(),
            null,
            new Dictionary<string, object?>(),
            null);

        var roles = await GetRolesAsync(createdTenantHubUser.Id);

        // We create the sleekflow user id here. The user needs to select a location first before they can create a sleekflow user.
        var sleekflowUserId = Guid.NewGuid().ToString();

        // Update the user app_metadata to Auth0.
        var auth0AppMetadata = new Auth0AppMetadata(
            roles: roles,
            phoneNumber: createdTenantHubUser.PhoneNumber,
            sleekflowId: sleekflowUserId,
            tenantHubUserId: createdTenantHubUser.Id,
            loginRequiresEmailVerification: true,
            loginAsUser: null,
            isRbacEnabled: null);

        auth0User.AppMetadata = auth0AppMetadata;

        return (createdTenantHubUser, auth0User);
    }

    public async Task<(Auth0User? Auth0User, TenantHubUser? TenantHubUser)> CreateNewDbUserAndAssociateWithAuth0User(
        Auth0User auth0User)
    {
        try
        {
            var duplicateUser = (auth0User.Email is null)
                ? await FindUserByUserNameAsync(auth0User.UserName)
                : await FindUserByEmailAsync(auth0User.Email);

            if (duplicateUser is not null)
            {
                throw new SfAuth0UserManagementException(
                    "Unable to create the user due to the email or username is duplicate.");
            }

            var (createdTenantHubUser, updatedAuth0User) = await CreateNewDbUserAsync(auth0User);

            if (updatedAuth0User != null)
            {
                return (updatedAuth0User, createdTenantHubUser);
            }

            throw new SfAuth0UserManagementException("Unable to associate with Auth0 user.");
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to create new user and associate with Auth0 user");
            throw new SfAuth0UserManagementException(e, "Unable to associate with Auth0 user.");
        }
    }

    public async Task<List<string>> GetRolesAsync(string userId)
    {
        var user = await _userService.GetUserAsync(userId);
        if (user == null)
        {
            return new List<string>();
        }

        var first = user.UserWorkspaces.Find(u => u.SleekflowUserId == userId);

        var roleIds =
            first?.SleekflowRoleIds ?? new List<string>();

        var allRoles = await _roleService.GetAllDefaultRolesAsync();
        var roles = allRoles?.Where(i => roleIds.Contains(i.Id)).Select(i => i.Name).ToList();

        return roles ?? new List<string>();
    }

    private async Task<List<Auth0User>> GetAuth0Users(string tenantHubUserId)
    {
        var getUsersRequest = new GetUsersRequest()
        {
            Query = $"app_metadata.tenanthub_user_id:\"{tenantHubUserId}\""
        };
        var auth0Users = await _managementApiClient.Users.GetAllAsync(getUsersRequest);
        if (auth0Users.Count == 0)
        {
            throw new SfAuth0UserManagementException("No user is found");
        }

        return auth0Users.ToList();
    }

    public async Task<List<Auth0User>> GetAllAuth0UsersByEmailAsync(string email)
    {
        var getUsersRequest = new GetUsersRequest()
        {
            Query = $"email:{email}"
        };
        var users = await _managementApiClient.Users.GetAllAsync(getUsersRequest);

        return users.ToList();
    }

    /**
     * <summary>
     * Delete the user from Auth0 by email.
     * It will delete all the users, including social and password account with the same email.
     * The user will be deleted from the Auth0 if the user is present.
     * </summary>
     * <param name="email">The email of the user to delete.</param>
     * @throws SfAuth0UserManagementException If the user is not found or failed to delete the user.
     * <returns>Task.</returns>
     */
    public async Task DeleteUserAsync(string email)
    {
        try
        {
            var users = await GetAllAuth0UsersByEmailAsync(email);

            _logger.LogInformation(
                "[{DeleteUserAsyncName}] Found {Count} Auth0 users ({UserEmail}) by Email to delete",
                nameof(DeleteUserAsync),
                users.Count,
                email);

            foreach (var user in users)
            {
                await _managementApiClient.Users.DeleteAsync(user.UserId);

                // Add the delay to avoid the rate limit.
                await Task.Delay(200);
            }
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "[{DeleteAllUserDataAsyncName}] Failed to delete the user from Auth0: {EMessage}",
                nameof(DeleteUserAsync),
                e.Message);
            throw new SfAuth0UserManagementException(e, e.Message);
        }
    }

    public async Task<bool> MigrateMetadataAsync(string email, string tenantHubUserId)
    {
        var auth0Users = await GetAllAuth0UsersByEmailAsync(email);
        foreach (var auth0User in auth0Users)
        {
            var appMetadata = GetAuth0AppMetadata(auth0User);
            if (appMetadata == null)
            {
                continue;
            }

            appMetadata.TenantHubUserId = tenantHubUserId;
            await _managementApiClient.Users.UpdateAsync(
                auth0User.UserId,
                new UserUpdateRequest()
                {
                    AppMetadata = appMetadata
                });

            await Task.Delay(200); // Add the delay to avoid the rate limit.
        }

        return true;
    }

    public async Task<bool> ValidateAuth0ActionToken(string jwt)
    {
        if (string.IsNullOrEmpty(jwt))
        {
            return false;
        }

        var jwtHandler = new JwtSecurityTokenHandler();

        // The secretKey
        // 1. No conflict with different environment. (e.g. uat only work in uat only and not work with production)
        // 2. Audience + random string can improve security.
        var secretKey =
            _configuration[Auth0ConfigurationNames.ActionIssuer] +
            _configuration[Auth0ConfigurationNames.ActionAudience] + SecretKeyEndStr;

        var tokenValidate = new TokenValidationParameters
        {
            ValidateLifetime = false,
            ValidateAudience = true,
            ValidateIssuer = true,
            ValidIssuer = _configuration[Auth0ConfigurationNames.ActionIssuer],
            ValidAudience = _configuration[Auth0ConfigurationNames.ActionAudience],
            RequireExpirationTime = true,
            RequireSignedTokens = true,
            IssuerSigningKey =
                new SymmetricSecurityKey(
                    Encoding.ASCII.GetBytes(
                        secretKey))
        };
        var principal = await jwtHandler.ValidateTokenAsync(jwt, tokenValidate);

        return principal.IsValid;
    }

    private static TenantHubUser ToTenantHubUser(TravisBackendApplicationUser applicationUser)
    {
        return new TenantHubUser(
            applicationUser.Id,
            applicationUser.UserName,
            applicationUser.FirstName,
            applicationUser.LastName,
            applicationUser.DisplayName,
            applicationUser.Email,
            applicationUser.PhoneNumber,
            new List<UserWorkspace>(),
            null,
            new List<string>(),
            new Dictionary<string, object?>(),
            applicationUser.CreatedAt,
            applicationUser.UpdatedAt);
    }
}