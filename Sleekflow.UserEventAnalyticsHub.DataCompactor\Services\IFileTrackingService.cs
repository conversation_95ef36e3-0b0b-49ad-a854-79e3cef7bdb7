using Sleekflow.UserEventAnalyticsHub.DataCompactor.Models;

namespace Sleekflow.UserEventAnalyticsHub.DataCompactor.Services;

public interface IFileTrackingService
{
    Task<bool> IsFileMigratedAsync(string sleekflowCompanyId, string blobPath);
    Task<List<BlobFileInfo>> GetUnmigratedFilesAsync(string sleekflowCompanyId, List<BlobFileInfo> allFiles);
    Task MarkFileAsStartedAsync(string sleekflowCompanyId, BlobFileInfo file);
    Task MarkFileAsCompletedAsync(string sleekflowCompanyId, BlobFileInfo file, int recordsCount);
    Task MarkFileAsFailedAsync(string sleekflowCompanyId, BlobFileInfo file, string errorMessage);
}