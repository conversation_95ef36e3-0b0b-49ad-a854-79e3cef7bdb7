using System.Collections.Concurrent;
using System.Text;
using System.Text.RegularExpressions;
using Azure;
using Azure.AI.DocumentIntelligence;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.AzureOpenAI;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Configs;
using Sleekflow.IntelligentHub.Models.Workers.FileIngestion;
using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.Ingestion;

public interface IExcelKnowledgeSource : IKnowledgeSource
{
}

public class ExcelKnowledgeSource : IExcelKnowledgeSource, IScopedService
{
    public class ExcelImage
    {
        public string MimeType { get; set; } = string.Empty;

        public byte[] ImageBytes { get; set; } = [];
    }

    public class ExcelTextBox
    {
        public string Text { get; set; } = string.Empty;
    }

    private readonly ILogger<ExcelKnowledgeSource> _logger;
    private readonly Kernel _kernel;
    private readonly IAzureFormRecognizerConfig _azureFormRecognizerConfig;

    public ExcelKnowledgeSource(
        ILogger<ExcelKnowledgeSource> logger,
        Kernel kernel,
        IAzureFormRecognizerConfig azureFormRecognizerConfig)
    {
        _logger = logger;
        _kernel = kernel;
        _azureFormRecognizerConfig = azureFormRecognizerConfig;
    }

    public async Task<IKnowledgeSource.IngestionResult> Ingest(
        Stream blobStream,
        object? fileIngestionProgress)
    {
        try
        {
            using var spreadsheetDocument = SpreadsheetDocument.Open(blobStream, false);
            var workbookPart = spreadsheetDocument.WorkbookPart;
            if (workbookPart == null)
            {
                throw new ArgumentException("The workbook part could not be found in the Excel file.");
            }

            var workbook = workbookPart.Workbook;
            var sheets = workbook.Descendants<Sheet>().ToList();
            var sharedStringTable = workbookPart.SharedStringTablePart?.SharedStringTable;

            // Identify visible worksheets and their parts
            var visibleWorksheetData = sheets
                .Select(
                    sheet =>
                    {
                        var isHidden = sheet.State != null &&
                                       (sheet.State.Value == SheetStateValues.Hidden ||
                                        sheet.State.Value == SheetStateValues.VeryHidden);
                        return new
                        {
                            Sheet = sheet, IsHidden = isHidden
                        };
                    })
                .Where(s => !s.IsHidden)
                .Select(
                    s =>
                    {
                        var worksheetPart = (WorksheetPart) workbookPart.GetPartById(s.Sheet.Id!);
                        return new
                        {
                            Sheet = s.Sheet, WorksheetPart = worksheetPart, Worksheet = worksheetPart.Worksheet
                        };
                    })
                .ToList();

            var totalVisibleWorksheets = visibleWorksheetData.Count;

            // Initialize or use existing progress state
            ExcelFileIngestionProgress excelFileIngestionProgress;

            switch (fileIngestionProgress)
            {
                case null:
                    // Initialize a new progress object if none is provided
                    excelFileIngestionProgress = new ExcelFileIngestionProgress(0, totalVisibleWorksheets);
                    break;
                case ExcelFileIngestionProgress progress:
                    // Direct instance of ExcelFileIngestionProgress
                    excelFileIngestionProgress = progress;
                    break;
                default:
                    try
                    {
                        // Try to deserialize as JSON
                        var jsonString = JsonConvert.SerializeObject(fileIngestionProgress);
                        excelFileIngestionProgress =
                            JsonConvert.DeserializeObject<ExcelFileIngestionProgress>(jsonString)
                            ?? throw new Exception(
                                "Failed to deserialize fileIngestionProgress to ExcelFileIngestionProgress");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to interpret fileIngestionProgress as ExcelFileIngestionProgress");
                        throw new Exception(
                            "FileIngestionProgress could not be interpreted as ExcelFileIngestionProgress",
                            ex);
                    }

                    break;
            }

            // Get the current worksheet to process
            var currentWorksheetIndex = excelFileIngestionProgress.ProcessedWorksheets;
            var currentWorksheetInfo = visibleWorksheetData[currentWorksheetIndex];
            var worksheetPart = currentWorksheetInfo.WorksheetPart;
            var worksheet = currentWorksheetInfo.Worksheet;
            var sheetName = currentWorksheetInfo.Sheet.Name?.Value ?? $"Worksheet {currentWorksheetIndex + 1}";

            _logger.LogInformation(
                "Processing worksheet {Index}/{Total}: {Name}",
                currentWorksheetIndex + 1,
                totalVisibleWorksheets,
                sheetName);

            var markdowns = new List<string>();

            // 1. Process cells and create CSV chunks
            var mainBodyMarkdown = await ProcessWorksheetBody(worksheet, sharedStringTable, sheetName);
            markdowns.AddRange(mainBodyMarkdown);

            // 2. Process text boxes
            var textBoxesMarkdown = ProcessWorksheetTextBoxes(worksheetPart, sheetName);
            if (!string.IsNullOrEmpty(textBoxesMarkdown))
            {
                markdowns.Add(textBoxesMarkdown);
            }

            // 3. Process images
            var imageMarkdown = await ProcessWorksheetImages(worksheetPart, sheetName);
            if (!string.IsNullOrEmpty(imageMarkdown))
            {
                markdowns.Add(imageMarkdown);
            }

            // Update progress state for the next call
            excelFileIngestionProgress.ProcessedWorksheets++;

            _logger.LogInformation(
                "Worksheet '{Name}' processing finished. Progress: {Current}/{Total}.",
                sheetName,
                excelFileIngestionProgress.ProcessedWorksheets,
                totalVisibleWorksheets);

            // Return the markdown for this worksheet and the updated state
            return new IKnowledgeSource.IngestionResult(markdowns.ToArray(), excelFileIngestionProgress);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Excel file during ingestion: {Message}", ex.Message);
            // Optionally, update state to reflect error or stop processing?
            // For now, rethrow to indicate failure of this step.
            throw;
        }
    }

    private async Task<string[]> ProcessWorksheetBody(
        Worksheet worksheet,
        SharedStringTable? sharedStringTable,
        string sheetName)
    {
        var csv = ProcessCells(worksheet, sharedStringTable);
        var csvRows = ParseCsvRows(csv);

        if (csvRows.Length > 0)
        {
            _logger.LogInformation(
                "Worksheet '{Name}': Found {Count} data rows. Processing in chunks.",
                sheetName,
                csvRows.Length);

            // break into smaller chunks to keep input context size small
            const int chunkSize = 10;
            const int maxParallelism = 20; // Max threads for parallel processing

            var headerRow = csvRows[0];
            var chunks = csvRows.Skip(1).Chunk(chunkSize);

            // Process chunks in parallel using Parallel.ForEachAsync
            var parallelOptions = new ParallelOptions
            {
                MaxDegreeOfParallelism = maxParallelism
            };

            var chunkResults = new ConcurrentDictionary<int, string>();
            await Parallel.ForEachAsync(
                chunks.Select((chunk, index) => (chunk, index)),
                parallelOptions,
                async (chunk, cancellationToken) =>
                {
                    var (csvChunkBody, index) = chunk;
                    try
                    {
                        _logger.LogInformation(
                            "Processing chunk {ChunkIndex} for worksheet '{SheetName}'",
                            index,
                            sheetName);

                        var fullCsvChunk = headerRow + '\n' + string.Join('\n', csvChunkBody);
                        var csvMarkdown = await ConvertCsvToMarkdown(fullCsvChunk);
                        chunkResults.TryAdd(index, csvMarkdown);

                        _logger.LogInformation(
                            "Finished processing chunk {ChunkIndex} for worksheet '{SheetName}'",
                            index,
                            sheetName);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Error processing CSV chunk {ChunkIndex} for worksheet '{SheetName}'",
                            index,
                            sheetName);

                        throw;
                    }
                });

            _logger.LogInformation(
                "Finished processing all cell data chunks for worksheet '{SheetName}'",
                sheetName);

            return chunkResults.OrderBy(kv => kv.Key).Select(kv => kv.Value).ToArray();
        }
        else
        {
            _logger.LogInformation("Worksheet '{Name}': No cell data found.", sheetName);
            return [];
        }
    }

    private string ProcessWorksheetTextBoxes(WorksheetPart worksheetPart, string sheetName)
    {
        var markdownBuilder = new StringBuilder();
        var textBoxes = ProcessTextBoxes(worksheetPart);
        if (textBoxes.Count > 0)
        {
            _logger.LogInformation("Worksheet '{Name}': Found {Count} text boxes.", sheetName, textBoxes.Count);
            for (int i = 0; i < textBoxes.Count; i++)
            {
                markdownBuilder.AppendLine($"### Text Box {i + 1}");
                markdownBuilder.AppendLine(textBoxes[i].Text);
                markdownBuilder.AppendLine();
            }

            return markdownBuilder.ToString();
        }
        else
        {
            _logger.LogInformation("Worksheet '{Name}': No text boxes found.", sheetName);
            return string.Empty;
        }
    }

    private async Task<string> ProcessWorksheetImages(WorksheetPart worksheetPart, string sheetName)
    {
        var images = ProcessImages(worksheetPart);
        if (images.Count > 0)
        {
            _logger.LogInformation(
                "Worksheet '{Name}': Found {Count} images. Processing in parallel.",
                sheetName,
                images.Count);
            var imageMarkdownResults = new ConcurrentBag<string>();
            var parallelOptions = new ParallelOptions
            {
                MaxDegreeOfParallelism = 8
            }; // Consider making configurable

            await Parallel.ForEachAsync(
                images.Select(
                    (img, idx) => new
                    {
                        Image = img, Index = idx
                    }),
                parallelOptions,
                async (item, cancellationToken) =>
                {
                    var imageMarkdown = await ConvertImageToMarkdown(item.Image);
                    if (!string.IsNullOrWhiteSpace(imageMarkdown))
                    {
                        // Add context to the image markdown
                        imageMarkdownResults.Add($"### Image {item.Index + 1}\n{imageMarkdown}");
                    }
                    else
                    {
                        imageMarkdownResults.Add(
                            $"### Image {item.Index + 1}\n*[No content extracted from image]*");
                    }
                });

            // Append results from the ConcurrentBag to the main StringBuilder, preserving order if needed (though parallel might not guarantee it)
            var markdownBuilder = new StringBuilder();
            foreach (var result in imageMarkdownResults.OrderBy(s => s)) // Simple ordering for consistency
            {
                markdownBuilder.AppendLine(result);
                markdownBuilder.AppendLine();
            }

            return markdownBuilder.ToString();
        }
        else
        {
            _logger.LogInformation("Worksheet '{Name}': No images found.", sheetName);
            return string.Empty;
        }
    }

    private string ProcessCells(
        Worksheet worksheet,
        SharedStringTable? sharedStringTable)
    {
        var csvBuilder = new StringBuilder();
        var sheetData = worksheet.GetFirstChild<SheetData>();

        if (sheetData == null || !sheetData.HasChildren)
        {
            Console.WriteLine("No data found in worksheet.");
            return string.Empty;
        }

        // Get all rows
        var rows = sheetData.Elements<Row>().ToList();
        if (rows.Count == 0)
        {
            Console.WriteLine("No rows found in worksheet.");
            return string.Empty;
        }

        // Find the maximum column index used
        int maxColumnIndex = 0;
        foreach (var row in rows)
        {
            foreach (var cell in row.Elements<Cell>())
            {
                var cellReference = cell.CellReference?.Value ?? string.Empty;
                if (!string.IsNullOrEmpty(cellReference))
                {
                    // Extract column letter (e.g., "A1" -> "A")
                    var columnLetter = Regex.Match(cellReference, @"^[A-Za-z]+").Value;
                    var columnIndex = ConvertColumnLetterToIndex(columnLetter);
                    maxColumnIndex = Math.Max(maxColumnIndex, columnIndex);
                }
            }
        }

        // Get merged cells information
        var mergedCells = worksheet.Elements<MergeCells>().FirstOrDefault();
        var mergedCellsDict = new Dictionary<string, (string Value, string TopLeftCellReference)>();

        if (mergedCells != null)
        {
            // Process each merged cell range
            foreach (var mergedCell in mergedCells.Elements<MergeCell>())
            {
                if (mergedCell.Reference?.Value == null) continue;

                // Parse the merged cell range (e.g., "A1:B2")
                var rangeParts = mergedCell.Reference.Value.Split(':');
                if (rangeParts.Length != 2) continue;

                var topLeftCellRef = rangeParts[0];
                var bottomRightCellRef = rangeParts[1];

                // Find the top-left cell to get its value
                Cell? topLeftCell = null;
                foreach (var row in rows)
                {
                    topLeftCell = row.Elements<Cell>().FirstOrDefault(
                        c =>
                            c.CellReference?.Value == topLeftCellRef);
                    if (topLeftCell != null) break;
                }

                if (topLeftCell == null) continue;

                // Get the value of the top-left cell
                var mergedValue = GetCellValue(topLeftCell, sharedStringTable);

                // Parse the range to get start and end row/column
                var startColLetter = Regex.Match(topLeftCellRef, @"^[A-Za-z]+").Value;
                var startRowNum = int.Parse(Regex.Match(topLeftCellRef, @"\d+$").Value);

                var endColLetter = Regex.Match(bottomRightCellRef, @"^[A-Za-z]+").Value;
                var endRowNum = int.Parse(Regex.Match(bottomRightCellRef, @"\d+$").Value);

                var startColIndex = ConvertColumnLetterToIndex(startColLetter);
                var endColIndex = ConvertColumnLetterToIndex(endColLetter);

                // Store the value for each cell in the merged range
                for (int rowNum = startRowNum; rowNum <= endRowNum; rowNum++)
                {
                    for (int colIndex = startColIndex; colIndex <= endColIndex; colIndex++)
                    {
                        var colLetter = ConvertIndexToColumnLetter(colIndex);
                        var cellRef = $"{colLetter}{rowNum}";
                        mergedCellsDict[cellRef] = (mergedValue, topLeftCellRef);
                    }
                }
            }
        }

        // Process each row
        foreach (var row in rows)
        {
            var cellValues = new string[maxColumnIndex + 1];
            Array.Fill(cellValues, ""); // Initialize with empty strings

            // Process each cell in the row
            foreach (var cell in row.Elements<Cell>())
            {
                string cellReference = cell.CellReference?.Value ?? string.Empty;
                if (string.IsNullOrEmpty(cellReference))
                    continue;

                // Extract column letter (e.g., "A1" -> "A")
                var columnLetter = Regex.Match(cellReference, @"^[A-Za-z]+").Value;
                var columnIndex = ConvertColumnLetterToIndex(columnLetter);

                // Check if this cell is part of a merged region
                if (mergedCellsDict.TryGetValue(cellReference, out var mergedInfo))
                {
                    // Use the value from the merged cell
                    cellValues[columnIndex] = mergedInfo.Value;
                }
                else
                {
                    // Get cell value normally
                    var cellValue = GetCellValue(cell, sharedStringTable);
                    cellValues[columnIndex] = cellValue;
                }
            }

            // Append the row to CSV
            csvBuilder.AppendLine(string.Join(",", cellValues.Select(EscapeCsvValue)));
        }

        return csvBuilder.ToString();
    }

    private string ConvertIndexToColumnLetter(int columnIndex)
    {
        // Convert a 0-based column index to an Excel column letter (A, B, C, ..., Z, AA, AB, ...)
        var dividend = columnIndex + 1;
        var columnName = string.Empty;

        while (dividend > 0)
        {
            var modulo = (dividend - 1) % 26;
            columnName = Convert.ToChar('A' + modulo) + columnName;
            dividend = (dividend - modulo) / 26;
        }

        return columnName;
    }

    private List<ExcelTextBox> ProcessTextBoxes(WorksheetPart worksheetPart)
    {
        var textBoxes = new List<ExcelTextBox>();
        var drawingsPart = worksheetPart.DrawingsPart;

        if (drawingsPart == null)
        {
            return textBoxes;
        }

        // Get all text boxes from the drawing part
        var shapes = drawingsPart.WorksheetDrawing.Descendants<DocumentFormat.OpenXml.Drawing.Spreadsheet.Shape>();

        foreach (var shape in shapes)
        {
            var textBody = shape.TextBody;
            if (textBody != null)
            {
                var textBuilder = new StringBuilder();

                // Extract text from paragraphs
                foreach (var paragraph in textBody.Descendants<DocumentFormat.OpenXml.Drawing.Paragraph>())
                {
                    foreach (var textRun in paragraph.Descendants<DocumentFormat.OpenXml.Drawing.Run>())
                    {
                        var text = textRun.Text;
                        if (text != null && !string.IsNullOrWhiteSpace(text.Text))
                        {
                            textBuilder.AppendLine(text.Text);
                        }
                    }

                    textBuilder.AppendLine(); // Add line break after each paragraph
                }

                if (textBuilder.Length > 0)
                {
                    textBoxes.Add(
                        new ExcelTextBox
                        {
                            Text = textBuilder.ToString().TrimEnd()
                        });
                }
            }
        }

        return textBoxes;
    }

    private List<ExcelImage> ProcessImages(WorksheetPart worksheetPart)
    {
        var images = new List<ExcelImage>();
        var drawingsPart = worksheetPart.DrawingsPart;

        if (drawingsPart == null)
        {
            return images;
        }

        // Get all pictures from the drawing part
        var pictures = drawingsPart.WorksheetDrawing.Descendants<DocumentFormat.OpenXml.Drawing.Spreadsheet.Picture>();

        foreach (var picture in pictures)
        {
            // Get the embedded image part
            var blipFill = picture.BlipFill;
            if (blipFill != null)
            {
                var blip = blipFill.Blip;
                if (blip != null && blip.Embed != null)
                {
                    var imagePartId = blip.Embed.Value;
                    var imagePart = (ImagePart) drawingsPart.GetPartById(imagePartId);

                    // Get image data
                    using (var stream = imagePart.GetStream())
                    {
                        var imageBytes = new byte[stream.Length];
                        stream.Read(imageBytes, 0, (int) stream.Length);

                        // Add image to the list
                        images.Add(
                            new ExcelImage
                            {
                                MimeType = imagePart.ContentType, ImageBytes = imageBytes
                            });
                    }
                }
            }
        }

        return images;
    }

    private string GetCellValue(Cell cell, SharedStringTable? sharedStringTable)
    {
        if (cell.CellValue == null)
        {
            return string.Empty;
        }

        var value = cell.CellValue.Text;

        // If the cell contains a shared string
        if (cell.DataType != null && cell.DataType.Value == CellValues.SharedString && sharedStringTable != null)
        {
            // Get the shared string value
            var sharedStringIndex = int.Parse(value);

            if (sharedStringIndex < 0 || sharedStringIndex >= sharedStringTable.ChildElements.Count)
            {
                throw new Exception("Invalid shared string index");
            }

            var sharedStringItem = sharedStringTable.ElementAt(sharedStringIndex);

            // Extract text from the shared string item
            return string.Join(
                "",
                sharedStringItem.Descendants<DocumentFormat.OpenXml.Spreadsheet.Text>().Select(t => t.Text));
        }

        return value;
    }

    private int ConvertColumnLetterToIndex(string columnLetter)
    {
        int index = 0;
        for (int i = 0; i < columnLetter.Length; i++)
        {
            index = index * 26 + (columnLetter[i] - 'A' + 1);
        }

        return index - 1; // Convert to 0-based index
    }

    private string EscapeCsvValue(string value)
    {
        if (string.IsNullOrEmpty(value))
        {
            return "";
        }

        // If the value contains a comma, newline, or double quote, wrap it in quotes
        if (value.Contains(",") || value.Contains("\n") || value.Contains("\""))
        {
            // Replace any double quotes with two double quotes
            value = value.Replace("\"", "\"\"");
            return $"\"{value}\"";
        }

        return value;
    }

    private bool IsEmptyRow(string row)
    {
        if (string.IsNullOrWhiteSpace(row))
        {
            return true;
        }

        // Check if row contains only commas and whitespace
        return row.Replace(",", "").Trim().Length == 0;
    }

    private string[] ParseCsvRows(string csvContent)
    {
        if (string.IsNullOrEmpty(csvContent))
        {
            return [];
        }

        var rows = new List<string>();
        var currentRow = new StringBuilder();
        var inQuotes = false;
        var i = 0;

        while (i < csvContent.Length)
        {
            var currentChar = csvContent[i];

            if (currentChar == '"')
            {
                // Check if this is an escaped quote (two consecutive quotes)
                if (i + 1 < csvContent.Length && csvContent[i + 1] == '"')
                {
                    // This is an escaped quote, add a single quote to the current row
                    currentRow.Append('"');
                    i += 2; // Skip both quotes
                    continue;
                }
                else
                {
                    // This is a quote boundary, toggle the inQuotes state
                    inQuotes = !inQuotes;
                    currentRow.Append(currentChar);
                    i++;
                    continue;
                }
            }

            if (!inQuotes)
            {
                // Check for line endings when not inside quotes
                if (currentChar == '\r' && i + 1 < csvContent.Length && csvContent[i + 1] == '\n')
                {
                    // CRLF line ending
                    var rowContent = currentRow.ToString();
                    if (!IsEmptyRow(rowContent))
                    {
                        rows.Add(rowContent);
                    }

                    currentRow.Clear();

                    i += 2; // Skip both \r and \n
                    continue;
                }
                else if (currentChar == '\n' || currentChar == '\r')
                {
                    // LF line ending
                    var rowContent = currentRow.ToString();
                    if (!IsEmptyRow(rowContent))
                    {
                        rows.Add(rowContent);
                    }

                    currentRow.Clear();

                    i++;
                    continue;
                }
            }

            // Add the current character to the current row
            currentRow.Append(currentChar);
            i++;
        }

        // Add the last row if there's content
        var lastRowContent = currentRow.ToString();
        if (!IsEmptyRow(lastRowContent))
        {
            rows.Add(lastRowContent);
        }

        return rows.ToArray();
    }

    private async Task<string> ConvertCsvToMarkdown(string csvContent)
    {
#pragma warning disable SKEXP0070
#pragma warning disable SKEXP0001
        var chatCompletionService =
            _kernel.GetRequiredService<IChatCompletionService>(SemanticKernelExtensions.S_GPT_4o);

        var chatHistory = new ChatHistory();
        chatHistory.AddSystemMessage(
            """
            You are a specialized CSV content transformer, expertly skilled at converting CSV data into meaningful markdown text.

            Each CSV document is provided as a string with comma-separated values, where each line represents a row.

            When processing CSV data, you will:

            1. Content Extraction and Organization:
            - Analyze the CSV structure to identify headers and data rows
            - Convert the tabular data into natural sentences while preserving all information and context
            - Maintain the logical flow and relationships between different data points
            - Ensure that the meaning of the entire table is preserved in the narrative form

            2. Formatting and Structure:
            - Transform complex data presentations into clear and accurate textual descriptions
            - Maintain logical progression between different parts of the data
            - Use appropriate markdown formatting to enhance readability

            3. Special Considerations:
            - Preserve exact numerical data, measurements, and specific values
            - Identify and explain relationships between columns when relevant
            - Interpret the data contextually to provide a coherent narrative

            4. Output Quality:
            - Preserve exact spelling and formatting of proper nouns, technical terms, and specialized vocabulary
            - Maintain professional language and formal tone
            - Structure information in a way that enhances readability while maintaining completeness

            5. Validation Checks:
            - Verify that the markdown contains all information from the CSV
            - Ensure no data points are omitted or misrepresented
            - Confirm that relationships between different pieces of information are maintained

            Output language:
            - The markdown should use the same language as the CSV content.

            The final output should be a complete, accurate representation of the original CSV data in markdown format that preserves the meaning and context of the tabular data.
            Answer right away, do not say things like: "Here is the converted markdown:"
            """);
        chatHistory.AddUserMessage(
        [
            new Microsoft.SemanticKernel.TextContent(csvContent)
        ]);

        var completeOutput = await chatCompletionService.GetChatMessageContentAsync(
            chatHistory,
            new AzureOpenAIPromptExecutionSettings()
            {
                Temperature = 0.1f
            },
            _kernel);

        // _logger.LogInformation(
        //     $"CSV conversion - Input tokens: {completeChatAsync.Usage?.InputTokenCount}, Output tokens: {completeChatAsync.Usage?.OutputTokenCount}");

        var content = completeOutput.Content;
        if (content == null)
        {
            throw new Exception("CSV to markdown conversion failed.");
        }

        return content;
#pragma warning restore SKEXP0070
#pragma warning restore SKEXP0001
    }

    private async Task<string> ConvertImageToMarkdown(ExcelImage image)
    {
        try
        {
            // Create Azure Document Intelligence client
            var credential = new AzureKeyCredential(_azureFormRecognizerConfig.AzureFormRecognizerKey);
            var client = new DocumentIntelligenceClient(
                new Uri(_azureFormRecognizerConfig.AzureFormRecognizerEndpoint),
                credential);

            // Convert image bytes to BinaryData
            var imageData = BinaryData.FromBytes(image.ImageBytes);

            // Set up analysis options
            var options = new AnalyzeDocumentOptions("prebuilt-layout", imageData)
            {
                OutputContentFormat = DocumentContentFormat.Markdown
            };

            // Start the analysis operation
            var operation = await client.AnalyzeDocumentAsync(
                WaitUntil.Started,
                options
            );

            // Wait for the operation to complete
            var analyzeResponse = await operation.WaitForCompletionAsync();

            // Extract the content from the analysis result
            var markdownContent = analyzeResponse.Value.Content;

            _logger.LogInformation("Converted image to markdown: {Length} characters", markdownContent.Length);

            return markdownContent;
        }
        catch (Exception ex)
        {
            _logger.LogInformation("Error converting image to markdown: {Message}", ex.Message);

            // Return a placeholder message instead of throwing an exception
            return $"*[Image content could not be extracted due to an error: {ex.Message}]*";
        }
    }
}