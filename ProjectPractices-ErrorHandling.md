# Error Handling & Observability

This document is part of [Sleekflow Project Practices](ProjectPractices.md).

## Exception Handling

1. All exceptions should be extended with:
   - `Sleekflow.Exceptions.ErrorCodeConstant`
   - `Sleekflow.Exceptions.ErrorCodeException`

2. Output messages should be consistently formatted:
   - `Sleekflow.Outputs.Output<T>` provides a standard output format across all projects
   - All handlers should properly use this format for error propagation

## Request Auditing

The system automatically tracks request details through a centralized audit mechanism:

```csharp
public class Request
{
    public string Id { get; set; }
    public string MachineName { get; set; }
    public DateTime StartTime { get; set; }
    public string Path { get; set; }
    public long? ElapsedMilliseconds { get; set; }
    public object? Input { get; set; }
    public object? Output { get; set; }
    public string? ExceptionStackTrace { get; set; }
    public bool? Success { get; set; }
}
```

This enables tracing failures across the distributed system and provides rich diagnostic information.

## Distributed Tracing

The system leverages Azure's monitoring infrastructure:

1. **Azure Log Analytics** - For querying logs across all systems
2. **Azure Application Insights** - For performance monitoring and diagnostics
3. **Distributed tracing** - For tracking requests across microservices