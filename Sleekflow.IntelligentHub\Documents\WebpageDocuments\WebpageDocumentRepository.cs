using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Documents.WebpageDocuments;

public interface IWebpageDocumentRepository : IDynamicFiltersRepository<Models.Documents.FilesDocuments.WebpageDocument>
{
}

public class WebpageDocumentRepository
    : DynamicFiltersBaseRepository<Models.Documents.FilesDocuments.WebpageDocument>, IWebpageDocumentRepository, IScopedService
{
    private readonly ILogger<WebpageDocumentRepository> _logger;

    public WebpageDocumentRepository(
        ILogger<WebpageDocumentRepository> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
        _logger = logger;
    }
}