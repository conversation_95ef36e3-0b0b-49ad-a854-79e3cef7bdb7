﻿using MassTransit;
using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.CrmHubConfigs;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.Schemas.Dtos;
using Sleekflow.CrmHub.Schemas.Utils;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.CrmHub;
using Sleekflow.Ids;
using Sleekflow.Locks;

namespace Sleekflow.CrmHub.Schemas;

public interface ISchemaService
{
    Task<Schema> GetAsync(string id, string sleekflowCompanyId);

    Task<(List<Schema> Schemas, string? NextContinuationToken)> GetContinuationTokenizedSchemasAsync(
        QueryDefinition queryDefinition,
        string? continuationToken,
        int limit = 10000);

    Task<Schema> CreateAndGetAsync(
        string displayName,
        string uniqueName,
        string sleekflowCompanyId,
        string relationshipType,
        List<PropertyInput> propertyInputs,
        PrimaryPropertyInput primaryPropertyInput,
        SchemaAccessibilitySettings schemaAccessibilitySettings);

    Task<Schema> UpdateAndGetAsync(
        string id,
        string sleekflowCompanyId,
        string displayName,
        string primaryPropertyDisplayName,
        bool isEnabled,
        List<Property> receivedProperties);

    Task<Schema> RearrangeDisplayOrderAsync(string id, string priorToSchemaId, string sleekflowCompanyId);

    Task DeleteAsync(string id, string sleekflowCompanyId);
}

public class SchemaService : IScopedService, ISchemaService
{
    private readonly ILogger<SchemaService> _logger;
    private readonly ISchemaRepository _schemaRepository;
    private readonly IIdService _idService;
    private readonly ILockService _lockService;
    private readonly ICrmHubConfigService _crmHubConfigService;
    private readonly IBus _bus;
    private readonly IPropertyConstructor _propertyConstructor;
    private readonly ISchemaValidator _schemaValidator;

    public SchemaService(
        ILogger<SchemaService> logger,
        ISchemaRepository schemaRepository,
        IIdService idService,
        ILockService lockService,
        ICrmHubConfigService crmHubConfigService,
        IBus bus,
        IPropertyConstructor propertyConstructor,
        ISchemaValidator schemaValidator)
    {
        _logger = logger;
        _schemaRepository = schemaRepository;
        _idService = idService;
        _lockService = lockService;
        _bus = bus;
        _crmHubConfigService = crmHubConfigService;
        _propertyConstructor = propertyConstructor;
        _schemaValidator = schemaValidator;
    }

    public async Task<Schema> GetAsync(string id, string sleekflowCompanyId)
    {
        var schemas = await _schemaRepository.GetObjectsAsync(
            s => s.Id == id && s.SleekflowCompanyId == sleekflowCompanyId && s.IsDeleted == false);

        if (schemas.Count != 1)
        {
            throw new SfNotFoundObjectException(id);
        }

        return schemas[0];
    }

    public async Task<(List<Schema> Schemas, string? NextContinuationToken)> GetContinuationTokenizedSchemasAsync(
        QueryDefinition queryDefinition,
        string? continuationToken,
        int limit = 10000)
    {
        return await _schemaRepository.GetContinuationTokenizedObjectsAsync(
            queryDefinition,
            continuationToken,
            limit);
    }

    public async Task<Schema> CreateAndGetAsync(
        string displayName,
        string uniqueName,
        string sleekflowCompanyId,
        string relationshipType,
        List<PropertyInput> propertyInputs,
        PrimaryPropertyInput primaryPropertyInput,
        SchemaAccessibilitySettings schemaAccessibilitySettings)
    {
        var primaryProperty = new PrimaryProperty(
            _idService.GetId(SysTypeNames.SchemaProperty),
            primaryPropertyInput.DisplayName,
            primaryPropertyInput.UniqueName,
            primaryPropertyInput.DataType,
            primaryPropertyInput.IsVisible,
            primaryPropertyInput.IsPinned,
            primaryPropertyInput.IsSearchable,
            primaryPropertyInput.PrimaryPropertyConfig,
            DateTimeOffset.UtcNow,
            primaryPropertyInput.CreatedBy);

        var properties = propertyInputs.Select(
                pi => _propertyConstructor.Construct(pi))
            .ToList();

        var crmHubConfig = await _crmHubConfigService.GetCrmHubConfigAsync(sleekflowCompanyId);

        _schemaValidator.ValidateSchemaMetadata(displayName, relationshipType, primaryProperty);
        _schemaValidator.ValidateAndSortProperties(properties);

        if (schemaAccessibilitySettings.Category is "custom")
        {
            _schemaValidator.ValidateIsExceedMaximumPropertyNumPerSchema(properties.Count, crmHubConfig);
            await _schemaValidator.ValidateSchemaUniqueNameAsync(uniqueName, sleekflowCompanyId);
            await _schemaValidator.ValidateIsReachedMaximumSchemaNumAsync(sleekflowCompanyId, crmHubConfig, schemaAccessibilitySettings);
        }

        var @lock = await _lockService.LockAsync(
            new[]
            {
                nameof(SchemaService),
                nameof(CreateAndGetAsync),
                sleekflowCompanyId,
                uniqueName
            },
            TimeSpan.FromSeconds(20));
        if (@lock == null)
        {
            throw new SfUserFriendlyException("Duplicated request.");
        }

        var id = _idService.GetId(SysTypeNames.Schema);
        try
        {
            var sortingWeight = await GetSortingWeightAsync(schemaAccessibilitySettings, sleekflowCompanyId);

            var schema = new Schema(
                id,
                sleekflowCompanyId,
                displayName,
                uniqueName,
                relationshipType,
                true,
                false,
                sortingWeight,
                properties,
                primaryProperty,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                schemaAccessibilitySettings);

            return await _schemaRepository.UpsertAndGetAsync(schema, sleekflowCompanyId);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<Schema> UpdateAndGetAsync(
        string id,
        string sleekflowCompanyId,
        string displayName,
        string primaryPropertyDisplayName,
        bool isEnabled,
        List<Property> receivedProperties)
    {
        var @lock = await _lockService.LockAsync(
            new[]
            {
                sleekflowCompanyId,
                id
            },
            TimeSpan.FromSeconds(20));

        if (@lock is null)
        {
            throw new SfCustomObjectConcurrentCallException();
        }

        try
        {
            var schema = await GetAsync(id, sleekflowCompanyId);

            var properties = schema.Properties;
            var updateSchemaChangeContext = UpdateProperties(properties, receivedProperties);

            var crmHubConfig = await _crmHubConfigService.GetCrmHubConfigAsync(sleekflowCompanyId);

            _schemaValidator.ValidateIsExceedMaximumPropertyNumPerSchema(properties.Count, crmHubConfig);
            _schemaValidator.ValidateAndSortProperties(properties);

            schema.DisplayName = displayName;
            schema.IsEnabled = isEnabled;
            schema.UpdatedAt = DateTimeOffset.UtcNow;
            schema.PrimaryProperty.DisplayName = primaryPropertyDisplayName;

            schema = await _schemaRepository.PatchAndGetSchemaAsync(schema);

            await HandlePostSchemaUpdateOperationsAsync(schema, updateSchemaChangeContext);

            return schema;
        }
        catch (SfValidationException)
        {
            throw;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Unable to update schema {Id}. {Message}", id, e.Message);

            throw;
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<Schema> RearrangeDisplayOrderAsync(string id, string priorToSchemaId, string sleekflowCompanyId)
    {
        var schemas = await _schemaRepository.GetUsageLimitCountableSchemasAsync(sleekflowCompanyId);
        var schema = schemas.First(s => s.Id == id);

        schema.SortingWeight = await CalculateSortingWeightAsync(schemas, priorToSchemaId, sleekflowCompanyId);

        return await _schemaRepository.PatchAndGetSchemaAsync(schema);
    }

    public async Task DeleteAsync(string id, string sleekflowCompanyId)
    {
        var schema = await GetAsync(id, sleekflowCompanyId);

        _logger.LogWarning(
            "Start delete schema {SchemaId}, companyId {SleekflowCompanyId}",
            id,
            sleekflowCompanyId);

        try
        {
            schema.IsDeleted = true;
            schema.SortingWeight = ushort.MaxValue;

            await _schemaRepository.PatchAndGetSchemaAsync(schema);

            var onSchemaOperationEvent = new OnSchemaOperationEvent(
                sleekflowCompanyId,
                id,
                OnSchemaOperationEvent.OperationDeleteSchema);

            await _bus.Publish(
                onSchemaOperationEvent,
                context => { context.ConversationId = Guid.Parse(sleekflowCompanyId); });
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Errors when delete schema {SchemaId}, message: {Message}", id, e.Message);

            throw;
        }
    }

    #region private
    private async Task<ushort> GetSortingWeightAsync(
        SchemaAccessibilitySettings schemaAccessibilitySettings,
        string sleekflowCompanyId)
    {
        if (schemaAccessibilitySettings.Category != SchemaCategories.Custom)
        {
            return ushort.MinValue;
        }

        var schemas = await _schemaRepository.GetUsageLimitCountableSchemasAsync(sleekflowCompanyId);

        var priorToSchemaId = schemas.Any() ? schemas[^1].Id : string.Empty;

        return await CalculateSortingWeightAsync(schemas, priorToSchemaId, sleekflowCompanyId);
    }

    private async Task<ushort> CalculateSortingWeightAsync(
        List<Schema> schemas,
        string priorToSchemaId,
        string sleekflowCompanyId,
        int recursiveTime = 1)
    {
        if (recursiveTime < 0)
        {
            _logger.LogError(
                "Not expected recursive time reached when {FuncName}, {SleekflowCompanyId} {RecursiveTime}",
                nameof(CalculateSortingWeightAsync),
                sleekflowCompanyId,
                recursiveTime);
            throw new SfInternalErrorException($"Cannot calculate Sorting Weight for company {sleekflowCompanyId}");
        }

        var priorToSchemaIndex = schemas.FindIndex(s => s.Id == priorToSchemaId);

        ushort preSortingWeight;
        ushort nextSortingWeight;

        if (string.IsNullOrEmpty(priorToSchemaId))
        {
            // move to first
            preSortingWeight = ushort.MinValue;
            nextSortingWeight = schemas.Any() ? schemas[0].SortingWeight : ushort.MaxValue;
        }
        else if (priorToSchemaIndex == -1)
        {
            throw new SfNotFoundObjectException(priorToSchemaId);
        }
        else if (priorToSchemaIndex == schemas.Count - 1)
        {
            // move to end
            preSortingWeight = schemas[priorToSchemaIndex].SortingWeight;
            nextSortingWeight = ushort.MaxValue;
        }
        else
        {
            // move to middle
            preSortingWeight = schemas[priorToSchemaIndex].SortingWeight;
            nextSortingWeight = schemas[priorToSchemaIndex + 1].SortingWeight;
        }

        if (preSortingWeight >= nextSortingWeight || nextSortingWeight - preSortingWeight == 1)
        {
            schemas = await UniformSortingWeights(sleekflowCompanyId);
            return await CalculateSortingWeightAsync(schemas, priorToSchemaId, sleekflowCompanyId, recursiveTime - 1);
        }

        return (ushort) ((preSortingWeight + nextSortingWeight) >> 1);
    }

    /// <summary>
    /// Supported max schema count per company: 2^16 / 2.
    /// </summary>
    /// <param name="sleekflowCompanyId">SleekFlow company Id.</param>
    /// <returns>Schemas with updated sorting weights.</returns>
    private async Task<List<Schema>> UniformSortingWeights(string sleekflowCompanyId)
    {
        _logger.LogInformation("Start uniforming the sorting weights for company {CompanyId}", sleekflowCompanyId);

        var schemas = await _schemaRepository.GetUsageLimitCountableSchemasAsync(sleekflowCompanyId);

        var count = schemas.Count;

        if ((count * 2) - 1 > ushort.MaxValue)
        {
            throw new SfUserFriendlyException($"Exceed the max schema count for company {sleekflowCompanyId}");
        }

        var step = (ushort) ((ushort.MaxValue + 1) / count);
        var currentSortingWeight = (ushort) (step >> 1);

        await _schemaRepository.ExecuteTransactionalBatchAsync(
            sleekflowCompanyId,
            async batch =>
            {
                for (var i = 0; i < count; i++)
                {
                    batch.PatchItem(
                        schemas[i].Id,
                        new List<PatchOperation>
                        {
                            PatchOperation.Replace(
                                $"/{Schema.PropertyNameSortingWeight}",
                                currentSortingWeight)
                        });

                    schemas[i].SortingWeight = currentSortingWeight;
                    currentSortingWeight += step;
                }
            });

        return schemas;
    }

    private UpdateSchemaChangeContext UpdateProperties(List<Property> properties, List<Property> receivedProperties)
    {
        var toBeDeletedPropertyIds = properties
            .Where(p => !receivedProperties.Exists(rp => rp.Id == p.Id))
            .Select(p => p.Id)
            .ToList();
        var toBeDeletedOptionDictionary = new Dictionary<string, (string, List<string>)>();
        var needReindexPropertyValues = false;

        foreach (var receivedProperty in receivedProperties)
        {
            var index = properties.FindIndex(p => p.Id == receivedProperty.Id);
            if (index == -1)
            {
                var newProperty = _propertyConstructor.Construct(receivedProperty);

                properties.Add(newProperty);
            }
            else
            {
                var updatePropertyChangeContext = _propertyConstructor.Update(properties[index], receivedProperty);

                needReindexPropertyValues = updatePropertyChangeContext.NeedReindexPropertyValue ||
                                            needReindexPropertyValues;

                if (updatePropertyChangeContext.ToBeDeletedOptionIds.Any())
                {
                    toBeDeletedOptionDictionary.Add(
                        receivedProperty.Id,
                        (receivedProperty.DataType.Name, updatePropertyChangeContext.ToBeDeletedOptionIds));
                }
            }
        }

        properties.RemoveAll(p => toBeDeletedPropertyIds.Contains(p.Id));

        return new UpdateSchemaChangeContext(
            needReindexPropertyValues,
            toBeDeletedPropertyIds,
            toBeDeletedOptionDictionary);
    }

    private async Task HandlePostSchemaUpdateOperationsAsync(
        Schema schema,
        UpdateSchemaChangeContext updateSchemaChangeContext)
    {
        if (updateSchemaChangeContext.ToBeDeletedPropertyIds.Any())
        {
            await HandleDeletePropertiesAsync(
                schema.Id,
                schema.SleekflowCompanyId,
                updateSchemaChangeContext.ToBeDeletedPropertyIds);
        }

        if (updateSchemaChangeContext.ToBeDeletedOptionDictionary.Any())
        {
            await HandleDeletePropertyOptionsAsync(
                schema.Id,
                schema.SleekflowCompanyId,
                updateSchemaChangeContext.ToBeDeletedOptionDictionary);
        }

        if (updateSchemaChangeContext.NeedReindexPropertyValues)
        {
            await HandleReindexPropertyValuesAsync(
                schema.Id,
                schema.SleekflowCompanyId,
                schema.Properties.Where(p => p.IsSearchable).Select(p => p.Id).ToList());
        }
    }

    private async Task HandleDeletePropertiesAsync(
        string schemaId,
        string sleekflowCompanyId,
        List<string> toBeDeletedPropertyIds)
    {
        try
        {
            var onSchemaOperationEvent = new OnSchemaOperationEvent(
                sleekflowCompanyId,
                schemaId,
                OnSchemaOperationEvent.OperationDeleteSchemaProperties,
                toBeDeletedPropertyIds: toBeDeletedPropertyIds);

            await _bus.Publish(
                onSchemaOperationEvent,
                context => { context.ConversationId = Guid.Parse(sleekflowCompanyId); });
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Errors when handling schema operation {Operation}, {SchemaId}, message: {Message}",
                nameof(HandleDeletePropertiesAsync),
                schemaId,
                e.Message);
        }
    }

    private async Task HandleDeletePropertyOptionsAsync(
        string schemaId,
        string sleekflowCompanyId,
        Dictionary<string, (string DataType, List<string> ToBeDeletedOptionId)> toBeDeletedOptionIdDictionary)
    {
        try
        {
            var onSchemaOperationEvent = new OnSchemaOperationEvent(
                sleekflowCompanyId,
                schemaId,
                OnSchemaOperationEvent.OperationDeleteSchemaPropertyOptions,
                toBeDeletedOptionIdDictionary: toBeDeletedOptionIdDictionary);

            await _bus.Publish(
                onSchemaOperationEvent,
                context => { context.ConversationId = Guid.Parse(sleekflowCompanyId); });
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Errors when handling schema operation {Operation}, {SchemaId}, message: {Message}",
                nameof(HandleDeletePropertyOptionsAsync),
                schemaId,
                e.Message);
        }
    }

    private async Task HandleReindexPropertyValuesAsync(string schemaId, string sleekflowCompanyId, List<string> indexedPropertyIds)
    {
        try
        {
            var onSchemaOperationEvent = new OnSchemaOperationEvent(
                sleekflowCompanyId,
                schemaId,
                OnSchemaOperationEvent.OperationReindexSchemaPropertyValues,
                indexedPropertyIds: indexedPropertyIds);

            await _bus.Publish(
                onSchemaOperationEvent,
                context => { context.ConversationId = Guid.Parse(sleekflowCompanyId); });
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Errors when handling schema operation {Operation}, {SchemaId}, message: {Message}",
                nameof(HandleReindexPropertyValuesAsync),
                schemaId,
                e.Message);
        }
    }

    #endregion
}