using MassTransit;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Configs;
using Sleekflow.FlowHub.EnrolmentMonetization;
using Sleekflow.FlowHub.FlowHubEvents;
using Sleekflow.FlowHub.Grains;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Evaluations;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.States.Abstractions;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.Models.WorkflowWebhookTriggers;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.Statistics;
using Sleekflow.FlowHub.StepExecutions;
using Sleekflow.FlowHub.Utils;
using Sleekflow.FlowHub.WorkflowAgentConfigMappings;
using Sleekflow.FlowHub.WorkflowGroups;
using Sleekflow.FlowHub.WorkflowWebhookTriggers;
using Sleekflow.Ids;
using Sleekflow.Locks;
using Sleekflow.Models.Events;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using JsonConfig = Sleekflow.FlowHub.JsonConfigs.JsonConfig;

namespace Sleekflow.FlowHub.Workflows;

public interface IWorkflowService
{
    Task<ProxyWorkflow> CreateWorkflowAsync(Workflow workflow, string sleekflowCompanyId);

    Task<ProxyWorkflow> GetLatestWorkflowAsync(string workflowId, string sleekflowCompanyId);

    Task<(ProxyWorkflow? ActiveWorkflow, List<ProxyWorkflow> VersionedWorkflows)> GetWorkflowAsync(
        string workflowId,
        string sleekflowCompanyId);

    Task<ProxyWorkflow?> GetActiveWorkflowAsync(string workflowId, string sleekflowCompanyId);

    Task<(List<LightWeightProxyWorkflow> Workflows, string? NextContinuationToken)>
        GetAllLatestWorkflowAndStatusTuplesAsync(
            string sleekflowCompanyId,
            string? continuationToken,
            int limit,
            string? searchName,
            WorkflowFilters? workflowFilters,
            bool includeDeleted = false);

    Task<ProxyWorkflow> UpdateWorkflowAsync(
        string workflowId,
        string sleekflowCompanyId,
        WorkflowTriggers triggers,
        WorkflowEnrollmentSettings? workflowEnrollmentSettings,
        WorkflowScheduleSettings? workflowScheduleSettings,
        List<Step> steps,
        string name,
        string? workflowGroupId,
        AuditEntity.SleekflowStaff sleekflowStaff,
        Dictionary<string, object?> metadata,
        string enrolmentPricingType,
        string? manualEnrollmentSource = null,
        bool? isDynamicVariableEnabled = null,
        string? workflowType = WorkflowType.Normal,
        string? dependencyWorkflowId = null);

    Task<ProxyWorkflow> EnableWorkflowAsync(
        string workflowVersionedId,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task<ProxyWorkflow> DisableWorkflowAsync(
        string workflowVersionedId,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task<(ProxyWorkflow Source, ProxyWorkflow Target)> SwapWorkflowsAsync(
        string sourceWorkflowVersionedId,
        string targetWorkflowVersionedId,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task<List<CompactWorkflow>> MatchWorkflowsAsync(
        string sleekflowCompanyId,
        EventBody eventBody,
        FlowHubConfig flowHubConfig,
        List<ProxyState> existingStates);

    Task DeleteWorkflowAsync(
        string workflowId,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task ScheduleDeleteWorkflowsAsync(
        List<string> workflowIds,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task DeleteVersionedWorkflowAsync(
        string workflowId,
        string workflowVersionedId,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task UnsetWorkflowGroupIdAsync(
        string sleekflowCompanyId,
        string workflowGroupId);

    Task<ProxyWorkflow> AssignWorkflowToGroupAsync(
        string workflowVersionedId,
        string sleekflowCompanyId,
        string? workflowGroupId,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task<ProxyWorkflow?> GetVersionedWorkflowOrDefaultAsync(
        string sleekflowCompanyId,
        string workflowVersionedId);

    Task DeleteLeftoverVersionedWorkflowAsync(
        string sleekflowCompanyId,
        string workflowVersionedId);

    Task<int> CountWorkflowsAsync(
        string sleekflowCompanyId,
        string? workflowId = null,
        string? activationStatus = null,
        string? workflowType = null);

    Task<int> PatchWorkflowAsync(
        string workflowVersionedId,
        string sleekflowCompanyId,
        List<PatchOperation> operations,
        string eTag,
        string? sleekflowStaffId = null);

    Task<ProxyWorkflow> GetProxyWorkflowAsync(Workflow workflow);
}

public class WorkflowService : IWorkflowService, IScopedService
{
    private readonly IBus _bus;
    private readonly IFlowHubEventEvaluator _flowHubEventEvaluator;
    private readonly IWorkflowRepository _workflowRepository;
    private readonly IWorkflowGroupService _workflowGroupService;
    private readonly IWorkflowStepsService _workflowStepsService;
    private readonly IWorkflowMetadataService _workflowMetadataService;
    private readonly IWorkflowStepCategoryStatisticsService _workflowStepCategoryStatisticsService;
    private readonly IIdService _idService;
    private readonly ILockService _lockService;
    private readonly IWorkflowDeletionConfig _workflowDeletionConfig;
    private readonly ILogger<WorkflowService> _logger;
    private readonly ICacheService _cacheService;
    private readonly IWorkflowWebhookTriggerService _workflowWebhookTriggerService;
    private readonly IWorkflowAgentConfigMappingService _workflowAgentConfigMappingService;
    private readonly IStepExecutionService _stepExecutionService;

    public WorkflowService(
        IBus bus,
        IFlowHubEventEvaluator flowHubEventEvaluator,
        IWorkflowRepository workflowRepository,
        IWorkflowGroupService workflowGroupService,
        IWorkflowStepsService workflowStepsService,
        IWorkflowMetadataService workflowMetadataService,
        IWorkflowStepCategoryStatisticsService workflowStepCategoryStatisticsService,
        IIdService idService,
        ILockService lockService,
        IWorkflowDeletionConfig workflowDeletionConfig,
        ILogger<WorkflowService> logger,
        ICacheService cacheService,
        IWorkflowWebhookTriggerService workflowWebhookTriggerService,
        IWorkflowAgentConfigMappingService workflowAgentConfigMappingService,
        IStepExecutionService stepExecutionService)
    {
        _bus = bus;
        _flowHubEventEvaluator = flowHubEventEvaluator;
        _workflowRepository = workflowRepository;
        _workflowGroupService = workflowGroupService;
        _workflowStepsService = workflowStepsService;
        _workflowMetadataService = workflowMetadataService;
        _workflowStepCategoryStatisticsService = workflowStepCategoryStatisticsService;
        _idService = idService;
        _lockService = lockService;
        _workflowDeletionConfig = workflowDeletionConfig;
        _logger = logger;
        _cacheService = cacheService;
        _workflowWebhookTriggerService = workflowWebhookTriggerService;
        _workflowAgentConfigMappingService = workflowAgentConfigMappingService;
        _stepExecutionService = stepExecutionService;
    }

    public async Task<ProxyWorkflow> CreateWorkflowAsync(Workflow workflow, string sleekflowCompanyId)
    {
        await AssertWorkflowGroupIdExists(
            sleekflowCompanyId,
            workflow.WorkflowGroupId);

        var savedSteps = await _workflowStepsService.SaveWorkflowStepsAsync(
            workflow.SleekflowCompanyId,
            workflow.WorkflowVersionedId,
            workflow.Steps);

        var savedMetadata = await _workflowMetadataService.SaveWorkflowMetadataAsync(
            workflow.SleekflowCompanyId,
            workflow.WorkflowVersionedId,
            workflow.Metadata);

        var createdWorkflow = await _workflowRepository.CreateAndGetAsync(workflow, sleekflowCompanyId);

        var workflowResult = new ProxyWorkflow(
            createdWorkflow,
            savedSteps,
            savedMetadata);

        var agentConfigIds = _workflowAgentConfigMappingService.GetAiAgentIdsFromWorkflowSteps(workflowResult);
        await _workflowAgentConfigMappingService.UpdateWorkflowAgentConfigMappingsAsync(
            sleekflowCompanyId,
            workflowResult.WorkflowId,
            agentConfigIds,
            workflowResult.CreatedBy);

        await _workflowStepCategoryStatisticsService.RecordWorkflowStepCategoriesAsync(workflowResult);

        return workflowResult;
    }

    public async Task<ProxyWorkflow> GetLatestWorkflowAsync(string workflowId, string sleekflowCompanyId)
    {
        var workflows = await _workflowRepository.GetObjectsAsync(
            new QueryDefinition(
                    "SELECT * " +
                    "FROM %%CONTAINER_NAME%% r " +
                    "WHERE r.workflow_id = @workflow_id AND r.sleekflow_company_id = @sleekflow_company_id " +
                    "ORDER BY r.created_at DESC " +
                    "OFFSET 0 LIMIT 1 ")
                .WithParameter("@workflow_id", workflowId)
                .WithParameter("@sleekflow_company_id", sleekflowCompanyId));

        var latestWorkflow = workflows.Single();

        return await GetProxyWorkflowAsync(latestWorkflow);
    }

    public async Task<(ProxyWorkflow? ActiveWorkflow, List<ProxyWorkflow> VersionedWorkflows)> GetWorkflowAsync(
        string workflowId,
        string sleekflowCompanyId)
    {
        var activeWorkflows = await _workflowRepository.GetObjectsAsync(
            new QueryDefinition(
                    "SELECT * " +
                    "FROM %%CONTAINER_NAME%% r " +
                    "WHERE r.workflow_id = @workflow_id AND r.sleekflow_company_id = @sleekflow_company_id AND r.activation_status = @activation_status " +
                    "ORDER BY r.created_at DESC ")
                .WithParameter("@workflow_id", workflowId)
                .WithParameter("@sleekflow_company_id", sleekflowCompanyId)
                .WithParameter("@activation_status", WorkflowActivationStatuses.Active));

        var versionedWorkflows = await _workflowRepository.GetObjectsAsync(
            new QueryDefinition(
                    "SELECT * " +
                    "FROM %%CONTAINER_NAME%% r " +
                    "WHERE r.workflow_id = @workflow_id AND r.sleekflow_company_id = @sleekflow_company_id " +
                    "ORDER BY r.created_at DESC " +
                    "OFFSET 0 LIMIT 20 ")
                .WithParameter("@workflow_id", workflowId)
                .WithParameter("@sleekflow_company_id", sleekflowCompanyId));

        var activeWorkflow = activeWorkflows.SingleOrDefault();

        var activeProxyWorkflow = activeWorkflow is null ? null : await GetProxyWorkflowAsync(activeWorkflow);
        var versionedProxyWorkflows = await GetProxyWorkflowsAsync(versionedWorkflows);

        return (activeProxyWorkflow, versionedProxyWorkflows);
    }

    public async Task<ProxyWorkflow?> GetActiveWorkflowAsync(string workflowId, string sleekflowCompanyId)
    {
        var workflows = await _workflowRepository.GetObjectsAsync(
            new QueryDefinition(
                    "SELECT * " +
                    "FROM %%CONTAINER_NAME%% r " +
                    "WHERE r.workflow_id = @workflow_id AND r.sleekflow_company_id = @sleekflow_company_id AND r.activation_status = @activation_status " +
                    "ORDER BY r.created_at DESC ")
                .WithParameter("@workflow_id", workflowId)
                .WithParameter("@sleekflow_company_id", sleekflowCompanyId)
                .WithParameter("@activation_status", WorkflowActivationStatuses.Active));

        var activeWorkflow = workflows.SingleOrDefault();
        var activeProxyWorkflow = activeWorkflow is null ? null : await GetProxyWorkflowAsync(activeWorkflow);

        return activeProxyWorkflow;
    }

    public Task<int> CountWorkflowsAsync(
        string sleekflowCompanyId,
        string? workflowId = null,
        string? activationStatus = null,
        string? workflowType = null)
        => _workflowRepository.CountWorkflowsAsync(
            sleekflowCompanyId,
            workflowId,
            activationStatus,
            workflowType);

    public async Task<(List<LightWeightProxyWorkflow> Workflows, string? NextContinuationToken)>
        GetAllLatestWorkflowAndStatusTuplesAsync(
            string sleekflowCompanyId,
            string? continuationToken,
            int limit,
            string? searchName,
            WorkflowFilters? workflowFilters,
            bool includeDeleted = false)
    {
        if (limit > 100)
        {
            throw new SfQueryException(
                $"Maximum query limit is 100. Received value: {limit}",
                nameof(GetAllLatestWorkflowAndStatusTuplesAsync));
        }

        var workflowIdAndMaxCreatedAtTuples =
            await _workflowRepository.GetLatestWorkflowIdCreatedAtTuplesAsync(sleekflowCompanyId);

        var agentConfigId = workflowFilters?.AgentConfigId;
        if (!string.IsNullOrEmpty(agentConfigId))
        {
            var workflowIdsFromAgentConfig =
                await _workflowAgentConfigMappingService.GetWorkflowIdsByAgentConfigIdAsync(
                    sleekflowCompanyId,
                    agentConfigId);

            if (!workflowIdsFromAgentConfig.Any())
            {
                return (new List<LightWeightProxyWorkflow>(), null);
            }

            workflowIdAndMaxCreatedAtTuples = workflowIdAndMaxCreatedAtTuples
                .Where(t => workflowIdsFromAgentConfig.Contains(t.WorkflowId))
                .ToList();

            if (!workflowIdAndMaxCreatedAtTuples.Any())
            {
                return (new List<LightWeightProxyWorkflow>(), null);
            }
        }

        var (workflows, nextContinuationToken) = await _workflowRepository.GetContinuationTokenizedObjectsAsync(
            new QueryDefinition(
                    $"""
                     SELECT *
                     FROM %%CONTAINER_NAME%% r
                     WHERE r.sleekflow_company_id = @sleekflow_company_id
                         AND ARRAY_CONTAINS(@tuples, [r.created_at, r.workflow_id])
                         {(!includeDeleted ? $"AND r.activation_status != '{WorkflowActivationStatuses.Deleted}'" : string.Empty)}
                         {(!string.IsNullOrWhiteSpace(searchName) ? "AND CONTAINS(r.name, @search_name, true) " : string.Empty)}
                         {(!string.IsNullOrWhiteSpace(workflowFilters?.ActivationStatus) ? "AND r.activation_status = @activation_status " : string.Empty)}
                         {(!string.IsNullOrWhiteSpace(workflowFilters?.CreatedBySleekflowStaffId) ? "AND r.created_by.sleekflow_staff_id = @created_by " : string.Empty)}
                         {(!string.IsNullOrWhiteSpace(workflowFilters?.UpdatedBySleekflowStaffId) ? "AND r.updated_by.sleekflow_staff_id = @updated_by " : string.Empty)}
                         {(!string.IsNullOrWhiteSpace(workflowFilters?.UpdatedFromDateTime.ToString()) ? "AND r.updated_at > @updated_from_date_time " : string.Empty)}
                         {(!string.IsNullOrWhiteSpace(workflowFilters?.UpdatedToDateTime.ToString()) ? "AND r.updated_at < @updated_to_date_time " : string.Empty)}
                         {(!string.IsNullOrWhiteSpace(workflowFilters?.WorkflowType) ? "AND r.workflow_type = @workflow_type " : string.Empty)}
                        {(!string.IsNullOrWhiteSpace(workflowFilters?.DependencyWorkflowId) ? "AND r.dependency_workflow_id = @dependency_workflow_id " : string.Empty)}
                     ORDER BY r.created_at DESC, r.workflow_id ASC
                     """)
                .WithParameter("@sleekflow_company_id", sleekflowCompanyId)
                .WithParameter("@search_name", searchName)
                .WithParameter("@activation_status", workflowFilters?.ActivationStatus)
                .WithParameter("@created_by", workflowFilters?.CreatedBySleekflowStaffId)
                .WithParameter("@updated_by", workflowFilters?.UpdatedBySleekflowStaffId)
                .WithParameter("@updated_from_date_time", workflowFilters?.UpdatedFromDateTime)
                .WithParameter("@updated_to_date_time", workflowFilters?.UpdatedToDateTime)
                .WithParameter("@workflow_type", workflowFilters?.WorkflowType)
                .WithParameter("@dependency_workflow_id", workflowFilters?.DependencyWorkflowId)
                .WithParameter(
                    "@tuples",
                    workflowIdAndMaxCreatedAtTuples
                        .Select(t => new string[]
                        {
                            t.MaxCreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                            t.WorkflowId
                        })
                        .ToList()),
            continuationToken,
            limit);

        return (
            workflows
                .Select(w => new LightWeightProxyWorkflow(w))
                .ToList(),
            nextContinuationToken);
    }

    public async Task<ProxyWorkflow> UpdateWorkflowAsync(
        string workflowId,
        string sleekflowCompanyId,
        WorkflowTriggers triggers,
        WorkflowEnrollmentSettings? workflowEnrollmentSettings,
        WorkflowScheduleSettings? workflowScheduleSettings,
        List<Step> steps,
        string name,
        string? workflowGroupId,
        AuditEntity.SleekflowStaff sleekflowStaff,
        Dictionary<string, object?> metadata,
        string enrolmentPricingType,
        string? manualEnrollmentSource = null,
        bool? isDynamicVariableEnabled = null,
        string? workflowType = WorkflowType.Normal,
        string? dependencyWorkflowId = null)
    {
        await AssertWorkflowGroupIdExists(sleekflowCompanyId, workflowGroupId);

        var workflow = await GetLatestWorkflowAsync(workflowId, sleekflowCompanyId);

        if (workflow.ActivationStatus == WorkflowActivationStatuses.Deleted)
        {
            throw new SfWorkflowDeletedException(workflow.WorkflowId);
        }

        if (workflow.Version != "v1" && !steps.Exists(s => s.Id == "enrollment-setup"))
        {
            var setupStep = new SimpleStep("enrollment-setup", "enrollment-setup", null, null);
            var assignments = new Assign()
            {
                ["conversation"] =
                    "{{ (trigger_event_body.contact_id | string.empty) ? {} : (trigger_event_body.contact_id | sleekflow.get_contact_conversation) }}",
                ["contactOwner"] =
                    "{{ usr_var_dict.contact ? ((usr_var_dict.contact[\"ContactOwner\"] | string.empty) ? \"\" : (usr_var_dict.contact[\"ContactOwner\"] | sleekflow.get_staff)) :  \"\" }}"
            };

            if (workflow.IsDynamicVariableEnabled != true)
            {
                assignments["contact"] =
                    "{{ (trigger_event_body.contact_id | string.empty) ? {} : (trigger_event_body.contact_id | sleekflow.get_contact) }}";
                assignments["lists"] =
                    "{{ (trigger_event_body.contact_id | string.empty) ? {} : (trigger_event_body.contact_id | sleekflow.get_contact_lists) }}";
            }

            setupStep = new SimpleStep(
                           "enrollment-setup",
                           "enrollment-setup",
                           assignments,
                           null);

            if (triggers.Webhook != null)
            {
                _logger.LogInformation("Processing webhook trigger for workflow {WorkflowId}", workflowId);

                // Deserialize metadata from NS_v1 namespace
                var metadataV1 =
                    JsonConvert.DeserializeObject<Dictionary<string, object>>(metadata["NS_v1"]?.ToString() ?? "{}");
                _logger.LogDebug("Deserialized metadata V1: {@MetadataV1}", metadataV1);

                // Get trigger configuration from metadata
                var triggerMetadata = JsonConvert.DeserializeObject<TriggerMetadata>(
                    JsonConvert.SerializeObject(metadataV1.GetValueOrDefault("trigger")));
                _logger.LogDebug("Trigger metadata: {@TriggerMetadata}", triggerMetadata);

                if (metadataV1.ContainsKey("trigger") && triggerMetadata == null)
                {
                    _logger.LogError(
                        "Invalid trigger configuration in workflow metadata for workflow {WorkflowId}",
                        workflowId);
                    throw new SfWorkflowTypeException("Invalid trigger configuration in workflow metadata");
                }

                // Flag to control auto creation of new contacts
                var enableAutoCreateContact = false;
                string? contactPayloadKey = null;

                var workflowWebhookTriggers = await _workflowWebhookTriggerService.GetWorkflowWebhookTriggersAsync(
                    workflowId,
                    sleekflowCompanyId);

                // Only one webhook trigger is allowed per workflow
                var workflowWebhookTrigger = workflowWebhookTriggers.FirstOrDefault();

                if (workflowWebhookTrigger == null)
                {
                    _logger.LogError("Webhook trigger not found for workflow {WorkflowId}", workflowId);
                    throw new SfWorkflowWebhookTriggerNotFoundException();
                }

                // Check if this is a webhook trigger event
                if (triggerMetadata?.Event?.Id == "webhook-received")
                {
                    _logger.LogInformation("Processing webhook-received event for workflow {WorkflowId}", workflowId);

                    // Get webhook setup configuration
                    var webhookSetup = triggerMetadata.Setup?.Find(s => s.ArgName == "customer_identifier");
                    if (webhookSetup != null)
                    {
                        // Extract webhook config values
                        var latestObjectType = webhookSetup.Value?.ObjectType;
                        contactPayloadKey = webhookSetup.Value?.ContactPayloadKey?.Replace("$", string.Empty);
                        enableAutoCreateContact = webhookSetup.Value?.NewCustomerEntrollment ?? false;

                        _logger.LogDebug(
                            "Webhook setup values - ObjectType: {ObjectType}, ContactPayloadKey: {ContactPayloadKey}, EnableAutoCreateContact: {EnableAutoCreateContact}",
                            latestObjectType,
                            contactPayloadKey,
                            enableAutoCreateContact);

                        // Update webhook trigger if required fields exist
                        if (!string.IsNullOrEmpty(latestObjectType) && !string.IsNullOrEmpty(contactPayloadKey))
                        {
                            var objectIdExpression = $"{{{{ (reqStr | json.deserialize){contactPayloadKey} }}}}";
                            _logger.LogInformation(
                                "Updating webhook trigger for workflow {WorkflowId} with objectType {ObjectType}",
                                workflowId,
                                latestObjectType);

                            await _workflowWebhookTriggerService.UpdateWorkflowWebhookTriggerAsync(
                                workflowWebhookTrigger.Id,
                                sleekflowCompanyId,
                                objectIdExpression,
                                latestObjectType,
                                sleekflowStaff);
                        }
                    }
                }
                else
                {
                    _logger.LogError("Webhook trigger setup not found in metadata for workflow {WorkflowId}", workflowId);
                    throw new SfWorkflowWebhookTriggerNotFoundException();
                }

                var latestWorkflowWebhookTriggers =
                    await _workflowWebhookTriggerService.GetWorkflowWebhookTriggersAsync(
                        workflowId,
                        sleekflowCompanyId);

                var latestWorkflowWebhookTrigger = latestWorkflowWebhookTriggers.FirstOrDefault();

                var objectType = latestWorkflowWebhookTrigger?.ObjectType;
                var identifierExpression = latestWorkflowWebhookTrigger?.ObjectIdExpression;

                if (contactPayloadKey == null)
                {
                    _logger.LogError("Contact payload key not found for workflow {WorkflowId}", workflowId);
                    throw new SfWorkflowWebhookTriggerNotFoundException();
                }

                if (!string.IsNullOrEmpty(objectType) && !string.IsNullOrEmpty(identifierExpression))
                {
                    _logger.LogDebug(
                        "Processing webhook with ObjectType: {ObjectType}, IdentifierExpression: {IdentifierExpression}",
                        objectType,
                        identifierExpression);

                    var getContactAssignment = setupStep.Assign;

                    if (getContactAssignment != null)
                    {
                        if (objectType == "Contact.Id" || objectType == "Contact")
                        {
                            _logger.LogDebug(
                                "Setting up Contact/Contact.Id assignments for workflow {WorkflowId}",
                                workflowId);
                            getContactAssignment["contact"] =
                                $"{{{{ (trigger_event_body.request_body_str | json.deserialize){contactPayloadKey} | sleekflow.get_contact }}}}";
                            getContactAssignment["lists"] =
                                $"{{{{ (trigger_event_body.request_body_str | json.deserialize){contactPayloadKey} | sleekflow.get_contact_lists }}}}";
                            getContactAssignment["conversation"] =
                                $"{{{{ (trigger_event_body.request_body_str | json.deserialize){contactPayloadKey} | sleekflow.get_contact_conversation }}}}";
                        }
                        else if (objectType == "Contact.PhoneNumber")
                        {
                            var phoneAction = enableAutoCreateContact
                                ? "get_or_create_contact_id_by_phone_number"
                                : "get_contact_id_by_phone_number";

                            _logger.LogDebug(
                                "Setting up Contact.PhoneNumber assignments with action {PhoneAction} for workflow {WorkflowId}",
                                phoneAction,
                                workflowId);

                            getContactAssignment["contact"] =
                                $"{{{{ (trigger_event_body.request_body_str | json.deserialize){contactPayloadKey} | sleekflow.{phoneAction} | sleekflow.get_contact }}}}";
                            getContactAssignment["lists"] =
                                $"{{{{ (trigger_event_body.request_body_str | json.deserialize){contactPayloadKey} | sleekflow.{phoneAction} | sleekflow.get_contact_lists }}}}";
                            getContactAssignment["conversation"] =
                                $"{{{{ (trigger_event_body.request_body_str | json.deserialize){contactPayloadKey} | sleekflow.{phoneAction} | sleekflow.get_contact_conversation }}}}";
                        }
                        else if (objectType == "Contact.Email")
                        {
                            var emailAction = enableAutoCreateContact
                                ? "get_or_create_contact_id_by_email"
                                : "get_contact_id_by_email";

                            _logger.LogDebug(
                                "Setting up Contact.Email assignments with action {EmailAction} for workflow {WorkflowId}",
                                emailAction,
                                workflowId);

                            getContactAssignment["contact"] =
                                $"{{{{ (trigger_event_body.request_body_str | json.deserialize){contactPayloadKey} | sleekflow.{emailAction} | sleekflow.get_contact }}}}";
                            getContactAssignment["lists"] =
                                $"{{{{ (trigger_event_body.request_body_str | json.deserialize){contactPayloadKey} | sleekflow.{emailAction} | sleekflow.get_contact_lists }}}}";
                            getContactAssignment["conversation"] =
                                $"{{{{ (trigger_event_body.request_body_str | json.deserialize){contactPayloadKey} | sleekflow.{emailAction} | sleekflow.get_contact_conversation }}}}";
                        }
                    }
                }
            }

            steps.Insert(0, setupStep);
        }

        var originalSleekflowStaff = workflow.CreatedBy;

        var isSameTriggers =
            JsonConvert.SerializeObject(triggers, JsonConfig.DefaultJsonSerializerSettings) ==
            JsonConvert.SerializeObject(workflow.Triggers, JsonConfig.DefaultJsonSerializerSettings);

        var isSameSteps =
            JsonConvert.SerializeObject(steps, JsonConfig.DefaultJsonSerializerSettings) ==
            JsonConvert.SerializeObject(workflow.Steps, JsonConfig.DefaultJsonSerializerSettings);
        var isSameName = name == workflow.Name;
        var isSameWorkflowGroup = workflowGroupId == workflow.WorkflowGroupId;

        var isSameWorkflowEnrollmentSettings =
            JsonConvert.SerializeObject(workflowEnrollmentSettings, JsonConfig.DefaultJsonSerializerSettings) ==
            JsonConvert.SerializeObject(workflow.WorkflowEnrollmentSettings, JsonConfig.DefaultJsonSerializerSettings);

        var isSameWorkflowScheduleSettings =
            JsonConvert.SerializeObject(workflowScheduleSettings, JsonConfig.DefaultJsonSerializerSettings) ==
            JsonConvert.SerializeObject(workflow.WorkflowScheduleSettings, JsonConfig.DefaultJsonSerializerSettings);

        var isSameMetadata =
            JsonConvert.SerializeObject(metadata, JsonConfig.DefaultJsonSerializerSettings) ==
            JsonConvert.SerializeObject(workflow.Metadata, JsonConfig.DefaultJsonSerializerSettings);

        var isSameDynamicVariableEnabled = (isDynamicVariableEnabled ?? workflow.IsDynamicVariableEnabled) == workflow.IsDynamicVariableEnabled;
        var isSameEnrolmentPricingType = enrolmentPricingType == workflow.EnrolmentPricingType;

        if (isSameTriggers
            && isSameSteps
            && isSameName
            && isSameWorkflowGroup
            && isSameWorkflowEnrollmentSettings
            && isSameWorkflowScheduleSettings
            && isSameMetadata
            && isSameDynamicVariableEnabled
            && isSameEnrolmentPricingType)
        {
            return workflow;
        }

        var workflowVersionedId = _idService.GetId("WorkflowVersioned", workflow.WorkflowId);

        var savedSteps = await _workflowStepsService.SaveWorkflowStepsAsync(
            workflow.SleekflowCompanyId,
            workflowVersionedId,
            steps);

        var savedMetadata = await _workflowMetadataService.SaveWorkflowMetadataAsync(
            workflow.SleekflowCompanyId,
            workflowVersionedId,
            metadata);

        var updatedWorkflow = await _workflowRepository.CreateAndGetAsync(
            new Workflow(
                workflowId,
                workflowVersionedId,
                name,
                workflowType,
                workflowGroupId,
                triggers,
                workflowEnrollmentSettings,
                workflowScheduleSettings,
                steps,
                WorkflowActivationStatuses.Draft,
                workflowVersionedId,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                sleekflowCompanyId,
                originalSleekflowStaff ?? sleekflowStaff,
                sleekflowStaff,
                metadata,
                workflow.Version,
                enrolmentPricingType,
                manualEnrollmentSource: manualEnrollmentSource,
                subWorkflowType: workflow.GetSubWorkflowType(),
                isDynamicVariableEnabled: workflow.IsDynamicVariableEnabled ?? false,
                dependencyWorkflowId: dependencyWorkflowId),
            sleekflowCompanyId);

        var workflowResult = new ProxyWorkflow(
            updatedWorkflow,
            savedSteps,
            savedMetadata);

        var agentConfigIds = _workflowAgentConfigMappingService.GetAiAgentIdsFromWorkflowSteps(workflowResult);
        await _workflowAgentConfigMappingService.UpdateWorkflowAgentConfigMappingsAsync(
            sleekflowCompanyId,
            workflowId,
            agentConfigIds,
            sleekflowStaff);

        await _workflowStepCategoryStatisticsService.RecordWorkflowStepCategoriesAsync(workflowResult);

        return workflowResult;
    }

    public async Task<ProxyWorkflow> EnableWorkflowAsync(
        string workflowVersionedId,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            new[]
            {
                nameof(WorkflowService),
                sleekflowCompanyId
            },
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(10));

        var strings = workflowVersionedId.Split("-");
        var workflowId = strings[0];

        try
        {
            var workflows =
                await _workflowRepository.GetObjectsAsync(w =>
                    w.ActivationStatus == WorkflowActivationStatuses.Active
                    && w.WorkflowId == workflowId);

            if (workflows.Any())
            {
                throw new SfMultipleWorkflowActiveException();
            }

            var workflowToEnable = await _workflowRepository.GetOrDefaultAsync(
                workflowVersionedId,
                sleekflowCompanyId);

            if (workflowToEnable is { ActivationStatus: WorkflowActivationStatuses.Deleted })
            {
                throw new SfWorkflowDeletedException(workflowToEnable.WorkflowId);
            }

            var workflow = await _workflowRepository.PatchAndGetAsync(
                workflowVersionedId,
                sleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Replace("/activation_status", WorkflowActivationStatuses.Active),
                    PatchOperation.Replace("/updated_at", DateTime.UtcNow),
                });

            await _cacheService.RemoveCacheAsync(
                FlowHubCacheKeyBuilder.BuildLatestActiveWorkflowSkeletonsKeyByCompany(sleekflowCompanyId));

            return await GetProxyWorkflowAsync(workflow);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<ProxyWorkflow> DisableWorkflowAsync(
        string workflowVersionedId,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            new[]
            {
                nameof(WorkflowService),
                sleekflowCompanyId
            },
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(10));

        try
        {
            var workflowToDisable = await _workflowRepository.GetOrDefaultAsync(
                workflowVersionedId,
                sleekflowCompanyId);

            if (workflowToDisable is { ActivationStatus: WorkflowActivationStatuses.Deleted })
            {
                throw new SfWorkflowDeletedException(workflowToDisable.WorkflowId);
            }

            var workflow = await _workflowRepository.PatchAndGetAsync(
                workflowVersionedId,
                sleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Replace("/activation_status", WorkflowActivationStatuses.Draft),
                    PatchOperation.Replace("/updated_at", DateTime.UtcNow),
                });

            await _cacheService.RemoveCacheAsync(
                FlowHubCacheKeyBuilder.BuildLatestActiveWorkflowSkeletonsKeyByCompany(sleekflowCompanyId));

            await _bus.Publish(
                new OnVersionedWorkflowDisabledEvent(
                    workflow.WorkflowId,
                    workflow.WorkflowVersionedId,
                    sleekflowCompanyId,
                    StateReasonCodes.VersionedWorkflowDisabled,
                    sleekflowStaff));

            return await GetProxyWorkflowAsync(workflow);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<int> PatchWorkflowAsync(
        string workflowVersionedId,
        string sleekflowCompanyId,
        List<PatchOperation> operations,
        string eTag,
        string? sleekflowStaffId = null)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            new[]
            {
                nameof(WorkflowService),
                workflowVersionedId,
                sleekflowCompanyId
            },
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(10));

        try
        {
            var workflowToPatch = await _workflowRepository.GetOrDefaultAsync(
                workflowVersionedId,
                sleekflowCompanyId);
            if (workflowToPatch?.ETag != eTag)
            {
                return 0;
            }

            await _workflowRepository.PatchAndGetAsync(
                workflowVersionedId,
                sleekflowCompanyId,
                operations);

            await _bus.Publish(
                new OnFlowHubWorkflowPatchedEvent(
                    sleekflowCompanyId,
                    null,
                    workflowToPatch.WorkflowId,
                    workflowVersionedId,
                    workflowToPatch));

            return 1;
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<(ProxyWorkflow Source, ProxyWorkflow Target)> SwapWorkflowsAsync(
        string sourceWorkflowVersionedId,
        string targetWorkflowVersionedId,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            new[]
            {
                nameof(WorkflowService),
                sleekflowCompanyId
            },
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(10));

        var sourceStrings = sourceWorkflowVersionedId.Split("-");
        var sourceWorkflowId = sourceStrings[0];

        var targetStrings = targetWorkflowVersionedId.Split("-");
        var targetWorkflowId = targetStrings[0];

        if (sourceWorkflowId != targetWorkflowId)
        {
            throw new SfWorkflowIdsNotMatchException();
        }

        try
        {
            var workflows =
                await _workflowRepository.GetObjectsAsync(w =>
                    w.ActivationStatus == WorkflowActivationStatuses.Active
                    && w.WorkflowId == sourceWorkflowId);
            var activeWorkflow = workflows.SingleOrDefault();

            if (activeWorkflow == null)
            {
                throw new SfNotFoundObjectException(sourceWorkflowVersionedId);
            }

            if (activeWorkflow.WorkflowVersionedId != sourceWorkflowVersionedId)
            {
                throw new SfWorkflowVersionedIdNotMatchedException();
            }

            if (activeWorkflow.ActivationStatus != WorkflowActivationStatuses.Active)
            {
                throw new SfWorkflowNotActiveException();
            }

            var workflowToDisable = await _workflowRepository.GetOrDefaultAsync(
                sourceWorkflowVersionedId,
                sleekflowCompanyId);

            if (workflowToDisable is { ActivationStatus: WorkflowActivationStatuses.Deleted })
            {
                throw new SfWorkflowDeletedException(workflowToDisable.WorkflowId);
            }

            var workflowToEnable = await _workflowRepository.GetOrDefaultAsync(
                targetWorkflowVersionedId,
                sleekflowCompanyId);

            if (workflowToEnable is { ActivationStatus: WorkflowActivationStatuses.Deleted })
            {
                throw new SfWorkflowDeletedException(workflowToEnable.WorkflowId);
            }

            await _cacheService.RemoveCacheAsync(
                FlowHubCacheKeyBuilder.BuildLatestActiveWorkflowSkeletonsKeyByCompany(sleekflowCompanyId));

            var sourceWorkflow = await _workflowRepository.PatchAndGetAsync(
                sourceWorkflowVersionedId,
                sleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Replace("/activation_status", WorkflowActivationStatuses.Draft),
                    PatchOperation.Replace("/updated_at", DateTime.UtcNow),
                    PatchOperation.Replace("/updated_by", sleekflowStaff),
                });

            var targetWorkflow = await _workflowRepository.PatchAndGetAsync(
                targetWorkflowVersionedId,
                sleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Replace("/activation_status", WorkflowActivationStatuses.Active),
                    PatchOperation.Replace("/updated_at", DateTime.UtcNow),
                    PatchOperation.Replace("/updated_by", sleekflowStaff),
                });

            await _bus.Publish(
                new OnVersionedWorkflowDisabledEvent(
                    sourceWorkflow.WorkflowId,
                    sourceWorkflow.WorkflowVersionedId,
                    sleekflowCompanyId,
                    StateReasonCodes.VersionedWorkflowOutdated,
                    sleekflowStaff));

            var result = (
                await GetProxyWorkflowAsync(sourceWorkflow),
                await GetProxyWorkflowAsync(targetWorkflow));

            await _workflowStepCategoryStatisticsService.RecordWorkflowStepCategoriesAsync(result.Item2);

            return result;
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<List<CompactWorkflow>> MatchWorkflowsAsync(
        string sleekflowCompanyId,
        EventBody eventBody,
        FlowHubConfig flowHubConfig,
        List<ProxyState> existingStates)
    {
        var workflows = await GetAllLatestActiveWorkflowsAsync(sleekflowCompanyId);
        var matchedWorkflows = new HashSet<CompactWorkflow>();
        _logger.LogInformation(
            "there are {WorkflowCount} active workflow(s) in {SleekflowCompanyId}",
            workflows.Count,
            sleekflowCompanyId);
        foreach (var workflow in workflows)
        {
            try
            {
                if (eventBody.EventName == EventNames.OnContactConversationStatusChanged
                    && workflow.Triggers.ConversationStatusChanged?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.ConversationStatusChanged.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnContactCreated
                    && workflow.Triggers.ContactCreated?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.ContactCreated.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnContactLabelRelationshipsChanged
                    && workflow.Triggers.ContactLabelRelationshipsChanged?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.ContactLabelRelationshipsChanged.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnContactListRelationshipsChanged
                    && workflow.Triggers.ContactListRelationshipsChanged?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.ContactListRelationshipsChanged.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnContactUpdated
                    && workflow.Triggers.ContactUpdated?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.ContactUpdated.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnMessageReceived
                    && workflow.Triggers.MessageReceived?.Condition != null)
                {
                    // only evaluate ai agent workflow condition if condition contains additional_data reference
                    EvaluationAdditionalData? additionalData = null;
                    
                    if (ContainsAdditionalDataReference(workflow.Triggers.MessageReceived.Condition))
                    {
                        var canProceedWithAiAgentWorkflow = await CanProceedWithAiAgentWorkflowAsync(workflow, existingStates);
                        additionalData = new EvaluationAdditionalData(canProceedWithAiAgentWorkflow);
                    }

                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.MessageReceived.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin,
                        additionalData);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnMessageSent
                    && workflow.Triggers.MessageSent?.Condition != null)
                {
                    // only evaluate ai agent workflow condition if condition contains additional_data reference
                    EvaluationAdditionalData? additionalData = null;
                    
                    if (ContainsAdditionalDataReference(workflow.Triggers.MessageSent.Condition))
                    {
                        var canProceedWithAiAgentWorkflow = await CanProceedWithAiAgentWorkflowAsync(workflow, existingStates);
                        additionalData = new EvaluationAdditionalData(canProceedWithAiAgentWorkflow);
                    }

                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.MessageSent.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin,
                        additionalData);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnWebhook
                    && eventBody is OnWebhookEventBody onWebhookEventBody
                    && workflow.WorkflowId == onWebhookEventBody.WorkflowId
                    && workflow.Triggers.Webhook?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.Webhook.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnFbIgPostCommentReceived
                    && workflow.Triggers.FbIgPostCommentReceived?.Condition != null)
                {
                    _logger.LogInformation(
                        "found OnFbIgPostCommentReceived event, eventBody: {EventBody}, Condition: {Condition}, companyId: {CompanyId}",
                        System.Text.Json.JsonSerializer.Serialize((OnFbIgPostCommentReceivedEventBody) eventBody),
                        workflow.Triggers.FbIgPostCommentReceived.Condition,
                        sleekflowCompanyId);
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.FbIgPostCommentReceived.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnInstagramMediaCommentReceived
                    && workflow.Triggers.InstagramMediaCommentReceived?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.InstagramMediaCommentReceived.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnSalesforceObjectCreated)
                {
                    // from V2 trigger node
                    if (workflow.Triggers.SalesforceObjectCreated?.Condition != null)
                    {
                        var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                            workflow.Triggers.SalesforceObjectCreated.Condition,
                            eventBody,
                            sleekflowCompanyId,
                            flowHubConfig.Origin);

                        if (evaluatedCondition)
                        {
                            matchedWorkflows.Add(workflow);
                        }
                    }

                    var salesforceEventBody = (OnSalesforceObjectCreatedEventBody) eventBody;

                    if (!salesforceEventBody.IsCustomObject)
                    {
                        if (salesforceEventBody.ObjectType is "Account"
                            && workflow.Triggers.SalesforceAccountCreated?.Condition != null)
                        {
                            var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                                workflow.Triggers.SalesforceAccountCreated.Condition,
                                eventBody,
                                sleekflowCompanyId,
                                flowHubConfig.Origin);

                            if (evaluatedCondition)
                            {
                                matchedWorkflows.Add(workflow);
                            }
                        }

                        if (salesforceEventBody.ObjectType is "Contact"
                            && workflow.Triggers.SalesforceContactCreated?.Condition != null)
                        {
                            var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                                workflow.Triggers.SalesforceContactCreated.Condition,
                                eventBody,
                                sleekflowCompanyId,
                                flowHubConfig.Origin);

                            if (evaluatedCondition)
                            {
                                matchedWorkflows.Add(workflow);
                            }
                        }

                        if (salesforceEventBody.ObjectType is "Lead"
                            && workflow.Triggers.SalesforceLeadCreated?.Condition != null)
                        {
                            var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                                workflow.Triggers.SalesforceLeadCreated.Condition,
                                eventBody,
                                sleekflowCompanyId,
                                flowHubConfig.Origin);

                            if (evaluatedCondition)
                            {
                                matchedWorkflows.Add(workflow);
                            }
                        }

                        if (salesforceEventBody.ObjectType is "Opportunity"
                            && workflow.Triggers.SalesforceOpportunityCreated?.Condition != null)
                        {
                            var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                                workflow.Triggers.SalesforceOpportunityCreated.Condition,
                                eventBody,
                                sleekflowCompanyId,
                                flowHubConfig.Origin);

                            if (evaluatedCondition)
                            {
                                matchedWorkflows.Add(workflow);
                            }
                        }

                        if (salesforceEventBody.ObjectType is "Campaign"
                            && workflow.Triggers.SalesforceCampaignCreated?.Condition != null)
                        {
                            var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                                workflow.Triggers.SalesforceCampaignCreated.Condition,
                                eventBody,
                                sleekflowCompanyId,
                                flowHubConfig.Origin);

                            if (evaluatedCondition)
                            {
                                matchedWorkflows.Add(workflow);
                            }
                        }
                    }

                    if (salesforceEventBody.IsCustomObject
                        && workflow.Triggers.SalesforceCustomObjectCreated?.Condition != null)
                    {
                        var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                            workflow.Triggers.SalesforceCustomObjectCreated.Condition,
                            eventBody,
                            sleekflowCompanyId,
                            flowHubConfig.Origin);

                        if (evaluatedCondition)
                        {
                            matchedWorkflows.Add(workflow);
                        }
                    }
                }

                if (eventBody.EventName == EventNames.OnSalesforceObjectUpdated)
                {
                    // from V2 trigger node
                    if (workflow.Triggers.SalesforceObjectUpdated?.Condition != null)
                    {
                        var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                            workflow.Triggers.SalesforceObjectUpdated.Condition,
                            eventBody,
                            sleekflowCompanyId,
                            flowHubConfig.Origin);

                        if (evaluatedCondition)
                        {
                            matchedWorkflows.Add(workflow);
                        }
                    }

                    var salesforceEventBody = (OnSalesforceObjectUpdatedEventBody) eventBody;

                    if (!salesforceEventBody.IsCustomObject)
                    {
                        if (salesforceEventBody.ObjectType is "Account"
                            && workflow.Triggers.SalesforceAccountUpdated?.Condition != null)
                        {
                            var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                                workflow.Triggers.SalesforceAccountUpdated.Condition,
                                eventBody,
                                sleekflowCompanyId,
                                flowHubConfig.Origin);

                            if (evaluatedCondition)
                            {
                                matchedWorkflows.Add(workflow);
                            }
                        }

                        if (salesforceEventBody.ObjectType is "Contact"
                            && workflow.Triggers.SalesforceContactUpdated?.Condition != null)
                        {
                            var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                                workflow.Triggers.SalesforceContactUpdated.Condition,
                                eventBody,
                                sleekflowCompanyId,
                                flowHubConfig.Origin);

                            if (evaluatedCondition)
                            {
                                matchedWorkflows.Add(workflow);
                            }
                        }

                        if (salesforceEventBody.ObjectType is "Lead"
                            && workflow.Triggers.SalesforceLeadUpdated?.Condition != null)
                        {
                            var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                                workflow.Triggers.SalesforceLeadUpdated.Condition,
                                eventBody,
                                sleekflowCompanyId,
                                flowHubConfig.Origin);

                            if (evaluatedCondition)
                            {
                                matchedWorkflows.Add(workflow);
                            }
                        }

                        if (salesforceEventBody.ObjectType is "Opportunity"
                            && workflow.Triggers.SalesforceOpportunityUpdated?.Condition != null)
                        {
                            var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                                workflow.Triggers.SalesforceOpportunityUpdated.Condition,
                                eventBody,
                                sleekflowCompanyId,
                                flowHubConfig.Origin);

                            if (evaluatedCondition)
                            {
                                matchedWorkflows.Add(workflow);
                            }
                        }

                        if (salesforceEventBody.ObjectType is "Campaign"
                            && workflow.Triggers.SalesforceCampaignUpdated?.Condition != null)
                        {
                            var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                                workflow.Triggers.SalesforceCampaignUpdated.Condition,
                                eventBody,
                                sleekflowCompanyId,
                                flowHubConfig.Origin);

                            if (evaluatedCondition)
                            {
                                matchedWorkflows.Add(workflow);
                            }
                        }
                    }

                    if (salesforceEventBody.IsCustomObject
                        && workflow.Triggers.SalesforceCustomObjectUpdated?.Condition != null)
                    {
                        var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                            workflow.Triggers.SalesforceCustomObjectUpdated.Condition,
                            eventBody,
                            sleekflowCompanyId,
                            flowHubConfig.Origin);

                        if (evaluatedCondition)
                        {
                            matchedWorkflows.Add(workflow);
                        }
                    }
                }

                if (eventBody.EventName == EventNames.OnClickToWhatsAppAdsMessageReceived
                    && workflow.Triggers.ClickToWhatsAppAdsMessageReceived?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.ClickToWhatsAppAdsMessageReceived.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnSchemafulObjectCreated
                    && workflow.Triggers.SchemafulObjectCreated?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.SchemafulObjectCreated.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnSchemafulObjectCreated
                    && eventBody is OnSchemafulObjectCreatedEventBody onSchemafulObjectCreatedEventBody
                    && workflow.Triggers.SchemafulObjectEnrolled?.Condition != null)
                {
                    if (workflow.WorkflowScheduleSettings.ScheduleType ==
                        WorkflowScheduleTypes.SchemafulObjectPropertyBasedDateTime
                        && workflow.WorkflowScheduleSettings.SchemafulObjectPropertyId != null
                        && onSchemafulObjectCreatedEventBody.PropertyValues
                            .ContainsKey(workflow.WorkflowScheduleSettings.SchemafulObjectPropertyId))
                    {
                        var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                            workflow.Triggers.SchemafulObjectEnrolled.Condition,
                            eventBody,
                            sleekflowCompanyId,
                            flowHubConfig.Origin);

                        if (evaluatedCondition)
                        {
                            matchedWorkflows.Add(workflow);
                        }
                    }
                }

                if (eventBody.EventName == EventNames.OnSchemafulObjecUpdated
                    && workflow.Triggers.SchemafulObjectUpdated?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.SchemafulObjectUpdated.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnSchemafulObjecUpdated
                    && eventBody is OnSchemafulObjectUpdatedEventBody onSchemafulObjectUpdatedEventBody
                    && workflow.Triggers.SchemafulObjectEnrolled?.Condition != null)
                {
                    if (workflow.WorkflowScheduleSettings.ScheduleType ==
                        WorkflowScheduleTypes.SchemafulObjectPropertyBasedDateTime
                        && onSchemafulObjectUpdatedEventBody.ChangeEntries
                            .Exists(x => x.PropertyId == workflow.WorkflowScheduleSettings.SchemafulObjectPropertyId))
                    {
                        var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                            workflow.Triggers.SchemafulObjectEnrolled.Condition,
                            eventBody,
                            sleekflowCompanyId,
                            flowHubConfig.Origin);

                        if (evaluatedCondition)
                        {
                            matchedWorkflows.Add(workflow);
                        }
                    }
                }

                // new scheduled workflow logic
                if (eventBody.EventName == EventNames.OnScheduledWorkflowEnrolled
                    && eventBody is OnScheduledWorkflowEnrollmentEventBody onScheduledWorkflowEnrollmentEventBody
                    && onScheduledWorkflowEnrollmentEventBody.WorkflowVersionedId == workflow.WorkflowVersionedId)
                {
                    // we have done the enrolment condition check in WorkflowEnrolmentConditionEvaluator.EvaluateScheduledWorkflowEnrolmentConditionAsync,
                    // which is called from azure durable function
                    matchedWorkflows.Add(workflow);
                }
                // `scheduled date and time`, `contact property date and time`, `custom object date and time` of advanced builder logic
                if (eventBody.EventName == EventNames.OnDateAndTimeArrived
                    && eventBody is OnDateAndTimeArrivedCommonEventBody onDateAndTimeArrivedCommonEventBody
                    && onDateAndTimeArrivedCommonEventBody.WorkflowVersionedId == workflow.WorkflowVersionedId)
                {
                    // condition match conducted already before message sent
                    matchedWorkflows.Add(workflow);
                }

                if (eventBody.EventName == EventNames.OnContactManuallyEnrolled
                    && eventBody is OnContactManuallyEnrolledEventBody onContactManuallyEnrolledEventBody
                    && onContactManuallyEnrolledEventBody.WorkflowVersionedId == workflow.WorkflowVersionedId
                    && workflow.Triggers.ContactManuallyEnrolled?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.ContactManuallyEnrolled.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                // old scheduled workflow logic specifically for contact created event
                if (eventBody.EventName == EventNames.OnContactCreated
                    && eventBody is OnContactCreatedEventBody onContactCreatedEventBody
                    && workflow.WorkflowScheduleSettings.ScheduleType != WorkflowScheduleTypes.None
                    && workflow.Triggers.ContactManuallyEnrolled?.Condition != null)
                {
                    if (workflow.WorkflowScheduleSettings.ScheduleType == WorkflowScheduleTypes.PredefinedDateTime)
                    {
                        // Non-recurring, enroll only if the event occurred before the scheduled time
                        if (workflow.WorkflowScheduleSettings.RecurringSettings is null
                            && onContactCreatedEventBody.CreatedAt <= workflow.WorkflowScheduleSettings.ScheduledAt
                            && !workflow.WorkflowScheduleSettings.IsNonOldScheduledWorkflowSchema())
                        {
                            var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                                workflow.Triggers.ContactManuallyEnrolled.Condition,
                                eventBody,
                                sleekflowCompanyId,
                                flowHubConfig.Origin);

                            if (evaluatedCondition)
                            {
                                matchedWorkflows.Add(workflow);
                            }
                        }

                        // Recurring
                        if (workflow.WorkflowScheduleSettings.RecurringSettings is not null
                            && !workflow.WorkflowScheduleSettings.IsNonOldScheduledWorkflowSchema())
                        {
                            var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                                workflow.Triggers.ContactManuallyEnrolled.Condition,
                                eventBody,
                                sleekflowCompanyId,
                                flowHubConfig.Origin);

                            if (evaluatedCondition)
                            {
                                matchedWorkflows.Add(workflow);
                            }
                        }
                    }
                }

                if (eventBody.EventName == EventNames.OnContactUpdated
                    && eventBody is OnContactUpdatedEventBody onContactUpdatedEventBody
                    && workflow.Triggers.ContactManuallyEnrolled?.Condition != null)
                {
                    if (workflow.WorkflowScheduleSettings.ScheduleType ==
                        WorkflowScheduleTypes.ContactPropertyBasedDateTime
                        && onContactUpdatedEventBody.ChangeEntries
                            .Exists(x => x.PropertyId == workflow.WorkflowScheduleSettings.ContactPropertyId))
                    {
                        var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                            workflow.Triggers.ContactManuallyEnrolled.Condition,
                            eventBody,
                            sleekflowCompanyId,
                            flowHubConfig.Origin);

                        if (evaluatedCondition)
                        {
                            matchedWorkflows.Add(workflow);
                        }
                    }
                }

                if (eventBody.EventName == EventNames.OnContactRecurrentlyEnrolled
                    && eventBody is OnContactRecurrentlyEnrolledEventBody onContactRecurrentlyEnrolledEvent
                    && onContactRecurrentlyEnrolledEvent.WorkflowVersionedId == workflow.WorkflowVersionedId
                    && workflow.Triggers.ContactManuallyEnrolled?.Condition != null
                    && workflow.WorkflowScheduleSettings.IsNewScheduledWorkflowSchema is not true
                    && workflow.WorkflowScheduleSettings
                        .IsOldScheduledWorkflowSchemaFirstRecurringCompleted is not true)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.ContactManuallyEnrolled.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnSalesforceObjectEnrolled)
                {
                    var salesforceEventBody = (OnSalesforceObjectEnrolledEventBody) eventBody;

                    if (salesforceEventBody.WorkflowVersionedId == workflow.WorkflowVersionedId)
                    {
                        if (!salesforceEventBody.IsCustomObject)
                        {
                            if (salesforceEventBody.ObjectType is "Account" &&
                                workflow.Triggers.SalesforceAccountEnrolled?.Condition != null)
                            {
                                var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                                    workflow.Triggers.SalesforceAccountEnrolled.Condition,
                                    eventBody,
                                    sleekflowCompanyId,
                                    flowHubConfig.Origin);

                                if (evaluatedCondition)
                                {
                                    matchedWorkflows.Add(workflow);
                                }
                            }

                            if (salesforceEventBody.ObjectType is "Contact" &&
                                workflow.Triggers.SalesforceContactEnrolled?.Condition != null)
                            {
                                var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                                    workflow.Triggers.SalesforceContactEnrolled.Condition,
                                    eventBody,
                                    sleekflowCompanyId,
                                    flowHubConfig.Origin);

                                if (evaluatedCondition)
                                {
                                    matchedWorkflows.Add(workflow);
                                }
                            }

                            if (salesforceEventBody.ObjectType is "Lead" &&
                                workflow.Triggers.SalesforceLeadEnrolled?.Condition != null)
                            {
                                var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                                    workflow.Triggers.SalesforceLeadEnrolled.Condition,
                                    eventBody,
                                    sleekflowCompanyId,
                                    flowHubConfig.Origin);

                                if (evaluatedCondition)
                                {
                                    matchedWorkflows.Add(workflow);
                                }
                            }

                            if (salesforceEventBody.ObjectType is "Opportunity" &&
                                workflow.Triggers.SalesforceOpportunityEnrolled?.Condition != null)
                            {
                                var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                                    workflow.Triggers.SalesforceOpportunityEnrolled.Condition,
                                    eventBody,
                                    sleekflowCompanyId,
                                    flowHubConfig.Origin);

                                if (evaluatedCondition)
                                {
                                    matchedWorkflows.Add(workflow);
                                }
                            }

                            if (salesforceEventBody.ObjectType is "Campaign" &&
                                workflow.Triggers.SalesforceCampaignEnrolled?.Condition != null)
                            {
                                var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                                    workflow.Triggers.SalesforceCampaignEnrolled.Condition,
                                    eventBody,
                                    sleekflowCompanyId,
                                    flowHubConfig.Origin);

                                if (evaluatedCondition)
                                {
                                    matchedWorkflows.Add(workflow);
                                }
                            }
                        }
                        else
                        {
                            if (workflow.Triggers.SalesforceCustomObjectEnrolled?.Condition != null)
                            {
                                var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                                    workflow.Triggers.SalesforceCustomObjectEnrolled.Condition,
                                    eventBody,
                                    sleekflowCompanyId,
                                    flowHubConfig.Origin);

                                if (evaluatedCondition)
                                {
                                    matchedWorkflows.Add(workflow);
                                }
                            }
                        }
                    }
                }

                if (eventBody.EventName == EventNames.OnContactEnrolled &&
                    eventBody is OnContactEnrolledEventBody onContactEnrolledEvent &&
                    onContactEnrolledEvent.WorkflowVersionedId == workflow.WorkflowVersionedId &&
                    workflow.Triggers.ContactEnrolled?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.ContactEnrolled.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnSchemafulObjectEnrolled &&
                    eventBody is OnSchemafulObjectEnrolledEventBody onSchemafulObjectEnrolledEvent &&
                    onSchemafulObjectEnrolledEvent.WorkflowVersionedId == workflow.WorkflowVersionedId &&
                    workflow.Triggers.SchemafulObjectEnrolled?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.SchemafulObjectEnrolled.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnWhatsappFlowSubmissionMessageReceived
                    && workflow.Triggers.WhatsappFlowSubmissionMessageReceived?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.WhatsappFlowSubmissionMessageReceived.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnMessageStatusUpdated
                    && workflow.Triggers.MessageStatusUpdated?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.MessageStatusUpdated.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnTicketUpdated
                    && workflow.Triggers.TicketUpdated?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.TicketUpdated.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnGoogleSheetsRowCreated
                    && workflow.Triggers.GoogleSheetsRowCreated?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.GoogleSheetsRowCreated.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnGoogleSheetsRowUpdated
                    && workflow.Triggers.GoogleSheetsRowUpdated?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.GoogleSheetsRowUpdated.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnHubspotObjectEnrolled &&
                    eventBody is OnHubspotObjectEnrolledEventBody onHubspotObjectEnrolledEventBody &&
                    onHubspotObjectEnrolledEventBody.WorkflowVersionedId == workflow.WorkflowVersionedId &&
                    workflow.Triggers.HubspotObjectEnrolled?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.HubspotObjectEnrolled.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnHubspotObjectCreated
                    && workflow.Triggers.HubspotObjectCreated?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.HubspotObjectCreated.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnHubspotObjectUpdated
                    && workflow.Triggers.HubspotObjectUpdated?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.HubspotObjectUpdated.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnZohoObjectCreated
                    && workflow.Triggers.ZohoObjectCreated?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.ZohoObjectCreated.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnZohoObjectUpdated
                    && workflow.Triggers.ZohoObjectUpdated?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.ZohoObjectUpdated.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnZohoObjectEnrolled
                    && workflow.Triggers.ZohoObjectEnrolled?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.ZohoObjectEnrolled.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnVtexOrderCreated
                    && workflow.Triggers.VtexOrderCreated?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.VtexOrderCreated.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);
                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnVtexOrderStatusChanged
                    && workflow.Triggers.VtexOrderStatusChanged?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.VtexOrderStatusChanged.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);
                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnVtexOrderEnrolled &&
                    eventBody is OnVtexOrderEnrolledEventBody onVtexOrderEnrolledEventBody &&
                    onVtexOrderEnrolledEventBody.WorkflowVersionedId == workflow.WorkflowVersionedId &&
                    workflow.Triggers.VtexOrderEnrolled?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.VtexOrderEnrolled.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);

                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnTikTokAdsLeadReceived
                    && workflow.Triggers.TikTokAdsLeadReceived?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.TikTokAdsLeadReceived.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);
                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnShopifyOrderCreated
                    && workflow.Triggers.ShopifyOrderCreated?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.ShopifyOrderCreated.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);
                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnShopifyOrderUpdated
                    && workflow.Triggers.ShopifyOrderUpdated?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.ShopifyOrderUpdated.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);
                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnShopifyCustomerCreated
                    && workflow.Triggers.ShopifyCustomerCreated?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.ShopifyCustomerCreated.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);
                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnShopifyCustomerUpdated
                    && workflow.Triggers.ShopifyCustomerUpdated?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.ShopifyCustomerUpdated.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);
                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnShopifyOrderEnrolled
                    && workflow.Triggers.ShopifyOrderEnrolled?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.ShopifyOrderEnrolled.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);
                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnShopifyAbandonedCartCreated
                    && workflow.Triggers.ShopifyAbandonedCartCreated?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.ShopifyAbandonedCartCreated.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);
                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }

                if (eventBody.EventName == EventNames.OnShopifyAbandonedCartUpdated
                    && workflow.Triggers.ShopifyAbandonedCartUpdated?.Condition != null)
                {
                    var evaluatedCondition = await _flowHubEventEvaluator.EvaluateConditionAsync(
                        workflow.Triggers.ShopifyAbandonedCartUpdated.Condition,
                        eventBody,
                        sleekflowCompanyId,
                        flowHubConfig.Origin);
                    if (evaluatedCondition)
                    {
                        matchedWorkflows.Add(workflow);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error occurred while evaluating condition for workflow {WorkflowVersionedId} in company {CompanyId}. Condition: {WorkflowTriggerCondition}",
                    workflow.WorkflowVersionedId,
                    workflow.SleekflowCompanyId,
                    JsonConvert.SerializeObject(workflow.Triggers));
            }
        }

        return matchedWorkflows.ToList();
    }

    public async Task DeleteWorkflowAsync(
        string workflowId,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        _logger.LogInformation(
            "Company {CompanyId} delete workflow performed by staff {StaffId}, workflow affected: {WorkflowId}",
            sleekflowCompanyId,
            sleekflowStaff.SleekflowStaffId,
            workflowId);

        var versionedWorkflows = await _workflowRepository.GetObjectsAsync(
            new QueryDefinition(
                    "SELECT * " +
                    "FROM %%CONTAINER_NAME%% r " +
                    "WHERE r.workflow_id = @workflow_id AND r.sleekflow_company_id = @sleekflow_company_id " +
                    "ORDER BY r.created_at DESC ")
                .WithParameter("@workflow_id", workflowId)
                .WithParameter("@sleekflow_company_id", sleekflowCompanyId));

        await Parallel.ForEachAsync(
            versionedWorkflows,
            new ParallelOptions()
            {
                MaxDegreeOfParallelism = 5
            },
            async (versionedWorkflow, ct) =>
            {
                await _workflowRepository.PatchAsync(
                    versionedWorkflow.Id,
                    sleekflowCompanyId,
                    new List<PatchOperation>()
                    {
                        PatchOperation.Set("/activation_status", WorkflowActivationStatuses.Deleted),
                        PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow)
                    });

                await _bus.Publish(
                    new OnVersionedWorkflowDeletedEvent(
                        sleekflowCompanyId,
                        versionedWorkflow.WorkflowId,
                        versionedWorkflow.WorkflowVersionedId,
                        sleekflowStaff),
                    context =>
                    {
                        context.Delay = TimeSpan.FromDays(
                            _workflowDeletionConfig.WorkflowVersionDeletionCleanupDelayDays);
                    });
            });

        await _workflowAgentConfigMappingService.UpdateWorkflowAgentConfigMappingsAsync(
            sleekflowCompanyId,
            workflowId,
            new List<string>(),
            sleekflowStaff);

        await _cacheService.RemoveCacheAsync(
            FlowHubCacheKeyBuilder.BuildLatestActiveWorkflowSkeletonsKeyByCompany(sleekflowCompanyId));

        await _bus.Publish(
            new OnWorkflowDeletedEvent(
                sleekflowCompanyId,
                workflowId));

        await _bus.Publish(
            new OnFlowHubWorkflowDeletedEvent(
                sleekflowCompanyId,
                sleekflowStaff.SleekflowStaffId,
                workflowId));
    }

    public async Task ScheduleDeleteWorkflowsAsync(
        List<string> workflowIds,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        _logger.LogInformation(
            "Company {CompanyId} schedule delete multiple workflows performed by staff {StaffId}, workflows affected: {WorkflowIds}",
            sleekflowCompanyId,
            sleekflowStaff.SleekflowStaffId,
            JsonConvert.SerializeObject(workflowIds));

        foreach (var workflowId in workflowIds)
        {
            await _bus.Publish(
                new OnScheduleWorkflowDeleteEvent(
                    sleekflowCompanyId,
                    workflowId,
                    sleekflowStaff));
        }
    }

    public async Task DeleteVersionedWorkflowAsync(
        string workflowId,
        string workflowVersionedId,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var (_, versionedWorkflows) = await GetWorkflowAsync(workflowId, sleekflowCompanyId);

        var versionedWorkflow = versionedWorkflows.Find(x => x.Id == workflowVersionedId);

        if (versionedWorkflow != null)
        {
            _logger.LogInformation(
                "Company {CompanyId} delete single workflow version performed by staff {StaffId}, workflow version affected: {WorkflowVersionedId}",
                sleekflowCompanyId,
                sleekflowStaff.SleekflowStaffId,
                workflowVersionedId);

            await _workflowRepository.PatchAsync(
                versionedWorkflow.Id,
                sleekflowCompanyId,
                new List<PatchOperation>()
                {
                    PatchOperation.Set("/activation_status", WorkflowActivationStatuses.Deleted),
                    PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow)
                });

            await _cacheService.RemoveCacheAsync(
                FlowHubCacheKeyBuilder.BuildLatestActiveWorkflowSkeletonsKeyByCompany(sleekflowCompanyId));

            await _bus.Publish(
                new OnVersionedWorkflowDeletedEvent(
                    sleekflowCompanyId,
                    versionedWorkflow.WorkflowId,
                    versionedWorkflow.WorkflowVersionedId,
                    sleekflowStaff),
                context =>
                {
                    context.Delay = TimeSpan.FromDays(_workflowDeletionConfig.WorkflowVersionDeletionCleanupDelayDays);
                });
        }
    }

    public async Task UnsetWorkflowGroupIdAsync(
        string sleekflowCompanyId,
        string workflowGroupId)
    {
        var workflows = await _workflowRepository.GetObjectsAsync(w =>
            w.SleekflowCompanyId == sleekflowCompanyId
            && w.WorkflowGroupId == workflowGroupId);

        if (workflows.Count == 0)
        {
            return;
        }

        await _workflowRepository.ExecuteTransactionalBatchAsync(
            sleekflowCompanyId,
            batch =>
            {
                foreach (var workflow in workflows)
                {
                    batch.PatchItem(
                        workflow.Id,
                        new List<PatchOperation>
                        {
                            PatchOperation.Set("/workflow_group_id", (string?) null)
                        });
                }

                return Task.CompletedTask;
            });
    }

    public async Task<ProxyWorkflow> AssignWorkflowToGroupAsync(
        string workflowVersionedId,
        string sleekflowCompanyId,
        string? workflowGroupId,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        workflowGroupId = string.IsNullOrWhiteSpace(workflowGroupId)
            ? null
            : workflowGroupId;

        await AssertWorkflowGroupIdExists(sleekflowCompanyId, workflowGroupId);

        var targetWorkflow = (await _workflowRepository.GetObjectsAsync(w =>
                w.SleekflowCompanyId == sleekflowCompanyId
                && w.Id == workflowVersionedId))
            .Single();

        var updatedWorkflow = await _workflowRepository.PatchAndGetAsync(
            targetWorkflow.Id,
            sleekflowCompanyId,
            new List<PatchOperation>()
            {
                PatchOperation.Set("/workflow_group_id", workflowGroupId),
                PatchOperation.Replace("/updated_at", DateTime.UtcNow),
                PatchOperation.Replace("/updated_by", sleekflowStaff),
            });

        return await GetProxyWorkflowAsync(updatedWorkflow);
    }

    public async Task<ProxyWorkflow?> GetVersionedWorkflowOrDefaultAsync(
        string sleekflowCompanyId,
        string workflowVersionedId)
    {
        var workflow = await _workflowRepository.GetOrDefaultAsync(
            workflowVersionedId,
            sleekflowCompanyId);

        if (workflow is null)
        {
            return null;
        }

        return await GetProxyWorkflowAsync(workflow);
    }

    public async Task DeleteLeftoverVersionedWorkflowAsync(string sleekflowCompanyId, string workflowVersionedId)
    {
        await _workflowStepsService.DeleteWorkflowStepsAsync(
            sleekflowCompanyId,
            workflowVersionedId);

        await _workflowMetadataService.DeleteWorkflowMetadataAsync(
            sleekflowCompanyId,
            workflowVersionedId);
    }

    private async Task AssertWorkflowGroupIdExists(
        string sleekflowCompanyId,
        string? workflowGroupId)
    {
        if (string.IsNullOrWhiteSpace(workflowGroupId))
        {
            return;
        }

        var workflowGroup = await _workflowGroupService.GetOrDefaultAsync(
            sleekflowCompanyId,
            workflowGroupId);

        _ = workflowGroup ??
            throw new SfNotFoundObjectException(
                workflowGroupId,
                partitionKey: sleekflowCompanyId);
    }

    private async Task<List<ProxyWorkflow>> GetProxyWorkflowsAsync(List<Workflow> workflows)
    {
        if (workflows is not { Count: > 0 })
        {
            return new();
        }

        var proxyWorkflows = await Task.WhenAll(
            workflows.Select(GetProxyWorkflowAsync));

        return proxyWorkflows.ToList();
    }

    public async Task<ProxyWorkflow> GetProxyWorkflowAsync(Workflow workflow)
    {
        var workflowSteps = await _workflowStepsService.GetWorkflowStepsAsync(
            workflow.SleekflowCompanyId,
            workflow.WorkflowVersionedId);

        var workflowMetadata =
            await _workflowMetadataService.GetWorkflowMetadataAsync(
                workflow.SleekflowCompanyId,
                workflow.WorkflowVersionedId);

        return new ProxyWorkflow(
            workflow,
            workflowSteps,
            workflowMetadata);
    }

    private Task<List<CompactWorkflow>> GetAllLatestActiveWorkflowsAsync(string sleekflowCompanyId)
    {
        return _cacheService.CacheAsync(
            FlowHubCacheKeyBuilder.BuildLatestActiveWorkflowSkeletonsKeyByCompany(sleekflowCompanyId),
            async () =>
            {
                var workflowIdAndMaxCreatedAtTuples =
                    await _workflowRepository.GetLatestWorkflowIdCreatedAtTuplesByStatusAsync(sleekflowCompanyId, WorkflowActivationStatuses.Active);

                var activeWorkflowSkeletons = await _workflowRepository.GetObjectsAsync<CompactWorkflow>(
                    new QueryDefinition(
                            """
                            SELECT r.sleekflow_company_id, r.workflow_id, r.workflow_versioned_id, r.workflow_type, r.triggers, r.workflow_enrollment_settings, r.workflow_schedule_settings, r.created_at
                            FROM %%CONTAINER_NAME%% r
                            WHERE r.sleekflow_company_id = @sleekflow_company_id AND r.activation_status = @activation_status
                            ORDER BY r.created_at DESC
                            """)
                        .WithParameter("@sleekflow_company_id", sleekflowCompanyId)
                        .WithParameter("@activation_status", WorkflowActivationStatuses.Active));

                return
                    activeWorkflowSkeletons
                        .Where(w => workflowIdAndMaxCreatedAtTuples
                            .Exists(t => t.WorkflowId == w.WorkflowId && t.MaxCreatedAt == w.CreatedAt))
                        .ToList();
            },
            TimeSpan.FromHours(1));
    }

    private async Task<bool> CanProceedWithAiAgentWorkflowAsync(CompactWorkflow workflow, List<ProxyState> existingStates)
    {
        if (workflow.WorkflowType != WorkflowType.AIAgent)
        {
            return true;
        }

        try
        {
            var (state, step) = StateUtils.GetParentState(existingStates, workflow.WorkflowId, _logger);

            #region check if step start but not complete

            // if the step not start, don't enroll ai workflow
            var startStepHistory = await _stepExecutionService.GetStepExecutionsByFilterAsync(
                state.Identity.SleekflowCompanyId,
                state.Id,
                step.Id,
                StepExecutionStatuses.Started);
            if (startStepHistory.Count == 0)
            {
                return false;
            }

            // if the step complete, don't enroll ai workflow
            var completeStepHistory = await _stepExecutionService.GetStepExecutionsByFilterAsync(
                state.Identity.SleekflowCompanyId,
                state.Id,
                step.Id,
                StepExecutionStatuses.Complete);
            if (completeStepHistory.Count == 1)
            {
                return false;
            }
            #endregion

            return state.StateStatus == StateStatuses.Running;
        }
        catch (Exception _)
        {
            return false;
        }
    }

    private bool ContainsAdditionalDataReference(string? condition)
    {
        if (string.IsNullOrWhiteSpace(condition))
        {
            return false;
        }
        
        // check if condition contains additional_data reference
        return condition.Contains("ctx?.additional_data") || 
               condition.Contains("ctx.additional_data") ||
               condition.Contains("additional_data");
    }
}