﻿namespace Sleekflow.Mvc.Telemetries.Constants;

public static class TraceEventNames
{
    public const string WhatsappCloudApiWebhookReceived = "whatsapp_cloud_api_webhook_received";
    public const string WhatsappCloudApiWebhookDropped = "whatsapp_cloud_api_webhook_dropped";
    public const string WhatsappCloudApiWebhookHandled = "whatsapp_cloud_api_webhook_handled";

    public const string SalesforceDataApiErrorResponseReceived = "salesforce_data_api_error_response_received";
    public const string SalesforceDataApiInvalidOutputReceived = "salesforce_data_api_invalid_output_received";
    public const string SalesforceOauthInitialAuthenticationErrorResponseReceived = "salesforce_oauth_initial_authentication_error_response_received";
    public const string SalesforceOauthReAuthenticationErrorResponseReceived = "salesforce_oauth_re_authentication_error_response_received";
    public const string SalesforceOauthCallbackErrorResponseReceived = "salesforce_oauth_callback_error_response_received";

    // Salesforce consumer events
    public const string SalesforceCreateObjectRequestReceived = "salesforce_create_object_request_received";
    public const string SalesforceCreateObjectRequestHandled = "salesforce_create_object_request_handled";
    public const string SalesforceUpdateObjectRequestReceived = "salesforce_update_object_request_received";
    public const string SalesforceUpdateObjectRequestHandled = "salesforce_update_object_request_handled";
    public const string SalesforceSearchObjectRequestReceived = "salesforce_search_object_request_received";
    public const string SalesforceSearchObjectRequestHandled = "salesforce_search_object_request_handled";
    public const string SalesforceCreateObjectRequestFailed = "salesforce_create_object_request_failed";
    public const string SalesforceUpdateObjectRequestFailed = "salesforce_update_object_request_failed";
    public const string SalesforceSearchObjectRequestFailed = "salesforce_search_object_request_failed";

    public const string HubspotDataApiErrorResponseReceived = "hubspot_data_api_error_response_received";
    public const string HubspotDataApiInvalidOutputReceived = "hubspot_data_api_invalid_output_received";
    public const string HubspotOauthReAuthenticationErrorResponseReceived = "hubspot_oauth_re_authentication_error_response_received";
    public const string HubspotOauthCallbackErrorResponseReceived = "hubspot_oauth_callback_error_response_received";

    // Hubspot consumer events
    public const string HubspotCreateObjectRequestReceived = "hubspot_create_object_request_received";
    public const string HubspotCreateObjectRequestHandled = "hubspot_create_object_request_handled";
    public const string HubspotUpdateObjectRequestReceived = "hubspot_update_object_request_received";
    public const string HubspotUpdateObjectRequestHandled = "hubspot_update_object_request_handled";
    public const string HubspotSearchObjectRequestReceived = "hubspot_search_object_request_received";
    public const string HubspotSearchObjectRequestHandled = "hubspot_search_object_request_handled";
    public const string HubspotCreateObjectRequestFailed = "hubspot_create_object_request_failed";
    public const string HubspotUpdateObjectRequestFailed = "hubspot_update_object_request_failed";
    public const string HubspotSearchObjectRequestFailed = "hubspot_search_object_request_failed";

    public const string ZohoDataApiErrorResponseReceived = "zoho_data_api_error_response_received";
    public const string ZohoDataApiInvalidOutputReceived = "zoho_data_api_invalid_output_received";
    public const string ZohoOauthCallbackErrorResponseReceived = "zoho_oauth_callback_error_response_received";
    public const string ZohoOauthInitialAuthenticationErrorResponseReceived = "zoho_oauth_initial_authentication_error_response_received";
    public const string ZohoOauthReAuthenticationErrorResponseReceived = "zoho_oauth_re_authentication_error_response_received";

    // Zoho consumer events
    public const string ZohoCreateObjectRequestReceived = "zoho_create_object_request_received";
    public const string ZohoCreateObjectRequestHandled = "zoho_create_object_request_handled";
    public const string ZohoUpdateObjectRequestReceived = "zoho_update_object_request_received";
    public const string ZohoUpdateObjectRequestHandled = "zoho_update_object_request_handled";
    public const string ZohoSearchObjectRequestReceived = "zoho_search_object_request_received";
    public const string ZohoSearchObjectRequestHandled = "zoho_search_object_request_handled";
    public const string ZohoCreateObjectRequestFailed = "zoho_create_object_request_failed";
    public const string ZohoUpdateObjectRequestFailed = "zoho_update_object_request_failed";
    public const string ZohoSearchObjectRequestFailed = "zoho_search_object_request_failed";

    public const string GoogleSheetsApiTooManyRequestsErrorReceived = "google_sheets_api_too_many_requests_error_received";
    public const string GoogleSheetsOauthApiErrorResponseReceived = "google_sheets_oauth_api_error_response_received";

    // Google Sheets consumer events
    public const string GoogleSheetsCreateRowRequestReceived = "google_sheets_create_row_request_received";
    public const string GoogleSheetsCreateRowRequestHandled = "google_sheets_create_row_request_handled";
    public const string GoogleSheetsUpdateRowRequestReceived = "google_sheets_update_row_request_received";
    public const string GoogleSheetsUpdateRowRequestHandled = "google_sheets_update_row_request_handled";
    public const string GoogleSheetsSearchRowRequestReceived = "google_sheets_search_row_request_received";
    public const string GoogleSheetsSearchRowRequestHandled = "google_sheets_search_row_request_handled";
    public const string GoogleSheetsCreateRowRequestFailed = "google_sheets_create_row_request_failed";
    public const string GoogleSheetsUpdateRowRequestFailed = "google_sheets_update_row_request_failed";
    public const string GoogleSheetsSearchRowRequestFailed = "google_sheets_search_row_request_failed";

    // TenantHub company staff events
    public const string TenantHubCompanyStaffDeleteCompleted = "tenanthub_company_staff_delete_completed";
    public const string TenantHubCompanyStaffDeleteFailed = "tenanthub_company_staff_delete_failed";

    // TenantHub invitation events
    public const string TenantHubEmailInvitationCompleted = "tenanthub_email_invitation_completed";
    public const string TenantHubEmailInvitationFailed = "tenanthub_email_invitation_failed";
    public const string TenantHubLinkInvitationCreated = "tenant_hub_link_invitation_created";
    public const string TenantHubLinkInvitationCompleted = "tenant_hub_link_invitation_completed";
    public const string TenantHubLinkInvitationFailed = "tenant_hub_link_invitation_failed";
    public const string TenantHubEmailInvitationCreated = "tenant_hub_email_invitation_created";
    public const string TenantHubEmailInvitationCreateFailed = "tenant_hub_email_invitation_create_failed";

    // TenantHub company registration events
    public const string TenantHubCompanyRegistrationStarted = "tenanthub_company_registration_started";
    public const string TenantHubCompanyRegistrationCompleted = "tenanthub_company_registration_completed";
    public const string TenantHubCompanyRegistrationFailed = "tenanthub_company_registration_failed";
    public const string TenantHubCompanyRegistrationValidationFailed = "tenanthub_company_registration_validation_failed";

    // FlowHub workflow execution status events
    public const string FlowHubWorkflowExecutionStarted = "flowhub_workflow_execution_started";
    public const string FlowHubWorkflowExecutionCompleted = "flowhub_workflow_execution_completed";
}