using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.TenantHub.Auth0;
using Sleekflow.TenantHub.Auth0;
using Sleekflow.TenantHub.Client;
using Sleekflow.TenantHub.Features;
using Sleekflow.TenantHub.Features.EnabledFeatures;
using Sleekflow.TenantHub.Models.Auth0s;
using Sleekflow.TenantHub.Models.Constants;
using Sleekflow.TenantHub.Models.Features;
using Sleekflow.TenantHub.Roles;
using Auth0User = Auth0.ManagementApi.Models.User;
using Auth0Connection = Auth0.ManagementApi.Models.Connection;

namespace Sleekflow.TenantHub.Triggers.Webhooks.Auth0;

[TriggerGroup(ControllerNames.Auth0, $"{BasePaths.Webhooks}")]
public class RequiresMfa
    : ITrigger<RequiresMfa.RequiresMfaInput, RequiresMfa.RequiresMfaOutput>
{
    private readonly ITravisBackendClient _travisBackendClient;
    private readonly IAuth0UserManagementService _auth0UserManagementService;
    private readonly IFeatureService _featureService;
    private readonly IEnabledFeatureService _enabledFeatureService;
    private readonly IRoleService _roleService;
    private readonly ILogger<RequiresMfa> _logger;

    public RequiresMfa(
        ITravisBackendClient travisBackendClient,
        IAuth0UserManagementService auth0UserManagementService,
        IFeatureService featureService,
        IEnabledFeatureService enabledFeatureService,
        IRoleService roleService,
        ILogger<RequiresMfa> logger)
    {
        _travisBackendClient = travisBackendClient;
        _auth0UserManagementService = auth0UserManagementService;
        _featureService = featureService;
        _enabledFeatureService = enabledFeatureService;
        _roleService = roleService;
        _logger = logger;
    }

    public class RequiresMfaInput
    {
        [Required]
        [JsonProperty("token")]
        public string Token { get; set; }

        [Required]
        [JsonProperty("auth0_event_user")]
        [Validations.ValidateObject]
        public Auth0User User { get; set; }

        [JsonProperty("auth0_client_name")]
        public string? Auth0ClientName { get; set; }

        [Required]
        [JsonProperty("connection_strategy")]
        public string ConnectionStrategy { get; set; }

        [Required]
        [JsonProperty("connection_name")]
        public string ConnectionName { get; set; }

        [JsonConstructor]
        public RequiresMfaInput(string token, Auth0User user, string connectionStrategy, string connectionName)
        {
            Token = token;
            User = user;
            ConnectionStrategy = connectionStrategy;
            ConnectionName = connectionName;
        }
    }

    public class RequiresMfaOutput
    {
        [JsonProperty("is_mfa_required")]
        public bool IsMfaRequired { get; set; }

        [JsonProperty("allow_remember_browser")]
        public bool AllowRememberBrowser { get; set; }

        [JsonConstructor]
        public RequiresMfaOutput(bool isMfaRequired, bool allowRememberBrowser = true)
        {
            IsMfaRequired = isMfaRequired;
            AllowRememberBrowser = allowRememberBrowser;
        }
    }

    public async Task<RequiresMfaOutput> F(RequiresMfaInput input)
    {
        var isValid = await _auth0UserManagementService.ValidateAuth0ActionToken(input.Token);
        if (!isValid)
        {
            throw new SfInvalidEventTokenException();
        }

        var appMetadata = Auth0UserManagementService.GetAuth0AppMetadata(input.User);
        if (appMetadata is null)
        {
            _logger.LogCritical(
                "[{RequiresMfaName}] AppMetadata of user {UserEmail} is not updated",
                nameof(RequiresMfa),
                input.User?.Email);
            return new RequiresMfaOutput(false);
        }

        // Check if user is using dive feature.
        if (appMetadata.LoginAsUser is not null
            && appMetadata.LoginAsUser?.SleekflowUserId is not null
            && appMetadata.LoginAsUser?.CompanyId is not null
            && appMetadata.Roles is not null
            && appMetadata.Roles.Contains(ApplicationUserRoles.InternalCmsSuperUser))
        {
            if (appMetadata.LoginAsUser.ExpireAt > DateTime.UtcNow)
            {
                return new RequiresMfaOutput(false);
            }
        }

        var searchId = !string.IsNullOrEmpty(appMetadata.TenantHubUserId)
            ? appMetadata.TenantHubUserId
            : appMetadata.SleekflowId ?? string.Empty;
        var user = await _auth0UserManagementService.FindUserAsync(searchId);

        if (user is null)
        {
            _logger.LogError(
                "[{RequiresMfaName}] User not found with UserId: {SearchUserId}",
                nameof(RequiresMfa),
                searchId);
            return new RequiresMfaOutput(false);
        }

        if (user.UserWorkspaces.Count == 0)
        {
            try
            {
                _logger.LogInformation(
                    "[{RequiresMfaName}] User {UserEmail} is not in any workspace. Checking if user is in travis backend.",
                    nameof(RequiresMfa),
                    user.Email);
                var travisBackendUser = await _travisBackendClient.GetUserAsync(searchId);
                if (travisBackendUser == null)
                {
                    return new RequiresMfaOutput(false);
                }

                #region Tempoary logic for non migrate user

                _logger.LogInformation(
                    "[{RequiresMfaName}] Fetched user {Email} from travis backend. Checking if 2FA is enabled. CompanyId: {CompanyId}",
                    nameof(RequiresMfa),
                    travisBackendUser.Email,
                    travisBackendUser.CompanyId);
                var allFeatures = await _featureService.GetAllAsync();
                var mfa = allFeatures.First(f => f.Name == "2FA");
                var mfaDisableRememberBrowser = allFeatures.First(f => f.Name == "2FA_DisableRememberBrowser");

                var isCompanyEnabled = await _enabledFeatureService.IsFeatureEnabledAsync(
                    travisBackendUser.CompanyId!,
                    mfa.Id);

                if (isCompanyEnabled)
                {
                    var isDisableRememberBrowser = await _enabledFeatureService.IsFeatureEnabledAsync(
                        travisBackendUser.CompanyId!,
                        mfaDisableRememberBrowser.Id);
                    var roles = await _roleService.GetAllDefaultRolesAsync();
                    var isEnabledForRolesDict = new Dictionary<string, bool>();
                    foreach (var role in roles)
                    {
                        var isFeatureEnabledForRole = await _enabledFeatureService.IsFeatureEnabledAsync(
                            travisBackendUser.CompanyId!,
                            mfa.Id,
                            RoleComparisonConditions.ContainsAny,
                            new FeatureAssociation(
                                sleekflowRoleIds: new List<string>
                                {
                                    role.Id
                                }));

                        isEnabledForRolesDict.Add(role.Name, isFeatureEnabledForRole);
                    }

                    // Temporary disable all enterprise connection
                    if (input.ConnectionStrategy is "adfs" or "oidc")
                    {
                        return new RequiresMfaOutput(false);
                    }

                    _logger.LogInformation(
                        "[{RequiresMfaName}] 2FA Feature enabled for user {Email}: company {CompanyId}, isDisableRememberBrowser: {IsDisableRememberBrowser}",
                        nameof(RequiresMfa),
                        travisBackendUser.Email,
                        travisBackendUser.CompanyId,
                        isDisableRememberBrowser);
                    return new RequiresMfaOutput(
                        isEnabledForRolesDict[travisBackendUser.RoleType],
                        !isDisableRememberBrowser);
                }
            }
            catch (Exception e)
            {
                _logger.LogCritical(
                    "[{RequiresMfaName}] user {UserEmail} not found in travis backend. {EMessage}",
                    nameof(RequiresMfa),
                    user.Email,
                    e.Message);
                return new RequiresMfaOutput(false);
            }

            #endregion
        }

        // For after migrated user
        var features = await _featureService.GetAllAsync();
        var mfaFeature = features.First(f => f.Name == "2FA");
        var mfaDisableRememberBrowserFeature = features.First(f => f.Name == "2FA_DisableRememberBrowser");
        _logger.LogInformation(
            "[{RequiresMfaName}] User {UserEmail} have workspace {SerializeObject}. Checking if 2FA is enabled",
            nameof(RequiresMfa),
            user.Email,
            JsonConvert.SerializeObject(user.UserWorkspaces));

        foreach (var workspace in user.UserWorkspaces)
        {
            var isCompanyEnabled = await _enabledFeatureService.IsFeatureEnabledAsync(
                workspace.SleekflowCompanyId,
                mfaFeature.Id);
            var isDisableRememberBrowser = await _enabledFeatureService.IsFeatureEnabledAsync(
                workspace.SleekflowCompanyId!,
                mfaDisableRememberBrowserFeature.Id);

            _logger.LogInformation(
                "[{RequiresMfaName}] Checking User {Email} with company {CompanyId}",
                nameof(RequiresMfa),
                user.Email,
                workspace.SleekflowCompanyId);

            if (isCompanyEnabled)
            {
                var isFeatureEnabled = await _enabledFeatureService.IsFeatureEnabledAsync(
                    workspace.SleekflowCompanyId,
                    mfaFeature.Id,
                    RoleComparisonConditions.ContainsAny,
                    new FeatureAssociation(
                        sleekflowRoleIds: workspace.SleekflowRoleIds));

                return new RequiresMfaOutput(isFeatureEnabled);
            }
        }

        return new RequiresMfaOutput(false);
    }
}