openapi: 3.0.1
info:
  title: Sleekflow 1.0
  version: '1.0'
servers:
  - url: https://localhost:7070
    description: Local
  - url: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/crm-hub
    description: Dev Apigw
  - url: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/crm-hub
    description: Prod Apigw
paths:
  /Blobs/CreateBlobDownloadSasUrls:
    post:
      tags:
        - Blobs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBlobDownloadSasUrlsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateBlobDownloadSasUrlsOutputOutput'
  /Blobs/CreateBlobUploadSasUrls:
    post:
      tags:
        - Blobs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBlobUploadSasUrlsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateBlobUploadSasUrlsOutputOutput'
  /Blobs/DeleteBlobs:
    post:
      tags:
        - Blobs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteBlobsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteBlobsOutputOutput'
  /CrmHubConfigs/GetCrmHubConfig:
    post:
      tags:
        - CrmHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCrmHubConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCrmHubConfigOutputOutput'
  /CrmHubConfigs/InitializeCrmHubConfig:
    post:
      tags:
        - CrmHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitializeCrmHubConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InitializeCrmHubConfigOutputOutput'
  /CrmHubConfigs/UpdateCrmHubConfig:
    post:
      tags:
        - CrmHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCrmHubConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateCrmHubConfigOutputOutput'
  /CrmHubConfigs/UpdateCrmHubConfigUsageLimitOffset:
    post:
      tags:
        - CrmHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCrmHubConfigUsageLimitOffsetInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateCrmHubConfigUsageLimitOffsetOutputOutput'
  /InflowActions/CreateSalesforceObject:
    post:
      tags:
        - InflowActions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSalesforceObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateSalesforceObjectOutputOutput'
  /InflowActions/EnrollContactsToFlowHub:
    post:
      tags:
        - InflowActions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnrollContactsToFlowHubInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnrollContactsToFlowHubOutputOutput'
  /InflowActions/EnrollContactToFlowHub:
    post:
      tags:
        - InflowActions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnrollContactToFlowHubInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnrollContactToFlowHubOutputOutput'
  /InflowActions/GetLoopThroughHubspotObjectsProgress:
    post:
      tags:
        - InflowActions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetLoopThroughHubspotObjectsProgressInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLoopThroughHubspotObjectsProgressOutputOutput'
  /InflowActions/GetLoopThroughSalesforceObjectsProgress:
    post:
      tags:
        - InflowActions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetLoopThroughSalesforceObjectsProgressInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLoopThroughSalesforceObjectsProgressOutputOutput'
  /InflowActions/GetLoopThroughSchemafulObjectsProgress:
    post:
      tags:
        - InflowActions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetLoopThroughSchemafulObjectsProgressInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLoopThroughSchemafulObjectsProgressOutputOutput'
  /InflowActions/GetSalesforceCustomObjectTypes:
    post:
      tags:
        - InflowActions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSalesforceCustomObjectTypesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSalesforceCustomObjectTypesOutputOutput'
  /InflowActions/LoopThroughAndEnrollHubspotObjectsToFlowHub:
    post:
      tags:
        - InflowActions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoopThroughAndEnrollHubspotObjectsToFlowHubInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoopThroughAndEnrollHubspotObjectsToFlowHubOutputOutput'
  /InflowActions/LoopThroughAndEnrollSalesforceObjectsToFlowHub:
    post:
      tags:
        - InflowActions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoopThroughAndEnrollSalesforceObjectsToFlowHubInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoopThroughAndEnrollSalesforceObjectsToFlowHubOutputOutput'
  /InflowActions/LoopThroughAndEnrollSchemafulObjectsToFlowHub:
    post:
      tags:
        - InflowActions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoopThroughAndEnrollSchemafulObjectsToFlowHubInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoopThroughAndEnrollSchemafulObjectsToFlowHubOutputOutput'
  /InflowActions/SearchSalesforceObject:
    post:
      tags:
        - InflowActions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchSalesforceObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchSalesforceObjectOutputOutput'
  /InflowActions/TerminateLoopThroughHubspotObjects:
    post:
      tags:
        - InflowActions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TerminateLoopThroughHubspotObjectsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TerminateLoopThroughHubspotObjectsOutputOutput'
  /InflowActions/TerminateLoopThroughSalesforceObjects:
    post:
      tags:
        - InflowActions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TerminateLoopThroughSalesforceObjectsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TerminateLoopThroughSalesforceObjectsOutputOutput'
  /InflowActions/TerminateLoopThroughSchemafulObjects:
    post:
      tags:
        - InflowActions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TerminateLoopThroughSchemafulObjectsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TerminateLoopThroughSchemafulObjectsOutputOutput'
  /InflowActions/UpdateSalesforceObject:
    post:
      tags:
        - InflowActions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSalesforceObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateSalesforceObjectOutputOutput'
  /Internals/GetPropertyValuesByContactIds:
    post:
      tags:
        - Internals
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetPropertyValuesByContactIdsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPropertyValuesByContactIdsOutputOutput'
  /Internals/LoopThroughAndEnrollSchemafulObjectsToFlowHubBatch:
    post:
      tags:
        - Internals
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchOutputOutput'
  /Migrations/MigrateProviderIntegrationToFlowHub:
    post:
      tags:
        - Migrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MigrateProviderIntegrationToFlowHubInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MigrateProviderIntegrationToFlowHubOutputOutput'
  /Migrations/PrepareMigrationToFlowHub:
    post:
      tags:
        - Migrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrepareMigrationToFlowHubInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrepareMigrationToFlowHubOutputOutput'
  /Objects/AssociateObject:
    post:
      tags:
        - Objects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AssociateObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssociateObjectOutputOutput'
  /Objects/GetObjects:
    post:
      tags:
        - Objects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetObjectsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetObjectsOutputOutput'
  /Objects/GetObjectsByIdentities:
    post:
      tags:
        - Objects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetObjectsByIdentitiesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetObjectsByIdentitiesOutputOutput'
  /Objects/GetObjectsCount:
    post:
      tags:
        - Objects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetObjectsCountInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetObjectsCountOutputOutput'
  /Objects/GetObjectsCountV2:
    post:
      tags:
        - Objects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetObjectsCountV2Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetObjectsCountV2OutputOutput'
  /Objects/GetObjectsV2:
    post:
      tags:
        - Objects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetObjectsV2Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetObjectsV2OutputOutput'
  /Objects/GetObjectsV3:
    post:
      tags:
        - Objects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetObjectsV3Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetObjectsV3OutputOutput'
  /Objects/GetProviders:
    post:
      tags:
        - Objects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProvidersInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProvidersOutputOutput'
  /Objects/GetTypeFields:
    post:
      tags:
        - Objects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetTypeFieldsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTypeFieldsOutputOutput'
  /Objects/GetTypeFieldValues:
    post:
      tags:
        - Objects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetTypeFieldValuesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTypeFieldValuesOutputOutput'
  /Objects/UnassociateObject:
    post:
      tags:
        - Objects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UnassociateObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnassociateObjectOutputOutput'
  /Objects/UpsertObject:
    post:
      tags:
        - Objects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpsertObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpsertObjectOutputOutput'
  /Providers/CreateObject:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateObjectOutputOutput'
  /Providers/CreateProviderUserMappingConfig:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateProviderUserMappingConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateProviderUserMappingConfigOutputOutput'
  /Providers/DeactivateProvider:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeactivateProviderInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeactivateProviderOutputOutput'
  /Providers/DeleteProviderConnection:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteProviderConnectionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteProviderConnectionOutputOutput'
  /Providers/GetProviderConfig:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProviderConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProviderConfigOutputOutput'
  /Providers/GetProviderConfigs:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProviderConfigsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProviderConfigsOutputOutput'
  /Providers/GetProviderConnectionExternalResources:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProviderConnectionExternalResourcesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProviderConnectionExternalResourcesOutputOutput'
  /Providers/GetProviderConnections:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProviderConnectionsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProviderConnectionsOutputOutput'
  /Providers/GetProviderCustomObjectTypes:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProviderCustomObjectTypesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProviderCustomObjectTypesOutputOutput'
  /Providers/GetProviderObjectDirectRefUrl:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProviderObjectDirectRefUrlInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProviderObjectDirectRefUrlOutputOutput'
  /Providers/GetProviderObjectsCount:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProviderObjectsCountInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProviderObjectsCountOutputOutput'
  /Providers/GetProviderObjectsCountV2:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProviderObjectsCountV2Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProviderObjectsCountV2OutputOutput'
  /Providers/GetProviderSubscriptions:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProviderSubscriptionsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProviderSubscriptionsOutputOutput'
  /Providers/GetProviderSupportedTypes:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProviderSupportedTypesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProviderSupportedTypesOutputOutput'
  /Providers/GetProviderSyncObjectsProgress:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProviderSyncObjectsProgressInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProviderSyncObjectsProgressOutputOutput'
  /Providers/GetProviderTypeFields:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProviderTypeFieldsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProviderTypeFieldsOutputOutput'
  /Providers/GetProviderTypeFieldsV2:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProviderTypeFieldsV2Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProviderTypeFieldsV2OutputOutput'
  /Providers/GetProviderUserMappingConfig:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProviderUserMappingConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProviderUserMappingConfigOutputOutput'
  /Providers/InitProvider:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitProviderInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InitProviderOutputOutput'
  /Providers/InitProviderTypesSync:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitProviderTypesSyncInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InitProviderTypesSyncOutputOutput'
  /Providers/InitProviderTypeSync:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitProviderTypeSyncInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InitProviderTypeSyncOutputOutput'
  /Providers/InitProviderV2:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitProviderV2Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InitProviderV2OutputOutput'
  /Providers/PreviewObjects:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PreviewObjectsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreviewObjectsOutputOutput'
  /Providers/PreviewObjectsV2:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PreviewObjectsV2Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreviewObjectsV2OutputOutput'
  /Providers/PropagateObjectUpdateToProvider:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PropagateObjectUpdateToProviderInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PropagateObjectUpdateToProviderOutputOutput'
  /Providers/ReInitProviderConnection:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReInitProviderConnectionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReInitProviderConnectionOutputOutput'
  /Providers/RenameProviderConnection:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RenameProviderConnectionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RenameProviderConnectionOutputOutput'
  /Providers/SearchObjects:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchObjectsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchObjectsOutputOutput'
  /Providers/TriggerProviderSyncObject:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TriggerProviderSyncObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TriggerProviderSyncObjectOutputOutput'
  /Providers/TriggerProviderSyncObjects:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TriggerProviderSyncObjectsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TriggerProviderSyncObjectsOutputOutput'
  /Providers/UpdateObject:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateObjectOutputOutput'
  /Providers/UpdateProvider:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProviderInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateProviderOutputOutput'
  /Providers/UpdateProviderUserMappingConfig:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProviderUserMappingConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateProviderUserMappingConfigOutputOutput'
  /Public/healthz:
    get:
      tags:
        - Public
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /SchemafulObjects/CountSchemafulObjects:
    post:
      tags:
        - SchemafulObjects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CountSchemafulObjectsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CountSchemafulObjectsOutputOutput'
  /SchemafulObjects/CreateSchemafulObject:
    post:
      tags:
        - SchemafulObjects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSchemafulObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateSchemafulObjectOutputOutput'
  /SchemafulObjects/DeleteSchemafulObjects:
    post:
      tags:
        - SchemafulObjects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteSchemafulObjectsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteSchemafulObjectsOutputOutput'
  /SchemafulObjects/EnqueueCreatePropertyDrivenSchemafulObject:
    post:
      tags:
        - SchemafulObjects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnqueueCreatePropertyDrivenSchemafulObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnqueueCreatePropertyDrivenSchemafulObjectOutputOutput'
  /SchemafulObjects/GetSchemafulObject:
    post:
      tags:
        - SchemafulObjects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSchemafulObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSchemafulObjectOutputOutput'
  /SchemafulObjects/GetSchemafulObjectCsvTemplate:
    post:
      tags:
        - SchemafulObjects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSchemafulObjectCsvTemplateInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSchemafulObjectCsvTemplateOutputOutput'
  /SchemafulObjects/GetSchemafulObjects:
    post:
      tags:
        - SchemafulObjects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSchemafulObjectsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSchemafulObjectsOutputOutput'
  /SchemafulObjects/GetSchemafulObjectSample:
    post:
      tags:
        - SchemafulObjects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSchemafulObjectSampleInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSchemafulObjectSampleOutputOutput'
  /SchemafulObjects/PartialUpdatePropertyValues:
    post:
      tags:
        - SchemafulObjects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PartialUpdatePropertyValuesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PartialUpdatePropertyValuesOutputOutput'
  /SchemafulObjects/ProcessSchemafulObjectCsvBatch:
    post:
      tags:
        - SchemafulObjects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProcessSchemafulObjectCsvBatchInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProcessSchemafulObjectCsvBatchOutputOutput'
  /SchemafulObjects/UpdateSchemafulObject:
    post:
      tags:
        - SchemafulObjects
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSchemafulObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateSchemafulObjectOutputOutput'
  /Schemas/CreateSchema:
    post:
      tags:
        - Schemas
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSchemaInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateSchemaOutputOutput'
  /Schemas/DeleteSchema:
    post:
      tags:
        - Schemas
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteSchemaInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteSchemaOutputOutput'
  /Schemas/GetSchema:
    post:
      tags:
        - Schemas
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSchemaInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSchemaOutputOutput'
  /Schemas/GetSchemas:
    post:
      tags:
        - Schemas
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSchemasInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSchemasOutputOutput'
  /Schemas/GetUsageStatistics:
    post:
      tags:
        - Schemas
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetUsageStatisticsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetUsageStatisticsOutputOutput'
  /Schemas/RearrangeSchemaOrder:
    post:
      tags:
        - Schemas
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RearrangeSchemaOrderInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RearrangeSchemaOrderOutputOutput'
  /Schemas/UpdateSchema:
    post:
      tags:
        - Schemas
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSchemaInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateSchemaOutputOutput'
  /UnifyRules/GetUnifyRules:
    post:
      tags:
        - UnifyRules
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetUnifyRulesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetUnifyRulesOutputOutput'
  /UnifyRules/UpsertUnifyRules:
    post:
      tags:
        - UnifyRules
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpsertUnifyRulesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpsertUnifyRulesOutputOutput'
  /Webhooks/GetWebhooks:
    post:
      tags:
        - Webhooks
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWebhooksInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWebhooksOutputOutput'
  /Webhooks/RegisterWebhook:
    post:
      tags:
        - Webhooks
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterWebhookInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterWebhookOutputOutput'
  /Webhooks/RemoveWebhooks:
    post:
      tags:
        - Webhooks
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RemoveWebhooksInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RemoveWebhooksOutputOutput'
components:
  schemas:
    ArrayExist:
      required:
        - field_name
        - filters
        - is_property_value
      type: object
      properties:
        field_name:
          maxLength: 128
          minLength: 1
          pattern: '^[a-zA-Z0-9_\[\]\.:]+$'
          type: string
        is_property_value:
          type: boolean
        filters:
          type: array
          items:
            $ref: '#/components/schemas/SchemafulObjectFilter'
      additionalProperties: false
    AssociateObjectInput:
      required:
        - entity_type_name
        - object_id
        - provider_name
        - provider_object_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 0
          type: string
        entity_type_name:
          maxLength: 64
          minLength: 0
          type: string
        object_id:
          maxLength: 128
          minLength: 0
          type: string
        provider_object_id:
          maxLength: 128
          minLength: 0
          type: string
        provider_name:
          maxLength: 128
          minLength: 0
          type: string
      additionalProperties: false
    AssociateObjectOutput:
      type: object
      additionalProperties: false
    AssociateObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AssociateObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CountSchemafulObjectsFilterGroup:
      required:
        - filters
      type: object
      properties:
        filters:
          type: array
          items:
            $ref: '#/components/schemas/SchemafulObjectFilter'
      additionalProperties: false
    CountSchemafulObjectsInput:
      required:
        - filter_groups
        - is_search_indexed_property_values
        - schema_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 1024
          minLength: 0
          type: string
        schema_id:
          minLength: 1
          type: string
        is_search_indexed_property_values:
          type: boolean
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/CountSchemafulObjectsFilterGroup'
        array_exists:
          type: array
          items:
            $ref: '#/components/schemas/ArrayExist'
          nullable: true
      additionalProperties: false
    CountSchemafulObjectsOutput:
      type: object
      properties:
        count:
          type: integer
          format: int64
      additionalProperties: false
    CountSchemafulObjectsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CountSchemafulObjectsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateBlobDownloadSasUrlsInput:
      required:
        - blob_names
        - blob_type
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        blob_names:
          type: array
          items:
            type: string
        blob_type:
          minLength: 1
          type: string
      additionalProperties: false
    CreateBlobDownloadSasUrlsOutput:
      type: object
      properties:
        download_blobs:
          type: array
          items:
            $ref: '#/components/schemas/PublicBlob'
          nullable: true
      additionalProperties: false
    CreateBlobDownloadSasUrlsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateBlobDownloadSasUrlsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateBlobUploadSasUrlsInput:
      required:
        - blob_type
        - number_of_blobs
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        number_of_blobs:
          maximum: 16
          minimum: 1
          type: integer
          format: int32
        blob_type:
          minLength: 1
          type: string
      additionalProperties: false
    CreateBlobUploadSasUrlsOutput:
      type: object
      properties:
        upload_blobs:
          type: array
          items:
            $ref: '#/components/schemas/PublicBlob'
          nullable: true
      additionalProperties: false
    CreateBlobUploadSasUrlsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateBlobUploadSasUrlsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateObjectInput:
      required:
        - dict
        - entity_type_name
        - provider_connection_id
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_connection_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        dict:
          type: object
          additionalProperties:
            nullable: true
        typed_ids:
          type: array
          items:
            $ref: '#/components/schemas/TypedId'
          nullable: true
      additionalProperties: false
    CreateObjectOutput:
      type: object
      additionalProperties: false
    CreateObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateProviderUserMappingConfigInput:
      required:
        - provider_connection_id
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        provider_connection_id:
          minLength: 1
          type: string
        user_mappings:
          type: array
          items:
            $ref: '#/components/schemas/UserMapping'
          nullable: true
      additionalProperties: false
    CreateProviderUserMappingConfigOutput:
      type: object
      properties:
        user_mapping_config:
          $ref: '#/components/schemas/ProviderUserMappingConfigDto'
      additionalProperties: false
    CreateProviderUserMappingConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateProviderUserMappingConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateSalesforceObjectInput:
      required:
        - dict
        - entity_type_name
        - salesforce_connection_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        salesforce_connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        dict:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    CreateSalesforceObjectOutput:
      type: object
      additionalProperties: false
    CreateSalesforceObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateSalesforceObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateSchemaInput:
      required:
        - display_name
        - primary_property_input
        - property_inputs
        - relationship_type
        - schema_accessibility_settings
        - sleekflow_company_id
        - unique_name
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        display_name:
          minLength: 1
          type: string
        unique_name:
          minLength: 1
          type: string
        relationship_type:
          minLength: 1
          type: string
        property_inputs:
          type: array
          items:
            $ref: '#/components/schemas/PropertyInput'
        primary_property_input:
          $ref: '#/components/schemas/PrimaryPropertyInput'
        schema_accessibility_settings:
          $ref: '#/components/schemas/SchemaAccessibilitySettings'
      additionalProperties: false
    CreateSchemaOutput:
      type: object
      properties:
        schema:
          $ref: '#/components/schemas/SchemaDto'
      additionalProperties: false
    CreateSchemaOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateSchemaOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateSchemafulObjectInput:
      required:
        - primary_property_value
        - property_values
        - schema_id
        - sleekflow_company_id
        - sleekflow_staff_id
        - sleekflow_user_profile_id
      type: object
      properties:
        schema_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        primary_property_value:
          type: string
        property_values:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_user_profile_id:
          type: string
        sleekflow_staff_id:
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        created_via:
          type: string
          nullable: true
      additionalProperties: false
    CreateSchemafulObjectOutput:
      type: object
      properties:
        schemaful_object:
          $ref: '#/components/schemas/SchemafulObjectDto'
      additionalProperties: false
    CreateSchemafulObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateSchemafulObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CrmHubConfig:
      type: object
      properties:
        usage_limit:
          $ref: '#/components/schemas/UsageLimit'
        usage_limit_offset:
          $ref: '#/components/schemas/UsageLimitOffset'
        feature_accessibility_settings:
          $ref: '#/components/schemas/FeatureAccessibilitySettings'
        _etag:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    CustomObjectType:
      type: object
      properties:
        api_name:
          type: string
          nullable: true
        display_name:
          type: string
          nullable: true
      additionalProperties: false
    DeactivateProviderInput:
      required:
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
      additionalProperties: false
    DeactivateProviderOutput:
      type: object
      additionalProperties: false
    DeactivateProviderOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeactivateProviderOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteBlobsInput:
      required:
        - blob_names
        - blob_type
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        blob_names:
          type: array
          items:
            type: string
        blob_type:
          minLength: 1
          type: string
      additionalProperties: false
    DeleteBlobsOutput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        blob_names:
          type: array
          items:
            type: string
          nullable: true
        blob_type:
          type: string
          nullable: true
      additionalProperties: false
    DeleteBlobsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteBlobsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteProviderConnectionInput:
      required:
        - provider_connection_id
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        provider_connection_id:
          minLength: 1
          type: string
      additionalProperties: false
    DeleteProviderConnectionOutput:
      type: object
      additionalProperties: false
    DeleteProviderConnectionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteProviderConnectionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteSchemaInput:
      required:
        - id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        id:
          minLength: 1
          type: string
      additionalProperties: false
    DeleteSchemaOutput:
      type: object
      additionalProperties: false
    DeleteSchemaOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteSchemaOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteSchemafulObjectsInput:
      required:
        - ids
        - schema_id
        - sleekflow_company_id
      type: object
      properties:
        ids:
          type: array
          items:
            type: string
        schema_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    DeleteSchemafulObjectsOutput:
      type: object
      properties:
        deleted_count:
          type: integer
          format: int32
      additionalProperties: false
    DeleteSchemafulObjectsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteSchemafulObjectsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    EnqueueCreatePropertyDrivenSchemafulObjectInput:
      type: object
      properties:
        schema_unique_name:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          type: string
          nullable: true
        property_values:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        created_via:
          type: string
          nullable: true
      additionalProperties: false
    EnqueueCreatePropertyDrivenSchemafulObjectOutput:
      type: object
      additionalProperties: false
    EnqueueCreatePropertyDrivenSchemafulObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/EnqueueCreatePropertyDrivenSchemafulObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    EnrollContactToFlowHubInput:
      required:
        - contact
        - contact_id
        - flow_hub_workflow_id
        - flow_hub_workflow_versioned_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        flow_hub_workflow_id:
          minLength: 1
          type: string
        flow_hub_workflow_versioned_id:
          minLength: 1
          type: string
      additionalProperties: false
    EnrollContactToFlowHubOutput:
      type: object
      additionalProperties: false
    EnrollContactToFlowHubOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/EnrollContactToFlowHubOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    EnrollContactsToFlowHubInput:
      required:
        - contacts
        - flow_hub_workflow_id
        - flow_hub_workflow_versioned_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        contacts:
          type: array
          items:
            type: object
            additionalProperties: { }
        flow_hub_workflow_id:
          minLength: 1
          type: string
        flow_hub_workflow_versioned_id:
          minLength: 1
          type: string
      additionalProperties: false
    EnrollContactsToFlowHubOutput:
      type: object
      additionalProperties: false
    EnrollContactsToFlowHubOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/EnrollContactsToFlowHubOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ExternalResource:
      type: object
      properties:
        id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        resource_type:
          type: string
          nullable: true
        sub_resources:
          type: array
          items:
            $ref: '#/components/schemas/ExternalResource'
          nullable: true
      additionalProperties: false
    FeatureAccessibilitySettings:
      type: object
      properties:
        can_access_custom_object:
          type: boolean
        can_access_custom_object_flow_builder_components:
          type: boolean
      additionalProperties: false
    FieldDto:
      type: object
      properties:
        name:
          type: string
          nullable: true
        types:
          type: array
          items: { }
          nullable: true
      additionalProperties: false
    Filter:
      required:
        - field_name
        - operator
      type: object
      properties:
        field_name:
          maxLength: 128
          minLength: 1
          pattern: '^[a-zA-Z0-9_\[\]\.:]+$'
          type: string
        operator:
          maxLength: 128
          minLength: 1
          pattern: ^(=|>|<|>=|<=|!=|contains|in|startsWith)$
          type: string
        value:
          nullable: true
      additionalProperties: false
    GetCrmHubConfigInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetCrmHubConfigOutput:
      type: object
      properties:
        crm_hub_config:
          $ref: '#/components/schemas/CrmHubConfig'
      additionalProperties: false
    GetCrmHubConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetCrmHubConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetLoopThroughHubspotObjectsProgressInput:
      required:
        - flow_hub_workflow_id
        - flow_hub_workflow_versioned_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        flow_hub_workflow_id:
          minLength: 1
          type: string
        flow_hub_workflow_versioned_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetLoopThroughHubspotObjectsProgressOutput:
      type: object
      properties:
        count:
          type: integer
          format: int32
        last_update_time:
          type: string
          format: date-time
        status:
          type: string
          nullable: true
      additionalProperties: false
    GetLoopThroughHubspotObjectsProgressOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetLoopThroughHubspotObjectsProgressOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetLoopThroughSalesforceObjectsProgressInput:
      required:
        - flow_hub_workflow_id
        - flow_hub_workflow_versioned_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        flow_hub_workflow_id:
          minLength: 1
          type: string
        flow_hub_workflow_versioned_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetLoopThroughSalesforceObjectsProgressOutput:
      type: object
      properties:
        count:
          type: integer
          format: int32
        last_update_time:
          type: string
          format: date-time
        status:
          type: string
          nullable: true
      additionalProperties: false
    GetLoopThroughSalesforceObjectsProgressOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetLoopThroughSalesforceObjectsProgressOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetLoopThroughSchemafulObjectsProgressInput:
      required:
        - flow_hub_workflow_id
        - flow_hub_workflow_versioned_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        flow_hub_workflow_id:
          minLength: 1
          type: string
        flow_hub_workflow_versioned_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetLoopThroughSchemafulObjectsProgressOutput:
      type: object
      properties:
        count:
          type: integer
          format: int32
        last_update_time:
          type: string
          format: date-time
        status:
          type: string
          nullable: true
      additionalProperties: false
    GetLoopThroughSchemafulObjectsProgressOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetLoopThroughSchemafulObjectsProgressOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetObjectsByIdentitiesInput:
      required:
        - entity_type_name
        - identities
        - sleekflow_company_id
        - sorts
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        identities:
          type: array
          items:
            $ref: '#/components/schemas/Identity'
        entity_type_name:
          minLength: 1
          type: string
        sorts:
          type: array
          items:
            $ref: '#/components/schemas/Sort'
      additionalProperties: false
    GetObjectsByIdentitiesOutput:
      type: object
      properties:
        records:
          type: array
          items:
            type: object
            additionalProperties: { }
          nullable: true
        count:
          type: integer
          format: int64
      additionalProperties: false
    GetObjectsByIdentitiesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetObjectsByIdentitiesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetObjectsCountInput:
      required:
        - entity_type_name
        - filter_groups
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        entity_type_name:
          maxLength: 128
          minLength: 1
          type: string
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/GetObjectsCountInputFilterGroup'
      additionalProperties: false
    GetObjectsCountInputFilterGroup:
      required:
        - filters
      type: object
      properties:
        filters:
          type: array
          items:
            $ref: '#/components/schemas/Filter'
      additionalProperties: false
    GetObjectsCountOutput:
      type: object
      properties:
        count:
          type: integer
          format: int64
      additionalProperties: false
    GetObjectsCountOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetObjectsCountOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetObjectsCountV2Input:
      required:
        - entity_type_name
        - filter_groups
        - group_bys
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        entity_type_name:
          maxLength: 128
          minLength: 1
          type: string
        group_bys:
          maxItems: 2
          type: array
          items:
            $ref: '#/components/schemas/GroupBy'
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/GetObjectsCountV2InputFilterGroup'
      additionalProperties: false
    GetObjectsCountV2InputFilterGroup:
      required:
        - filters
      type: object
      properties:
        filters:
          type: array
          items:
            $ref: '#/components/schemas/Filter'
      additionalProperties: false
    GetObjectsCountV2Output:
      type: object
      properties:
        count:
          type: integer
          format: int64
        aggregated_counts:
          type: array
          items:
            type: object
            additionalProperties: { }
          nullable: true
      additionalProperties: false
    GetObjectsCountV2OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetObjectsCountV2Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetObjectsInput:
      required:
        - entity_type_name
        - filter_groups
        - limit
        - sleekflow_company_id
        - sorts
      type: object
      properties:
        continuation_token:
          maxLength: 16384
          minLength: 1
          type: string
          nullable: true
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        entity_type_name:
          maxLength: 128
          minLength: 1
          type: string
        limit:
          maximum: 200
          minimum: 1
          type: integer
          format: int32
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/GetObjectsInputFilterGroup'
        sorts:
          type: array
          items:
            $ref: '#/components/schemas/Sort'
      additionalProperties: false
    GetObjectsInputFilterGroup:
      required:
        - filters
      type: object
      properties:
        filters:
          type: array
          items:
            $ref: '#/components/schemas/Filter'
      additionalProperties: false
    GetObjectsOutput:
      type: object
      properties:
        continuation_token:
          type: string
          nullable: true
        records:
          type: array
          items:
            type: object
            additionalProperties: { }
          nullable: true
        count:
          type: integer
          format: int64
      additionalProperties: false
    GetObjectsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetObjectsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetObjectsV2Input:
      required:
        - entity_type_name
        - expands
        - filter_groups
        - limit
        - sleekflow_company_id
        - sorts
      type: object
      properties:
        continuation_token:
          maxLength: 16384
          minLength: 1
          type: string
          nullable: true
        sleekflow_company_id:
          maxLength: 1024
          minLength: 0
          type: string
        entity_type_name:
          maxLength: 128
          minLength: 0
          type: string
        limit:
          maximum: 200
          minimum: 1
          type: integer
          format: int32
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/GetObjectsV2InputFilterGroup'
        sorts:
          type: array
          items:
            $ref: '#/components/schemas/Sort'
        expands:
          type: array
          items:
            $ref: '#/components/schemas/GetObjectsV2InputExpand'
      additionalProperties: false
    GetObjectsV2InputExpand:
      required:
        - as_field_name
        - from_field_name
        - to_entity_name
        - to_field_name
      type: object
      properties:
        from_field_name:
          maxLength: 255
          minLength: 2
          pattern: '^[a-zA-Z0-9_\[\]\.:]+$'
          type: string
        to_entity_name:
          maxLength: 255
          minLength: 2
          pattern: '^[a-zA-Z0-9_\[\]\.:]+$'
          type: string
        to_field_name:
          maxLength: 255
          minLength: 2
          pattern: '^[a-zA-Z0-9_\[\]\.:]+$'
          type: string
        as_field_name:
          maxLength: 255
          minLength: 2
          pattern: '^[a-zA-Z0-9_\[\]\.:]+$'
          type: string
      additionalProperties: false
    GetObjectsV2InputFilterGroup:
      required:
        - filters
      type: object
      properties:
        filters:
          type: array
          items:
            $ref: '#/components/schemas/Filter'
      additionalProperties: false
    GetObjectsV2Output:
      type: object
      properties:
        continuation_token:
          type: string
          nullable: true
        records:
          type: array
          items:
            type: object
            additionalProperties: { }
          nullable: true
        count:
          type: integer
          format: int64
      additionalProperties: false
    GetObjectsV2OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetObjectsV2Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetObjectsV3Input:
      required:
        - entity_type_name
        - expands
        - filter_groups
        - limit
        - sleekflow_company_id
        - sorts
      type: object
      properties:
        continuation_token:
          maxLength: 16384
          minLength: 1
          type: string
          nullable: true
        sleekflow_company_id:
          maxLength: 1024
          minLength: 0
          type: string
        entity_type_name:
          maxLength: 128
          minLength: 0
          type: string
        limit:
          maximum: 200
          minimum: 1
          type: integer
          format: int32
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/GetObjectsV3InputFilterGroup'
        sorts:
          type: array
          items:
            $ref: '#/components/schemas/Sort'
        expands:
          type: array
          items:
            $ref: '#/components/schemas/GetObjectsV3InputExpand'
      additionalProperties: false
    GetObjectsV3InputExpand:
      required:
        - as_field_name
        - from_field_name
        - to_entity_type_name
        - to_field_name
      type: object
      properties:
        from_field_name:
          maxLength: 255
          minLength: 2
          pattern: '^[a-zA-Z0-9_\[\]\.:]+$'
          type: string
        to_entity_type_name:
          maxLength: 255
          minLength: 2
          pattern: '^[a-zA-Z0-9_\[\]\.:]+$'
          type: string
        to_field_name:
          maxLength: 255
          minLength: 2
          pattern: '^[a-zA-Z0-9_\[\]\.:]+$'
          type: string
        as_field_name:
          maxLength: 255
          minLength: 2
          pattern: '^[a-zA-Z0-9_\[\]\.:]+$'
          type: string
      additionalProperties: false
    GetObjectsV3InputFilter:
      required:
        - field_name
        - operator
      type: object
      properties:
        field_name:
          maxLength: 128
          minLength: 1
          pattern: '^[a-zA-Z0-9_\[\]\.:]+$'
          type: string
        operator:
          maxLength: 128
          minLength: 1
          pattern: ^(=|>|<|>=|<=|!=|contains|in|startsWith|query)$
          type: string
        value:
          nullable: true
      additionalProperties: false
    GetObjectsV3InputFilterGroup:
      required:
        - filters
      type: object
      properties:
        filters:
          type: array
          items:
            $ref: '#/components/schemas/GetObjectsV3InputFilter'
      additionalProperties: false
    GetObjectsV3Output:
      type: object
      properties:
        continuation_token:
          type: string
          nullable: true
        records:
          type: array
          items:
            type: object
            additionalProperties: { }
          nullable: true
        count:
          type: integer
          format: int64
      additionalProperties: false
    GetObjectsV3OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetObjectsV3Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetPropertyValuesByContactIdsInput:
      required:
        - contact_ids
        - property_id
        - schema_id
        - sleekflow_company_id
      type: object
      properties:
        schema_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        contact_ids:
          type: array
          items:
            type: string
        property_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetPropertyValuesByContactIdsOutput:
      required:
        - property_values
      type: object
      properties:
        property_values:
          type: object
          additionalProperties:
            type: array
            items: { }
            nullable: true
      additionalProperties: false
    GetPropertyValuesByContactIdsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetPropertyValuesByContactIdsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProviderConfigInput:
      required:
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
      additionalProperties: false
    GetProviderConfigOutput:
      type: object
      properties:
        provider_config:
          $ref: '#/components/schemas/ProviderConfig'
      additionalProperties: false
    GetProviderConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProviderConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProviderConfigsInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetProviderConfigsOutput:
      type: object
      properties:
        provider_configs:
          type: array
          items:
            $ref: '#/components/schemas/ProviderConfigDto'
          nullable: true
      additionalProperties: false
    GetProviderConfigsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProviderConfigsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProviderConnectionExternalResourcesInput:
      required:
        - provider_connection_id
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        provider_connection_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetProviderConnectionExternalResourcesOutput:
      type: object
      properties:
        external_resources:
          type: array
          items:
            $ref: '#/components/schemas/ExternalResource'
          nullable: true
      additionalProperties: false
    GetProviderConnectionExternalResourcesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProviderConnectionExternalResourcesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProviderConnectionsInput:
      required:
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
      additionalProperties: false
    GetProviderConnectionsOutput:
      type: object
      properties:
        connections:
          type: array
          items:
            $ref: '#/components/schemas/ProviderConnectionDto'
          nullable: true
      additionalProperties: false
    GetProviderConnectionsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProviderConnectionsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProviderCustomObjectTypesInput:
      required:
        - provider_connection_id
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        provider_connection_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetProviderCustomObjectTypesOutput:
      type: object
      properties:
        custom_object_types:
          type: array
          items:
            $ref: '#/components/schemas/CustomObjectType'
          nullable: true
      additionalProperties: false
    GetProviderCustomObjectTypesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProviderCustomObjectTypesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProviderObjectDirectRefUrlInput:
      required:
        - entity_type_name
        - object_id
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        object_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetProviderObjectDirectRefUrlOutput:
      required:
        - object_direct_ref_url
      type: object
      properties:
        object_direct_ref_url:
          minLength: 1
          type: string
      additionalProperties: false
    GetProviderObjectDirectRefUrlOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProviderObjectDirectRefUrlOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProviderObjectsCountInput:
      required:
        - entity_type_name
        - filters
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilter'
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
      additionalProperties: false
    GetProviderObjectsCountOutput:
      required:
        - count
      type: object
      properties:
        count:
          type: integer
          format: int64
      additionalProperties: false
    GetProviderObjectsCountOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProviderObjectsCountOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProviderObjectsCountV2Input:
      required:
        - entity_type_name
        - filter_groups
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilterGroup'
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
      additionalProperties: false
    GetProviderObjectsCountV2Output:
      required:
        - count
      type: object
      properties:
        count:
          type: integer
          format: int64
      additionalProperties: false
    GetProviderObjectsCountV2OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProviderObjectsCountV2Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProviderSubscriptionsInput:
      required:
        - entity_type_name
        - provider_connection_id
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        provider_connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        typed_ids:
          type: array
          items:
            $ref: '#/components/schemas/TypedId'
          nullable: true
      additionalProperties: false
    GetProviderSubscriptionsOutput:
      type: object
      properties:
        subscriptions:
          type: array
          items:
            $ref: '#/components/schemas/ProviderSubscriptionDto'
          nullable: true
      additionalProperties: false
    GetProviderSubscriptionsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProviderSubscriptionsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProviderSupportedTypesInput:
      required:
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
      additionalProperties: false
    GetProviderSupportedTypesOutput:
      type: object
      properties:
        supported_entity_types:
          type: array
          items:
            $ref: '#/components/schemas/GetSupportedTypesOutputSupportedType'
          nullable: true
      additionalProperties: false
    GetProviderSupportedTypesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProviderSupportedTypesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProviderSyncObjectsProgressInput:
      required:
        - provider_name
        - provider_state_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        provider_state_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetProviderSyncObjectsProgressOutput:
      type: object
      properties:
        count:
          type: integer
          format: int32
        last_update_time:
          type: string
          format: date-time
        status:
          type: string
          nullable: true
      additionalProperties: false
    GetProviderSyncObjectsProgressOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProviderSyncObjectsProgressOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProviderTypeFieldsInput:
      required:
        - entity_type_name
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
      additionalProperties: false
    GetProviderTypeFieldsOutput:
      type: object
      properties:
        updatable_fields:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputFieldDto'
          nullable: true
        creatable_fields:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputFieldDto'
          nullable: true
        viewable_fields:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputFieldDto'
          nullable: true
      additionalProperties: false
    GetProviderTypeFieldsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProviderTypeFieldsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProviderTypeFieldsV2Input:
      required:
        - entity_type_name
        - provider_connection_id
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_connection_id:
          minLength: 1
          type: string
        typed_ids:
          type: array
          items:
            $ref: '#/components/schemas/TypedId'
          nullable: true
        entity_type_name:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
      additionalProperties: false
    GetProviderTypeFieldsV2Output:
      type: object
      properties:
        updatable_fields:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputFieldDto'
          nullable: true
        creatable_fields:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputFieldDto'
          nullable: true
        viewable_fields:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputFieldDto'
          nullable: true
      additionalProperties: false
    GetProviderTypeFieldsV2OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProviderTypeFieldsV2Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProviderUserMappingConfigInput:
      required:
        - provider_connection_id
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        provider_connection_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetProviderUserMappingConfigOutput:
      type: object
      properties:
        user_mapping_config:
          $ref: '#/components/schemas/ProviderUserMappingConfigDto'
      additionalProperties: false
    GetProviderUserMappingConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProviderUserMappingConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProvidersInput:
      type: object
      additionalProperties: false
    GetProvidersOutput:
      type: object
      properties:
        providers:
          type: array
          items:
            $ref: '#/components/schemas/Provider'
          nullable: true
      additionalProperties: false
    GetProvidersOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProvidersOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetSalesforceCustomObjectTypesInput:
      required:
        - salesforce_connection_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        salesforce_connection_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetSalesforceCustomObjectTypesOutput:
      type: object
      properties:
        custom_object_types:
          type: array
          items:
            $ref: '#/components/schemas/CustomObjectType'
          nullable: true
      additionalProperties: false
    GetSalesforceCustomObjectTypesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetSalesforceCustomObjectTypesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetSchemaInput:
      required:
        - id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        id:
          minLength: 1
          type: string
      additionalProperties: false
    GetSchemaOutput:
      type: object
      properties:
        schema:
          $ref: '#/components/schemas/SchemaDto'
      additionalProperties: false
    GetSchemaOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetSchemaOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetSchemafulObjectCsvTemplateInput:
      required:
        - schema_id
        - sleekflow_company_id
      type: object
      properties:
        schema_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetSchemafulObjectCsvTemplateOutput:
      type: object
      properties:
        csv_string:
          type: string
          nullable: true
      additionalProperties: false
    GetSchemafulObjectCsvTemplateOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetSchemafulObjectCsvTemplateOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetSchemafulObjectInput:
      required:
        - id
        - schema_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        schema_id:
          minLength: 1
          type: string
        id:
          minLength: 1
          type: string
      additionalProperties: false
    GetSchemafulObjectOutput:
      type: object
      properties:
        schemaful_object:
          $ref: '#/components/schemas/SchemafulObjectDto'
      additionalProperties: false
    GetSchemafulObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetSchemafulObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetSchemafulObjectSampleInput:
      required:
        - schema_id
        - sleekflow_company_id
      type: object
      properties:
        schema_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetSchemafulObjectSampleOutput:
      type: object
      properties:
        schemaful_object:
          $ref: '#/components/schemas/SchemafulObjectDto'
      additionalProperties: false
    GetSchemafulObjectSampleOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetSchemafulObjectSampleOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetSchemafulObjectsFilterGroup:
      required:
        - filters
      type: object
      properties:
        filters:
          type: array
          items:
            $ref: '#/components/schemas/SchemafulObjectFilter'
      additionalProperties: false
    GetSchemafulObjectsInput:
      required:
        - filter_groups
        - is_search_indexed_property_values
        - limit
        - schema_id
        - sleekflow_company_id
        - sorts
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 1024
          minLength: 0
          type: string
        schema_id:
          minLength: 1
          type: string
        continuation_token:
          maxLength: 16384
          minLength: 1
          type: string
          nullable: true
        limit:
          maximum: 1000
          minimum: 1
          type: integer
          format: int32
        is_search_indexed_property_values:
          type: boolean
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/GetSchemafulObjectsFilterGroup'
        sorts:
          type: array
          items:
            $ref: '#/components/schemas/SchemafulObjectSort'
        array_exists:
          type: array
          items:
            $ref: '#/components/schemas/ArrayExist'
          nullable: true
      additionalProperties: false
    GetSchemafulObjectsOutput:
      type: object
      properties:
        schemaful_objects:
          type: array
          items:
            $ref: '#/components/schemas/SchemafulObjectDto'
          nullable: true
        continuation_token:
          type: string
          nullable: true
        count:
          type: integer
          format: int64
      additionalProperties: false
    GetSchemafulObjectsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetSchemafulObjectsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetSchemasFilterGroup:
      required:
        - filters
      type: object
      properties:
        filters:
          type: array
          items:
            $ref: '#/components/schemas/SchemaFilter'
      additionalProperties: false
    GetSchemasInput:
      required:
        - filter_groups
        - limit
        - sleekflow_company_id
        - sorts
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        limit:
          maximum: 200
          minimum: 1
          type: integer
          format: int32
        continuation_token:
          type: string
          nullable: true
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/GetSchemasFilterGroup'
        sorts:
          type: array
          items:
            $ref: '#/components/schemas/SchemaSort'
      additionalProperties: false
    GetSchemasOutput:
      type: object
      properties:
        schemas:
          type: array
          items:
            $ref: '#/components/schemas/SchemaDto'
          nullable: true
        continuation_token:
          type: string
          nullable: true
        count:
          type: integer
          format: int32
      additionalProperties: false
    GetSchemasOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetSchemasOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetSupportedTypesOutputSupportedType:
      type: object
      properties:
        name:
          type: string
          nullable: true
      additionalProperties: false
    GetTypeFieldValuesInput:
      required:
        - entity_type_name
        - field_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        field_name:
          minLength: 1
          type: string
      additionalProperties: false
    GetTypeFieldValuesOutput:
      type: object
      properties:
        values:
          type: array
          items: { }
          nullable: true
      additionalProperties: false
    GetTypeFieldValuesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetTypeFieldValuesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetTypeFieldsInput:
      required:
        - entity_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    GetTypeFieldsOutput:
      type: object
      properties:
        fields:
          type: array
          items:
            $ref: '#/components/schemas/FieldDto'
          nullable: true
      additionalProperties: false
    GetTypeFieldsOutputFieldDto:
      type: object
      properties:
        calculated:
          type: boolean
        compound_field_name:
          type: string
          nullable: true
        createable:
          type: boolean
        custom:
          type: boolean
        encrypted:
          type: boolean
        label:
          type: string
          nullable: true
        length:
          type: integer
          format: int32
        name:
          type: string
          nullable: true
        picklist_values:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputPicklistValue'
          nullable: true
        soap_type:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        unique:
          type: boolean
        updateable:
          type: boolean
        mandatory:
          type: boolean
          nullable: true
      additionalProperties: false
    GetTypeFieldsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetTypeFieldsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetTypeFieldsOutputPicklistValue:
      type: object
      properties:
        label:
          type: string
          nullable: true
        value:
          type: string
          nullable: true
      additionalProperties: false
    GetUnifyRulesInput:
      required:
        - entity_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    GetUnifyRulesOutput:
      type: object
      properties:
        unify_rules:
          type: array
          items:
            $ref: '#/components/schemas/UnifyRule'
          nullable: true
      additionalProperties: false
    GetUnifyRulesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetUnifyRulesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetUsageStatisticsInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetUsageStatisticsOutput:
      type: object
      properties:
        custom_object_schema_num:
          type: integer
          format: int32
        custom_object_schemaful_object_num_per_company:
          type: integer
          format: int32
      additionalProperties: false
    GetUsageStatisticsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetUsageStatisticsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWebhooksInput:
      required:
        - entity_type_name
        - event_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        event_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    GetWebhooksOutput:
      type: object
      properties:
        webhooks:
          type: array
          items:
            $ref: '#/components/schemas/Webhook'
          nullable: true
      additionalProperties: false
    GetWebhooksOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWebhooksOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GroupBy:
      required:
        - field_name
        - is_case_sensitive
      type: object
      properties:
        field_name:
          maxLength: 128
          minLength: 1
          pattern: '^[a-zA-Z0-9_\[\]\.:]+$'
          type: string
        is_case_sensitive:
          type: boolean
      additionalProperties: false
    IDataType:
      type: object
      properties:
        name:
          type: string
          nullable: true
        inner_schema:
          $ref: '#/components/schemas/InnerSchema'
      additionalProperties: false
    Identity:
      type: object
      properties:
        phone_number:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        external_id:
          type: string
          nullable: true
        object_id:
          type: string
          nullable: true
      additionalProperties: false
    ImageDto:
      type: object
      properties:
        download_url:
          maxLength: 4096
          minLength: 1
          type: string
          nullable: true
        public_blob:
          $ref: '#/components/schemas/PublicBlobDto'
      additionalProperties: false
    InitProviderInput:
      required:
        - default_region_code
        - provider_name
        - return_to_url
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        return_to_url:
          minLength: 1
          type: string
        default_region_code:
          minLength: 1
          type: string
        additional_details:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    InitProviderOutput:
      type: object
      properties:
        provider_name:
          type: string
          nullable: true
        context:
          nullable: true
      additionalProperties: false
    InitProviderOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/InitProviderOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    InitProviderTypeSyncInput:
      required:
        - entity_type_name
        - provider_connection_id
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        provider_connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        sync_interval:
          type: integer
          format: int32
          nullable: true
        is_flows_based:
          type: boolean
          nullable: true
        typed_ids:
          type: array
          items:
            $ref: '#/components/schemas/TypedId'
          nullable: true
      additionalProperties: false
    InitProviderTypeSyncOutput:
      type: object
      additionalProperties: false
    InitProviderTypeSyncOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/InitProviderTypeSyncOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    InitProviderTypesSyncInput:
      required:
        - entity_type_name_to_sync_config_dict
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name_to_sync_config_dict:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/SyncConfig'
        provider_name:
          minLength: 1
          type: string
      additionalProperties: false
    InitProviderTypesSyncOutput:
      type: object
      properties:
        entity_type_name_to_init_status_dict:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/InitStatus'
          nullable: true
      additionalProperties: false
    InitProviderTypesSyncOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/InitProviderTypesSyncOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    InitProviderV2Input:
      required:
        - default_region_code
        - failure_url
        - provider_name
        - sleekflow_company_id
        - success_url
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        success_url:
          minLength: 1
          type: string
        failure_url:
          minLength: 1
          type: string
        default_region_code:
          minLength: 1
          type: string
        additional_details:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    InitProviderV2Output:
      type: object
      properties:
        provider_name:
          type: string
          nullable: true
        context:
          nullable: true
      additionalProperties: false
    InitProviderV2OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/InitProviderV2Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    InitStatus:
      type: object
      properties:
        entity_type_name:
          type: string
          nullable: true
        is_updated:
          type: boolean
        message:
          type: string
          nullable: true
      additionalProperties: false
    InitializeCrmHubConfigInput:
      required:
        - sleekflow_company_id
        - usage_limit
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        usage_limit:
          $ref: '#/components/schemas/UsageLimit'
        feature_accessibility_settings:
          $ref: '#/components/schemas/FeatureAccessibilitySettings'
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    InitializeCrmHubConfigOutput:
      type: object
      properties:
        crm_hub_config:
          $ref: '#/components/schemas/CrmHubConfig'
      additionalProperties: false
    InitializeCrmHubConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/InitializeCrmHubConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    InnerSchema:
      type: object
      properties:
        properties:
          type: array
          items:
            $ref: '#/components/schemas/Property'
          nullable: true
      additionalProperties: false
    InnerSchemafulObjectDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        property_values:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    InnerSchemafulObjectInputDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        property_values:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        created_at:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    LoopThroughAndEnrollHubspotObjectsToFlowHubInput:
      required:
        - entity_type_name
        - flow_hub_workflow_id
        - flow_hub_workflow_versioned_id
        - hubspot_connection_id
        - is_custom_object
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        hubspot_connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        flow_hub_workflow_id:
          minLength: 1
          type: string
        flow_hub_workflow_versioned_id:
          minLength: 1
          type: string
        is_custom_object:
          type: boolean
      additionalProperties: false
    LoopThroughAndEnrollHubspotObjectsToFlowHubOutput:
      type: object
      properties:
        loop_through_objects_progress_state_id:
          type: string
          nullable: true
      additionalProperties: false
    LoopThroughAndEnrollHubspotObjectsToFlowHubOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/LoopThroughAndEnrollHubspotObjectsToFlowHubOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    LoopThroughAndEnrollSalesforceObjectsToFlowHubInput:
      required:
        - entity_type_name
        - flow_hub_workflow_id
        - flow_hub_workflow_versioned_id
        - is_custom_object
        - salesforce_connection_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        salesforce_connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        flow_hub_workflow_id:
          minLength: 1
          type: string
        flow_hub_workflow_versioned_id:
          minLength: 1
          type: string
        is_custom_object:
          type: boolean
      additionalProperties: false
    LoopThroughAndEnrollSalesforceObjectsToFlowHubOutput:
      type: object
      properties:
        loop_through_objects_progress_state_id:
          type: string
          nullable: true
      additionalProperties: false
    LoopThroughAndEnrollSalesforceObjectsToFlowHubOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/LoopThroughAndEnrollSalesforceObjectsToFlowHubOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchInput:
      required:
        - flow_hub_workflow_id
        - flow_hub_workflow_versioned_id
        - schema_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        schema_id:
          minLength: 1
          type: string
        continuation_token:
          type: string
          nullable: true
        flow_hub_workflow_id:
          minLength: 1
          type: string
        flow_hub_workflow_versioned_id:
          minLength: 1
          type: string
      additionalProperties: false
    LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchOutput:
      required:
        - count
      type: object
      properties:
        count:
          type: integer
          format: int64
        next_continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    LoopThroughAndEnrollSchemafulObjectsToFlowHubInput:
      required:
        - flow_hub_workflow_id
        - flow_hub_workflow_versioned_id
        - schema_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        schema_id:
          minLength: 1
          type: string
        flow_hub_workflow_id:
          minLength: 1
          type: string
        flow_hub_workflow_versioned_id:
          minLength: 1
          type: string
      additionalProperties: false
    LoopThroughAndEnrollSchemafulObjectsToFlowHubOutput:
      type: object
      properties:
        loop_through_objects_progress_state_id:
          type: string
          nullable: true
      additionalProperties: false
    LoopThroughAndEnrollSchemafulObjectsToFlowHubOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/LoopThroughAndEnrollSchemafulObjectsToFlowHubOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    MigrateProviderIntegrationToFlowHubInput:
      required:
        - provider_name
        - sleekflow_company_id
        - sleekflow_fields
        - sleekflow_staff_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        sleekflow_fields:
          type: array
          items:
            type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    MigrateProviderIntegrationToFlowHubOutput:
      type: object
      additionalProperties: false
    MigrateProviderIntegrationToFlowHubOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/MigrateProviderIntegrationToFlowHubOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    MySchemafulObjectCsvReaderState:
      type: object
      properties:
        last_byte_position:
          type: integer
          format: int64
        num_of_records:
          type: integer
          format: int64
        headers:
          type: array
          items:
            type: string
          nullable: true
        is_completed:
          type: boolean
      additionalProperties: false
    Option:
      type: object
      properties:
        id:
          type: string
          nullable: true
        value:
          type: string
          nullable: true
        display_order:
          type: integer
          format: int32
      additionalProperties: false
    PartialUpdatePropertyValuesInput:
      required:
        - id
        - property_values
        - schema_id
        - sleekflow_company_id
        - sleekflow_staff_id
      type: object
      properties:
        id:
          minLength: 1
          type: string
        schema_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        property_values:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_id:
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        updated_via:
          type: string
          nullable: true
      additionalProperties: false
    PartialUpdatePropertyValuesOutput:
      type: object
      properties:
        schemaful_object:
          $ref: '#/components/schemas/SchemafulObjectDto'
      additionalProperties: false
    PartialUpdatePropertyValuesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/PartialUpdatePropertyValuesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    PrepareMigrationToFlowHubInput:
      required:
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
      additionalProperties: false
    PrepareMigrationToFlowHubOutput:
      type: object
      additionalProperties: false
    PrepareMigrationToFlowHubOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/PrepareMigrationToFlowHubOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    PreviewObjectsInput:
      required:
        - entity_type_name
        - filters
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilter'
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
      additionalProperties: false
    PreviewObjectsOutput:
      type: object
      properties:
        records:
          type: array
          items:
            type: object
            additionalProperties: { }
          nullable: true
      additionalProperties: false
    PreviewObjectsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/PreviewObjectsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    PreviewObjectsV2Input:
      required:
        - entity_type_name
        - provider_connection_id
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        provider_connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilter'
          nullable: true
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
        next_records_url:
          type: string
          nullable: true
      additionalProperties: false
    PreviewObjectsV2Output:
      type: object
      properties:
        objects:
          type: array
          items:
            type: object
            additionalProperties: { }
          nullable: true
        next_records_url:
          type: string
          nullable: true
      additionalProperties: false
    PreviewObjectsV2OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/PreviewObjectsV2Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    PrimaryProperty:
      type: object
      properties:
        id:
          type: string
          nullable: true
        display_name:
          type: string
          nullable: true
        unique_name:
          type: string
          nullable: true
        data_type:
          $ref: '#/components/schemas/IDataType'
        is_visible:
          type: boolean
        is_pinned:
          type: boolean
        is_searchable:
          type: boolean
        primary_property_config:
          $ref: '#/components/schemas/PrimaryPropertyConfig'
        created_at:
          type: string
          format: date-time
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
      additionalProperties: false
    PrimaryPropertyConfig:
      type: object
      properties:
        is_auto_generated:
          type: boolean
        sequential:
          $ref: '#/components/schemas/Sequential'
      additionalProperties: false
    PrimaryPropertyInput:
      required:
        - data_type
        - display_name
        - is_pinned
        - is_searchable
        - is_visible
        - primary_property_config
        - unique_name
      type: object
      properties:
        display_name:
          minLength: 1
          type: string
        unique_name:
          minLength: 1
          type: string
        data_type:
          $ref: '#/components/schemas/IDataType'
        is_visible:
          type: boolean
        is_pinned:
          type: boolean
        is_searchable:
          type: boolean
        primary_property_config:
          $ref: '#/components/schemas/PrimaryPropertyConfig'
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
      additionalProperties: false
    ProcessSchemafulObjectCsvBatchInput:
      required:
        - batch_size
        - blob_sas_uri
        - schema_id
        - sleekflow_company_id
      type: object
      properties:
        schema_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        blob_sas_uri:
          minLength: 1
          type: string
        batch_size:
          maximum: 1000
          minimum: 1
          type: integer
          format: int32
        my_schemaful_object_csv_reader_state:
          $ref: '#/components/schemas/MySchemafulObjectCsvReaderState'
      additionalProperties: false
    ProcessSchemafulObjectCsvBatchOutput:
      type: object
      properties:
        count:
          type: integer
          format: int64
        my_custom_catalog_csv_reader_state:
          $ref: '#/components/schemas/MySchemafulObjectCsvReaderState'
      additionalProperties: false
    ProcessSchemafulObjectCsvBatchOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ProcessSchemafulObjectCsvBatchOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    PropagateObjectUpdateToProviderInput:
      required:
        - entity_type_name
        - object_id
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        object_id:
          minLength: 1
          type: string
      additionalProperties: false
    PropagateObjectUpdateToProviderOutput:
      required:
        - object_id
      type: object
      properties:
        object_id:
          minLength: 1
          type: string
      additionalProperties: false
    PropagateObjectUpdateToProviderOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/PropagateObjectUpdateToProviderOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Property:
      type: object
      properties:
        id:
          type: string
          nullable: true
        display_name:
          type: string
          nullable: true
        unique_name:
          type: string
          nullable: true
        data_type:
          $ref: '#/components/schemas/IDataType'
        is_required:
          type: boolean
        is_visible:
          type: boolean
        is_pinned:
          type: boolean
        is_searchable:
          type: boolean
        display_order:
          type: integer
          format: int32
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        options:
          type: array
          items:
            $ref: '#/components/schemas/Option'
          nullable: true
      additionalProperties: false
    PropertyInput:
      required:
        - data_type
        - display_name
        - display_order
        - is_pinned
        - is_required
        - is_searchable
        - is_visible
        - unique_name
      type: object
      properties:
        display_name:
          minLength: 1
          type: string
        unique_name:
          minLength: 1
          type: string
        data_type:
          $ref: '#/components/schemas/IDataType'
        is_required:
          type: boolean
        is_visible:
          type: boolean
        is_pinned:
          type: boolean
        is_searchable:
          type: boolean
        display_order:
          maximum: **********
          minimum: 0
          type: integer
          format: int32
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        options:
          type: array
          items:
            $ref: '#/components/schemas/Option'
          nullable: true
      additionalProperties: false
    Provider:
      type: object
      properties:
        name:
          type: string
          nullable: true
        capability_matrix:
          type: object
          additionalProperties:
            type: boolean
          nullable: true
      additionalProperties: false
    ProviderConfig:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        key:
          type: string
          nullable: true
        entity_type_name_to_sync_config_dict:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/SyncConfig'
          nullable: true
        provider_name:
          type: string
          nullable: true
        is_authenticated:
          type: boolean
        default_region_code:
          type: string
          nullable: true
        record_statuses:
          type: array
          items:
            type: string
          nullable: true
        provider_limit:
          $ref: '#/components/schemas/ProviderLimit'
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    ProviderConfigDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        key:
          type: string
          nullable: true
        entity_type_name_to_sync_config_dict:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/SyncConfigDto'
          nullable: true
        provider_name:
          type: string
          nullable: true
        is_authenticated:
          type: boolean
        default_region_code:
          type: string
          nullable: true
        record_statuses:
          type: array
          items:
            type: string
          nullable: true
        provider_limit:
          $ref: '#/components/schemas/ProviderLimit'
      additionalProperties: false
    ProviderConnectionDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        organization_id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        environment:
          type: string
          nullable: true
        is_active:
          type: boolean
        is_api_request_limit_exceeded:
          type: boolean
          nullable: true
        connected_at:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    ProviderLimit:
      type: object
      properties:
        is_read_only_integration:
          type: boolean
        object_count_limit_dict:
          type: object
          additionalProperties:
            type: integer
            format: int32
          nullable: true
      additionalProperties: false
    ProviderSubscriptionDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        entity_type_name:
          type: string
          nullable: true
        interval:
          type: integer
          format: int32
        is_flows_based:
          type: boolean
          nullable: true
        connection_id:
          type: string
          nullable: true
        typed_ids:
          type: array
          items:
            $ref: '#/components/schemas/TypedId'
          nullable: true
      additionalProperties: false
    ProviderUserMappingConfigDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        connection_id:
          type: string
          nullable: true
        user_mappings:
          type: array
          items:
            $ref: '#/components/schemas/UserMapping'
          nullable: true
      additionalProperties: false
    PublicBlob:
      type: object
      properties:
        container_name:
          type: string
          nullable: true
        blob_name:
          type: string
          nullable: true
        blob_id:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        expires_on:
          type: string
          format: date-time
        content_type:
          type: string
          nullable: true
      additionalProperties: false
    PublicBlobDto:
      type: object
      properties:
        blob_type:
          type: string
          nullable: true
        blob_name:
          type: string
          nullable: true
        blob_id:
          type: string
          nullable: true
        file_name:
          type: string
          nullable: true
        content_type:
          type: string
          nullable: true
      additionalProperties: false
    ReInitProviderConnectionInput:
      required:
        - failure_url
        - provider_connection_id
        - provider_name
        - sleekflow_company_id
        - success_url
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        provider_connection_id:
          minLength: 1
          type: string
        success_url:
          minLength: 1
          type: string
        failure_url:
          minLength: 1
          type: string
      additionalProperties: false
    ReInitProviderConnectionOutput:
      type: object
      properties:
        provider_name:
          type: string
          nullable: true
        is_re_authentication_required:
          type: boolean
        context:
          nullable: true
      additionalProperties: false
    ReInitProviderConnectionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ReInitProviderConnectionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    RearrangeSchemaOrderInput:
      required:
        - id
        - prior_to_schema_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        id:
          minLength: 1
          type: string
        prior_to_schema_id:
          type: string
      additionalProperties: false
    RearrangeSchemaOrderOutput:
      type: object
      additionalProperties: false
    RearrangeSchemaOrderOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RearrangeSchemaOrderOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    RegisterWebhookInput:
      required:
        - context
        - entity_type_name
        - event_type_name
        - sleekflow_company_id
        - url
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        event_type_name:
          minLength: 1
          type: string
        url:
          minLength: 1
          type: string
        context:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    RegisterWebhookOutput:
      type: object
      additionalProperties: false
    RegisterWebhookOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RegisterWebhookOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    RemoveWebhooksInput:
      required:
        - entity_type_name
        - event_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        event_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    RemoveWebhooksOutput:
      type: object
      additionalProperties: false
    RemoveWebhooksOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RemoveWebhooksOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    RenameProviderConnectionInput:
      required:
        - name
        - provider_connection_id
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        provider_connection_id:
          minLength: 1
          type: string
        name:
          minLength: 1
          type: string
      additionalProperties: false
    RenameProviderConnectionOutput:
      type: object
      properties:
        connection:
          $ref: '#/components/schemas/ProviderConnectionDto'
      additionalProperties: false
    RenameProviderConnectionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RenameProviderConnectionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SchemaAccessibilitySettings:
      type: object
      properties:
        category:
          type: string
          nullable: true
      additionalProperties: false
    SchemaDto:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        display_name:
          type: string
          nullable: true
        unique_name:
          type: string
          nullable: true
        relationship_type:
          type: string
          nullable: true
        is_enabled:
          type: boolean
        sorting_weight:
          type: integer
          format: int32
        primary_property:
          $ref: '#/components/schemas/PrimaryProperty'
        properties:
          type: array
          items:
            $ref: '#/components/schemas/Property'
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        schema_accessibility_settings:
          $ref: '#/components/schemas/SchemaAccessibilitySettings'
        id:
          type: string
          nullable: true
      additionalProperties: false
    SchemaFilter:
      required:
        - field_name
        - operator
      type: object
      properties:
        field_name:
          maxLength: 128
          minLength: 1
          pattern: '^[a-zA-Z0-9_\[\]\.:]+$'
          type: string
        operator:
          maxLength: 128
          minLength: 1
          pattern: ^(=|>|<|>=|<=|!=|contains|arrayContains|in|startsWith)$
          type: string
        value:
          nullable: true
      additionalProperties: false
    SchemaPropertyDataTypes:
      type: object
      additionalProperties: false
    SchemaSort:
      required:
        - direction
        - field_name
      type: object
      properties:
        field_name:
          maxLength: 128
          minLength: 1
          pattern: '^[a-zA-Z0-9_\[\]\.:]+$'
          type: string
        direction:
          maxLength: 128
          minLength: 1
          pattern: ^(asc|desc|ASC|DESC)$
          type: string
      additionalProperties: false
    SchemafulObjectDto:
      type: object
      properties:
        schema_id:
          type: string
          nullable: true
        primary_property_value:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          type: string
          nullable: true
        property_values:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        created_via:
          type: string
          nullable: true
        updated_via:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
      additionalProperties: false
    SchemafulObjectFilter:
      required:
        - field_name
        - is_property_value
        - operator
      type: object
      properties:
        field_name:
          maxLength: 128
          minLength: 1
          pattern: '^[a-zA-Z0-9_\[\]\.:]+$'
          type: string
        operator:
          maxLength: 128
          minLength: 1
          pattern: ^(=|>|<|>=|<=|!=|contains|arrayContains|in|startsWith|isDefined|notContains|notArrayContains|notIn)$
          type: string
        value:
          nullable: true
        is_property_value:
          type: boolean
      additionalProperties: false
    SchemafulObjectSort:
      required:
        - direction
        - field_name
        - is_property_value
      type: object
      properties:
        field_name:
          maxLength: 128
          minLength: 1
          pattern: '^[a-zA-Z0-9_\[\]\.:]+$'
          type: string
        direction:
          maxLength: 128
          minLength: 1
          pattern: ^(asc|desc|ASC|DESC)$
          type: string
        is_property_value:
          type: boolean
      additionalProperties: false
    SearchObjectCondition:
      required:
        - field_name
        - operator
      type: object
      properties:
        field_name:
          minLength: 1
          type: string
        operator:
          minLength: 1
          type: string
        value:
          nullable: true
      additionalProperties: false
    SearchObjectsInput:
      required:
        - entity_type_name
        - provider_connection_id
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_connection_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/SearchObjectCondition'
          nullable: true
        typed_ids:
          type: array
          items:
            $ref: '#/components/schemas/TypedId'
          nullable: true
      additionalProperties: false
    SearchObjectsOutput:
      type: object
      properties:
        records:
          type: array
          items:
            type: object
            additionalProperties: { }
          nullable: true
      additionalProperties: false
    SearchObjectsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SearchObjectsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SearchSalesforceObjectInput:
      required:
        - conditions
        - entity_type_name
        - salesforce_connection_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        salesforce_connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/SearchObjectCondition'
      additionalProperties: false
    SearchSalesforceObjectOutput:
      type: object
      properties:
        record:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    SearchSalesforceObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SearchSalesforceObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Sequential:
      type: object
      properties:
        sequence_prefix:
          maxLength: 10
          minLength: 1
          type: string
          nullable: true
        sequence_digit_length:
          maximum: 20
          minimum: 5
          type: integer
          format: int32
      additionalProperties: false
    SleekflowStaff:
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    Sort:
      required:
        - direction
        - field_name
        - is_case_sensitive
      type: object
      properties:
        field_name:
          maxLength: 128
          minLength: 1
          pattern: '^[a-zA-Z0-9_\[\]\.:]+$'
          type: string
        direction:
          maxLength: 128
          minLength: 1
          pattern: ^(asc|desc|ASC|DESC)$
          type: string
        is_case_sensitive:
          type: boolean
      additionalProperties: false
    SyncConfig:
      required:
        - interval
      type: object
      properties:
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilterGroup'
          nullable: true
        filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilter'
          nullable: true
          writeOnly: true
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
        interval:
          maximum: 86400
          minimum: 3600
          type: integer
          format: int32
        entity_type_name:
          type: string
          nullable: true
        sync_mode:
          type: string
          nullable: true
      additionalProperties: false
    SyncConfigDto:
      type: object
      properties:
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilterGroup'
          nullable: true
        filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilter'
          nullable: true
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
        interval:
          type: integer
          format: int32
        entity_type_name:
          type: string
          nullable: true
        sync_mode:
          type: string
          nullable: true
      additionalProperties: false
    SyncConfigFieldFilter:
      type: object
      properties:
        name:
          type: string
          nullable: true
      additionalProperties: false
    SyncConfigFilter:
      type: object
      properties:
        field_name:
          type: string
          nullable: true
        value:
          type: string
          nullable: true
        operator:
          type: string
          nullable: true
      additionalProperties: false
    SyncConfigFilterGroup:
      type: object
      properties:
        filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilter'
          nullable: true
      additionalProperties: false
    TerminateLoopThroughHubspotObjectsInput:
      required:
        - flow_hub_workflow_id
        - flow_hub_workflow_versioned_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        flow_hub_workflow_id:
          minLength: 1
          type: string
        flow_hub_workflow_versioned_id:
          minLength: 1
          type: string
      additionalProperties: false
    TerminateLoopThroughHubspotObjectsOutput:
      type: object
      properties:
        is_terminated:
          type: boolean
      additionalProperties: false
    TerminateLoopThroughHubspotObjectsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/TerminateLoopThroughHubspotObjectsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    TerminateLoopThroughSalesforceObjectsInput:
      required:
        - flow_hub_workflow_id
        - flow_hub_workflow_versioned_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        flow_hub_workflow_id:
          minLength: 1
          type: string
        flow_hub_workflow_versioned_id:
          minLength: 1
          type: string
      additionalProperties: false
    TerminateLoopThroughSalesforceObjectsOutput:
      type: object
      properties:
        is_terminated:
          type: boolean
      additionalProperties: false
    TerminateLoopThroughSalesforceObjectsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/TerminateLoopThroughSalesforceObjectsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    TerminateLoopThroughSchemafulObjectsInput:
      required:
        - flow_hub_workflow_id
        - flow_hub_workflow_versioned_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        flow_hub_workflow_id:
          minLength: 1
          type: string
        flow_hub_workflow_versioned_id:
          minLength: 1
          type: string
      additionalProperties: false
    TerminateLoopThroughSchemafulObjectsOutput:
      type: object
      properties:
        is_terminated:
          type: boolean
      additionalProperties: false
    TerminateLoopThroughSchemafulObjectsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/TerminateLoopThroughSchemafulObjectsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    TriggerProviderSyncObjectInput:
      required:
        - entity_type_name
        - object_id
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        object_id:
          minLength: 1
          type: string
      additionalProperties: false
    TriggerProviderSyncObjectOutput:
      type: object
      additionalProperties: false
    TriggerProviderSyncObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/TriggerProviderSyncObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    TriggerProviderSyncObjectsInput:
      required:
        - entity_type_name
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    TriggerProviderSyncObjectsOutput:
      type: object
      properties:
        count:
          type: integer
          format: int64
        provider_state_id:
          type: string
          nullable: true
      additionalProperties: false
    TriggerProviderSyncObjectsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/TriggerProviderSyncObjectsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    TypedId:
      type: object
      properties:
        type:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
      additionalProperties: false
    UnassociateObjectInput:
      required:
        - entity_type_name
        - object_id
        - provider_name
        - provider_object_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 0
          type: string
        entity_type_name:
          maxLength: 64
          minLength: 0
          type: string
        object_id:
          maxLength: 128
          minLength: 0
          type: string
        provider_object_id:
          maxLength: 128
          minLength: 0
          type: string
        provider_name:
          maxLength: 128
          minLength: 0
          type: string
      additionalProperties: false
    UnassociateObjectOutput:
      type: object
      additionalProperties: false
    UnassociateObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UnassociateObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UnifyRule:
      type: object
      properties:
        field_name:
          type: string
          nullable: true
        strategy:
          type: string
          nullable: true
        provider_precedences:
          type: array
          items:
            type: string
          nullable: true
        is_system:
          type: boolean
      additionalProperties: false
    UnifyRuleDto:
      type: object
      properties:
        field_name:
          type: string
          nullable: true
        strategy:
          type: string
          nullable: true
        provider_precedences:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpdateCrmHubConfigInput:
      required:
        - id
        - sleekflow_company_id
        - usage_limit
      type: object
      properties:
        id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        usage_limit:
          $ref: '#/components/schemas/UsageLimit'
        feature_accessibility_settings:
          $ref: '#/components/schemas/FeatureAccessibilitySettings'
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpdateCrmHubConfigOutput:
      type: object
      properties:
        crm_hub_config:
          $ref: '#/components/schemas/CrmHubConfig'
      additionalProperties: false
    UpdateCrmHubConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateCrmHubConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateCrmHubConfigUsageLimitOffsetInput:
      required:
        - id
        - sleekflow_company_id
        - usage_limit_offset
      type: object
      properties:
        id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        usage_limit_offset:
          $ref: '#/components/schemas/UsageLimitOffset'
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpdateCrmHubConfigUsageLimitOffsetOutput:
      type: object
      properties:
        crm_hub_config:
          $ref: '#/components/schemas/CrmHubConfig'
      additionalProperties: false
    UpdateCrmHubConfigUsageLimitOffsetOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateCrmHubConfigUsageLimitOffsetOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateObjectInput:
      required:
        - dict
        - entity_type_name
        - provider_connection_id
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        provider_connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        dict:
          type: object
          additionalProperties:
            nullable: true
        typed_ids:
          type: array
          items:
            $ref: '#/components/schemas/TypedId'
          nullable: true
      additionalProperties: false
    UpdateObjectOutput:
      type: object
      additionalProperties: false
    UpdateObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateProviderInput:
      required:
        - default_region_code
        - entity_type_name_to_sync_config_dict
        - provider_name
        - record_statuses
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        default_region_code:
          minLength: 1
          type: string
        entity_type_name_to_sync_config_dict:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/UpdateSyncConfigDto'
        record_statuses:
          type: array
          items:
            type: string
        provider_limit:
          $ref: '#/components/schemas/ProviderLimit'
      additionalProperties: false
    UpdateProviderOutput:
      type: object
      properties:
        provider_config:
          $ref: '#/components/schemas/ProviderConfig'
      additionalProperties: false
    UpdateProviderOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateProviderOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateProviderUserMappingConfigInput:
      required:
        - provider_name
        - provider_user_mapping_config_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        provider_user_mapping_config_id:
          minLength: 1
          type: string
        user_mappings:
          type: array
          items:
            $ref: '#/components/schemas/UserMapping'
          nullable: true
      additionalProperties: false
    UpdateProviderUserMappingConfigOutput:
      type: object
      properties:
        user_mapping_config:
          $ref: '#/components/schemas/ProviderUserMappingConfigDto'
      additionalProperties: false
    UpdateProviderUserMappingConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateProviderUserMappingConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateSalesforceObjectInput:
      required:
        - dict
        - entity_type_name
        - object_id
        - salesforce_connection_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        salesforce_connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        object_id:
          minLength: 1
          type: string
        dict:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    UpdateSalesforceObjectOutput:
      type: object
      additionalProperties: false
    UpdateSalesforceObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateSalesforceObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateSchemaInput:
      required:
        - display_name
        - id
        - is_enabled
        - primary_property_display_name
        - properties
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        id:
          minLength: 1
          type: string
        display_name:
          minLength: 1
          type: string
        primary_property_display_name:
          minLength: 1
          type: string
        is_enabled:
          type: boolean
        properties:
          type: array
          items:
            $ref: '#/components/schemas/Property'
      additionalProperties: false
    UpdateSchemaOutput:
      type: object
      properties:
        schema:
          $ref: '#/components/schemas/SchemaDto'
      additionalProperties: false
    UpdateSchemaOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateSchemaOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateSchemafulObjectInput:
      required:
        - id
        - property_values
        - schema_id
        - sleekflow_company_id
        - sleekflow_staff_id
        - sleekflow_user_profile_id
      type: object
      properties:
        id:
          minLength: 1
          type: string
        schema_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        property_values:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_user_profile_id:
          type: string
        sleekflow_staff_id:
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        updated_via:
          type: string
          nullable: true
      additionalProperties: false
    UpdateSchemafulObjectOutput:
      type: object
      properties:
        schemaful_object:
          $ref: '#/components/schemas/SchemafulObjectDto'
      additionalProperties: false
    UpdateSchemafulObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateSchemafulObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateSyncConfigDto:
      required:
        - interval
      type: object
      properties:
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilterGroup'
          nullable: true
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
        interval:
          maximum: 86400
          minimum: 3600
          type: integer
          format: int32
        entity_type_name:
          type: string
          nullable: true
        sync_mode:
          type: string
          nullable: true
      additionalProperties: false
    UpsertObjectInput:
      required:
        - dict
        - entity_type_name
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        dict:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    UpsertObjectOutput:
      type: object
      additionalProperties: false
    UpsertObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpsertObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpsertUnifyRulesInput:
      required:
        - entity_type_name
        - sleekflow_company_id
        - unify_rules
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        unify_rules:
          type: array
          items:
            $ref: '#/components/schemas/UnifyRuleDto'
      additionalProperties: false
    UpsertUnifyRulesOutput:
      type: object
      additionalProperties: false
    UpsertUnifyRulesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpsertUnifyRulesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UsageLimit:
      type: object
      properties:
        custom_object_maximum_schema_num:
          type: integer
          format: int32
          nullable: true
        custom_object_maximum_property_num_per_schema:
          type: integer
          format: int32
          nullable: true
        custom_object_maximum_schemaful_object_num_per_schema:
          type: integer
          format: int32
          nullable: true
        custom_object_maximum_schemaful_object_num_per_company:
          type: integer
          format: int32
          nullable: true
        custom_object_maximum_array_object_array_size:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    UsageLimitOffset:
      type: object
      properties:
        custom_object_maximum_schema_num_offset:
          type: integer
          format: int32
          nullable: true
        custom_object_maximum_property_num_per_schema_offset:
          type: integer
          format: int32
          nullable: true
        custom_object_maximum_schemaful_object_num_per_schema_offset:
          type: integer
          format: int32
          nullable: true
        custom_object_maximum_schemaful_object_num_per_company_offset:
          type: integer
          format: int32
          nullable: true
        custom_object_maximum_array_object_array_size_offset:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    UserMapping:
      type: object
      properties:
        provider_user_id:
          type: string
          nullable: true
        sleekflow_user_id:
          type: string
          nullable: true
      additionalProperties: false
    Webhook:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        entity_type_name:
          type: string
          nullable: true
        event_type_name:
          type: string
          nullable: true
        max_retry_count:
          type: integer
          format: int32
          nullable: true
        url:
          type: string
          nullable: true
        secondary_url:
          type: string
          nullable: true
        context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false