using MassTransit;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutions;
using Sleekflow.FlowHub.StepExecutors.Calls;
using Sleekflow.Ids;
using Sleekflow.Models.Events;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnAiAgentStepExitEventEventConsumerDefinition
    : ConsumerDefinition<OnAiAgentStepExitEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnAiAgentStepExitEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 128;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 0;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(2);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnAiAgentStepExitEventConsumer(
    ICoreCommander coreCommander,
    IServiceBusManager serviceBusManager,
    IStateService stateService,
    IStepExecutionService stepExecutionService,
    ILogger<OnAiAgentStepExitEventConsumer> logger,
    IIdService idService)
    : IConsumer<OnAiAgentStepExitEvent>, IScopedService
{
    public async Task Consume(ConsumeContext<OnAiAgentStepExitEvent> context)
    {
        var message = context.Message;
        logger.LogInformation("Consuming OnAiAgentStepExitEvent for StepId: {StepId}, StateId: {StateId}, WorkflowId: {WorkflowId}", message.StepId, message.StateId, message.WorkflowId);

        var state = await stateService.GetProxyStateAsync(message.StateId);

        if (state == null)
        {
            throw new Exception($"State not found: {message.StateId} for OnAiAgentStepExitEvent consumer.");
        }

        if (state.Identity == null)
        {
            throw new Exception($"State.Identity is null for StateId: {message.StateId} in OnAiAgentStepExitEvent consumer.");
        }

        var stepExecutionHistory = await stepExecutionService.GetStepExecutionsByFilterAsync(
            state.Identity.SleekflowCompanyId,
            state.Id,
            message.StepId,
            StepExecutionStatuses.Complete);

        if (stepExecutionHistory.Count != 0)
        {
            return;
        }

        try
        {
            logger.LogInformation(
                "OnAiAgentStepExitEventConsumer for StepId: {StepId}, StateId: {StateId}, WorkflowId: {WorkflowId}, ExitCondition: {ExitCondition}",
                message.StepId, message.StateId, message.WorkflowId, message.ExitCondition);

            // to support old workflow trigger, will keep this part
            await coreCommander.ExecuteAsync(
                state.Origin,
                "UpdateContactLabelRelationships",
                new UpdateContactLabelRelationshipsStepExecutor.UpdateContactLabelRelationshipsInput(
                    message.StateId,
                    state.Identity,
                    state.Identity.ObjectId,
                    null,
                    [$"AI_POC_{message.WorkflowId}_{message.StepId}", "AI_SESSION"],
                    null));
            logger.LogInformation("Labels removed for StateId: {StateId}", message.StateId);

            await coreCommander.ExecuteAsync(
                state.Origin,
                "AddInternalNoteToContact",
                new AddInternalNoteToContactStepExecutor.AddInternalNoteToContactInput(
                    message.StateId,
                    state.Identity,
                    state.Identity.ObjectId,
                    $"[[SLEEKFLOW_AI_HANDOVER]] \nAI Agent exited with reason: {message.ExitCondition}"));

            var aggregateContext = new Dictionary<string, string>
            {
                {
                    "exit_condition", message.ExitCondition
                }
            };

            await serviceBusManager.PublishAsync(
                new OnAgentCompleteStepActivationEvent(
                    message.StepId,
                    message.StateId,
                    message.StackEntries,
                    JsonConvert.SerializeObject(aggregateContext)));

            logger.LogInformation("OnAgentCompleteStepActivationEvent published for StepId: {StepId}, StateId: {StateId}", message.StepId, message.StateId);

            await serviceBusManager.PublishAsync(
                new OnUserEventHappenedEventHubEvent(
                    idService.GetId(SysTypeNames.UserEventHubUserEvent),
                    "AIConversationClosed",
                    state.Identity.SleekflowCompanyId,
                    state.Identity.ObjectId,
                    state.Identity.ObjectType,
                    JsonConvert.SerializeObject(aggregateContext.First()),
                    new OnUserEventHappenedEventHubEventProperties(state.WorkflowContext.SnapshottedWorkflow.Name),
                    new OnUserEventHappenedEventHubEventMetadata(
                        state.Identity.WorkflowId,
                        state.Identity.WorkflowVersionedId,
                        null,
                        null,
                        null,
                        null)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing OnAiAgentStepExitEvent for StepId: {StepId}, StateId: {StateId}. This will likely be retried or moved to an error queue.", message.StepId, message.StateId);
        }
    }
}