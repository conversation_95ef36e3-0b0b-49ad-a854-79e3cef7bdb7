using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States; // Assuming StackEntry is here

namespace Sleekflow.Models.Events
{
    public abstract class AgentEventBase
    {
        [JsonProperty("aggregate_step_id")]
        public string AggregateStepId { get; set; }

        [JsonProperty("proxy_state_id")]
        public string ProxyStateId { get; set; }

        [JsonProperty("stack_entries")]
        public Stack<StackEntry> StackEntries { get; set; }

        [JsonProperty("workflow_id")]
        public string? WorkflowId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        public string? WorkflowVersionedId { get; set; }

        [JsonConstructor]
        protected AgentEventBase(
            string aggregateStepId,
            string proxyStateId,
            Stack<StackEntry> stackEntries,
            string? workflowId = null,
            string? workflowVersionedId = null)
        {
            AggregateStepId = aggregateStepId;
            ProxyStateId = proxyStateId;
            StackEntries = stackEntries;
            WorkflowId = workflowId;
            WorkflowVersionedId = workflowVersionedId;
        }
    }
}