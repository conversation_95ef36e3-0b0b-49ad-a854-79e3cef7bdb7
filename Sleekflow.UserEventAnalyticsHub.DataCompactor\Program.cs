﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Extensions;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Services;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Configs;
using Sleekflow.DependencyInjection;

var host = Host.CreateDefaultBuilder(args)
    .ConfigureServices((context, services) =>
    {
        // Configure logging
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });

                // Register data compactor services
        services.AddDataCompactorServices();
    })
    .Build();

var logger = host.Services.GetRequiredService<ILogger<Program>>();

try
{
    logger.LogInformation("Starting DataCompactor service...");

    // Test connectivity to all services
    await TestConnectivityAsync(host.Services, logger);

    // Check if we're in discovery-only mode
    var discoveryOnlyMode = Environment.GetEnvironmentVariable("COMPACTOR_DISCOVERY_ONLY")?.ToLower() == "true";
    var sqlJobTestMode = Environment.GetEnvironmentVariable("COMPACTOR_SQL_JOB_TEST")?.ToLower() == "true";

    if (discoveryOnlyMode)
    {
        logger.LogInformation("Running in discovery-only mode...");
        await RunDiscoveryOnlyAsync(host.Services, logger);
    }
    else if (sqlJobTestMode)
    {
        logger.LogInformation("Running in SQL Job test mode...");
        await RunSqlJobTestAsync(host.Services, logger);
    }
    else
    {
        // Start the main processing logic
        await RunDataCompactionAsync(host.Services, logger);
    }

    logger.LogInformation("DataCompactor service completed successfully.");
}
catch (Exception ex)
{
    logger.LogError(ex, "DataCompactor service failed with exception");
    throw;
}

static async Task TestConnectivityAsync(IServiceProvider services, ILogger logger)
{
    logger.LogInformation("Testing connectivity to all services...");

    try
    {
        var blobService = services.GetRequiredService<IBlobDiscoveryService>();
        var schemaService = services.GetRequiredService<IPostgreSqlSchemaService>();

        // Test connectivity
        await blobService.TestConnectivityAsync();
        await schemaService.TestConnectivityAsync();

        // Test SQL Job Processing Service (if needed)
        try
        {
            var sqlJobService = services.GetService<ISqlJobProcessingService>();
            if (sqlJobService != null)
            {
                logger.LogInformation("SqlJobProcessingService is available for testing");
            }
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "SqlJobProcessingService is not available - this is expected if not configured");
        }

        logger.LogInformation("All connectivity tests passed.");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Connectivity test failed");
        throw;
    }
}

static async Task RunDiscoveryOnlyAsync(IServiceProvider services, ILogger logger)
{
    logger.LogInformation("Starting discovery-only mode...");

    try
    {
        var blobService = services.GetRequiredService<IBlobDiscoveryService>();
        var schemaService = services.GetRequiredService<IPostgreSqlSchemaService>();

        // Discover companies
        logger.LogInformation("Discovering companies...");
        var companyIds = await blobService.GetAllCompanyIdsAsync();

        logger.LogInformation("Found {CompanyCount} companies", companyIds.Count);

        // Display company information
        var maxCompaniesToShow = int.TryParse(Environment.GetEnvironmentVariable("COMPACTOR_MAX_DISCOVERY_COMPANIES"), out var maxShow) ? maxShow : 5;
        var companiesToShow = companyIds.Take(maxCompaniesToShow).ToList();

        foreach (var companyId in companiesToShow)
        {
            var files = await blobService.DiscoverCompanyFilesAsync(companyId);
            logger.LogInformation("Company: {CompanyId}, Files: {FileCount}", companyId, files.Count);

            // Optionally show file details
            var showFileDetails = Environment.GetEnvironmentVariable("COMPACTOR_SHOW_FILE_DETAILS")?.ToLower() == "true";
            if (showFileDetails)
            {
                var maxFilesToShow = int.TryParse(Environment.GetEnvironmentVariable("COMPACTOR_MAX_FILES_TO_SHOW"), out var maxFiles) ? maxFiles : 3;

                foreach (var file in files.Take(maxFilesToShow))
                {
                    logger.LogInformation("  File: {BlobPath}", file.BlobPath);
                    logger.LogInformation("    Size: {SizeBytes:N0} bytes, Modified: {LastModified:yyyy-MM-dd HH:mm:ss}", file.SizeBytes, file.LastModified);
                }

                if (files.Count > maxFilesToShow)
                {
                    logger.LogInformation("  ... and {RemainingFiles} more files", files.Count - maxFilesToShow);
                }
            }
        }

        if (companyIds.Count > maxCompaniesToShow)
        {
            logger.LogInformation("... and {RemainingCompanies} more companies", companyIds.Count - maxCompaniesToShow);
        }

        // Test schema creation for first company if requested
        var testSchemaCreation = Environment.GetEnvironmentVariable("COMPACTOR_TEST_SCHEMA_CREATION")?.ToLower() == "true";
        if (testSchemaCreation && companyIds.Count > 0)
        {
            var firstCompanyId = companyIds.First();
            logger.LogInformation("Testing schema creation for company: {CompanyId}", firstCompanyId);

            await schemaService.EnsureCompanySchemaAsync(firstCompanyId);
            logger.LogInformation("Schema creation test completed successfully");
        }

        logger.LogInformation("Discovery-only mode completed successfully.");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Discovery-only mode failed");
        throw;
    }
}

static async Task RunSqlJobTestAsync(IServiceProvider services, ILogger logger)
{
    logger.LogInformation("Starting SQL Job test mode...");

    try
    {
        var sqlJobService = services.GetService<ISqlJobProcessingService>();

        if (sqlJobService == null)
        {
            logger.LogError("SqlJobProcessingService is not registered. Please ensure it's properly configured in the DI container.");
            return;
        }

        // Test parameters
        var testCompanyId = Environment.GetEnvironmentVariable("TEST_COMPANY_ID") ?? "test-company-123";
        var testJobId = Environment.GetEnvironmentVariable("TEST_JOB_ID") ?? "test-job-456";
        var testSqlQuery = Environment.GetEnvironmentVariable("TEST_SQL_QUERY") ?? "SELECT COUNT(*) as total_events FROM events WHERE eventType = 'user_action'";

        logger.LogInformation("Testing SQL Job Processing with:");
        logger.LogInformation("  Company ID: {CompanyId}", testCompanyId);
        logger.LogInformation("  Job ID: {JobId}", testJobId);
        logger.LogInformation("  SQL Query: {SqlQuery}", testSqlQuery);

        // Test short SQL job (in-memory results)
        try
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMinutes(5));
            var startTime = DateTime.UtcNow;

            logger.LogInformation("Starting short SQL job test...");
            var results = await sqlJobService.ProcessShortSqlJobAsync(testJobId, testCompanyId, cts.Token);

            var duration = DateTime.UtcNow - startTime;
            logger.LogInformation("Short SQL job completed in {Duration:mm\\:ss\\.fff}", duration);
            logger.LogInformation("Returned {ResultCount} results", results.Count);

            // Log first few results for inspection
            var maxResultsToShow = Math.Min(results.Count, 3);
            for (int i = 0; i < maxResultsToShow; i++)
            {
                var result = results[i];
                var resultJson = System.Text.Json.JsonSerializer.Serialize(result, new System.Text.Json.JsonSerializerOptions { WriteIndented = false });
                logger.LogInformation("  Result {Index}: {ResultData}", i + 1, resultJson);
            }

            if (results.Count > maxResultsToShow)
            {
                logger.LogInformation("  ... and {RemainingResults} more results", results.Count - maxResultsToShow);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Short SQL job test failed");
            throw;
        }

        logger.LogInformation("SQL Job test mode completed successfully.");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "SQL Job test mode failed");
        throw;
    }
}

static async Task RunDataCompactionAsync(IServiceProvider services, ILogger logger)
{
    logger.LogInformation("Starting data compaction process...");

    try
    {
        var compactionService = services.GetRequiredService<IParquetCompactionService>();

        // Run the compaction process
        await compactionService.CompactAllCompaniesAsync();

        logger.LogInformation("Data compaction process completed successfully.");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Data compaction process failed");
        throw;
    }
}