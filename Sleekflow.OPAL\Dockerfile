﻿# Use the official Permit.io OPAL server image
FROM permitio/opal-server

EXPOSE 80

# Set the container to run in interactive mode with a pseudo-TTY
# This is equivalent to the -it flags in the docker run command
ENV SERVER_BIND_PORT=80
ENV SERVER_PORT=80
ENV DOCKER_INTERACTIVE=1
ENV DOCKER_TTY=1
ENV OPAL_DATA_CONFIG_SOURCES='{"config":{"entries":[{"url":"dummy","config":{"fetcher":"BlobFetcher"},"topics":["policy_data"],"dst_path":"company","polling_interval":30}]}}'
ENV SERVER_HOST=0.0.0.0
#ENV OPAL_POLICY_REPO_URL=""
#ENV OPAL_POLICY_REPO_MAIN_BRANCH="main"
#ENV OPAL_POLICY_REPO_PATH="Policies"
#ENV OPAL_POLICY_REPO_POLL_TIMEOUT="30s"
#ENV OPAL_POLICY_REPO_SSH_KEY=""

# The CMD instruction provides defaults for an executing container
# In this case, we're not specifying any command, so it will use
# the default command defined in the base image
#CMD ["gunicorn", "-b", "0.0.0.0:80", "-k", "uvicorn.workers.UvicornWorker", "--workers=1", "-c", "./gunicorn_conf.py", "opal_server.main:app", "-t", "30", "--keep-alive", "5"]
