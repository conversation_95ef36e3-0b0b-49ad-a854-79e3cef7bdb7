using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents.WebpageDocuments;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;
using Sleekflow.Validations;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.KnowledgeBases;

[TriggerGroup(
    ControllerNames.KnowledgeBases,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class CreateWebpageDocuments
    : ITrigger<CreateWebpageDocuments.CreateWebpageDocumentsInput,
        CreateWebpageDocuments.CreateWebpageDocumentsOutput>
{
    private readonly ISleekflowAuthorizationContext _authorizationContext;
    private readonly IWebpageDocumentService _webpageDocumentService;

    public CreateWebpageDocuments(
        ISleekflowAuthorizationContext authorizationContext,
        IWebpageDocumentService webpageDocumentService)
    {
        _authorizationContext = authorizationContext;
        _webpageDocumentService = webpageDocumentService;
    }

    public class CreateWebpageDocumentsInput
    {
        [JsonProperty("session_id_to_urls")]
        [Required]
        [ValidateObject]
        public Dictionary<string, List<string>> SessionIdToUrls { get; set; }

        [JsonProperty("agent_ids")]
        [Required]
        [ValidateArray]
        public List<string> AgentIds { get; set; }

        [JsonConstructor]
        public CreateWebpageDocumentsInput(
            Dictionary<string, List<string>> sessionIdToUrls,
            List<string> agentIds)
        {
            SessionIdToUrls = sessionIdToUrls;
            AgentIds = agentIds;
        }
    }

    public class CreateWebpageDocumentsOutput
    {
        [JsonConstructor]
        public CreateWebpageDocumentsOutput()
        {
        }
    }

    public async Task<CreateWebpageDocumentsOutput> F(
        CreateWebpageDocumentsInput input)
    {
        var sleekflowCompanyId = _authorizationContext.SleekflowCompanyId!;
        var staffId = _authorizationContext.SleekflowStaffId!;

        await _webpageDocumentService.CreateWebpageDocumentAsync(
            sleekflowCompanyId,
            input.SessionIdToUrls,
            input.AgentIds,
            staffId);

        return new CreateWebpageDocumentsOutput();
    }
}