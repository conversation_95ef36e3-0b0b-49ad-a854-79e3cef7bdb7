using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.KnowledgeBases;

[TriggerGroup(
    ControllerNames.KnowledgeBases,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class GetFileDocumentList
    : ITrigger<
        GetFileDocumentList.GetFileDocumentListInput,
        GetFileDocumentList.GetFileDocumentListOutput>
{
    private readonly ISleekflowAuthorizationContext _authorizationContext;
    private readonly IKbDocumentService _kbDocumentService;

    public GetFileDocumentList(
        ISleekflowAuthorizationContext authorizationContext,
        IKbDocumentService kbDocumentService)
    {
        _authorizationContext = authorizationContext;
        _kbDocumentService = kbDocumentService;
    }

    public class GetFileDocumentListInput
    {
        [JsonProperty("filter_training_status")]
        public string? FilterTrainingStatus { get; set; }

        [JsonProperty("filter_exclude_training_status")]
        [Validations.ValidateArray]
        public List<string>? FilterExcludeTrainingStatus { get; set; }

        [JsonProperty("filter_agent")]
        public string? FilterAgent { get; set; }

        [JsonProperty("filter_exclude_agent")]
        public string? FilterExcludeAgent { get; set; }

        [JsonProperty("sort_by")]
        public string? SortBy { get; set; }

        [JsonProperty("sort_direction")]
        public string? SortDirection { get; set; }

        [JsonConstructor]
        public GetFileDocumentListInput(
            string? filterTrainingStatus,
            List<string>? filterExcludeTrainingStatus,
            string? filterAgent,
            string? filterExcludeAgent,
            string? sortBy,
            string? sortDirection)
        {
            FilterTrainingStatus = filterTrainingStatus;
            FilterExcludeTrainingStatus = filterExcludeTrainingStatus;
            FilterAgent = filterAgent;
            FilterExcludeAgent = filterExcludeAgent;
            SortBy = sortBy;
            SortDirection = sortDirection;
        }
    }

    public class GetFileDocumentListOutput
    {
        [JsonProperty("kb_documents")]
        public List<KbDocument> KbDocuments { get; set; }

        [JsonConstructor]
        public GetFileDocumentListOutput(List<KbDocument> kbDocuments)
        {
            KbDocuments = kbDocuments;
        }
    }

    public async Task<GetFileDocumentListOutput> F(
        GetFileDocumentListInput getFileDocumentListInput)
    {
        var sleekflowCompanyId = _authorizationContext.SleekflowCompanyId!;
        var filterTrainingStatus = getFileDocumentListInput.FilterTrainingStatus;
        var filterExcludeTrainingStatus = getFileDocumentListInput.FilterExcludeTrainingStatus;
        var filterAgent = getFileDocumentListInput.FilterAgent;
        var filterExcludeAgent = getFileDocumentListInput.FilterExcludeAgent;
        var sortBy = getFileDocumentListInput.SortBy;
        var sortDirection = getFileDocumentListInput.SortDirection;

        var kbDocuments = await _kbDocumentService.GetKbDocumentsByCompanyAsync(
            sleekflowCompanyId,
            filterTrainingStatus,
            filterExcludeTrainingStatus,
            filterAgent,
            filterExcludeAgent);

        IEnumerable<KbDocument> resultLists = kbDocuments;

        if (!string.IsNullOrEmpty(sortBy) && !string.IsNullOrEmpty(sortDirection))
        {
            var webpageDocuments = resultLists.OfType<WebpageDocument>();
            var websiteDocuments = resultLists.OfType<WebsiteDocument>();
            var fileDocuments = resultLists.OfType<FileDocument>();

            if (sortBy == FilterConditions.TotalPages)
            {
                // WebsiteDocument at the top without sorting, then FileDocument sorted by TotalPages
                IEnumerable<FileDocument> sortedFileDocuments;
                if (sortDirection == FilterConditions.OrderAsc)
                {
                    sortedFileDocuments = fileDocuments.OrderBy(e => e.DocumentStatistics.TotalPages);
                }
                else
                {
                    sortedFileDocuments = fileDocuments.OrderByDescending(e => e.DocumentStatistics.TotalPages);
                }

                resultLists = webpageDocuments.Concat(websiteDocuments.Concat(sortedFileDocuments.Cast<KbDocument>()));
            }
            else if (sortBy == FilterConditions.SourceName)
            {
                // WebsiteDocument sorted by BaseUrl at the top, then FileDocument sorted by FileName
                IEnumerable<WebpageDocument> sortedWebpageDocuments;
                IEnumerable<WebsiteDocument> sortedWebsiteDocuments;
                IEnumerable<FileDocument> sortedFileDocuments;

                if (sortDirection == FilterConditions.OrderAsc)
                {
                    sortedWebpageDocuments = webpageDocuments.OrderBy(e => e.Url);
                    sortedWebsiteDocuments = websiteDocuments.OrderBy(e => e.BaseUrl);
                    sortedFileDocuments = fileDocuments.OrderBy(e => e.FileName);
                }
                else
                {
                    sortedWebpageDocuments = webpageDocuments.OrderByDescending(e => e.Url);
                    sortedWebsiteDocuments = websiteDocuments.OrderByDescending(e => e.BaseUrl);
                    sortedFileDocuments = fileDocuments.OrderByDescending(e => e.FileName);
                }

                resultLists = sortedWebpageDocuments.Concat(sortedWebsiteDocuments.Concat(sortedFileDocuments.Cast<KbDocument>()));
            }
            else
            {
                // For other sorting fields (UploadDate, LastUpdated), use the original logic
                if (sortDirection == FilterConditions.OrderAsc)
                {
                    resultLists = sortBy switch
                    {
                        FilterConditions.UploadDate => resultLists.OrderBy(e => e.CreatedAt),
                        FilterConditions.LastUpdated => resultLists.OrderBy(e => e.UpdatedAt),
                        _ => resultLists
                    };
                }
                else if (sortDirection == FilterConditions.OrderDesc)
                {
                    resultLists = sortBy switch
                    {
                        FilterConditions.UploadDate => resultLists.OrderByDescending(e => e.CreatedAt),
                        FilterConditions.LastUpdated => resultLists.OrderByDescending(e => e.UpdatedAt),
                        _ => resultLists
                    };
                }
            }
        }

        return new GetFileDocumentListOutput(resultLists.ToList());
    }
}