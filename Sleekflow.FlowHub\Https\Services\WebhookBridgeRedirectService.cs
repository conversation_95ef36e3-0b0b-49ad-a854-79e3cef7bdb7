using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.Models.Events;

namespace Sleekflow.FlowHub.Https.Services;

public interface IWebhookBridgeRedirectService
{
    Task<(bool IsSuccess, string Response)> RedirectAsync(
        string url,
        string method,
        CancellationToken cancellationToken = default);

    Task<(bool IsSuccess, string Response)> RedirectAsync(
        string url,
        string method,
        Dictionary<string, object?>? headerDict,
        string? httpContent,
        string? mediaType,
        CancellationToken cancellationToken = default);
}

public class WebhookBridgeRedirectService : IScopedService, IWebhookBridgeRedirectService
{
    private readonly IRequestClient<OnHttpRedirectRequest> _client;
    private readonly ILogger<WebhookBridgeRedirectService> _logger;

    public WebhookBridgeRedirectService(
        IRequestClient<OnHttpRedirectRequest> client,
        ILogger<WebhookBridgeRedirectService> logger)
    {
        _client = client;
        _logger = logger;
    }

    public async Task<(bool IsSuccess, string Response)> RedirectAsync(
        string url,
        string method,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var evt = new OnHttpRedirectRequest(
                url,
                method,
                null,
                null,
                null);

            var res = await _client.GetResponse<OnHttpRedirectReply>(evt, cancellationToken);
            return (res.Message.Success, res.Message.HttpResponseMessage);
        }
        catch (Exception e)
        {
            _logger.LogError(
                "[WebhookBridgeRedirectService]: failed to redirect due to {Error}",
                e.Message);
            return (false, e.Message);
        }
    }

    public async Task<(bool IsSuccess, string Response)> RedirectAsync(
        string url,
        string method,
        Dictionary<string, object?>? headerDict,
        string? httpContent,
        string? mediaType,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var evt = new OnHttpRedirectRequest(
                url,
                method,
                headerDict,
                httpContent,
                mediaType);

            var res = await _client.GetResponse<OnHttpRedirectReply>(evt, cancellationToken);

            return (res.Message.Success, res.Message.HttpResponseMessage);
        }
        catch (Exception e)
        {
            _logger.LogError(
                "[WebhookBridgeRedirectService]: failed to redirect due to {Error}",
                e.Message);
            return (false, e.Message);
        }
    }
}