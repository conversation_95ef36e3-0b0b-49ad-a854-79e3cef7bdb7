﻿namespace Sleekflow.Models.Events;

public class OnHttpRedirectRequest
{
    public string Url { get; set; }

    public string Method { get; set; }

    public Dictionary<string, object?>? HeaderDict { get; set; }

    public string? HttpContent { get; set; }

    public string? MediaType { get; set; }

    public OnHttpRedirectRequest(
        string url,
        string method,
        Dictionary<string, object?>? headerDict,
        string? httpContent,
        string? mediaType)
    {
        Url = url;
        Method = method;
        HeaderDict = headerDict;
        HttpContent = httpContent;
        MediaType = mediaType;
    }
}