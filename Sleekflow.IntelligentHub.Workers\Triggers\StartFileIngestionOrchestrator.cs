﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.IntelligentHub.Workers.Activities;

namespace Sleekflow.IntelligentHub.Workers.Triggers;

public class StartFileIngestionOrchestrator
{
    private readonly ILogger<StartFileIngestionOrchestrator> _logger;

    public StartFileIngestionOrchestrator(ILogger<StartFileIngestionOrchestrator> logger)
    {
        _logger = logger;
    }

    public class ProcessFileDocumentOrchestratorCustomStatusOutput
    {
        [JsonProperty("process_file_document_output")]
        public ProcessFileDocument.ProcessFileDocumentOutput? ProcessFileDocumentOutput { get; set; }

        [JsonProperty("last_update_time")]
        public DateTime LastUpdateTime { get; set; }

        [JsonConstructor]
        public ProcessFileDocumentOrchestratorCustomStatusOutput(
            ProcessFileDocument.ProcessFileDocumentOutput? processFileDocumentOutput,
            DateTime lastUpdateTime)
        {
            ProcessFileDocumentOutput = processFileDocumentOutput;
            LastUpdateTime = lastUpdateTime;
        }
    }

    public class ProcessFileDocumentOrchestratorOutput
    {
        [JsonConstructor]
        public ProcessFileDocumentOrchestratorOutput()
        {
        }
    }

    [Function(nameof(StartFileIngestionOrchestrator))]
    public async Task<ProcessFileDocumentOrchestratorOutput> RunOrchestrator(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var processFileDocumentInput = context.GetInput<StartFileIngestionEvent>();

        var taskOptions = new TaskOptions(new TaskRetryOptions(new RetryPolicy(5, TimeSpan.FromSeconds(60), 1)));

        try
        {
            await context
                .CallActivityAsync<PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusOutput>(
                    nameof(PatchProcessFileDocumentStatus),
                    new PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusInput(
                        processFileDocumentInput.SleekflowCompanyId,
                        processFileDocumentInput.DocumentId,
                        ProcessFileDocumentStatuses.Converting,
                        0.0),
                    taskOptions);

            ProcessFileDocument.ProcessFileDocumentOutput? progressOutput = null;
            while (true)
            {
                _logger.LogInformation(
                    "StartFileIngestionOrchestrator progress {Progress}",
                    JsonConvert.SerializeObject(progressOutput));

                var processFileDocumentOutput = await context
                    .CallActivityAsync<ProcessFileDocument.ProcessFileDocumentOutput>(
                        nameof(ProcessFileDocument),
                        new ProcessFileDocument.ProcessFileDocumentInput(
                            processFileDocumentInput.SleekflowCompanyId,
                            processFileDocumentInput.DocumentId,
                            processFileDocumentInput.SasDownloadUrl,
                            progressOutput?.FileIngestionProgress),
                        taskOptions);

                progressOutput = processFileDocumentOutput;

                if (progressOutput.IsCompleted)
                {
                    break;
                }

                await context
                    .CallActivityAsync<
                        PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusOutput>(
                        nameof(PatchProcessFileDocumentStatus),
                        new PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusInput(
                            processFileDocumentInput.SleekflowCompanyId,
                            processFileDocumentInput.DocumentId,
                            ProcessFileDocumentStatuses.Converting,
                            progressOutput.ProgressPercentage),
                        taskOptions);
            }

            await context
                .CallActivityAsync<
                    PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusOutput>(
                    nameof(PatchProcessFileDocumentStatus),
                    new PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusInput(
                        processFileDocumentInput.SleekflowCompanyId,
                        processFileDocumentInput.DocumentId,
                        ProcessFileDocumentStatuses.ReadyToAssign,
                        100.0),
                    taskOptions);
        }
        catch (Exception)
        {
            await context
                .CallActivityAsync<
                    PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusOutput>(
                    nameof(PatchProcessFileDocumentStatus),
                    new PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusInput(
                        processFileDocumentInput.SleekflowCompanyId,
                        processFileDocumentInput.DocumentId,
                        ProcessFileDocumentStatuses.FailedToConvert,
                        0.0),
                    taskOptions);
        }

        await context
            .CallSubOrchestratorAsync(
                nameof(UploadFileDocumentToAllAssignedAgentsOrchestrator),
                new StartUploadToAgentKnowledgeBasesEvent(
                    processFileDocumentInput.SleekflowCompanyId,
                    processFileDocumentInput.DocumentId),
                taskOptions);

        return new ProcessFileDocumentOrchestratorOutput();
    }
}