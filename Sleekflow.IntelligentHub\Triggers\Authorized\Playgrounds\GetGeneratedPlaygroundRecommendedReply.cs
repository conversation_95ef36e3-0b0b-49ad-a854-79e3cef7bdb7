﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.KnowledgeBaseEntries;
using Sleekflow.IntelligentHub.Playgrounds;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.Playgrounds;

[TriggerGroup(
    ControllerNames.Playgrounds,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class GetGeneratedPlaygroundRecommendedReply
    : ITrigger<GetGeneratedPlaygroundRecommendedReply.GetGeneratedPlaygroundRecommendedReplyInput,
        GetGeneratedPlaygroundRecommendedReply.GetGeneratedPlaygroundRecommendedReplyOutput>
{
    private readonly IPlaygroundService _playgroundService;
    private readonly ISleekflowAuthorizationContext _authorizationContext;

    public GetGeneratedPlaygroundRecommendedReply(
        IPlaygroundService playgroundService,
        ISleekflowAuthorizationContext authorizationContext)
    {
        _playgroundService = playgroundService;
        _authorizationContext = authorizationContext;
    }

    public class GetGeneratedPlaygroundRecommendedReplyInput
    {
        [Required]
        [JsonProperty("session_id")]
        public string SessionId { get; set; }

        [Required]
        [JsonProperty("message_id")]
        public string MessageId { get; set; }

        [JsonConstructor]
        public GetGeneratedPlaygroundRecommendedReplyInput(string sessionId, string messageId)
        {
            SessionId = sessionId;
            MessageId = messageId;
        }
    }

    public class GetGeneratedPlaygroundRecommendedReplyOutput
    {
        [JsonProperty("recommended_reply")]
        public List<string> RecommendedReply { get; set; }

        [JsonProperty("reply")]
        public string? Reply { get; set; }

        [JsonProperty("knowledge_citations")]
        public List<KnowledgeCitationDto>? KnowledgeCitations { get; set; }

        [JsonConstructor]
        public GetGeneratedPlaygroundRecommendedReplyOutput(
            List<string> recommendedReply,
            string? reply,
            List<KnowledgeCitationDto>? knowledgeCitations)
        {
            RecommendedReply = recommendedReply;
            Reply = reply;
            KnowledgeCitations = knowledgeCitations;
        }
    }

    public async Task<GetGeneratedPlaygroundRecommendedReplyOutput> F(GetGeneratedPlaygroundRecommendedReplyInput input)
    {
        var playground = await _playgroundService.GetOrDefaultAsync(
            input.SessionId,
            _authorizationContext.SleekflowCompanyId!,
            _authorizationContext.SleekflowUserId!);

        if (playground == null)
        {
            throw new SfNotFoundObjectException("Playground not found");
        }

        var recommendedReplies = playground.RecommendedReplies
            .Where(r => r.MessageId == input.MessageId).ToList();

        // ONLY one recommended reply are generated with given message id
        var recommendedReply = recommendedReplies.FirstOrDefault();

        return new GetGeneratedPlaygroundRecommendedReplyOutput(
            recommendedReplies.Select(r => r.RecommendedReply).ToList(),
            recommendedReply?.RecommendedReply,
            recommendedReply?.KnowledgeCitations?.Select(k => new KnowledgeCitationDto(k)).ToList());
    }
}