openapi: 3.0.1
info:
  title: Sleekflow 1.0
  version: '1.0'
servers:
  - url: https://localhost:7080
    description: Local
  - url: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/intelligent-hub
    description: Dev Apigw
  - url: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/intelligent-hub
    description: Prod Apigw
paths:
  /authorized/CompanyAgentConfigs/CreateCompanyAgentConfig:
    post:
      tags:
        - AuthorizedCompanyAgentConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedCreateCompanyAgentConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedCreateCompanyAgentConfigOutputOutput'
  /authorized/CompanyAgentConfigs/DeleteCompanyAgentConfig:
    post:
      tags:
        - AuthorizedCompanyAgentConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedDeleteCompanyAgentConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedDeleteCompanyAgentConfigOutputOutput'
  /authorized/CompanyAgentConfigs/GetCompanyAgentConfig:
    post:
      tags:
        - AuthorizedCompanyAgentConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetCompanyAgentConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetCompanyAgentConfigOutputOutput'
  /authorized/CompanyAgentConfigs/GetCompanyAgentConfigs:
    post:
      tags:
        - AuthorizedCompanyAgentConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetCompanyAgentConfigsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetCompanyAgentConfigsOutputOutput'
  /authorized/CompanyAgentConfigs/PatchCompanyAgentConfig:
    post:
      tags:
        - AuthorizedCompanyAgentConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedPatchCompanyAgentConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedPatchCompanyAgentConfigOutputOutput'
  /authorized/CompanyConfigs/ClearCompanyConfig:
    post:
      tags:
        - AuthorizedCompanyConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedClearCompanyConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedClearCompanyConfigOutputOutput'
  /authorized/CompanyConfigs/GetCompanyConfig:
    post:
      tags:
        - AuthorizedCompanyConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetCompanyConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetCompanyConfigOutputOutput'
  /authorized/CompanyConfigs/PatchCompanyConfig:
    post:
      tags:
        - AuthorizedCompanyConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedPatchCompanyConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedPatchCompanyConfigOutputOutput'
  /authorized/Conversations/GetAgentGeneratedReplies:
    post:
      tags:
        - AuthorizedConversations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetAgentGeneratedRepliesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetAgentGeneratedRepliesOutputOutput'
  /authorized/KnowledgeBases/CreateWebpageDocuments:
    post:
      tags:
        - AuthorizedKnowledgeBases
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedCreateWebpageDocumentsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedCreateWebpageDocumentsOutputOutput'
  /authorized/KnowledgeBases/CreateWebsiteDocument:
    post:
      tags:
        - AuthorizedKnowledgeBases
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedCreateWebsiteDocumentInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedCreateWebsiteDocumentOutputOutput'
  /authorized/KnowledgeBases/GetFileDocumentChunks:
    post:
      tags:
        - AuthorizedKnowledgeBases
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetFileDocumentChunksInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetFileDocumentChunksOutputOutput'
  /authorized/KnowledgeBases/GetFileDocumentList:
    post:
      tags:
        - AuthorizedKnowledgeBases
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetFileDocumentListInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetFileDocumentListOutputOutput'
  /authorized/KnowledgeBases/GetKnowledgeBaseSearchResults:
    post:
      tags:
        - AuthorizedKnowledgeBases
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetKnowledgeBaseSearchResultsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetKnowledgeBaseSearchResultsOutputOutput'
  /authorized/KnowledgeBases/GetWebCrawlingSessionStatus:
    post:
      tags:
        - AuthorizedKnowledgeBases
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetWebCrawlingSessionStatusInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetWebCrawlingSessionStatusOutputOutput'
  /authorized/KnowledgeBases/PauseWebCrawlingSession:
    post:
      tags:
        - AuthorizedKnowledgeBases
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedPauseWebCrawlingSessionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedPauseWebCrawlingSessionOutputOutput'
  /authorized/KnowledgeBases/ResumeWebCrawlingSession:
    post:
      tags:
        - AuthorizedKnowledgeBases
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedResumeWebCrawlingSessionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedResumeWebCrawlingSessionOutputOutput'
  /authorized/KnowledgeBases/SearchDocument:
    post:
      tags:
        - AuthorizedKnowledgeBases
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedSearchDocumentInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedSearchDocumentOutputOutput'
  /authorized/KnowledgeBases/StartWebCrawlingSession:
    post:
      tags:
        - AuthorizedKnowledgeBases
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedStartWebCrawlingSessionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedStartWebCrawlingSessionOutputOutput'
  /authorized/KnowledgeBases/UpdateDocumentAgentAssignmentsForAgent:
    post:
      tags:
        - AuthorizedKnowledgeBases
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedUpdateDocumentAgentAssignmentsForAgentInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedUpdateDocumentAgentAssignmentsForAgentOutputOutput'
  /authorized/KnowledgeBases/UpdateDocumentAgentAssignmentsForDocument:
    post:
      tags:
        - AuthorizedKnowledgeBases
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedUpdateDocumentAgentAssignmentsForDocumentInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedUpdateDocumentAgentAssignmentsForDocumentOutputOutput'
  /authorized/Playgrounds/CreatePlaygroundSession:
    post:
      tags:
        - AuthorizedPlaygrounds
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedCreatePlaygroundSessionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedCreatePlaygroundSessionOutputOutput'
  /authorized/Playgrounds/EnqueueGeneratePlaygroundRecommendedReplyEvent:
    post:
      tags:
        - AuthorizedPlaygrounds
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedEnqueueGeneratePlaygroundRecommendedReplyEventInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedEnqueueGeneratePlaygroundRecommendedReplyEventOutputOutput'
  /authorized/Playgrounds/GetGeneratedPlaygroundRecommendedReply:
    post:
      tags:
        - AuthorizedPlaygrounds
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetGeneratedPlaygroundRecommendedReplyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetGeneratedPlaygroundRecommendedReplyOutputOutput'
  /Blobs/CreateBlobDownloadSasUrls:
    post:
      tags:
        - Blobs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBlobDownloadSasUrlsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateBlobDownloadSasUrlsOutputOutput'
  /Blobs/CreateBlobUploadSasUrls:
    post:
      tags:
        - Blobs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBlobUploadSasUrlsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateBlobUploadSasUrlsOutputOutput'
  /Blobs/DeleteBlobs:
    post:
      tags:
        - Blobs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteBlobsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteBlobsOutputOutput'
  /Blobs/GetBlobUploadHistory:
    post:
      tags:
        - Blobs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetBlobUploadHistoryInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetBlobUploadHistoryOutputOutput'
  /Blobs/RecordBlobUploadHistory:
    post:
      tags:
        - Blobs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RecordBlobUploadHistoryInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RecordBlobUploadHistoryOutputOutput'
  /Documents/CalculateFileDocumentStatistics:
    post:
      tags:
        - Documents
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculateFileDocumentStatisticsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculateFileDocumentStatisticsOutputOutput'
  /Documents/CalculateRemainingPages:
    post:
      tags:
        - Documents
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculateRemainingPagesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculateRemainingPagesOutputOutput'
  /Documents/CreateFileDocument:
    post:
      tags:
        - Documents
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateFileDocumentInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateFileDocumentOutputOutput'
  /Documents/DeleteFileDocument:
    post:
      tags:
        - Documents
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteFileDocumentInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteFileDocumentOutputOutput'
  /Documents/EditFileDocumentChunks:
    post:
      tags:
        - Documents
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EditFileDocumentChunksInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EditFileDocumentChunksOutputOutput'
  /Documents/GetFileDocumentChunkIds:
    post:
      tags:
        - Documents
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetFileDocumentChunkIdsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetFileDocumentChunkIdsOutputOutput'
  /Documents/GetFileDocumentChunks:
    post:
      tags:
        - Documents
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetFileDocumentChunksInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetFileDocumentChunksOutputOutput'
  /Documents/GetFileDocumentInformation:
    post:
      tags:
        - Documents
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetFileDocumentInformationInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetFileDocumentInformationOutputOutput'
  /Documents/GetProcessFileDocumentStatus:
    post:
      tags:
        - Documents
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProcessFileDocumentStatusInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProcessFileDocumentStatusOutputOutput'
  /IntelligentHubConfigs/GetAiFeatureSetting:
    post:
      tags:
        - IntelligentHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetAiFeatureSettingInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAiFeatureSettingOutputOutput'
  /IntelligentHubConfigs/GetFeatureUsages:
    post:
      tags:
        - IntelligentHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetFeatureUsagesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetFeatureUsagesOutputOutput'
  /IntelligentHubConfigs/GetIntelligentHubConfig:
    post:
      tags:
        - IntelligentHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetIntelligentHubConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetIntelligentHubConfigOutputOutput'
  /IntelligentHubConfigs/GetIntelligentHubConfigs:
    post:
      tags:
        - IntelligentHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetIntelligentHubConfigsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetIntelligentHubConfigsOutputOutput'
  /IntelligentHubConfigs/InitializeIntelligentHubConfig:
    post:
      tags:
        - IntelligentHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitializeIntelligentHubConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InitializeIntelligentHubConfigOutputOutput'
  /IntelligentHubConfigs/UpdateAiFeatureSetting:
    post:
      tags:
        - IntelligentHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAiFeatureSettingInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateAiFeatureSettingOutputOutput'
  /IntelligentHubConfigs/UpdateIntelligentHubConfig:
    post:
      tags:
        - IntelligentHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateIntelligentHubConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateIntelligentHubConfigOutputOutput'
  /IntelligentHubConfigs/UpdateIntelligentHubConfigUsageLimitOffsets:
    post:
      tags:
        - IntelligentHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateIntelligentHubConfigUsageLimitOffsetsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateIntelligentHubConfigUsageLimitOffsetsOutputOutput'
  /Internals/GetCompanyAgentConfigList:
    post:
      tags:
        - Internals
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCompanyAgentConfigListInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCompanyAgentConfigListOutputOutput'
  /KnowledgeBases/GetKnowledgeBaseEntries:
    post:
      tags:
        - KnowledgeBases
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetKnowledgeBaseEntriesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetKnowledgeBaseEntriesOutputOutput'
  /KnowledgeBases/GetKnowledgeBaseEntry:
    post:
      tags:
        - KnowledgeBases
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetKnowledgeBaseEntryInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetKnowledgeBaseEntryOutputOutput'
  /RecommendedReplies/RecommendReply:
    post:
      tags:
        - RecommendedReplies
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RecommendReplyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RecommendReplyOutputOutput'
  /RecommendedReplies/RecommendReplyStreaming:
    post:
      tags:
        - RecommendedReplies
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RecommendReplyStreamingInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RecommendReplyStreamingOutputOutput'
  /TextEnrichments/ChangeTone:
    post:
      tags:
        - TextEnrichments
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangeToneInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChangeToneOutputOutput'
  /TextEnrichments/CustomPromptRewrite:
    post:
      tags:
        - TextEnrichments
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomPromptRewriteInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomPromptRewriteOutputOutput'
  /TextEnrichments/Rephrase:
    post:
      tags:
        - TextEnrichments
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RephraseInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RephraseOutputOutput'
  /TextEnrichments/Translate:
    post:
      tags:
        - TextEnrichments
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TranslateInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TranslateOutputOutput'
  /TopicAnalytics/CreateTopic:
    post:
      tags:
        - TopicAnalytics
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTopicInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateTopicOutputOutput'
  /TopicAnalytics/DeleteTopics:
    post:
      tags:
        - TopicAnalytics
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteTopicsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteTopicsOutputOutput'
  /TopicAnalytics/GetTopics:
    post:
      tags:
        - TopicAnalytics
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetTopicsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTopicsOutputOutput'
  /TopicAnalytics/UpdateTopic:
    post:
      tags:
        - TopicAnalytics
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTopicInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateTopicOutputOutput'
  /TopicAnalytics/UpdateTopics:
    post:
      tags:
        - TopicAnalytics
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTopicsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateTopicsOutputOutput'
  /Webhook/web-scraper:
    post:
      tags:
        - Webhook
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /WebScraperRuns/AbortWebScraperRun:
    post:
      tags:
        - WebScraperRuns
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AbortWebScraperRunInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AbortWebScraperRunOutputOutput'
  /WebScraperRuns/GetOneTimeRuns:
    post:
      tags:
        - WebScraperRuns
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetOneTimeRunsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetOneTimeRunsOutputOutput'
  /WebScraperRuns/GetWebPageContent:
    post:
      tags:
        - WebScraperRuns
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWebPageContentInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWebPageContentOutputOutput'
  /WebScraperRuns/GetWebScraperRun:
    post:
      tags:
        - WebScraperRuns
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWebScraperRunInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWebScraperRunOutputOutput'
  /WebScraperRuns/ResurrectWebScraperRun:
    post:
      tags:
        - WebScraperRuns
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResurrectWebScraperRunInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResurrectWebScraperRunOutputOutput'
  /WebScraperRuns/StartOneTimeRun:
    post:
      tags:
        - WebScraperRuns
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StartOneTimeRunInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StartOneTimeRunOutputOutput'
  /WebScraperRuns/SyncWebScraperRun:
    post:
      tags:
        - WebScraperRuns
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SyncWebScraperRunInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SyncWebScraperRunOutputOutput'
  /WebScrapers/GetWebScraper:
    post:
      tags:
        - WebScrapers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWebScraperInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWebScraperOutputOutput'
  /WebScraperTasks/CreateWebScraperTask:
    post:
      tags:
        - WebScraperTasks
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateWebScraperTaskInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateWebScraperTaskOutputOutput'
  /WebScraperTasks/DeleteTaskScheduler:
    post:
      tags:
        - WebScraperTasks
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteTaskSchedulerInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteTaskSchedulerOutputOutput'
  /WebScraperTasks/DeleteWebScraperTask:
    post:
      tags:
        - WebScraperTasks
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteWebScraperTaskInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteWebScraperTaskOutputOutput'
  /WebScraperTasks/GetWebScraperTask:
    post:
      tags:
        - WebScraperTasks
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWebScraperTaskInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWebScraperTaskOutputOutput'
  /WebScraperTasks/GetWebScraperTaskRuns:
    post:
      tags:
        - WebScraperTasks
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWebScraperTaskRunsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWebScraperTaskRunsOutputOutput'
  /WebScraperTasks/ScheduleWebScraperTask:
    post:
      tags:
        - WebScraperTasks
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ScheduleWebScraperTaskInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScheduleWebScraperTaskOutputOutput'
  /WebScraperTasks/StartWebScraperTask:
    post:
      tags:
        - WebScraperTasks
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StartWebScraperTaskInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StartWebScraperTaskOutputOutput'
  /WebScraperTasks/UpdateWebScraperTask:
    post:
      tags:
        - WebScraperTasks
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateWebScraperTaskInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateWebScraperTaskOutputOutput'
components:
  schemas:
    AbortWebScraperRunInput:
      required:
        - apify_run_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        apify_run_id:
          minLength: 1
          type: string
      additionalProperties: false
    AbortWebScraperRunOutput:
      type: object
      properties:
        web_scraper_run:
          $ref: '#/components/schemas/WebScraperRun'
      additionalProperties: false
    AbortWebScraperRunOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AbortWebScraperRunOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AddLabelActionDto:
      type: object
      properties:
        labels:
          type: array
          items:
            $ref: '#/components/schemas/LabelDto'
          nullable: true
        instructions:
          type: string
          nullable: true
        enabled:
          type: boolean
      additionalProperties: false
    AdditionalHandsOffClassificationRule:
      type: object
      properties:
        rule:
          type: string
          nullable: true
      additionalProperties: false
    AdditionalHandsOffClassificationRules:
      type: object
      properties:
        rules:
          type: array
          items:
            $ref: '#/components/schemas/AdditionalHandsOffClassificationRule'
          nullable: true
      additionalProperties: false
    AgentAssignment:
      type: object
      properties:
        agent_id:
          type: string
          nullable: true
        rag_status:
          pattern: pending|in-progress|completed|failed
          type: string
          nullable: true
        rag_status_upload_percentage:
          type: number
          format: double
      additionalProperties: false
    AgentRecommendReplySnapshotDto:
      type: object
      properties:
        conversation_context:
          type: string
          nullable: true
        knowledge_base_entries:
          type: string
          nullable: true
        output_message:
          type: string
          nullable: true
        collaboration_mode:
          type: string
          nullable: true
        confidence_score:
          type: integer
          format: int32
        agent_versioned_id:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    AssignmentPair:
      type: object
      properties:
        rule:
          type: string
          nullable: true
        team_name:
          type: string
          nullable: true
        team_id:
          type: string
          nullable: true
      additionalProperties: false
    AssignmentTool:
      type: object
      properties:
        assignments:
          type: array
          items:
            $ref: '#/components/schemas/AssignmentPair'
          nullable: true
      additionalProperties: false
    AuthorizedClearCompanyConfigInput:
      type: object
      additionalProperties: false
    AuthorizedClearCompanyConfigOutput:
      type: object
      additionalProperties: false
    AuthorizedClearCompanyConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedClearCompanyConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedCreateCompanyAgentConfigInput:
      required:
        - is_chat_history_enabled_as_context
        - is_contact_properties_enabled_as_context
        - name
        - number_of_previous_messages_in_chat_history_available_as_context
        - type
      type: object
      properties:
        name:
          minLength: 1
          type: string
        description:
          type: string
          nullable: true
        type:
          minLength: 1
          pattern: sales|support|custom
          type: string
        is_chat_history_enabled_as_context:
          type: boolean
        is_contact_properties_enabled_as_context:
          type: boolean
        number_of_previous_messages_in_chat_history_available_as_context:
          maximum: 100
          minimum: 0
          type: integer
          format: int32
        channel_type:
          type: string
          nullable: true
        channel_id:
          type: string
          nullable: true
        collaboration_mode:
          type: string
          nullable: true
        demo:
          type: boolean
          nullable: true
        lead_nurturing_tools:
          $ref: '#/components/schemas/LeadNurturingTools'
        tools_config:
          $ref: '#/components/schemas/ToolsConfig'
        enricher_configs:
          type: array
          items:
            $ref: '#/components/schemas/EnricherConfig'
          nullable: true
        knowledge_retrieval_config:
          $ref: '#/components/schemas/KnowledgeRetrievalConfig'
        actions:
          $ref: '#/components/schemas/CompanyAgentConfigActionsDto'
        prompt_instruction:
          $ref: '#/components/schemas/PromptInstructionDto'
        edit_mode:
          pattern: basic|advanced
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedCreateCompanyAgentConfigOutput:
      type: object
      properties:
        company_agent_config:
          $ref: '#/components/schemas/CompanyAgentConfigDto'
      additionalProperties: false
    AuthorizedCreateCompanyAgentConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedCreateCompanyAgentConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedCreatePlaygroundSessionInput:
      required:
        - bot_name
        - prompt_instruction
      type: object
      properties:
        bot_name:
          minLength: 1
          type: string
        prompt_instruction:
          $ref: '#/components/schemas/PromptInstructionDto'
        collaboration_mode:
          type: string
          nullable: true
        agent_config:
          $ref: '#/components/schemas/CompanyAgentConfigDto'
      additionalProperties: false
    AuthorizedCreatePlaygroundSessionOutput:
      type: object
      properties:
        session_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedCreatePlaygroundSessionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedCreatePlaygroundSessionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedCreateWebpageDocumentsInput:
      required:
        - agent_ids
        - session_id_to_urls
      type: object
      properties:
        session_id_to_urls:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
        agent_ids:
          type: array
          items:
            type: string
      additionalProperties: false
    AuthorizedCreateWebpageDocumentsOutput:
      type: object
      additionalProperties: false
    AuthorizedCreateWebpageDocumentsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedCreateWebpageDocumentsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedCreateWebsiteDocumentInput:
      required:
        - agent_ids
        - base_url
        - selected_urls
        - web_crawling_session_id
      type: object
      properties:
        web_crawling_session_id:
          minLength: 1
          type: string
        base_url:
          minLength: 1
          type: string
        base_url_title:
          type: string
          nullable: true
        selected_urls:
          type: array
          items:
            type: string
        agent_ids:
          type: array
          items:
            type: string
      additionalProperties: false
    AuthorizedCreateWebsiteDocumentOutput:
      required:
        - id
      type: object
      properties:
        id:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedCreateWebsiteDocumentOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedCreateWebsiteDocumentOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedDeleteCompanyAgentConfigInput:
      required:
        - _etag
        - id
      type: object
      properties:
        id:
          minLength: 1
          type: string
        _etag:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedDeleteCompanyAgentConfigOutput:
      type: object
      additionalProperties: false
    AuthorizedDeleteCompanyAgentConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedDeleteCompanyAgentConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedEnqueueGeneratePlaygroundRecommendedReplyEventInput:
      required:
        - conversation_context
        - session_id
      type: object
      properties:
        session_id:
          minLength: 1
          type: string
        conversation_context:
          type: array
          items:
            $ref: '#/components/schemas/SfChatEntry'
      additionalProperties: false
    AuthorizedEnqueueGeneratePlaygroundRecommendedReplyEventOutput:
      type: object
      properties:
        message_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedEnqueueGeneratePlaygroundRecommendedReplyEventOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedEnqueueGeneratePlaygroundRecommendedReplyEventOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGeneratedReplySnapshot:
      type: object
      properties:
        agent_recommend_reply_snapshot:
          $ref: '#/components/schemas/AgentRecommendReplySnapshotDto'
        playground_recommend_reply_snapshot:
          $ref: '#/components/schemas/PlaygroundRecommendReplySnapshotDto'
      additionalProperties: false
    AuthorizedGetAgentGeneratedRepliesInput:
      required:
        - agent_id
        - generated_types
        - limit
      type: object
      properties:
        agent_id:
          minLength: 1
          type: string
        limit:
          maximum: 100
          minimum: 1
          type: integer
          format: int32
        generated_types:
          type: array
          items:
            type: string
        continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetAgentGeneratedRepliesOutput:
      type: object
      properties:
        generated_replies:
          type: array
          items:
            $ref: '#/components/schemas/AuthorizedGeneratedReplySnapshot'
          nullable: true
        next_continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetAgentGeneratedRepliesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetAgentGeneratedRepliesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetCompanyAgentConfigInput:
      required:
        - id
      type: object
      properties:
        id:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedGetCompanyAgentConfigOutput:
      type: object
      properties:
        company_agent_config:
          $ref: '#/components/schemas/CompanyAgentConfigDto'
        character_count:
          type: integer
          format: int32
        character_limit:
          type: integer
          format: int32
      additionalProperties: false
    AuthorizedGetCompanyAgentConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetCompanyAgentConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetCompanyAgentConfigsInput:
      type: object
      additionalProperties: false
    AuthorizedGetCompanyAgentConfigsOutput:
      type: object
      properties:
        company_agent_configs:
          type: array
          items:
            $ref: '#/components/schemas/CompanyAgentConfigDto'
          nullable: true
        character_count_dictionary:
          type: object
          additionalProperties:
            type: integer
            format: int32
          nullable: true
        character_limit:
          type: integer
          format: int32
      additionalProperties: false
    AuthorizedGetCompanyAgentConfigsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetCompanyAgentConfigsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetCompanyConfigInput:
      type: object
      additionalProperties: false
    AuthorizedGetCompanyConfigOutput:
      type: object
      properties:
        company_config:
          $ref: '#/components/schemas/CompanyConfigDto'
      additionalProperties: false
    AuthorizedGetCompanyConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetCompanyConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetFileDocumentChunksInput:
      required:
        - document_id
        - limit
      type: object
      properties:
        document_id:
          minLength: 1
          type: string
        continuation_token:
          type: string
          nullable: true
        limit:
          maximum: 1000
          minimum: 1
          type: integer
          format: int32
      additionalProperties: false
    AuthorizedGetFileDocumentChunksOutput:
      type: object
      properties:
        document_chunks:
          type: array
          items:
            $ref: '#/components/schemas/ChunkDto'
          nullable: true
        next_continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetFileDocumentChunksOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetFileDocumentChunksOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetFileDocumentListInput:
      type: object
      properties:
        filter_training_status:
          type: string
          nullable: true
        filter_exclude_training_status:
          type: array
          items:
            type: string
          nullable: true
        filter_agent:
          type: string
          nullable: true
        filter_exclude_agent:
          type: string
          nullable: true
        sort_by:
          type: string
          nullable: true
        sort_direction:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetFileDocumentListOutput:
      type: object
      properties:
        kb_documents:
          type: array
          items:
            $ref: '#/components/schemas/KbDocument'
          nullable: true
      additionalProperties: false
    AuthorizedGetFileDocumentListOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetFileDocumentListOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetGeneratedPlaygroundRecommendedReplyInput:
      required:
        - message_id
        - session_id
      type: object
      properties:
        session_id:
          minLength: 1
          type: string
        message_id:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedGetGeneratedPlaygroundRecommendedReplyOutput:
      type: object
      properties:
        recommended_reply:
          type: array
          items:
            type: string
          nullable: true
        reply:
          type: string
          nullable: true
        knowledge_citations:
          type: array
          items:
            $ref: '#/components/schemas/KnowledgeCitationDto'
          nullable: true
      additionalProperties: false
    AuthorizedGetGeneratedPlaygroundRecommendedReplyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetGeneratedPlaygroundRecommendedReplyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetKnowledgeBaseSearchResultsInput:
      required:
        - search_text
      type: object
      properties:
        search_text:
          minLength: 1
          type: string
        sf_chat_entries:
          type: array
          items:
            $ref: '#/components/schemas/SfChatEntry'
          nullable: true
      additionalProperties: false
    AuthorizedGetKnowledgeBaseSearchResultsOutput:
      type: object
      properties:
        search_results:
          type: array
          items:
            $ref: '#/components/schemas/AuthorizedSearchResult'
          nullable: true
      additionalProperties: false
    AuthorizedGetKnowledgeBaseSearchResultsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetKnowledgeBaseSearchResultsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetWebCrawlingSessionStatusInput:
      required:
        - web_crawling_session_id
      type: object
      properties:
        web_crawling_session_id:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedGetWebCrawlingSessionStatusOutput:
      type: object
      properties:
        status:
          type: string
          nullable: true
        base_url:
          type: string
          nullable: true
        crawling_results:
          type: array
          items:
            $ref: '#/components/schemas/CrawlingResult'
          nullable: true
      additionalProperties: false
    AuthorizedGetWebCrawlingSessionStatusOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetWebCrawlingSessionStatusOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedPatchCompanyAgentConfigInput:
      required:
        - _etag
        - id
        - name
        - prompt_instruction
      type: object
      properties:
        id:
          minLength: 1
          type: string
        name:
          minLength: 1
          type: string
        description:
          type: string
          nullable: true
        is_chat_history_enabled_as_context:
          type: boolean
          nullable: true
        is_contact_properties_enabled_as_context:
          type: boolean
          nullable: true
        number_of_previous_messages_in_chat_history_available_as_context:
          type: integer
          format: int32
          nullable: true
        channel_type:
          type: string
          nullable: true
        channel_id:
          type: string
          nullable: true
        type:
          pattern: sales|support|custom
          type: string
          nullable: true
        prompt_instruction:
          $ref: '#/components/schemas/PromptInstructionDto'
        collaboration_mode:
          pattern: Long|Short|Medium|LeadNurturing|ManagerLeadNurturing|ReAct
          type: string
          nullable: true
        lead_nurturing_tools:
          $ref: '#/components/schemas/LeadNurturingTools'
        tools_config:
          $ref: '#/components/schemas/ToolsConfig'
        enricher_configs:
          type: array
          items:
            $ref: '#/components/schemas/EnricherConfig'
          nullable: true
        knowledge_retrieval_config:
          $ref: '#/components/schemas/KnowledgeRetrievalConfig'
        actions:
          $ref: '#/components/schemas/CompanyAgentConfigActionsDto'
        edit_mode:
          pattern: basic|advanced
          type: string
          nullable: true
        _etag:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedPatchCompanyAgentConfigOutput:
      type: object
      properties:
        company_agent_config:
          $ref: '#/components/schemas/CompanyAgentConfigDto'
      additionalProperties: false
    AuthorizedPatchCompanyAgentConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedPatchCompanyAgentConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedPatchCompanyConfigInput:
      type: object
      properties:
        name:
          type: string
          nullable: true
        background_information:
          type: string
          nullable: true
        preferred_language:
          type: string
          nullable: true
        collaboration_mode:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedPatchCompanyConfigOutput:
      type: object
      properties:
        company_config:
          $ref: '#/components/schemas/CompanyConfigDto'
      additionalProperties: false
    AuthorizedPatchCompanyConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedPatchCompanyConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedPauseWebCrawlingSessionInput:
      required:
        - web_crawling_session_id
      type: object
      properties:
        web_crawling_session_id:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedPauseWebCrawlingSessionOutput:
      type: object
      properties:
        status:
          type: string
          nullable: true
        base_url:
          type: string
          nullable: true
        crawling_results:
          type: array
          items:
            $ref: '#/components/schemas/CrawlingResult'
          nullable: true
      additionalProperties: false
    AuthorizedPauseWebCrawlingSessionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedPauseWebCrawlingSessionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedResumeWebCrawlingSessionInput:
      required:
        - web_crawling_session_id
      type: object
      properties:
        web_crawling_session_id:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedResumeWebCrawlingSessionOutput:
      type: object
      properties:
        status:
          type: string
          nullable: true
        base_url:
          type: string
          nullable: true
        crawling_results:
          type: array
          items:
            $ref: '#/components/schemas/CrawlingResult'
          nullable: true
      additionalProperties: false
    AuthorizedResumeWebCrawlingSessionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedResumeWebCrawlingSessionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedSearchDocumentInput:
      required:
        - search_input
      type: object
      properties:
        search_input:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedSearchDocumentOutput:
      type: object
      properties:
        kb_documents:
          type: array
          items:
            $ref: '#/components/schemas/KbDocument'
          nullable: true
      additionalProperties: false
    AuthorizedSearchDocumentOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedSearchDocumentOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedSearchResult:
      type: object
      properties:
        sourcepage:
          type: string
          nullable: true
        content:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedStartWebCrawlingSessionInput:
      required:
        - is_crawling_enabled
        - url
      type: object
      properties:
        url:
          minLength: 1
          type: string
        is_crawling_enabled:
          type: boolean
      additionalProperties: false
    AuthorizedStartWebCrawlingSessionOutput:
      type: object
      properties:
        web_crawling_session_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedStartWebCrawlingSessionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedStartWebCrawlingSessionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedUpdateDocumentAgentAssignmentsForAgentInput:
      required:
        - agent_id
        - document_ids
      type: object
      properties:
        agent_id:
          minLength: 1
          type: string
        document_ids:
          type: array
          items:
            type: string
      additionalProperties: false
    AuthorizedUpdateDocumentAgentAssignmentsForAgentOutput:
      type: object
      additionalProperties: false
    AuthorizedUpdateDocumentAgentAssignmentsForAgentOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedUpdateDocumentAgentAssignmentsForAgentOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedUpdateDocumentAgentAssignmentsForDocumentInput:
      required:
        - agent_ids
        - document_id
      type: object
      properties:
        document_id:
          minLength: 1
          type: string
        agent_ids:
          type: array
          items:
            type: string
      additionalProperties: false
    AuthorizedUpdateDocumentAgentAssignmentsForDocumentOutput:
      type: object
      additionalProperties: false
    AuthorizedUpdateDocumentAgentAssignmentsForDocumentOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedUpdateDocumentAgentAssignmentsForDocumentOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    BlobUploadHistory:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        blob_id:
          type: string
          nullable: true
        file_name:
          type: string
          nullable: true
        uploaded_by:
          type: string
          nullable: true
        source_type:
          type: string
          nullable: true
        blob_type:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    CalculateFileDocumentStatisticsInput:
      required:
        - blob_id
        - blob_type
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        blob_id:
          minLength: 1
          type: string
        blob_type:
          minLength: 1
          type: string
      additionalProperties: false
    CalculateFileDocumentStatisticsOutput:
      type: object
      properties:
        document_statistics:
          $ref: '#/components/schemas/DocumentStatistics'
      additionalProperties: false
    CalculateFileDocumentStatisticsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CalculateFileDocumentStatisticsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CalculateLeadScoreActionDto:
      type: object
      properties:
        criteria:
          type: array
          items:
            $ref: '#/components/schemas/LeadScoreCriterionDto'
          nullable: true
        enabled:
          type: boolean
      additionalProperties: false
    CalculateRemainingPagesInput:
      required:
        - page_limit
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        page_limit:
          maximum: 1000
          minimum: 1
          type: integer
          format: int32
      additionalProperties: false
    CalculateRemainingPagesOutput:
      required:
        - current_usage
        - page_limit
        - remaining_page
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        page_limit:
          type: integer
          format: int32
        current_usage:
          type: integer
          format: int32
        remaining_page:
          type: integer
          format: int32
      additionalProperties: false
    CalculateRemainingPagesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CalculateRemainingPagesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Category:
      type: object
      properties:
        category_name:
          type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    ChangeToneInput:
      required:
        - message
        - sleekflow_company_id
        - tone_type
      type: object
      properties:
        message:
          minLength: 1
          type: string
        tone_type:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        intelligent_hub_usage_filter:
          $ref: '#/components/schemas/IntelligentHubUsageFilter'
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
      additionalProperties: false
    ChangeToneOutput:
      type: object
      properties:
        output_message:
          type: string
          nullable: true
      additionalProperties: false
    ChangeToneOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ChangeToneOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ChiliPiperConfig:
      type: object
      properties:
        api_url:
          type: string
          nullable: true
        field_mappings:
          type: object
          additionalProperties:
            type: string
          nullable: true
      additionalProperties: false
    Chunk:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        content:
          type: string
          nullable: true
        content_en:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        categories:
          type: array
          items:
            $ref: '#/components/schemas/Category'
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    ChunkDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        content:
          type: string
          nullable: true
      additionalProperties: false
    CompanyAgentConfigActionsDto:
      type: object
      properties:
        send_message:
          $ref: '#/components/schemas/SendMessageActionDto'
        calculate_lead_score:
          $ref: '#/components/schemas/CalculateLeadScoreActionDto'
        exit_conversation:
          $ref: '#/components/schemas/ExitConversationActionDto'
        add_label:
          $ref: '#/components/schemas/AddLabelActionDto'
      additionalProperties: false
    CompanyAgentConfigDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        is_chat_history_enabled_as_context:
          type: boolean
        is_contact_properties_enabled_as_context:
          type: boolean
        number_of_previous_messages_in_chat_history_availabe_as_context:
          type: integer
          format: int32
        channel_type:
          type: string
          nullable: true
        channel_id:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        prompt_instruction:
          $ref: '#/components/schemas/PromptInstructionDto'
        collaboration_mode:
          pattern: Long|Short|Medium|LeadNurturing|ManagerLeadNurturing|ReAct
          type: string
          nullable: true
        effective_collaboration_mode:
          type: string
          nullable: true
        knowledge_retrieval_config:
          $ref: '#/components/schemas/KnowledgeRetrievalConfig'
        type:
          type: string
          nullable: true
        lead_nurturing_tools:
          $ref: '#/components/schemas/LeadNurturingTools'
        tools_config:
          $ref: '#/components/schemas/ToolsConfig'
        enricher_configs:
          type: array
          items:
            $ref: '#/components/schemas/EnricherConfig'
          nullable: true
        active_workflow_count:
          type: integer
          format: int32
        created_at:
          type: string
          format: date-time
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_at:
          type: string
          format: date-time
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        _etag:
          type: string
          nullable: true
        actions:
          $ref: '#/components/schemas/CompanyAgentConfigActionsDto'
        edit_mode:
          pattern: basic|advanced
          type: string
          nullable: true
        is_transcription_enabled:
          type: boolean
      additionalProperties: false
    CompanyConfigDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        background_information:
          type: string
          nullable: true
        preferred_language:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_at:
          type: string
          format: date-time
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        _etag:
          type: string
          nullable: true
      additionalProperties: false
    CrawlingResult:
      type: object
      properties:
        url:
          type: string
          nullable: true
        character_count:
          type: integer
          format: int32
        webpage_title:
          type: string
          nullable: true
      additionalProperties: false
    CreateBlobDownloadSasUrlsInput:
      required:
        - blob_names
        - blob_type
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        blob_names:
          type: array
          items:
            type: string
        blob_type:
          minLength: 1
          type: string
      additionalProperties: false
    CreateBlobDownloadSasUrlsOutput:
      type: object
      properties:
        download_blobs:
          type: array
          items:
            $ref: '#/components/schemas/PublicBlob'
          nullable: true
      additionalProperties: false
    CreateBlobDownloadSasUrlsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateBlobDownloadSasUrlsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateBlobUploadSasUrlsInput:
      required:
        - blob_type
        - number_of_blobs
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        number_of_blobs:
          maximum: 16
          minimum: 1
          type: integer
          format: int32
        blob_type:
          minLength: 1
          type: string
      additionalProperties: false
    CreateBlobUploadSasUrlsOutput:
      type: object
      properties:
        upload_blobs:
          type: array
          items:
            $ref: '#/components/schemas/PublicBlob'
          nullable: true
      additionalProperties: false
    CreateBlobUploadSasUrlsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateBlobUploadSasUrlsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateFileDocumentInput:
      required:
        - assign_to_agent_ids
        - blob_id
        - blob_type
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        blob_id:
          minLength: 1
          type: string
        blob_type:
          minLength: 1
          type: string
        assign_to_agent_ids:
          type: array
          items:
            type: string
      additionalProperties: false
    CreateFileDocumentOutput:
      type: object
      properties:
        file_document:
          $ref: '#/components/schemas/FileDocument'
      additionalProperties: false
    CreateFileDocumentOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateFileDocumentOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateTopicInput:
      required:
        - sleekflow_staff_id
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        topic_name:
          type: string
          nullable: true
        terms:
          type: array
          items:
            $ref: '#/components/schemas/TopicAnalyticsTerm'
          nullable: true
      additionalProperties: false
    CreateTopicOutput:
      type: object
      properties:
        topic:
          $ref: '#/components/schemas/TopicAnalyticsTopic'
      additionalProperties: false
    CreateTopicOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateTopicOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileInput:
      required:
        - name
        - phone_number
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        name:
          minLength: 1
          type: string
        phone_number:
          minLength: 1
          type: string
      additionalProperties: false
    CreateUserProfileOutput:
      type: object
      properties:
        Id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
      additionalProperties: false
    CreateWebScraperTaskInput:
      required:
        - display_name
        - sleekflow_company_id
        - web_scraper_setting
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        display_name:
          minLength: 1
          type: string
        web_scraper_setting:
          $ref: '#/components/schemas/WebScraperSetting'
      additionalProperties: false
    CreateWebScraperTaskOutput:
      type: object
      properties:
        web_scraper_task:
          $ref: '#/components/schemas/WebScraperTask'
      additionalProperties: false
    CreateWebScraperTaskOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateWebScraperTaskOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CustomPromptRewriteInput:
      required:
        - message
        - prompt
        - sleekflow_company_id
      type: object
      properties:
        message:
          minLength: 1
          type: string
        prompt:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        intelligent_hub_usage_filter:
          $ref: '#/components/schemas/IntelligentHubUsageFilter'
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
      additionalProperties: false
    CustomPromptRewriteOutput:
      type: object
      properties:
        output_message:
          type: string
          nullable: true
      additionalProperties: false
    CustomPromptRewriteOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CustomPromptRewriteOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DebugTimestamps:
      type: object
      properties:
        conversion_started:
          type: string
          format: date-time
          nullable: true
        conversion_ended:
          type: string
          format: date-time
          nullable: true
        upload_started:
          type: string
          format: date-time
          nullable: true
        upload_ended:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    DeleteBlobsInput:
      required:
        - blob_names
        - blob_type
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        blob_names:
          type: array
          items:
            type: string
        blob_type:
          minLength: 1
          type: string
      additionalProperties: false
    DeleteBlobsOutput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        blob_names:
          type: array
          items:
            type: string
          nullable: true
        blob_type:
          type: string
          nullable: true
      additionalProperties: false
    DeleteBlobsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteBlobsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteFileDocumentInput:
      required:
        - blob_id
        - document_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        document_id:
          minLength: 1
          type: string
        blob_id:
          minLength: 1
          type: string
      additionalProperties: false
    DeleteFileDocumentOutput:
      type: object
      additionalProperties: false
    DeleteFileDocumentOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteFileDocumentOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteTaskSchedulerInput:
      required:
        - apify_task_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        apify_task_id:
          minLength: 1
          type: string
      additionalProperties: false
    DeleteTaskSchedulerOutput:
      type: object
      properties:
        web_scraper_task:
          $ref: '#/components/schemas/WebScraperTask'
      additionalProperties: false
    DeleteTaskSchedulerOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteTaskSchedulerOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteTopicsInput:
      required:
        - sleekflow_staff_id
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        ids:
          type: array
          items:
            type: string
          nullable: true
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    DeleteTopicsOutput:
      type: object
      additionalProperties: false
    DeleteTopicsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteTopicsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteWebScraperTaskInput:
      required:
        - apify_task_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        apify_task_id:
          minLength: 1
          type: string
      additionalProperties: false
    DeleteWebScraperTaskOutput:
      type: object
      additionalProperties: false
    DeleteWebScraperTaskOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteWebScraperTaskOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DemoTool:
      type: object
      properties:
        required_fields:
          type: array
          items:
            $ref: '#/components/schemas/RequiredField'
          nullable: true
        chili_piper_config:
          $ref: '#/components/schemas/ChiliPiperConfig'
      additionalProperties: false
    DocumentStatistics:
      type: object
      properties:
        total_token_counts:
          type: integer
          format: int32
        total_word_counts:
          type: integer
          format: int32
        total_characters:
          type: integer
          format: int32
        total_pages:
          type: integer
          format: int32
        file_size_bytes:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    EditFileDocumentChunksInput:
      required:
        - document_id
        - editing_chunks
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        document_id:
          minLength: 1
          type: string
        editing_chunks:
          type: array
          items:
            $ref: '#/components/schemas/EditingChunkDto'
      additionalProperties: false
    EditFileDocumentChunksOutput:
      type: object
      properties:
        edited_chunks:
          type: array
          items:
            $ref: '#/components/schemas/EditingChunkDto'
          nullable: true
      additionalProperties: false
    EditFileDocumentChunksOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/EditFileDocumentChunksOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    EditingChunkDto:
      type: object
      properties:
        chunk_id:
          type: string
          nullable: true
        content:
          type: string
          nullable: true
        categories:
          type: array
          items:
            $ref: '#/components/schemas/Category'
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    EnricherConfig:
      type: object
      properties:
        type:
          type: string
          nullable: true
        is_enabled:
          type: boolean
        parameters:
          type: object
          additionalProperties:
            type: string
          nullable: true
      additionalProperties: false
    ExitConditionDto:
      type: object
      properties:
        title:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        operator:
          type: string
          nullable: true
        values:
          type: array
          items: { }
          nullable: true
      additionalProperties: false
    ExitConversationActionDto:
      type: object
      properties:
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/ExitConditionDto'
          nullable: true
        enabled:
          type: boolean
      additionalProperties: false
    FileDocument:
      type: object
      properties:
        blob_id:
          type: string
          nullable: true
        file_name:
          type: string
          nullable: true
        blob_type:
          type: string
          nullable: true
        document_statistics:
          $ref: '#/components/schemas/DocumentStatistics'
        language_iso_code:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        content_type:
          type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        file_document_process_status:
          type: string
          nullable: true
        file_document_process_percentage:
          type: number
          format: double
        agent_assignments:
          type: array
          items:
            $ref: '#/components/schemas/AgentAssignment'
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        uploaded_by:
          type: string
          nullable: true
        debug_timestamps:
          $ref: '#/components/schemas/DebugTimestamps'
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    GetAiFeatureSettingInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetAiFeatureSettingOutput:
      type: object
      properties:
        enable_writing_assistant:
          type: boolean
        enable_smart_reply:
          type: boolean
      additionalProperties: false
    GetAiFeatureSettingOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetAiFeatureSettingOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetBlobUploadHistoryInput:
      required:
        - blob_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        blob_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetBlobUploadHistoryOutput:
      type: object
      properties:
        BlobUploadHistory:
          $ref: '#/components/schemas/BlobUploadHistory'
      additionalProperties: false
    GetBlobUploadHistoryOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetBlobUploadHistoryOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetCompanyAgentConfigListInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetCompanyAgentConfigListOutput:
      type: object
      properties:
        company_agent_configs:
          type: array
          items:
            $ref: '#/components/schemas/CompanyAgentConfigDto'
          nullable: true
      additionalProperties: false
    GetCompanyAgentConfigListOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetCompanyAgentConfigListOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetFeatureUsagesInput:
      required:
        - feature_names
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        feature_names:
          uniqueItems: true
          type: array
          items:
            type: string
        intelligent_hub_usage_filter:
          $ref: '#/components/schemas/IntelligentHubUsageFilter'
      additionalProperties: false
    GetFeatureUsagesOutput:
      type: object
      properties:
        feature_usages:
          type: object
          additionalProperties:
            type: integer
            format: int32
          nullable: true
      additionalProperties: false
    GetFeatureUsagesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetFeatureUsagesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetFileDocumentChunkIdsInput:
      required:
        - document_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        document_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetFileDocumentChunkIdsOutput:
      type: object
      properties:
        chunk_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    GetFileDocumentChunkIdsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetFileDocumentChunkIdsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetFileDocumentChunksInput:
      required:
        - document_id
        - limit
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        document_id:
          minLength: 1
          type: string
        continuation_token:
          type: string
          nullable: true
        limit:
          maximum: 1000
          minimum: 1
          type: integer
          format: int32
      additionalProperties: false
    GetFileDocumentChunksOutput:
      type: object
      properties:
        document_chunks:
          type: array
          items:
            $ref: '#/components/schemas/Chunk'
          nullable: true
        next_continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetFileDocumentChunksOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetFileDocumentChunksOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetFileDocumentInformationInput:
      required:
        - blob_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        blob_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetFileDocumentInformationOutput:
      type: object
      properties:
        document:
          $ref: '#/components/schemas/KbDocument'
      additionalProperties: false
    GetFileDocumentInformationOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetFileDocumentInformationOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetIntelligentHubConfigInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetIntelligentHubConfigOutput:
      type: object
      properties:
        intelligent_hub_config:
          $ref: '#/components/schemas/IntelligentHubConfig'
      additionalProperties: false
    GetIntelligentHubConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetIntelligentHubConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetIntelligentHubConfigsInput:
      required:
        - limit
      type: object
      properties:
        continuation_token:
          type: string
          nullable: true
        limit:
          maximum: 1000
          minimum: 1
          type: integer
          format: int32
      additionalProperties: false
    GetIntelligentHubConfigsOutput:
      type: object
      properties:
        intelligent_hub_configs:
          type: array
          items:
            $ref: '#/components/schemas/IntelligentHubConfig'
          nullable: true
        continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetIntelligentHubConfigsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetIntelligentHubConfigsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetKnowledgeBaseEntriesFilters:
      required:
        - source_id
        - source_type
      type: object
      properties:
        source_id:
          minLength: 1
          type: string
        source_type:
          minLength: 1
          type: string
      additionalProperties: false
    GetKnowledgeBaseEntriesInput:
      required:
        - filters
        - limit
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        filters:
          $ref: '#/components/schemas/GetKnowledgeBaseEntriesFilters'
        limit:
          maximum: 1000
          minimum: 1
          type: integer
          format: int32
        continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetKnowledgeBaseEntriesOutput:
      type: object
      properties:
        knowledge_base_entries:
          type: array
          items:
            $ref: '#/components/schemas/KnowledgeBaseEntry'
          nullable: true
        next_continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetKnowledgeBaseEntriesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetKnowledgeBaseEntriesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetKnowledgeBaseEntryInput:
      required:
        - knowledge_base_entry_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        knowledge_base_entry_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetKnowledgeBaseEntryOutput:
      type: object
      properties:
        knowledge_base_entry:
          $ref: '#/components/schemas/KnowledgeBaseEntry'
      additionalProperties: false
    GetKnowledgeBaseEntryOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetKnowledgeBaseEntryOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetOneTimeRunsInput:
      required:
        - limit
        - offset
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        offset:
          type: integer
          format: int32
        limit:
          type: integer
          format: int32
      additionalProperties: false
    GetOneTimeRunsOutput:
      type: object
      properties:
        web_scraper_runs:
          type: array
          items:
            $ref: '#/components/schemas/WebScraperRun'
          nullable: true
      additionalProperties: false
    GetOneTimeRunsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetOneTimeRunsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProcessFileDocumentStatusInput:
      required:
        - document_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        document_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetProcessFileDocumentStatusOutput:
      type: object
      properties:
        document_id:
          type: string
          nullable: true
        file_document_process_status:
          type: string
          nullable: true
      additionalProperties: false
    GetProcessFileDocumentStatusOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProcessFileDocumentStatusOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetTopicsInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
      additionalProperties: false
    GetTopicsOutput:
      type: object
      properties:
        topics:
          type: array
          items:
            $ref: '#/components/schemas/TopicAnalyticsTopic'
          nullable: true
      additionalProperties: false
    GetTopicsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetTopicsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetUserProfileInput:
      required:
        - phone_number
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        phone_number:
          minLength: 1
          type: string
      additionalProperties: false
    GetUserProfileOutput:
      type: object
      properties:
        Id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
      additionalProperties: false
    GetWebPageContentInput:
      required:
        - apify_run_id
        - sleekflow_company_id
        - web_page_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        apify_run_id:
          minLength: 1
          type: string
        web_page_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetWebPageContentOutput:
      type: object
      properties:
        web_page_content:
          type: string
          nullable: true
      additionalProperties: false
    GetWebPageContentOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWebPageContentOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWebScraperInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetWebScraperOutput:
      type: object
      properties:
        web_scraper:
          $ref: '#/components/schemas/WebScraper'
      additionalProperties: false
    GetWebScraperOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWebScraperOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWebScraperRunInput:
      required:
        - apify_run_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        apify_run_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetWebScraperRunOutput:
      type: object
      properties:
        web_scraper_run:
          $ref: '#/components/schemas/WebScraperRun'
      additionalProperties: false
    GetWebScraperRunOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWebScraperRunOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWebScraperTaskInput:
      required:
        - apify_task_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        apify_task_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetWebScraperTaskOutput:
      type: object
      properties:
        web_scraper_task:
          $ref: '#/components/schemas/WebScraperTask'
      additionalProperties: false
    GetWebScraperTaskOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWebScraperTaskOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWebScraperTaskRunsInput:
      required:
        - apify_task_id
        - limit
        - offset
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        apify_task_id:
          minLength: 1
          type: string
        offset:
          type: integer
          format: int32
        limit:
          type: integer
          format: int32
      additionalProperties: false
    GetWebScraperTaskRunsOutput:
      type: object
      properties:
        web_scraper_runs:
          type: array
          items:
            $ref: '#/components/schemas/WebScraperRun'
          nullable: true
      additionalProperties: false
    GetWebScraperTaskRunsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWebScraperTaskRunsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Guardrail:
      required:
        - how_to_react
        - observe_for
        - title
      type: object
      properties:
        title:
          minLength: 1
          type: string
        observe_for:
          minLength: 1
          type: string
        how_to_react:
          minLength: 1
          type: string
      additionalProperties: false
    HandsOffCustomObjectTool:
      type: object
      properties:
        schema_id:
          type: string
          nullable: true
        assignment_reason_field_id:
          type: string
          nullable: true
        lead_score_field_id:
          type: string
          nullable: true
        short_summary_field_id:
          type: string
          nullable: true
      additionalProperties: false
    HubspotTool:
      type: object
      properties:
        api_key:
          type: string
          nullable: true
      additionalProperties: false
    InitializeIntelligentHubConfigInput:
      required:
        - sleekflow_company_id
        - usage_limits
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        usage_limits:
          type: object
          additionalProperties:
            type: integer
            format: int32
      additionalProperties: false
    InitializeIntelligentHubConfigOutput:
      type: object
      properties:
        intelligent_hub_config:
          $ref: '#/components/schemas/IntelligentHubConfig'
      additionalProperties: false
    InitializeIntelligentHubConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/InitializeIntelligentHubConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    IntelligentHubConfig:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        usage_limits:
          type: object
          additionalProperties:
            type: integer
            format: int32
          nullable: true
        usage_limit_offsets:
          type: object
          additionalProperties:
            type: integer
            format: int32
            nullable: true
          nullable: true
        enable_writing_assistant:
          type: boolean
        enable_smart_reply:
          type: boolean
        _etag:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    IntelligentHubUsageFilter:
      type: object
      properties:
        from_date_time:
          type: string
          format: date-time
          nullable: true
        to_date_time:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    KbDocument:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        content_type:
          type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        file_document_process_status:
          type: string
          nullable: true
        file_document_process_percentage:
          type: number
          format: double
        agent_assignments:
          type: array
          items:
            $ref: '#/components/schemas/AgentAssignment'
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        uploaded_by:
          type: string
          nullable: true
        debug_timestamps:
          $ref: '#/components/schemas/DebugTimestamps'
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    KnowledgeBaseEntry:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        chunk_id:
          type: string
          nullable: true
        content:
          type: string
          nullable: true
        content_en:
          type: string
          nullable: true
        knowledge_base_entry_source:
          $ref: '#/components/schemas/KnowledgeBaseEntrySource'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        categories:
          type: array
          items:
            $ref: '#/components/schemas/Category'
          nullable: true
        record_statuses:
          type: array
          items:
            type: string
          nullable: true
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    KnowledgeBaseEntrySource:
      type: object
      properties:
        source_id:
          type: string
          nullable: true
        source_type:
          type: string
          nullable: true
      additionalProperties: false
    KnowledgeCitationDto:
      type: object
      properties:
        chunk:
          type: string
          nullable: true
      additionalProperties: false
    KnowledgeRetrievalConfig:
      type: object
      properties:
        web_search:
          $ref: '#/components/schemas/WebSearchConfig'
        static_search:
          $ref: '#/components/schemas/StaticSearchConfig'
      additionalProperties: false
    LabelDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        hashtag:
          type: string
          nullable: true
        hashTagColor:
          type: string
          nullable: true
      additionalProperties: false
    LeadNurturingTools:
      type: object
      properties:
        lead_score_custom_object_tool:
          $ref: '#/components/schemas/LeadScoreCustomObjectTool'
        hands_off_custom_object_tool:
          $ref: '#/components/schemas/HandsOffCustomObjectTool'
        assignment_tool:
          $ref: '#/components/schemas/AssignmentTool'
        demo_tool:
          $ref: '#/components/schemas/DemoTool'
        additional_hands_off_classification_rules:
          $ref: '#/components/schemas/AdditionalHandsOffClassificationRules'
        is_followup_agent_enabled:
          type: boolean
      additionalProperties: false
    LeadScoreCriterionDto:
      type: object
      properties:
        weight:
          type: integer
          format: int32
        description:
          type: string
          nullable: true
      additionalProperties: false
    LeadScoreCustomObjectTool:
      type: object
      properties:
        schema_id:
          type: string
          nullable: true
        classification_field_id:
          type: string
          nullable: true
        reasoning_field_id:
          type: string
          nullable: true
        score_field_id:
          type: string
          nullable: true
      additionalProperties: false
    PlaygroundRecommendReplySnapshotDto:
      type: object
      properties:
        conversation_context:
          type: string
          nullable: true
        knowledge_base_entries:
          type: string
          nullable: true
        output_message:
          type: string
          nullable: true
        collaboration_mode:
          type: string
          nullable: true
        playground_session_id:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    PromptInstructionDto:
      type: object
      properties:
        objective:
          type: string
          nullable: true
        tone:
          pattern: Casual|Technical|Professional
          type: string
          nullable: true
        disclose_level:
          pattern: always|when_asked
          type: string
          nullable: true
        response_level:
          pattern: long|short|normal
          type: string
          nullable: true
        restrictiveness_level:
          pattern: normal|relaxed
          type: string
          nullable: true
        greeting_message:
          type: string
          nullable: true
        additional_instruction_core:
          type: string
          nullable: true
        additional_instruction_strategy:
          type: string
          nullable: true
        additional_instruction_response:
          type: string
          nullable: true
        additional_instruction_knowledge_retrieval:
          type: string
          nullable: true
        guardrails:
          type: array
          items:
            $ref: '#/components/schemas/Guardrail'
          nullable: true
      additionalProperties: false
    PublicBlob:
      type: object
      properties:
        container_name:
          type: string
          nullable: true
        blob_name:
          type: string
          nullable: true
        blob_id:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        expires_on:
          type: string
          format: date-time
        content_type:
          type: string
          nullable: true
      additionalProperties: false
    RecommendReplyInput:
      required:
        - conversation_context
        - sleekflow_company_id
      type: object
      properties:
        conversation_context:
          type: array
          items:
            $ref: '#/components/schemas/SfChatEntry'
        sleekflow_company_id:
          minLength: 1
          type: string
        intelligent_hub_usage_filter:
          $ref: '#/components/schemas/IntelligentHubUsageFilter'
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
      additionalProperties: false
    RecommendReplyOutput:
      type: object
      properties:
        recommended_reply:
          type: string
          nullable: true
      additionalProperties: false
    RecommendReplyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RecommendReplyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    RecommendReplyStreamingInput:
      required:
        - client_request_id
        - conversation_context
        - session_id
        - sleekflow_company_id
      type: object
      properties:
        conversation_context:
          type: array
          items:
            $ref: '#/components/schemas/SfChatEntry'
        sleekflow_company_id:
          minLength: 1
          type: string
        intelligent_hub_usage_filter:
          $ref: '#/components/schemas/IntelligentHubUsageFilter'
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        session_id:
          minLength: 1
          type: string
        client_request_id:
          minLength: 1
          type: string
      additionalProperties: false
    RecommendReplyStreamingOutput:
      type: object
      additionalProperties: false
    RecommendReplyStreamingOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RecommendReplyStreamingOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    RecordBlobUploadHistoryInput:
      required:
        - blob_id
        - blob_type
        - file_name
        - sleekflow_company_id
        - source_type
        - uploaded_by
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        blob_id:
          minLength: 1
          type: string
        file_name:
          minLength: 1
          type: string
        uploaded_by:
          minLength: 1
          type: string
        source_type:
          minLength: 1
          type: string
        blob_type:
          minLength: 1
          type: string
      additionalProperties: false
    RecordBlobUploadHistoryOutput:
      type: object
      properties:
        BlobUploadHistory:
          $ref: '#/components/schemas/BlobUploadHistory'
      additionalProperties: false
    RecordBlobUploadHistoryOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RecordBlobUploadHistoryOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    RephraseInput:
      required:
        - message
        - rephrase_target_type
        - sleekflow_company_id
      type: object
      properties:
        message:
          minLength: 1
          type: string
        rephrase_target_type:
          minLength: 1
          type: string
        intelligent_hub_usage_filter:
          $ref: '#/components/schemas/IntelligentHubUsageFilter'
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    RephraseOutput:
      type: object
      properties:
        output_message:
          type: string
          nullable: true
      additionalProperties: false
    RephraseOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RephraseOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    RequiredField:
      type: object
      properties:
        name:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        is_required:
          type: boolean
      additionalProperties: false
    ResurrectWebScraperRunInput:
      required:
        - apify_run_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        apify_run_id:
          minLength: 1
          type: string
      additionalProperties: false
    ResurrectWebScraperRunOutput:
      type: object
      properties:
        web_scraper_run:
          $ref: '#/components/schemas/WebScraperRun'
      additionalProperties: false
    ResurrectWebScraperRunOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ResurrectWebScraperRunOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ScheduleWebScraperTaskInput:
      required:
        - apify_task_id
        - cron_expression
        - is_enabled
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        apify_task_id:
          minLength: 1
          type: string
        cron_expression:
          minLength: 1
          type: string
        is_enabled:
          type: boolean
      additionalProperties: false
    ScheduleWebScraperTaskOutput:
      type: object
      properties:
        web_scraper_task:
          $ref: '#/components/schemas/WebScraperTask'
      additionalProperties: false
    ScheduleWebScraperTaskOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ScheduleWebScraperTaskOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SendMessageActionDto:
      type: object
      properties:
        response_type:
          type: string
          nullable: true
        instructions:
          type: string
          nullable: true
        enabled:
          type: boolean
      additionalProperties: false
    SfChatEntry:
      type: object
      properties:
        user:
          type: string
          nullable: true
        bot:
          type: string
          nullable: true
        sys:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
          nullable: true
        files:
          type: array
          items:
            $ref: '#/components/schemas/SfChatEntryFile'
          nullable: true
      additionalProperties: false
    SfChatEntryFile:
      type: object
      properties:
        url:
          type: string
          nullable: true
        mime_type:
          type: string
          nullable: true
        file_size:
          type: integer
          format: int64
      additionalProperties: false
    SleekflowSendMessageTool:
      type: object
      properties:
        channel_identity_id__expr:
          type: string
          nullable: true
        channel__expr:
          type: string
          nullable: true
        impersonation_staff_id__expr:
          type: string
          nullable: true
      additionalProperties: false
    SleekflowStaff:
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    StartOneTimeRunInput:
      required:
        - one_time_run_name
        - sleekflow_company_id
        - web_scraper_setting
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        one_time_run_name:
          minLength: 1
          type: string
        web_scraper_setting:
          $ref: '#/components/schemas/WebScraperSetting'
      additionalProperties: false
    StartOneTimeRunOutput:
      type: object
      properties:
        web_scraper_run:
          $ref: '#/components/schemas/WebScraperRun'
      additionalProperties: false
    StartOneTimeRunOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/StartOneTimeRunOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    StartWebScraperTaskInput:
      required:
        - apify_task_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        apify_task_id:
          minLength: 1
          type: string
      additionalProperties: false
    StartWebScraperTaskOutput:
      type: object
      properties:
        web_scraper_run:
          $ref: '#/components/schemas/WebScraperRun'
      additionalProperties: false
    StartWebScraperTaskOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/StartWebScraperTaskOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    StaticPageEntry:
      type: object
      properties:
        page:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
      additionalProperties: false
    StaticSearchConfig:
      type: object
      properties:
        pages:
          type: array
          items:
            $ref: '#/components/schemas/StaticPageEntry'
          nullable: true
      additionalProperties: false
    SyncWebScraperRunInput:
      required:
        - apify_run_id
        - is_sync_database
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        apify_run_id:
          minLength: 1
          type: string
        is_sync_database:
          type: boolean
      additionalProperties: false
    SyncWebScraperRunOutput:
      type: object
      properties:
        web_scraper_run:
          $ref: '#/components/schemas/WebScraperRun'
      additionalProperties: false
    SyncWebScraperRunOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SyncWebScraperRunOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ToolsConfig:
      type: object
      properties:
        sleekflow_send_message_tool:
          $ref: '#/components/schemas/SleekflowSendMessageTool'
        hubspot_tool:
          $ref: '#/components/schemas/HubspotTool'
      additionalProperties: false
    TopicAnalyticsTerm:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    TopicAnalyticsTopic:
      type: object
      properties:
        name:
          type: string
          nullable: true
        terms:
          type: array
          items:
            $ref: '#/components/schemas/TopicAnalyticsTerm'
          nullable: true
        record_statuses:
          type: array
          items:
            type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    TranslateInput:
      required:
        - message
        - sleekflow_company_id
        - target_language_code
      type: object
      properties:
        message:
          minLength: 1
          type: string
        target_language_code:
          minLength: 1
          type: string
        intelligent_hub_usage_filter:
          $ref: '#/components/schemas/IntelligentHubUsageFilter'
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    TranslateOutput:
      type: object
      properties:
        output_message:
          type: string
          nullable: true
      additionalProperties: false
    TranslateOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/TranslateOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    TriggerSiteMapping:
      type: object
      properties:
        trigger:
          type: string
          nullable: true
        site:
          type: string
          nullable: true
        pattern:
          type: string
          nullable: true
      additionalProperties: false
    UpdateAiFeatureSettingInput:
      required:
        - enable_smart_reply
        - enable_writing_assistant
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        enable_writing_assistant:
          type: boolean
        enable_smart_reply:
          type: boolean
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpdateAiFeatureSettingOutput:
      type: object
      properties:
        enable_writing_assistant:
          type: boolean
        enable_smart_reply:
          type: boolean
      additionalProperties: false
    UpdateAiFeatureSettingOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateAiFeatureSettingOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateIntelligentHubConfigInput:
      required:
        - sleekflow_company_id
        - usage_limits
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        usage_limits:
          type: object
          additionalProperties:
            type: integer
            format: int32
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpdateIntelligentHubConfigOutput:
      type: object
      properties:
        intelligent_hub_config:
          $ref: '#/components/schemas/IntelligentHubConfig'
      additionalProperties: false
    UpdateIntelligentHubConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateIntelligentHubConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateIntelligentHubConfigUsageLimitOffsetsInput:
      required:
        - sleekflow_company_id
        - usage_limit_offsets
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        usage_limit_offsets:
          type: object
          additionalProperties:
            type: integer
            format: int32
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpdateIntelligentHubConfigUsageLimitOffsetsOutput:
      type: object
      properties:
        intelligent_hub_config:
          $ref: '#/components/schemas/IntelligentHubConfig'
      additionalProperties: false
    UpdateIntelligentHubConfigUsageLimitOffsetsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateIntelligentHubConfigUsageLimitOffsetsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateTopicInput:
      required:
        - sleekflow_staff_id
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        updated_properties:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    UpdateTopicOutput:
      type: object
      properties:
        topic:
          $ref: '#/components/schemas/TopicAnalyticsTopic'
      additionalProperties: false
    UpdateTopicOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateTopicOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateTopicsInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        topics:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/TopicAnalyticsTopic'
          nullable: true
      additionalProperties: false
    UpdateTopicsOutput:
      type: object
      properties:
        topics:
          type: array
          items:
            $ref: '#/components/schemas/TopicAnalyticsTopic'
          nullable: true
      additionalProperties: false
    UpdateTopicsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateTopicsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateWebScraperTaskInput:
      required:
        - apify_task_id
        - display_name
        - sleekflow_company_id
        - web_scraper_setting
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        apify_task_id:
          minLength: 1
          type: string
        display_name:
          minLength: 1
          type: string
        web_scraper_setting:
          $ref: '#/components/schemas/WebScraperSetting'
      additionalProperties: false
    UpdateWebScraperTaskOutput:
      type: object
      properties:
        web_scraper_task:
          $ref: '#/components/schemas/WebScraperTask'
      additionalProperties: false
    UpdateWebScraperTaskOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateWebScraperTaskOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    WebPage:
      type: object
      properties:
        web_page_id:
          type: string
          nullable: true
        apify_run_id:
          type: string
          nullable: true
        web_page_uri:
          type: string
          nullable: true
        scraped_time:
          type: string
          format: date-time
          nullable: true
        language_iso_code:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        keywords:
          type: string
          nullable: true
        author:
          type: string
          nullable: true
      additionalProperties: false
    WebScraper:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        code:
          maxLength: 8
          minLength: 8
          type: string
          nullable: true
        web_scraper_tasks:
          type: array
          items:
            $ref: '#/components/schemas/WebScraperTask'
          nullable: true
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    WebScraperRun:
      type: object
      properties:
        apify_run_id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        apify_task_id:
          type: string
          nullable: true
        is_one_time_run:
          type: boolean
        one_time_run_name:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        started_at:
          type: string
          format: date-time
          nullable: true
        finished_at:
          type: string
          format: date-time
          nullable: true
        status:
          type: string
          nullable: true
        status_message:
          type: string
          nullable: true
        apify_key_value_store_id:
          type: string
          nullable: true
        apify_dataset_id:
          type: string
          nullable: true
        apify_request_queue_id:
          type: string
          nullable: true
        web_scraper_setting:
          $ref: '#/components/schemas/WebScraperSetting'
        web_pages:
          type: array
          items:
            $ref: '#/components/schemas/WebPage'
          nullable: true
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    WebScraperSetting:
      type: object
      properties:
        dynamic_content_wait_secs:
          type: integer
          format: int32
        max_crawl_depth:
          type: integer
          format: int32
        max_crawl_pages:
          type: integer
          format: int32
        max_concurrent_pages:
          type: integer
          format: int32
        start_url:
          type: string
          nullable: true
        initial_cookies:
          nullable: true
      additionalProperties: false
    WebScraperTask:
      type: object
      properties:
        apify_task_id:
          type: string
          nullable: true
        display_name:
          type: string
          nullable: true
        code:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        web_scraper_setting:
          $ref: '#/components/schemas/WebScraperSetting'
        web_scraper_task_scheduler:
          $ref: '#/components/schemas/WebScraperTaskScheduler'
      additionalProperties: false
    WebScraperTaskScheduler:
      type: object
      properties:
        name:
          type: string
          nullable: true
        apify_scheduler_id:
          type: string
          nullable: true
        cron_expression:
          type: string
          nullable: true
        is_enabled:
          type: boolean
      additionalProperties: false
    WebSearchConfig:
      type: object
      properties:
        trigger_site_mappings:
          type: array
          items:
            $ref: '#/components/schemas/TriggerSiteMapping'
          nullable: true
      additionalProperties: false