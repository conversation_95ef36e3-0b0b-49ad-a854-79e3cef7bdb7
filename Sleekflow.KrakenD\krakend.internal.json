{
  "endpoints": [
    {
      "endpoint": "/v1/healthz",
      "backend": [
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "USER_EVENT_HUB_HOST" }}"
          ],
          "encoding": "string",
          "group": "user-event-hub",
          "method": "GET"
        }
      ],
      "extra_config": {
        "qos/ratelimit/router": {
          "client_max_rate": 5.0,
          "key": "X-Azure-ClientIP",
          "max_rate": 100.0,
          "strategy": "ip"
        }
      },
      "method": "GET"
    },
    {
      "endpoint": "/v1/user-event-hub/SignalRWebhook",
      "backend": [
        {
          "url_pattern": "/SignalRWebhook",
          "host": [
            " {{ env "USER_EVENT_HUB_HOST" }} "
          ],
          "encoding": "no-op",
          "method": "POST"
        }
      ],
      "input_headers": [
        "*"
      ],
      "method": "POST",
      "output_encoding": "no-op"
    }
  ],
  "extra_config": {
    "router": {
      "auto_options": true,
      "hide_version_header": true,
      "logger_skip_paths": [
        "/__health",
        "/v1/tenant-hub/management/Rbac/IsRbacEnabled",
        "/v1/audit-hub/SystemAuditLogs/CreateSystemAuditLog",
        "/v1/user-event-hub/SignalRWebhook",
        "/v1/user-event-hub/Messages/SendMessageToUsers",
        "/v1/user-event-hub/Notifications/SendPushNotification",
        "/v1/user-event-hub/ReliableMessage/negotiate",
        "/v1/flow-hub/internals/CheckWorkflowContactEnrolmentConditionV2"
      ]
    },
    "telemetry/logging": {
      "level": "INFO",
      "prefix": "[KRAKEND]",
      "stdout": false,
      "syslog": false
    }
  },
  "plugin": {
    "folder": "./plugins/",
    "pattern": ".so"
  },
  "timeout": "60000ms",
  "version": 3
}