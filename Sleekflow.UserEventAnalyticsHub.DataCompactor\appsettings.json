{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "CompactorConfig": {"StorageAccountName": "", "StorageAccountKey": "", "EventsContainerName": "events", "MaxConcurrentCompanies": 3, "FileBatchSize": 100, "RetryAttempts": 3, "ProcessingTimeoutMinutes": 30}, "PostgreSqlConfig": {"ConnectionString": "", "DatabaseName": "sleekflow_events", "SchemaName": "public", "MaxConnections": 10}, "ProcessingConfig": {"DuckDbMemoryLimitMB": 1536, "MaxParallelCompanies": 3, "BatchCommitSize": 1000, "ProgressReportingIntervalSeconds": 30, "DuckDbThreads": 16, "DuckDbMaxMemoryMB": 2048}}