using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.TenantHub;
using Sleekflow.TenantHub.Configs;
using Sleekflow.TenantHub.Models.Auth0s;
using Sleekflow.TenantHub.Models.Constants;
using Sleekflow.TenantHub.Models.TravisBackend;
using Sleekflow.TenantHub.Utils;
using Sleekflow.Utils;
using static Sleekflow.TenantHub.Models.Constants.TravisBackendPaths;
using Auth0User = Auth0.ManagementApi.Models.User;
using TenantHubUser = Sleekflow.TenantHub.Models.Users.User;

namespace Sleekflow.TenantHub.Client;

public interface ITravisBackendClient
{
    Task<GetUserOutput?> GetUserAsync(string sleekflowUserId, string? location = ServerLocations.EastAsia);

    Task<RefreshIpWhitelistCacheOutput?> RefreshIpWhitelistCacheAsync(
        string sleekflowCompanyId,
        string? location = ServerLocations.EastAsia);

    Task<CleanIpWhitelistCacheOutput?> CleanIpWhitelistCacheAsync(
        string sleekflowCompanyId,
        string? location = ServerLocations.EastAsia);

    Task<CreateNewDbUserOutput?> CreateNewDbUserAsync(
        CreateNewDbUserInput auth0User,
        string? location = ServerLocations.EastAsia);

    Task<UpdateDbUserOutput?> UpdateDbUserAsync(UpdateDbUserInput input, string? location = ServerLocations.EastAsia);

    Task<IActionResult?> DeleteDbUserAsync(
        string sleekflowUserId,
        bool alsoDeleteAuth0User,
        string? location = ServerLocations.EastAsia);

    Task<GenerateInviteLinkOutput?> GenerateInviteLinkAsync(
        string sleekflowUserId,
        ShareableInvitationViewModel shareableInvitation,
        string? location);

    Task ResendInvitationEmailAsync(
        string sleekflowStaffUserId,
        string tenanthubUserId,
        string sleekflowAdminUserId,
        string sleekflowCompanyId,
        string location);

    Task<List<InviteUserByEmailResponseObject>?> InviteUserByEmailAsync(
        string sleekflowUserId,
        string sleekflowCompanyId,
        List<InviteUserObject> invitedUsers,
        List<long> teamIds,
        string location,
        string tenantHubUserId);

    Task<CompleteEmailInvitationResponse?> CompleteEmailInvitationAsync(
        TenantHubUser user,
        string sleekflowUserId,
        string token,
        string password,
        string position,
        string timeZoneInfoId,
        string location);

    Task<CompleteLinkInvitationResponse?> CompleteLinkInvitationAsync(
        string shareableId,
        InviteUserByLinkObject userByLink,
        string? location);

    Task<RegisterCompanyResponse?> RegisterCompanyAsync(
        string companyId,
        string userId,
        string? firstName,
        string? lastName,
        string phoneNumber,
        string industry,
        string? onlineShopSystem,
        string? companyWebsite,
        string companyName,
        string timeZoneInfoId,
        string companySize,
        string subscriptionPlanId,
        string? lmref,
        string? heardFrom,
        string? promotionCode,
        string? webClientUuid,
        string? referral,
        string companyType,
        List<string>? communicationTools,
        bool? isAgreeMarketingConsent,
        string? platformUsageIntent,
        string connectionStrategy,
        string location);

    Task<IsCompanyRegisteredResponse?> IsCompanyRegisteredAsync(string userId);

    Task<TravisBackendApplicationUser?> GetApplicationUserAsync(
        string? sleekflowUserId = null,
        string? email = null,
        string? userName = null);

    Task<AssociateDbUserWithAuth0UserOutput?> AssociateDbUserWithAuth0UserAsync(
        TravisBackendApplicationUser applicationUser,
        Auth0User auth0User,
        List<string?> roles);

    Task<IActionResult?> DeleteUserAsync(
        string sleekflowUserId,
        bool isAlsoDeleteAuth0User = false,
        string? location = ServerLocations.EastAsia);

    Task<ObjectResult?> OnUserChangePasswordAsync(string userId, string? location = ServerLocations.EastAsia);

    Task<DeleteCompanyStaffResponse?> DeleteCompanyStaffAsync(string companyId, string staffId, string? location = ServerLocations.EastAsia);

    Task<GetUserStaffOutput?> GetUserStaffAsync(string sleekflowUserId);

    Task<GetCompanyOwnerOutput?> GetCompanyOwnerAsync(string companyId, string? location = ServerLocations.EastAsia);

    Task<UpdateStaffRoleResponse?> UpdateStaffRoleAsync(
        string staffId,
        string companyId,
        string newRole,
        string? location = ServerLocations.EastAsia);

    Task<IsRbacEnabledFromServerEnvironmentResponse> IsRbacEnabledFromServerEnvironmentAsync(string? location = ServerLocations.EastAsia);

    Task<ImportUserFromCsvResponse?> ImportUserFromCsvAsync(
        string sleekflowCompanyId,
        List<ImportUserFromCsvObject> importUsers,
        string location);

    Task<GetCompanyTeamIdsByNamesResponse?> GetCompanyTeamIdsByNamesAsync(string companyId, List<string> teamNames, string location);

    Task<GetCompanyUsageResponse?> GetCompanyUsageAsync(
        string companyId,
        string? location = ServerLocations.EastAsia);

    Task<GetUserLossInfoOutput?> GetUserLostInfoAsync(
        string userId,
        string email,
        string location);

    Task OnRbacFeatureEnabledAsync(
        string sleekflowCompanyId,
        bool isFirstTimeEnabled,
        string? location = ServerLocations.EastAsia);

    Task OnRbacFeatureDisabledAsync(
        string sleekflowCompanyId,
        string? location = ServerLocations.EastAsia);

    Task OnUserRoleAssignedAsync(
        string sleekflowCompanyId,
        string userId,
        string staffId,
        Dictionary<string, string> roles,
        DateTimeOffset assignedAt,
        string? location = ServerLocations.EastAsia);

    Task OnCompanyPoliciesSavedAsync(
        string sleekflowCompanyId,
        Dictionary<string, Dictionary<string, List<string>>> rolePermissions,
        DateTimeOffset savedAt,
        string? location = ServerLocations.EastAsia);
}

public class TravisBackendClient : ITravisBackendClient, IScopedService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<TravisBackendClient> _logger;
    private readonly ITravisBackendConfig _travisBackendConfig;

    public TravisBackendClient(
        ILogger<TravisBackendClient> logger,
        IHttpClientFactory httpClientFactory,
        ITravisBackendConfig travisBackendConfig)
    {
        _logger = logger;
        _travisBackendConfig = travisBackendConfig;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public async Task<GetUserOutput?> GetUserAsync(string sleekflowUserId, string? location = ServerLocations.EastAsia)
    {
        return await SendAsync<GetUserOutput?, GetUserInput>(
            new GetUserInput(sleekflowUserId),
            GetUser,
            location);
    }

    public async Task<RefreshIpWhitelistCacheOutput?> RefreshIpWhitelistCacheAsync(
        string sleekflowCompanyId,
        string? location = ServerLocations.EastAsia)
    {
        return await SendAsync<RefreshIpWhitelistCacheOutput, RefreshIpWhitelistCacheInput>(
            new RefreshIpWhitelistCacheInput(sleekflowCompanyId),
            IpWhiteListRefreshCache,
            location);
    }

    public async Task<CleanIpWhitelistCacheOutput?> CleanIpWhitelistCacheAsync(
        string sleekflowCompanyId,
        string? location = ServerLocations.EastAsia)
    {
        return await SendAsync<CleanIpWhitelistCacheOutput, RefreshIpWhitelistCacheInput>(
            new RefreshIpWhitelistCacheInput(sleekflowCompanyId),
            IpWhiteListCleanCache,
            location);
    }

    public async Task<CreateNewDbUserOutput?> CreateNewDbUserAsync(
        CreateNewDbUserInput auth0User,
        string? location = ServerLocations.EastAsia)
    {
        return await SendAsync<CreateNewDbUserOutput,
            CreateNewDbUserInput>(
            auth0User,
            CreateNewDbUser,
            location);
    }

    public async Task<UpdateDbUserOutput?> UpdateDbUserAsync(
        UpdateDbUserInput input,
        string? location = ServerLocations.EastAsia)
    {
        return await SendAsync<UpdateDbUserOutput, UpdateDbUserInput>(
            input,
            UpdateDbUser,
            location);
    }

    public async Task<IActionResult?> DeleteDbUserAsync(
        string sleekflowUserId,
        bool alsoDeleteAuth0User,
        string? location = ServerLocations.EastAsia)
    {
        return await SendAsync<IActionResult, DeleteSleekflowUserInput>(
            new DeleteSleekflowUserInput(sleekflowUserId, alsoDeleteAuth0User),
            DeleteUser,
            location);
    }

    public async Task<GenerateInviteLinkOutput?> GenerateInviteLinkAsync(
        string sleekflowUserId,
        ShareableInvitationViewModel shareableInvitation,
        string? location)
    {
        return await SendAsync<GenerateInviteLinkOutput, GenerateInviteLinkInput>(
            new GenerateInviteLinkInput(sleekflowUserId, shareableInvitation, location),
            GenerateShareableLink,
            location);
    }

    public async Task ResendInvitationEmailAsync(
        string sleekflowStaffUserId,
        string tenanthubUserId,
        string sleekflowAdminUserId,
        string sleekflowCompanyId,
        string location)
    {
        await SendAsync<IActionResult, ResendInvitationEmailRequest>(
            new ResendInvitationEmailRequest(sleekflowStaffUserId, tenanthubUserId, sleekflowAdminUserId, sleekflowCompanyId, location),
            ResendEmailInvitation,
            location);
    }

    public async Task<List<InviteUserByEmailResponseObject>?> InviteUserByEmailAsync(
        string sleekflowUserId,
        string sleekflowCompanyId,
        List<InviteUserObject> invitedUsers,
        List<long> teamIds,
        string location,
        string tenantHubUserId)
    {
        var request = new InviteUserByEmailRequest(
            sleekflowUserId,
            sleekflowCompanyId,
            invitedUsers,
            teamIds,
            tenantHubUserId);
        return await SendAsync<List<InviteUserByEmailResponseObject>, InviteUserByEmailRequest>(
            request,
            InviteUserByEmail,
            location);
    }

    public async Task<CompleteEmailInvitationResponse?> CompleteEmailInvitationAsync(
        TenantHubUser user,
        string sleekflowUserId,
        string token,
        string password,
        string position,
        string timeZoneInfoId,
        string location)
    {
        return await SendAsync<CompleteEmailInvitationResponse, CompleteEmailInvitationRequest>(
            new CompleteEmailInvitationRequest(
                user,
                sleekflowUserId,
                token,
                password,
                position,
                timeZoneInfoId,
                location),
            TravisBackendPaths.CompleteEmailInvitation,
            location);
    }

    public async Task<CompleteLinkInvitationResponse?> CompleteLinkInvitationAsync(
        string shareableId,
        InviteUserByLinkObject userByLink,
        string? location)
    {
        return await SendAsync<CompleteLinkInvitationResponse, CompleteLinkInvitationRequest>(
            new CompleteLinkInvitationRequest(shareableId, userByLink, location!),
            CompleteLinkInvitation,
            location);
    }

    public async Task<RegisterCompanyResponse?> RegisterCompanyAsync(
        string companyId,
        string userId,
        string? firstName,
        string? lastName,
        string phoneNumber,
        string industry,
        string? onlineShopSystem,
        string? companyWebsite,
        string companyName,
        string timeZoneInfoId,
        string companySize,
        string subscriptionPlanId,
        string? lmref,
        string? heardFrom,
        string? promotionCode,
        string? webClientUuid,
        string? referral,
        string companyType,
        List<string>? communicationTools,
        bool? isAgreeMarketingConsent,
        string? platformUsageIntent,
        string connectionStrategy,
        string location)
    {
        var request = new RegisterCompanyRequest(
            companyId,
            userId,
            firstName,
            lastName,
            phoneNumber,
            industry,
            onlineShopSystem,
            companyWebsite,
            companyName,
            timeZoneInfoId,
            companySize,
            subscriptionPlanId,
            lmref,
            heardFrom,
            promotionCode,
            webClientUuid,
            referral,
            companyType,
            communicationTools,
            isAgreeMarketingConsent,
            platformUsageIntent,
            connectionStrategy,
            location);

        return await SendAsync<RegisterCompanyResponse, RegisterCompanyRequest>(
            request,
            RegisterCompany,
            location);
    }

    public async Task<IsCompanyRegisteredResponse?> IsCompanyRegisteredAsync(string userId)
    {
        return await SendAsync<IsCompanyRegisteredResponse, IsCompanyRegisterRequest>(
            new IsCompanyRegisterRequest(userId),
            IsCompanyRegistered);
    }

    public async Task<TravisBackendApplicationUser?> GetApplicationUserAsync(
        string? sleekflowUserId = null,
        string? email = null,
        string? userName = null)
    {
        var request = new GetApplicationUserRequest(sleekflowUserId, email, userName);
        return await SendAsync<TravisBackendApplicationUser, GetApplicationUserRequest>(
            request,
            GetApplicationUser);
    }

    public async Task<GetUserStaffOutput?> GetUserStaffAsync(string sleekflowUserId)
    {
        return await SendAsync<GetUserStaffOutput, GetUserStaffInput>(
            new GetUserStaffInput(sleekflowUserId),
            GetUserStaff);
    }

    public async Task<AssociateDbUserWithAuth0UserOutput?> AssociateDbUserWithAuth0UserAsync(
        TravisBackendApplicationUser applicationUser,
        Auth0User auth0User,
        List<string?> roles)
    {
        return await SendAsync<AssociateDbUserWithAuth0UserOutput, AssociateDbUserWithAuth0UserInput>(
            new AssociateDbUserWithAuth0UserInput(applicationUser, auth0User, roles),
            AssociateDbUserToAuth0);
    }

    public async Task<IActionResult?> DeleteUserAsync(
        string sleekflowUserId,
        bool isAlsoDeleteAuth0User = false,
        string? location = ServerLocations.EastAsia)
    {
        return await SendAsync<IActionResult, DeleteSleekflowUserInput>(
            new DeleteSleekflowUserInput(sleekflowUserId, isAlsoDeleteAuth0User),
            DeleteUser,
            location);
    }

    public async Task<ObjectResult?> OnUserChangePasswordAsync(
        string userId,
        string? location = ServerLocations.EastAsia)
    {
        return await SendAsync<ObjectResult, OnUserChangePasswordRequest>(
            new OnUserChangePasswordRequest(userId),
            OnUserChangePassword,
            location);
    }

    public async Task<DeleteCompanyStaffResponse?> DeleteCompanyStaffAsync(string companyId, string staffId, string? location = ServerLocations.EastAsia)
    {
        // The command timeout of remove company staff on Travis Backend is 240 seconds
        return await SendAsync<DeleteCompanyStaffResponse, DeleteCompanyStaffRequest>(
            new DeleteCompanyStaffRequest(companyId, staffId),
            DeleteCompanyStaff,
            location);
    }

    public async Task<UpdateStaffRoleResponse?> UpdateStaffRoleAsync(
        string staffId,
        string companyId,
        string newRole,
        string? location = ServerLocations.EastAsia)
    {
        return await SendAsync<UpdateStaffRoleResponse, UpdateStaffRoleRequest>(
            new UpdateStaffRoleRequest(staffId, companyId, newRole),
            UpdateStaffRole,
            location);
    }

    public async Task<IsRbacEnabledFromServerEnvironmentResponse?> IsRbacEnabledFromServerEnvironmentAsync(
        string? location = ServerLocations.EastAsia)
    {

        return await SendAsync<IsRbacEnabledFromServerEnvironmentResponse, IsRbacEnabledFromServerEnvironmentRequest>(
            new IsRbacEnabledFromServerEnvironmentRequest(),
            IsRbacEnabled,
            location);
    }

    public async Task<ImportUserFromCsvResponse?> ImportUserFromCsvAsync(
        string sleekflowCompanyId,
        List<ImportUserFromCsvObject> importUsers,
        string location)
    {
        return await SendAsync<ImportUserFromCsvResponse, ImportUserFromCsvRequest>(
            new ImportUserFromCsvRequest(sleekflowCompanyId, importUsers),
            ImportUserFromCsv,
            location);
    }

    public async Task<GetCompanyTeamIdsByNamesResponse?> GetCompanyTeamIdsByNamesAsync(
        string companyId,
        List<string> teamNames,
        string location)
    {
        return await SendAsync<GetCompanyTeamIdsByNamesResponse, GetCompanyTeamIdsByNamesRequest>(
            new GetCompanyTeamIdsByNamesRequest(companyId, teamNames),
            GetCompanyTeamIdsByNames,
            location);
    }

    public async Task<GetCompanyUsageResponse?> GetCompanyUsageAsync(
        string companyId,
        string? location = ServerLocations.EastAsia)
    {
        return await SendAsync<GetCompanyUsageResponse, GetCompanyUsageRequest>(
            new GetCompanyUsageRequest(companyId),
            GetCompanyUsage,
            location);
    }

    public async Task OnRbacFeatureEnabledAsync(
        string sleekflowCompanyId,
        bool isFirstTimeEnabled,
        string? location = ServerLocations.EastAsia)
    {
        await SendAsync<IActionResult, OnRbacFeatureEnabledRequest>(
            new OnRbacFeatureEnabledRequest(sleekflowCompanyId, isFirstTimeEnabled),
            OnRbacFeatureEnabled,
            location);
    }

    public async Task OnRbacFeatureDisabledAsync(
        string sleekflowCompanyId,
        string? location = ServerLocations.EastAsia)
    {
        await SendAsync<IActionResult, OnRbacFeatureDisabledRequest>(
            new OnRbacFeatureDisabledRequest(sleekflowCompanyId),
            OnRbacFeatureDisabled,
            location);
    }

    public async Task OnUserRoleAssignedAsync(
        string sleekflowCompanyId,
        string userId,
        string staffId,
        Dictionary<string, string> roles,
        DateTimeOffset assignedAt,
        string? location = ServerLocations.EastAsia)
    {
        await SendAsync<IActionResult, OnUserRoleAssignedRequest>(
            new OnUserRoleAssignedRequest(sleekflowCompanyId, userId, staffId, roles, assignedAt),
            OnUserRoleAssigned,
            location);
    }

    public async Task OnCompanyPoliciesSavedAsync(
        string sleekflowCompanyId,
        Dictionary<string, Dictionary<string, List<string>>> rolePermissions,
        DateTimeOffset savedAt,
        string? location = ServerLocations.EastAsia)
    {
        await SendAsync<IActionResult, OnCompanyPoliciesSavedRequest>(
            new OnCompanyPoliciesSavedRequest(sleekflowCompanyId, rolePermissions, savedAt),
            OnCompanyPoliciesSaved,
            location);
    }

    public async Task<GetCompanyOwnerOutput?> GetCompanyOwnerAsync(
        string companyId,
        string? location = ServerLocations.EastAsia)
    {
        return await SendAsync<GetCompanyOwnerOutput?, GetCompanyOwnerRequest>(
            new GetCompanyOwnerRequest(companyId),
            GetCompanyOwner,
            location);
    }

    public async Task<GetUserLossInfoOutput?> GetUserLostInfoAsync(string userId, string email, string location)
    {
        return await SendAsync<GetUserLossInfoOutput, GetUserLossInfoInput>(
            new GetUserLossInfoInput(userId, email), GetUserLossInfo, location);
    }

    private string CreateAuth0EventToken()
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(),
            Expires = DateTime.UtcNow.AddMinutes(5),
            Issuer = string.Empty,
            Audience = string.Empty,
            SigningCredentials = new SigningCredentials(
                new SymmetricSecurityKey(Encoding.ASCII.GetBytes(_travisBackendConfig.Secret)),
                SecurityAlgorithms.HmacSha256Signature)
        };
        return tokenHandler.WriteToken(tokenHandler.CreateToken(tokenDescriptor));
    }

    // Default value of timeoutSeconds is 100 seconds
    private async Task<TResponse?> SendAsync<TResponse, TRequest>(
        TRequest input,
        string endPoint,
        string? location = ServerLocations.EastAsia,
        Dictionary<string, string>? additionHeaders = null)
    {
        try
        {
            var inputString = JsonConvert.SerializeObject(input);

            var requestMessage = new HttpRequestMessage
            {
                Method = HttpMethod.Post,
                Content = new StringContent(inputString, Encoding.UTF8, "application/json"),
                Headers =
                {
                    {
                        "X-Tenant-Hub-Authorization", CreateAuth0EventToken()
                    }
                },
                RequestUri = new Uri($"{_travisBackendConfig.BaseUrl}{endPoint}")
            };

            if (location is not null)
            {
                requestMessage.Headers.Add("X-Sleekflow-Location", location);
            }

            if (additionHeaders is not null)
            {
                foreach (var header in additionHeaders)
                {
                    requestMessage.Headers.Add(header.Key, header.Value);
                }
            }

            _logger.LogInformation(
                "POST [{Type}] Data to Travis Backend request: {Request}",
                typeof(TRequest).Name,
                JsonConvert.SerializeObject(requestMessage));

            var responseMessage = await _httpClient.SendAsync(requestMessage);
            _logger.LogInformation(
                "POST [{Type}] Data to Travis Backend result: {Response}",
                typeof(TResponse).Name,
                JsonConvert.SerializeObject(responseMessage));

            if (!responseMessage.IsSuccessStatusCode)
            {
                var message = await responseMessage.Content.ReadAsStringAsync();
                if (TravisBackendUtils.IsConvertibleResponse<SfCoreInvalidModelStateException>(message))
                {
                    throw new SfCoreInvalidModelStateException(
                        JsonConvert.DeserializeObject<SfCoreInvalidModelState>(message)!);
                }

                throw new SfTravisBackendErrorResponseException(message);
            }

            return (await responseMessage.Content.ReadAsStringAsync()).ToObject<TResponse>();
        }
        catch (SfCoreInvalidModelStateException e)
        {
            _logger.LogError(
                e,
                "[{TypeOfRequest}] {ErrorMessage}.\nDetails:\n{Exception}\ninput:\n{CompanyInput}",
                nameof(TRequest),
                e.Message,
                e.SerializedContext,
                JsonConvert.SerializeObject(input));
            throw;
        }
        catch (SfTravisBackendErrorResponseException e)
        {
            _logger.LogError(
                e,
                "[{TypeOfRequest}] {ErrorMessage}.\nDetails:\n{Exception}\ninput:\n{CompanyInput}",
                nameof(TRequest),
                e.Message,
                e.SerializedContext,
                JsonConvert.SerializeObject(input));
            throw;
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "[{TypeOfRequest}] {ErrorMessage}. input:\n{CompanyInput}",
                nameof(TRequest),
                e.Message,
                JsonConvert.SerializeObject(input));

            throw new SfInternalErrorException(e, $"{e.Message}");
        }
    }
}