﻿using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.NeedConfigs;

public class TriggerNeedConfig
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("label")]
    public string Label { get; set; }

    [JsonProperty("arg_name")]
    public string ArgName { get; set; }

    [JsonProperty("field_path")]
    public string FieldPath { get; set; }

    [JsonProperty("trigger_id")]
    public string TriggerId { get; set; }

    [JsonProperty("input_type")]
    public string InputType { get; set; }

    [JsonProperty("input_group")]
    public string? InputGroup { get; set; }

    [JsonProperty("validations")]
    public Dictionary<string, object?>? Validations { get; set; }

    [JsonProperty("placeholder")]
    public string? Placeholder { get; set; }

    [JsonProperty("helper_text")]
    public string? HelperText { get; set; }

    [JsonProperty("options")]
    public List<NeedConfigOption>? Options { get; set; }

    [JsonProperty("options_request_path")]
    public string? OptionsRequestPath { get; set; }

    [JsonProperty("options_request_arg_names")]
    public List<string>? OptionsRequestArgNames { get; set; }

    [JsonProperty("data_request_path")]
    public string? DataRequestPath { get; set; }

    [JsonProperty("data_request_arg_names")]
    public List<string>? DataRequestArgNames { get; set; }

    [JsonProperty("conditions__expr")]
    public string? ConditionsExpr { get; set; }

    [JsonProperty("disabled")]
    public bool? Disabled { get; set; }

    [JsonProperty("is_with_copy")]
    public bool? IsWithCopy { get; set; }

    [JsonProperty("items")]
    public List<TriggerNeedConfig>? Items { get; set; }

    [JsonProperty("hidden")]
    public bool? Hidden { get; set; }

    [JsonProperty("condition_ignore")]
    public bool? ConditionIgnore { get; set; }

    [JsonProperty("default_value")]
    public object? DefaultValue { get; set; }

    [JsonConstructor]
    public TriggerNeedConfig(
        string id,
        string label,
        string argName,
        string fieldPath,
        string triggerId,
        string inputType,
        string? inputGroup,
        Dictionary<string, object?>? validations,
        string? placeholder,
        string? helperText,
        List<NeedConfigOption>? options,
        string? optionsRequestPath,
        List<string>? optionsRequestArgNames,
        string? dataRequestPath,
        List<string>? dataRequestArgNames,
        string? conditionsExpr,
        List<TriggerNeedConfig>? items,
        bool disabled = false,
        bool isWithCopy = false,
        bool hidden = false,
        bool conditionIgnore = false,
        object? defaultValue = null)
    {
        Id = id;
        Label = label;
        ArgName = argName;
        FieldPath = fieldPath;
        TriggerId = triggerId;
        InputType = inputType;
        InputGroup = inputGroup;
        Validations = validations;
        Placeholder = placeholder;
        HelperText = helperText;
        Options = options;
        OptionsRequestPath = optionsRequestPath;
        OptionsRequestArgNames = optionsRequestArgNames;
        DataRequestPath = dataRequestPath;
        DataRequestArgNames = dataRequestArgNames;
        ConditionsExpr = conditionsExpr;
        Items = items;
        Disabled = disabled;
        IsWithCopy = isWithCopy;
        Hidden = hidden;
        ConditionIgnore = conditionIgnore;
        DefaultValue = defaultValue;
    }
}