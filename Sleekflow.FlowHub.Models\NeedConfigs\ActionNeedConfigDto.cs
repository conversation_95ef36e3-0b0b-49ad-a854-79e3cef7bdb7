﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.NeedConfigs;

public class ActionNeedConfigDto
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("arg_name")]
    public string ArgName { get; set; }

    [JsonProperty("label")]
    public string Label { get; set; }

    [JsonProperty("action_group")]
    public string ActionGroup { get; set; }

    [JsonProperty("action_subgroup")]
    public string ActionSubgroup { get; set; }

    [JsonProperty("input_type")]
    [RegularExpression("single_select|multi_select|text|number|date|textarea|file|whatsapp_template")]
    public string InputType { get; set; }

    [JsonProperty("input_group")]
    public string? InputGroup { get; set; }

    [JsonProperty("validations")]
    public Dictionary<string, object?>? Validations { get; set; }

    [JsonProperty("fields")]
    public List<ActionNeedConfig>? Fields { get; set; }

    [JsonProperty("options")]
    public List<NeedConfigOption>? Options { get; set; }

    [JsonProperty("options_request_path")]
    public string? OptionsRequestPath { get; set; }

    [JsonProperty("options_request_arg_names")]
    public List<string>? OptionsRequestArgNames { get; set; }

    [JsonProperty("placeholder")]
    public string? Placeholder { get; set; }

    [JsonProperty("helper_text")]
    public string? HelperText { get; set; }

    [JsonProperty("is_expression_supported")]
    public bool IsExpressionSupported { get; set; }

    [JsonProperty("hidden")]
    public bool? Hidden { get; set; }

    [JsonProperty("default_value")]
    public object? DefaultValue { get; set; }

    [JsonConstructor]
    public ActionNeedConfigDto(
        string id,
        string argName,
        string label,
        string actionGroup,
        string actionSubgroup,
        string inputType,
        string? inputGroup,
        Dictionary<string, object?>? validations,
        List<ActionNeedConfig>? fields,
        List<NeedConfigOption>? options,
        string? optionsRequestPath,
        List<string>? optionsRequestArgNames,
        string? placeholder,
        string? helperText,
        bool isExpressionSupported,
        bool? hidden,
        object? defaultValue)
    {
        Id = id;
        ArgName = argName;
        Label = label;
        ActionGroup = actionGroup;
        ActionSubgroup = actionSubgroup;
        InputType = inputType;
        InputGroup = inputGroup;
        Validations = validations;
        Fields = fields;
        Options = options;
        OptionsRequestPath = optionsRequestPath;
        OptionsRequestArgNames = optionsRequestArgNames;
        Placeholder = placeholder;
        HelperText = helperText;
        IsExpressionSupported = isExpressionSupported;
        Hidden = hidden;
        DefaultValue = defaultValue;
    }

    public ActionNeedConfigDto(ActionNeedConfig config)
    {
        Id = config.Id;
        ArgName = config.ArgName;
        Label = config.Label;
        ActionGroup = config.ActionGroup;
        ActionSubgroup = config.ActionSubgroup;
        InputType = config.InputType;
        InputGroup = config.InputGroup;
        Validations = config.Validations;
        Fields = config.Fields;
        Options = config.Options;
        OptionsRequestPath = config.OptionsRequestPath;
        OptionsRequestArgNames = config.OptionsRequestArgNames;
        Placeholder = config.Placeholder;
        HelperText = config.HelperText;
        IsExpressionSupported = config.IsExpressionSupported;
        Hidden = config.Hidden;
        DefaultValue = config.DefaultValue;
    }
}