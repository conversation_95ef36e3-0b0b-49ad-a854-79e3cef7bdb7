using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Workflows.Operators;

namespace Sleekflow.FlowHub.Workflows.Operators;

public interface IOperatorService
{
    Task<List<Operator>> GetOperatorsAsync(
        string variableType,
        string? triggerId);
}

public class OperatorService : IOperatorService, IScopedService
{
    private readonly IOperatorRepository _operatorRepository;

    public OperatorService(IOperatorRepository operatorRepository)
    {
        _operatorRepository = operatorRepository;
    }

    public async Task<List<Operator>> GetOperatorsAsync(
        string variableType,
        string? triggerId)
    {
        var operators = await _operatorRepository.GetOperatorsAsync(
            variableType,
            triggerId);
        return operators;
    }
}