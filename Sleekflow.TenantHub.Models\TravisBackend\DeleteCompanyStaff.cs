using Newtonsoft.Json;

namespace Sleekflow.TenantHub.Models.TravisBackend;

public class DeleteCompanyStaffRequest
{
    [JsonProperty("company_id")]
    public string CompanyId { get; set; }

    [JsonProperty("staff_id")]
    public string StaffId { get; set; }

    [JsonConstructor]
    public DeleteCompanyStaffRequest(string companyId, string staffId)
    {
        CompanyId = companyId;
        StaffId = staffId;
    }
}

public class DeleteCompanyStaffResponse
{
    [JsonProperty("success")]
    public bool Success { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonConstructor]
    public DeleteCompanyStaffResponse(bool success, string message)
    {
        Success = success;
        Message = message;
    }
}