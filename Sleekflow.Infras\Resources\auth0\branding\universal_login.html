<!DOCTYPE html>
<html lang="{{locale}}">

<head>
  {%- auth0:head -%}
  <style>
    @font-face {
      font-family: 'Matter';
      font-style: normal;
      font-weight: 600;
      font-display: swap;
      src: url(https://sleekflow.io/fonts/Matter-SemiBold.woff2) format('woff2'),
      url(https://sleekflow.io/fonts/Matter-SemiBold.woff) format('woff');
    }

    body {
      padding: 32px 64px 0 64px;
      background-color: #F7FAFF;
      font-family: 'Inter';
      color: #696E81;
    }

    /* for page to send reset password email  */
    .reset-password > div > div > main > ._prompt-box-outer > div > div ,
      /* for page to show check email screen */
    .reset-password > div > div > main > ._prompt-box-outer > div {
      min-height: initial;
    }

    .header {
      display: flex;
      justify-content: space-between;
      height: 40px;
    }

    .header>* {
      vertical-align: middle;
    }

    .header>a>img {
      width: 160px;
      height: auto;
    }

    .icon-check-circle {
      fill: #0066FF;
    }

    select {
      background-color: transparent;
      border: none;
      color: #0D122C;
      font-weight: 400;
      font-size: 12px;
      line-height: 18px;
      outline: none;
    }

    #content {
      display: flex;
      justify-content: space-between;
      margin: 0 auto;
      align-items: center;
      max-width: 1400px;
      height: max-content;
      min-height: calc(100svh - 142px);
      width: 100%;
    }

    .section {
      width: 50%;
      min-width: 480px;
      align-items: center;
      margin: 0 16px;
    }

    .section.full-width {
      width: 100%;
      margin: 0;
    }

    .control-wrapper {
      margin: 36px auto;
      max-width: 450px;
    }

    #carousel.login > .control-wrapper,
    #carousel.login-id > .control-wrapper {
      display: flex;
      justify-content: center;
    }

    .section.marketing-section {
      position: relative;
    }

    #carousel {
      width: 100%;
      position: relative;
    }

    .centering {
      width: 480px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    #carousel .title {
      font-size: 24px;
      font-weight: 700;
      line-height: 26px;
      margin-bottom: 8px;
      color: #0D122C;
    }

    h1,
    button,
    #selected-language-label>span {
      font-family: 'Matter';
    }

    #carousel .description {
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      font-family: 'Inter';
      color: #6A6E81;

    }

    #slide-container {
      scroll-snap-type: x mandatory;
      overflow-x: scroll;
      overflow-y: hidden;
      display: flex;
      align-items: center;
      gap: 36px;
      -webkit-overflow-scrolling: touch;
      scroll-behavior: smooth;
      margin: 0 20px;
    }

    /* hanlde no scoll bar */
    #slide-container {
      -ms-overflow-style: none;
      /* Internet Explorer 10+ */
      scrollbar-width: none;
      /* Firefox */
    }

    #slide-container::-webkit-scrollbar {
      display: none;
      /* Safari and Chrome */
    }
    /* end of handle no scoll bar */

    .slide {
      scroll-snap-align: center;
      position: relative;
      min-width: 100%;
    }


    #carousel.login > #slide-container > .slide,
    #carousel.login-id > #slide-container > .slide {
      text-align: center;
    }

    .slide-image-wrapper {
      height: 245px;
      margin-bottom: 32px;
    }

    .slide img {
      height: 245px;
      width: auto;
    }

    .indicator-wrapper {
      display: flex;
      gap: 8px;
      vertical-align: middle;
    }

    .arrow {
      cursor: pointer;
      border: solid #576E93;
      border-width: 0 2px 2px 0;
      width: 8px;
      height: 8px;
      display: inline-block;
    }

    .arrow.right {
      transform: rotate(-45deg);
      -webkit-transform: rotate(-45deg);
    }

    .arrow.down {
      transform: rotate(45deg);
      -webkit-transform: rotate(45deg);
      margin-bottom: 4px;
    }

    .arrow.left {
      transform: rotate(135deg);
      -webkit-transform: rotate(135deg);
    }

    .arrow.up {
      transform: rotate(225deg);
      -webkit-transform: rotate(225deg);
      margin-top: 2px
    }

    .slide-indicator {
      width: 8px;
      height: 8px;
      background-color: #576E93;
      border-radius: 4px;
      opacity: 0.25;
      vertical-align: middle;
      cursor: pointer;
    }

    #back-button,
    #forward-button {
      position: absolute;
    }

    #back-button {
      top: 125px;
    }

    #carousel.signup > #back-button, #carousel.signup > #forward-button {
      display: none;
    }

    #forward-button {
      top: 125px;
      right: 0;
    }

    .slide-indicator.active {
      opacity: 1;
    }

    .slider {
      vertical-align: middle;
    }

    .testimonial {
      color: #0D122C;
    }

    .testimonial>svg {
      margin-bottom: 48px;
    }

    .testimonial>.text {
      font-family: 'Matter';
      font-style: normal;
      font-weight: 600;
      font-size: 28px;
      line-height: 32px;
      margin-bottom: 40px;
    }

    .testimonial>.source {
      font-weight: 600;
      font-size: 20px;
      line-height: 24px;
      margin-bottom: 8px;
    }

    .testimonial>.source-position {
      font-weight: 600;
      font-size: 12px;
      line-height: 16px;
      color: #4B9BE8;
    }

    .testimonial>.logo-wrapper {
      width: 88px;
      height: 88px;
      background-color: #FFFFFF;
      border-radius: 50%;
      position: relative;
      margin-top: 16px;
    }

    .testimonial>.logo-wrapper>img {
      width: 72px;
      height: auto;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);

    }

    ._prompt-wrapper {
      display: flex;
      flex-direction: column;
    }

    ._prompt-box-outer>div {
      grid-area: initial !important;
    }

    .footer {
      z-index: 999;
      padding:24px 0;
      position: sticky;
      bottom: 0px;
      width: 100%;
      font-size: 12px !important;
      line-height: 16px !important;
    }

    .footer ul {
      display: flex;
      column-gap: 32px;
    }

    .footer ul li {
      font-weight: 600;
      display: inline-block;
    }

    a {
      color: #576E93;
      font-size: 12px;
      line-height: 16px;
    }

    #language-select {
      position: relative;
      color: #6A6E81;
      line-height: 40px;
    }

    #selected-language-label {
      cursor: pointer;
    }

    #selected-language-label>svg,
    #selected-language-label>span {
      margin-right: 8px;
      line-height: 18px;
    }

    #selected-language-label>* {
      vertical-align: middle;
    }

    #language-dropdown {
      position: absolute;
      top: 36px;
      right: 0;
      width: 200px;
      background: #FFFFFF;
      padding: 16px 0;
      box-shadow: 0px 10px 20px 2px rgba(0, 102, 255, 0.1);
      border-radius: 8px;
      z-index: 1000;
      line-height: 16px;
      color: #576E93;
      font-weight: 600;
      opacity: 0;
      display: none;
      transform: none;
      transition: opacity 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, transform 167ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
      transform-origin: 197.371px 5.75px;
    }

    #language-dropdown>div {
      padding: 8px 16px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
    }

    #language-dropdown>div.selected {
      background-color: #F7FAFF;
    }

    .en>#en {
      background-color: #F7FAFF;
    }

    .zh-HK>#zh-HK,
    .zh-TW>#zh-HK {
      background-color: #F7FAFF;
    }

    .zh-CN>#zh-CN {
      background-color: #F7FAFF;
    }

    ._widget-auto-layout {
      height: initial;
      min-height: initial !important;
    }

    ._widget {
      /* height: calc(100vh - 142px) !important; */
      /* min-height: 650px !important; */
      padding: 0 !important;
    }

    .prompt-wrapper {
      display: flex;
      flex-direction: column;
    }

    .prompt-wrapper>main>._prompt-box-outer {
      width: 480px;
    }

    .prompt-wrapper.full-width>main>._prompt-box-outer {
      width: 560px;
      justify-content: initial;
      height: initial;
      min-height: initial;
    }

    #custom-prompt-logo {
      display: none !important;
    }

    .success-email,
    .success-lock {
      /* = icons shown after sending email for reset password, or succesfully changing password */
      --color: #13972A;
    }

    a:focus {
      background-color: none;
    }

    .ulp-alternate-action {
      line-height: 20px;
    }

    #overlay {
      display: none;
    }

    .footer-overlay {
      border-top:1px solid #d6e4fb;
      position:absolute;
      width:100vw;
      background:#F7FAFF;
      height:100%;
      left: 50%;
      transform: translate(-50%, 0);
      top:0;
      z-index: -1;
    }

    @media only screen and (max-width: 1000px) {

      #content {
        display: flex;
        flex-direction: column-reverse;
        justify-content: flex-end;
      }

      .section {
        width: 100%;
        min-width: initial;
      }

      .centering {
        width: initial;
        position: initial;
        top: initial;
        transform: initial;
      }

      #carousel {
        width: 480px !important;
        margin: 0 auto 96px;
      }

      ._widget {
        /* height: 600px !important; */
      }

      /* ._prompt-box-outer, .disclaimer {
          width: 100% !important;
          padding: 0 64px
        } */
      body {
        padding: 16px 32px 0 32px;
      }

      .testimonial {
        padding-top: 64px;
      }
    }

    .logout-button-mobile {
      display: none;
    }

    @media only screen and (max-width: 600px) {
      #content > div.widget-logout-wrapper > div > main > section > div {
        box-shadow: none !important;
      }

      .footer-overlay {
        background:#FFF;
      }

      .logout-button-desktop {
        display: none;
      }

      .logout-button-mobile {
        display: block;
      }
      #content {
        display: flex;
        flex-direction: column-reverse;
      }

      .section.marketing-section {
        display: none;
      }

      .section {
        width: 100%;
        margin: 0;
      }

      ._widget {
        width: 100% !important;
      }

      body {
        padding: 24px 0 0 0;
      }

      .header>a>img {
        width: 120px;
      }

      #language-select {
        line-height: 24px;
      }

      .header,
      .footer {
        padding: 8px 40px;
      }



      .prompt-wrapper>main>._prompt-box-outer,
      .prompt-wrapper.full-width>main>._prompt-box-outer {
        width: 100vw;
      }

      .footer>ul {
        justify-content: space-between;
      }

      .toc-wrapper {
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding-top:16px;
      }

      .footer-group-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
      }

    }

    body.isMobile > .header > #language-select {
      display: none;
    }

    body.login.isMobile > #content > .prompt-wrapper > main > section > div > div > div > form {
      /* display: none; */
    }

    body.login.isMobile > #content > .widget-logout-wrapper > .prompt-wrapper > main > section > div > div > div > .ulp-alternate-action,
    body.login-id.isMobile > #content > .widget-logout-wrapper > .prompt-wrapper > main > section > div > div > div > .ulp-alternate-action,
    body.login-password.isMobile > #content > .widget-logout-wrapper > .prompt-wrapper > main > section > div > div > div > .ulp-alternate-action {
      display: none;
    }

    body.login.isMobile > #content > .widget-logout-wrapper > .prompt-wrapper > main > section > div > div > div > div:not(:last-child) {
      display: none;
    }

    body.login.isMobileSplashScreen,
    body.login-id.isMobileSplashScreen{
      background: white !important;
      height: 100vh !important;
      padding: 16px;
    }


    body.login.isMobileSplashScreen>.header,
    body.login-id.isMobileSplashScreen>.header {
      padding: 0;
    }

    body.login.isMobileSplashScreen>.header>#language-select {
      /* display: none; */
    }

    body.login.isMobileSplashScreen>#content,
    body.login-id.isMobileSplashScreen>#content {
      display: none;
    }

    body.login.isMobileSplashScreen>.footer,
    body.login-id.isMobileSplashScreen>.footer {
      display: none;
    }

    body.login.isMobileSplashScreen>#overlay,
    body.login-id.isMobileSplashScreen>#overlay {
      display: block;
    }

    body.login.isMobileSplashScreen>#overlay.enabled>.container,
    body.login-id.isMobileSplashScreen>#overlay.enabled>.container {
      background: #F2F7FF;
      border-radius: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: 'Matter';
      text-align: center;
    }

    body.login.isMobileSplashScreen>#overlay.enabled>.container>.message,
    body.login-id.isMobileSplashScreen>#overlay.enabled>.container>.message {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 48px 16px;
      gap: 24px;
      color: #0D122C;
      font-size: 30px;
      line-height: 36px;
      font-weight: 600;
    }

    body.login.isMobileSplashScreen>#overlay.enabled>.container>.message>div>.company-name,
    body.login-id.isMobileSplashScreen>#overlay.enabled>.container>.message>div>.company-name {
      color: #4B9BE8;
    }

    .get-our-app {
      font-family: 'Matter';
      font-size: 14px;
      font-weight: 600;
      line-height: 16px;
      letter-spacing: 1.5px;
      text-align: center;
    }

    .store {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .store > img {
      height: 40px;
    }


    .back-to-home-wrapper {
      display: flex;
      justify-content: center;
    }

    #back-to-home {
      margin-top: 44px;
      font-family: 'Matter';
      color: #576E94;
      background-color: transparent;
      border: none;
      outline: none;
      padding: 8px 20px;
    }

    #back-to-home:hover {
      background-color: hsla(218, 36%, 96%, 1);
    }

    .logout-button {
      font-size:14px;
      font-weight: 600;
      color:#6A6E81;
      font-family: 'Matter';
    }

    .widget-logout-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      padding-top:36px;
      text-align: center;
      line-height: initial;
    }

    .prompt-wrapper {
      padding-bottom:36px;
      padding-top:36px;
    }

    .signing-in-as-email {
      font-weight: 600;
    }



  </style>
  <title>
    {% if application.name == 'sleekflow-client-powerflow-app' %} Log in |
    PowerFlow {% elsif application.name == 'Pulumi' %} Log in | Pulumi {%
    elsif application.name == 'Krakend Test' %} Log in | Krakend Test {% elsif
    application.name == 'API Explorer Application' %} Log in | API Explorer
    Application {% else %} Log in | SleekFlow {% endif %}
  </title>
  <link
    rel="shortcut icon"
    type="image/png"
    href="https://v1.sleekflow.io/favicon.ico"
  />
  <link rel="stylesheet" href="https://fonts.bunny.net/css?family=Inter">
</head>
<body class=" _use-custom-prompt-logo {{prompt.name}}">
{% assign normalised_locale = locale %}
{% if locale == 'zh-TW' %}
{% assign normalised_locale = 'zh-HK' %}
{% endif %}
{% assign marketingPageUrl = 'https://sleekflow.io/' %}
<div class="header">
  <a href="{{marketingPageUrl | append: normalised_locale | downcase}}" target="_blank">
    <img src="https://v1.sleekflow.io/static/media/Black_logo.2f034189.svg" />
  </a>
  <div id="language-select">
      <span id="selected-language-label">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M8.00001 1.33325C9.66753 3.15882 10.6152 5.52794 10.6667 7.99992C10.6152 10.4719 9.66753 12.841 8.00001 14.6666M8.00001 1.33325C6.33249 3.15882 5.38484 5.52794 5.33334 7.99992C5.38484 10.4719 6.33249 12.841 8.00001 14.6666M8.00001 1.33325C4.31811 1.33325 1.33334 4.31802 1.33334 7.99992C1.33334 11.6818 4.31811 14.6666 8.00001 14.6666M8.00001 1.33325C11.6819 1.33325 14.6667 4.31802 14.6667 7.99992C14.6667 11.6818 11.6819 14.6666 8.00001 14.6666M1.66669 5.99992H14.3334M1.66668 9.99992H14.3333"
            stroke="#576E93" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
        <span>
          {% if locale == 'en' %}
          English
          {% endif %}
          {% if locale == 'zh-HK' or locale == 'zh-TW' %}
          繁體中文
          {% endif %}
          {% if locale == 'zh-CN' %}
          简体中文
          {% endif %}
          {% if locale == 'id' %}
          Bahasa Indonesia
          {% endif %}
          {% if locale == 'pt' %}
          Português (BR)
          {% endif %}
          {% if locale == 'de' %}
          Deutsch
          {% endif %}
          {% if locale == 'it' %}
          Italiano
          {% endif %}
        </span>
        <div id="language-select-arrow" class="arrow down"></div>
      </span>
    <div id="language-dropdown" class="{{ locale }}">
      <div id="en" value="en">
        <div>
          English
        </div>
        {% if locale == 'en' %}
        <svg class="icon-check-circle" width="16" height="16" viewBox="0 0 16 16">
          <path
            d="M8 1.973c-3.328 0-6.027 2.698-6.027 6.027s2.698 6.027 6.027 6.027c3.328 0 6.027-2.698 6.027-6.027s-2.698-6.027-6.027-6.027zM0.693 8c0-4.035 3.271-7.307 7.307-7.307s7.307 3.271 7.307 7.307-3.271 7.307-7.307 7.307c-4.035 0-7.307-3.271-7.307-7.307zM11.453 5.547c0.25 0.25 0.25 0.655 0 0.905l-4 4c-0.25 0.25-0.655 0.25-0.905 0l-2-2c-0.25-0.25-0.25-0.655-0-0.905s0.655-0.25 0.905-0l1.547 1.547 3.547-3.547c0.25-0.25 0.655-0.25 0.905 0z">
          </path>
        </svg>
        {% endif %}
      </div>
      <div id="zh-HK" value="zh-HK">
        <div>
          繁體中文
        </div>
        {% if locale == 'zh-HK' or locale == 'zh-TW' %}
        <svg class="icon-check-circle" width="16" height="16" viewBox="0 0 16 16">
          <path
            d="M8 1.973c-3.328 0-6.027 2.698-6.027 6.027s2.698 6.027 6.027 6.027c3.328 0 6.027-2.698 6.027-6.027s-2.698-6.027-6.027-6.027zM0.693 8c0-4.035 3.271-7.307 7.307-7.307s7.307 3.271 7.307 7.307-3.271 7.307-7.307 7.307c-4.035 0-7.307-3.271-7.307-7.307zM11.453 5.547c0.25 0.25 0.25 0.655 0 0.905l-4 4c-0.25 0.25-0.655 0.25-0.905 0l-2-2c-0.25-0.25-0.25-0.655-0-0.905s0.655-0.25 0.905-0l1.547 1.547 3.547-3.547c0.25-0.25 0.655-0.25 0.905 0z">
          </path>
        </svg>
        {% endif %}
      </div>
      <div id="zh-CN" value="zh-CN-">
        <div>
          简体中文
        </div>
        {% if locale == 'zh-CN' %}
        <svg class="icon-check-circle" width="16" height="16" viewBox="0 0 16 16">
          <path
            d="M8 1.973c-3.328 0-6.027 2.698-6.027 6.027s2.698 6.027 6.027 6.027c3.328 0 6.027-2.698 6.027-6.027s-2.698-6.027-6.027-6.027zM0.693 8c0-4.035 3.271-7.307 7.307-7.307s7.307 3.271 7.307 7.307-3.271 7.307-7.307 7.307c-4.035 0-7.307-3.271-7.307-7.307zM11.453 5.547c0.25 0.25 0.25 0.655 0 0.905l-4 4c-0.25 0.25-0.655 0.25-0.905 0l-2-2c-0.25-0.25-0.25-0.655-0-0.905s0.655-0.25 0.905-0l1.547 1.547 3.547-3.547c0.25-0.25 0.655-0.25 0.905 0z">
          </path>
        </svg>
        {% endif %}
      </div>
      <div id="id" value="zh-CN-">
        <div>
          Bahasa Indonesia
        </div>
        {% if locale == 'id' %}
        <svg class="icon-check-circle" width="16" height="16" viewBox="0 0 16 16">
          <path
            d="M8 1.973c-3.328 0-6.027 2.698-6.027 6.027s2.698 6.027 6.027 6.027c3.328 0 6.027-2.698 6.027-6.027s-2.698-6.027-6.027-6.027zM0.693 8c0-4.035 3.271-7.307 7.307-7.307s7.307 3.271 7.307 7.307-3.271 7.307-7.307 7.307c-4.035 0-7.307-3.271-7.307-7.307zM11.453 5.547c0.25 0.25 0.25 0.655 0 0.905l-4 4c-0.25 0.25-0.655 0.25-0.905 0l-2-2c-0.25-0.25-0.25-0.655-0-0.905s0.655-0.25 0.905-0l1.547 1.547 3.547-3.547c0.25-0.25 0.655-0.25 0.905 0z">
          </path>
        </svg>
        {% endif %}
      </div>
      <div id="pt-BR" value="zh-CN-">
        <div>
          Português (BR)
        </div>
        {% if locale == 'pt' %}
        <svg class="icon-check-circle" width="16" height="16" viewBox="0 0 16 16">
          <path
            d="M8 1.973c-3.328 0-6.027 2.698-6.027 6.027s2.698 6.027 6.027 6.027c3.328 0 6.027-2.698 6.027-6.027s-2.698-6.027-6.027-6.027zM0.693 8c0-4.035 3.271-7.307 7.307-7.307s7.307 3.271 7.307 7.307-3.271 7.307-7.307 7.307c-4.035 0-7.307-3.271-7.307-7.307zM11.453 5.547c0.25 0.25 0.25 0.655 0 0.905l-4 4c-0.25 0.25-0.655 0.25-0.905 0l-2-2c-0.25-0.25-0.25-0.655-0-0.905s0.655-0.25 0.905-0l1.547 1.547 3.547-3.547c0.25-0.25 0.655-0.25 0.905 0z">
          </path>
        </svg>
        {% endif %}
      </div>
      <div id="de" value="zh-CN-">
        <div>
          Deutsch
        </div>
        {% if locale == 'de' %}
        <svg class="icon-check-circle" width="16" height="16" viewBox="0 0 16 16">
          <path
            d="M8 1.973c-3.328 0-6.027 2.698-6.027 6.027s2.698 6.027 6.027 6.027c3.328 0 6.027-2.698 6.027-6.027s-2.698-6.027-6.027-6.027zM0.693 8c0-4.035 3.271-7.307 7.307-7.307s7.307 3.271 7.307 7.307-3.271 7.307-7.307 7.307c-4.035 0-7.307-3.271-7.307-7.307zM11.453 5.547c0.25 0.25 0.25 0.655 0 0.905l-4 4c-0.25 0.25-0.655 0.25-0.905 0l-2-2c-0.25-0.25-0.25-0.655-0-0.905s0.655-0.25 0.905-0l1.547 1.547 3.547-3.547c0.25-0.25 0.655-0.25 0.905 0z">
          </path>
        </svg>
        {% endif %}
      </div>
      <div id="it" value="zh-CN-">
        <div>
          Italiano
        </div>
        {% if locale == 'it' %}
        <svg class="icon-check-circle" width="16" height="16" viewBox="0 0 16 16">
          <path
            d="M8 1.973c-3.328 0-6.027 2.698-6.027 6.027s2.698 6.027 6.027 6.027c3.328 0 6.027-2.698 6.027-6.027s-2.698-6.027-6.027-6.027zM0.693 8c0-4.035 3.271-7.307 7.307-7.307s7.307 3.271 7.307 7.307-3.271 7.307-7.307 7.307c-4.035 0-7.307-3.271-7.307-7.307zM11.453 5.547c0.25 0.25 0.25 0.655 0 0.905l-4 4c-0.25 0.25-0.655 0.25-0.905 0l-2-2c-0.25-0.25-0.25-0.655-0-0.905s0.655-0.25 0.905-0l1.547 1.547 3.547-3.547c0.25-0.25 0.655-0.25 0.905 0z">
          </path>
        </svg>
        {% endif %}
      </div>
    </div>
  </div>
</div>
<div id="overlay">
  <div class="container">
    <div class="message">
      <div>
        {% if locale == 'en' %}
        Use desktop or mobile app to have better experience with
        <span class="company-name"> SleekFlow</span>
        {% elsif locale == 'zh-TW' %}
        於 <span class="company-name">SleekFlow</span> 桌面版及手機應用程式享受社交商務嶄新體驗
        {% else %}
        于 <span class="company-name">SleekFlow</span> 桌面版及手机应用程式享受社交商务崭新体验
        {% endif %}
      </div>
      <div class="get-our-app">
        {% if locale == 'en' %}
        Download our app
        {% elsif locale == 'zh-TW' %}
        下載手機應用程式
        {% else %}
        下载手机应用程式
        {% endif %}
      </div>
      <a class="store" href="https://apps.apple.com/app/sleekflow-social-commerce/id1495751100?l=en" target="_blank">
        {% assign appStoreImageUrl = '' %}
        {% if locale == 'en' %}
        {% assign appStoreImageUrl = 'https://images.contentful.com/tu2uwzoyozk8/zRqIO8xfw8UhlgpmmVoG8/c2a29b1734ff523c7386d2efaa62441c/appstore-en.png' %}
        {% elsif locale == 'zh-TW' %}
        {% assign appStoreImageUrl = 'https://images.contentful.com/tu2uwzoyozk8/6wZrsDlKpYtm8XkOm2UJzc/6e18c35290ba66253bf41460b4534d17/appstore-zh-TW.png' %}
        {% else %}
        {% assign appStoreImageUrl = 'https://images.contentful.com/tu2uwzoyozk8/6DJ61VunkZXx4jgT7ecyjR/fea9f0dd5062f94cd8dc60e1cfb6bcfc/appstore-zh-CN.png' %}
        {% endif %}
        <img src="{{appStoreImageUrl}}" alt="app-store" />
      </a>
      <a class="store" href="https://play.google.com/store/apps/details?id=io.sleekflow.sleekflow" target="_blank">
        {% assign googlePlayImageUrl = '' %}
        {% if locale == 'en' %}
        {% assign googlePlayImageUrl = 'https://images.contentful.com/tu2uwzoyozk8/7geLzeDvUq017aKDBWbzoT/ecf31bf8851e068f1ddb496657b0626f/googleplay-en.png' %}
        {% endif %}
        {% if locale == 'zh-TW' %}
        {% assign googlePlayImageUrl = 'https://images.contentful.com/tu2uwzoyozk8/5nSmzjrqEqw0NqjBY405b9/d5301989e0e033101a68fb566d21bdfb/googleplay-zh-TW.png' %}
        {% endif %}
        {% if locale == 'zh-CN' %}
        {% assign googlePlayImageUrl = 'https://images.contentful.com/tu2uwzoyozk8/4Ai5BgYyQIUAIMplv99ok3/bc53a1b34d35da9b7cc554ea4308a8c1/googleplay-zh-CN.png' %}
        {% endif %}
        <img src="{{googlePlayImageUrl}}" alt="app-store" />
      </a>
    </div>
  </div>
  <!-- <div class="back-to-home-wrapper">
    <a href="{{marketingPageUrl | append: normalised_locale | downcase}}" target="_blank">
      <button id="back-to-home">
        {% if locale == 'en' %}
          Back to Home
        {% elsif locale == 'zh-TW' %}
          返回主頁
        {% elsif locale == 'zh-CN' %}
          返回主页
        {% elsif locale == 'pt' %}
          Voltar à Página Inicial
        {% elsif locale == 'id' %}
          Kembali ke Beranda
        {% endif %}
      </button>
    </a>
  </div> -->
  <div class="back-to-home-wrapper">
    <button id="back-to-home">
      {% if locale == 'en' %}
      Continue
      {% elsif locale == 'zh-TW' %}
      繼續
      {% elsif locale == 'zh-CN' %}
      继续
      {% elsif locale == 'pt' %}
      Continuar
      {% elsif locale == 'id' %}
      Melanjutkan
      {% endif %}
    </button>
  </div>
</div>
<div id="content">
  {% if prompt.name == "login" or prompt.name == "signup" or prompt.name == "login-id" or prompt.name == "signup-id" %}
  <div class="section marketing-section">
    <div class="centering">
      {% assign inboxImageUrl = '' %}
      {% assign integrationImageUrl = '' %}
      {% assign templateImageUrl = '' %}
      {% assign paymentImageUrl = '' %}
      {% if locale == 'en' %}
      {% assign inboxImageUrl = 'https://images.contentful.com/tu2uwzoyozk8/30CtIivZvNykQy373FZMou/e72466dd90fad57482cdd10eb76a6f37/inbox-en.png' %}
      {% assign integrationImageUrl = 'https://images.contentful.com/tu2uwzoyozk8/1W0zPNij6ZD3DTAdOrsBJw/11a339c4709679c8bc576ebff572ab5d/integrations-en.png' %}
      {% assign templateImageUrl = 'https://images.contentful.com/tu2uwzoyozk8/4Hapq8xLYKZTceBTDaWRTW/56cc4a7f3b53959acf3630cc456362fa/template-en.png' %}
      {% assign paymentImageUrl = 'https://images.contentful.com/tu2uwzoyozk8/1wVo1uQuFWd99R7tar8zEI/fef6daec9cdb7acdf7d3ef359c6f7b6e/payment-en.png' %}
      {% elsif locale == 'zh-TW' %}
      {% assign inboxImageUrl = 'https://images.contentful.com/tu2uwzoyozk8/4x9P0plhF8ktVP2O4syfUX/2d04d1ce985544bb9432e39d0ebd54ce/inbox-zh-TW.png' %}
      {% assign integrationImageUrl = 'https://images.contentful.com/tu2uwzoyozk8/7szhwdWOHTj0aU0HRAlKpt/afd84dfa4e4cb3e1562f68ad8f25aa15/integrations-zh-TW.png' %}
      {% assign templateImageUrl = 'https://images.contentful.com/tu2uwzoyozk8/22D8hQjO3Vhnwcul295Qx0/08842895b39c208a7279859af6a2935f/template-zh-TW.png' %}
      {% assign paymentImageUrl = 'https://images.contentful.com/tu2uwzoyozk8/fjkk6fhskTGw1KcNl3MVj/1b6b9a6f69b77ace42237fdcf4e230d2/payment-zh-TW.png' %}
      {% elsif locale == 'id' %}
      {% assign inboxImageUrl = 'https://res.cloudinary.com/dqhdmvyeh/image/upload/v1688981967/Log%20in%20Page/id/homepage-shared-team-inbox-id-id.png' %}
      {% assign integrationImageUrl = 'https://res.cloudinary.com/dqhdmvyeh/image/upload/v1688981967/Log%20in%20Page/id/homepage-integrations-id-id.png' %}
      {% assign templateImageUrl = 'https://res.cloudinary.com/dqhdmvyeh/image/upload/v1688981967/Log%20in%20Page/id/homepage-broadcast-campaign-id-id.png' %}
      {% assign paymentImageUrl = 'https://res.cloudinary.com/dqhdmvyeh/image/upload/v1688981967/Log%20in%20Page/id/homepage-in-chat-payment-links-id-id.png' %}
      {% elsif locale == 'pt' %}
      {% assign inboxImageUrl = 'https://res.cloudinary.com/dqhdmvyeh/image/upload/v1688981973/Log%20in%20Page/pt-br/homepage-shared-team-inbox-pt-br.png' %}
      {% assign integrationImageUrl = 'https://res.cloudinary.com/dqhdmvyeh/image/upload/v1688981973/Log%20in%20Page/pt-br/homepage-integrations-pt-br.png' %}
      {% assign templateImageUrl = 'https://res.cloudinary.com/dqhdmvyeh/image/upload/v1688981973/Log%20in%20Page/pt-br/homepage-broadcast-campaign-pt-br.png' %}
      {% assign paymentImageUrl = 'https://res.cloudinary.com/dqhdmvyeh/image/upload/v1688981974/Log%20in%20Page/pt-br/homepage-in-chat-payment-links-pt-br.png' %}
      {% else %}
      {% assign inboxImageUrl = 'https://images.contentful.com/tu2uwzoyozk8/5B8LMCUEcmjbXrIgqF1ndm/addfb380f230a4e1893fecfd75bc9d4c/inbox-zh-CN.png' %}
      {% assign integrationImageUrl = 'https://images.contentful.com/tu2uwzoyozk8/5qmeXXBDI0LnxteKuOZwZP/13ae893e05d3ba6101e5b061dd2c11d0/integrations-zh-CN.png' %}
      {% assign templateImageUrl = 'https://images.contentful.com/tu2uwzoyozk8/7jUBNvciuhC03B1Rgh2sEY/9165e372b5a72b6977b6ac0aa8e95d77/template-zh-CN.png' %}
      {% assign paymentImageUrl = 'https://images.contentful.com/tu2uwzoyozk8/3dFTicF6OsnmIbfh1dQ5EU/88e5d885984239fbb2ed7b5818066932/payment-zh-CN.png' %}
      {% endif %}
      <div id="carousel" class="{{prompt.name}}">
        <i id="back-button" class="arrow left"></i>
        <div id="slide-container">
          <div class="slide" data-slideIndex="0">
            {% if prompt.name == "login" or prompt.name == "login-id" %}
            <div class="slide-image-wrapper">
              <img src="{{inboxImageUrl}}" />
            </div>
            <h1 class="title">
              {% if locale == 'en' %}
              Collaborate on key conversations
              {% elsif locale == 'zh-TW' %}
              多人協作毫不遺漏
              {% elsif locale == 'zh-CN' %}
              多人协作毫不遗漏
              {% elsif locale == 'pt' %}
              Colabore em conversas decisivas
              {% elsif locale == 'id' %}
              Balas pesan dari berbagai channel lewat satu platform
              {% elsif locale == 'de' %}
              Arbeiten Sie an wichtigen Gesprächen zusammen
              {% elsif locale == 'it' %}
              Collabora alle conversazioni chiave
              {% endif %}
            </h1>
            <div class="description">
              {% if locale == 'en' %}
              Combine all your messaging channels into one and work together efficiently across teams through automatic chat assignment and internal notes.
              {% elsif locale == 'zh-TW' %}
              獨家功能可多人同時管理對話，啟動自動委派，縮短分派客戶工序。
              {% elsif locale == 'zh-CN' %}
              独家功能可多人同时管理对话，启动自动委派，缩短分派客户工序。
              {% elsif locale == 'pt' %}
              Reúna todos os seus canais de mensagem em um só. Aumente a eficiência do trabalho em equipe por meio de atribuições automáticas para chats e anotações internas.
              {% elsif locale == 'id' %}
              Gabungkan semua pesan di channel penjualan kamu dalam satu aplikasi. Pekerjaan dan komunikasi antartim jadi lebih efisien dengan penugasan chat otomatis dan pembuatan catatan internal.
              {% elsif locale == 'de' %}
              Kombinieren Sie alle Ihre Messaging-Kanäle in einem und arbeiten Sie effizient teamübergreifend durch automatische Chat-Zuweisung und interne Notizen zusammen.
              {% elsif locale == 'it' %}
              Combina tutti i tuoi canali di messaggistica in uno e collabora in modo efficiente con i team tramite assegnazione automatica delle chat e note interne.
              {% endif %}
            </div>
            {% endif %}
            {% if prompt.name == "signup" or prompt.name == "signup-id" %}
            <div class="testimonial">
              <svg width="56" height="48" viewBox="0 0 56 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M53.3291 0L55.68 5.125C50.8956 7.125 47.5136 9.33333 45.5339 11.75C43.5541 14.1667 42.5643 16.7917 42.5643 19.625C42.5643 21.375 42.8942 22.8333 43.5541 24C44.2965 25.1667 45.2039 26.2083 46.2763 27.125C47.3486 27.9583 48.421 28.8333 49.4933 29.75C50.5657 30.5833 51.4318 31.625 52.0917 32.875C52.8341 34.125 53.2053 35.75 53.2053 37.75C53.2053 40.5833 52.133 43 49.9883 45C47.926 47 45.2452 48 41.9456 48C39.9659 48 37.9861 47.4583 36.0064 46.375C34.1092 45.2917 32.5006 43.5833 31.1808 41.25C29.9435 38.8333 29.3248 35.6667 29.3248 31.75C29.3248 27.25 30.1909 23 31.9232 19C33.738 15 36.4188 11.375 39.9659 8.125C43.5129 4.875 47.9673 2.16667 53.3291 0ZM24.0043 0L26.3552 5.125C21.5708 7.125 18.1888 9.33333 16.2091 11.75C14.2293 14.1667 13.2395 16.7917 13.2395 19.625C13.2395 21.375 13.5694 22.8333 14.2293 24C14.9717 25.1667 15.8791 26.2083 16.9515 27.125C18.0238 27.9583 19.0962 28.8333 20.1685 29.75C21.2409 30.5833 22.107 31.625 22.7669 32.875C23.5093 34.125 23.8805 35.75 23.8805 37.75C23.8805 40.5833 22.8082 43 20.6635 45C18.6012 47 15.9204 48 12.6208 48C10.6411 48 8.66133 47.4583 6.6816 46.375C4.78436 45.2917 3.17582 43.5833 1.856 41.25C0.618667 38.8333 0 35.6667 0 31.75C0 27.25 0.866133 23 2.5984 19C4.41316 15 7.09404 11.375 10.6411 8.125C14.1881 4.875 18.6425 2.16667 24.0043 0Z"
                  fill="#4B9BE8" />
              </svg>
              <div class="text">
                {% if locale == 'en' %}
                With a deeper understanding of our client's needs since using SleekFlow, we have improved our retargeting strategies, resulting in 21% more box requests.
                {% elsif locale == 'zh-TW' %}
                自從使用 SleekFlow 後，我們對客戶需求有了更深入的了解，從而改善了重新定位策略，令客戶請求數量增加了 21%。
                {% elsif locale == 'zh-CN' %}
                自从使用 SleekFlow 后，我们对客户需求有了更深入的了解，从而改善了重新定位策略，令客户请求数量增加了 21%。
                {% elsif locale == 'pt' %}
                Com uma compreensão mais aprofundada das necessidades dos nossos clientes desde que começamos a usar o SleekFlow, melhoramos nossas estratégias de redirecionamento, resultando em 21% a mais de solicitações específicas.
                {% elsif locale == 'id' %}
                Semenjak menggunakan SleekFlow kami lebih dapat memahami kebutuha klien, kami juga telah meningkatkan strategi retargeting dan menghasilkan 21% lebih banyak rikues pelanggan.
                {% elsif locale == 'de' %}
                Mit einem tieferen Verständnis für die Bedürfnisse unserer Kunden seit der Nutzung von SleekFlow haben wir unsere Retargeting-Strategien verbessert, was zu 21% mehr Box-Anfragen geführt hat.
                {% elsif locale == 'it' %}
                Con una comprensione più approfondita delle esigenze dei nostri clienti da quando utilizziamo SleekFlow, abbiamo migliorato le nostre strategie di retargeting, ottenendo il 21% in più di richieste di box.
                {% endif %}
              </div>
              <div class="source">Nadia Benjelloun</div>
              <div class="source-position">
                {% if locale == 'en' %}
                RETENTION LEAD
                {% elsif locale == 'zh-TW' %}
                客戶維繫主管
                {% elsif locale == 'zh-CN' %}
                客户维系主管
                {% elsif locale == 'pt' %}
                Responsável pela Retenção
                {% elsif locale == 'id' %}
                RETENTION LEAD
                {% elsif locale == 'de' %}
                Leiter für Kundenbindung
                {% elsif locale == 'it' %}
                Responsabile della Fidelizzazione
                {% else %}
                RETENTION LEAD
                {% endif %}
              </div>
              <div class="logo-wrapper">
                <img
                  src="https://images.ctfassets.net/tu2uwzoyozk8/AhVD98je67OFJZ2tKXFsK/4906182ec7038b8479f10b4d4adb749b/wear_that.png"
                  alt="Wear That" />
              </div>
            </div>
            {% endif %}
          </div>

          <div class="slide" data-slideIndex="1">
            {% if prompt.name == "login" or prompt.name == "login-id" %}
            <div class="slide-image-wrapper">
              <img src="{{integrationImageUrl}}" />
            </div>
            <h1 class="title">
              {% if locale == 'en' %}
              Scale with powerful integrations
              {% elsif locale == 'zh-TW' %}
              輕鬆連接過千平台系統
              無需編程
              {% elsif locale == 'zh-CN' %}
              轻松连接过千平台系统
              无需编程
              {% elsif locale == 'pt' %}
              Expanda seus negócios com integrações estratégicas
              {% elsif locale == 'id' %}
              Fitur Integrasi untuk tingkatkan bisnis kamu
              {% elsif locale == 'de' %}
              Skalieren Sie mit leistungsstarken Integrationen
              {% elsif locale == 'it' %}
              Scala con potenti integrazioni
              {% endif %}
            </h1>
            <div class="description">
              {% if locale == 'en' %}
              Merge data and functions from Salesforce, HubSpot, and more. Power up workflows through enhanced re-marketing, targeted promotions, and view customer data in real-time.
              {% elsif locale == 'zh-TW' %}
              進一步提升你的客戶管理工序，一次過連接多個實用軟件，包括 Salesforce、HubSpot 等大型服務平台，整合所有客戶資料，簡單進行客服或行銷活動，例如連接網上預約系統及自動 WhatsApp 預約確認訊息等。
              {% elsif locale == 'zh-CN' %}
              进一步提升你的客户管理工序，一次过连接多个实用软件，包括 Salesforce、HubSpot 等大型服务平台，整合所有客户资料，简单进行客服或行销活动，例如连接网上预约系统及自动 WhatsApp 预约确认讯息等。
              {% elsif locale == 'pt' %}
              Combine dados e funcionalidades do Salesforce, HubSpot e outros serviços. Reforce fluxos de trabalho aperfeiçoando o re-marketing, criando promoções direcionadas e observando dados sobre consumidores em tempo real.
              {% elsif locale == 'id' %}
              Gabungkan data dan fungsi dari Salesforce, Hubspot dan lainnya. Tingkatkan penjualan dan keuntungan lewat promo dengan target pemasaran yang tepat serta pantau data customer secara real-time.
              {% elsif locale == 'de' %}
              Fügen Sie Daten und Funktionen aus Salesforce, HubSpot und mehr zusammen. Optimieren Sie Workflows durch verbessertes Remarketing, gezielte Werbeaktionen und zeigen Sie Kundendaten in Echtzeit an.
              {% elsif locale == 'it' %}
              Unisci dati e funzioni da Salesforce, HubSpot e altro. Potenzia i flussi di lavoro tramite remarketing avanzato, promozioni mirate e visualizza i dati dei clienti in tempo reale.
              {% endif %}
            </div>
            {% elsif prompt.name == "signup" or prompt.name == "signup-id" %}
            <div class="testimonial">
              <svg width="56" height="48" viewBox="0 0 56 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M53.3291 0L55.68 5.125C50.8956 7.125 47.5136 9.33333 45.5339 11.75C43.5541 14.1667 42.5643 16.7917 42.5643 19.625C42.5643 21.375 42.8942 22.8333 43.5541 24C44.2965 25.1667 45.2039 26.2083 46.2763 27.125C47.3486 27.9583 48.421 28.8333 49.4933 29.75C50.5657 30.5833 51.4318 31.625 52.0917 32.875C52.8341 34.125 53.2053 35.75 53.2053 37.75C53.2053 40.5833 52.133 43 49.9883 45C47.926 47 45.2452 48 41.9456 48C39.9659 48 37.9861 47.4583 36.0064 46.375C34.1092 45.2917 32.5006 43.5833 31.1808 41.25C29.9435 38.8333 29.3248 35.6667 29.3248 31.75C29.3248 27.25 30.1909 23 31.9232 19C33.738 15 36.4188 11.375 39.9659 8.125C43.5129 4.875 47.9673 2.16667 53.3291 0ZM24.0043 0L26.3552 5.125C21.5708 7.125 18.1888 9.33333 16.2091 11.75C14.2293 14.1667 13.2395 16.7917 13.2395 19.625C13.2395 21.375 13.5694 22.8333 14.2293 24C14.9717 25.1667 15.8791 26.2083 16.9515 27.125C18.0238 27.9583 19.0962 28.8333 20.1685 29.75C21.2409 30.5833 22.107 31.625 22.7669 32.875C23.5093 34.125 23.8805 35.75 23.8805 37.75C23.8805 40.5833 22.8082 43 20.6635 45C18.6012 47 15.9204 48 12.6208 48C10.6411 48 8.66133 47.4583 6.6816 46.375C4.78436 45.2917 3.17582 43.5833 1.856 41.25C0.618667 38.8333 0 35.6667 0 31.75C0 27.25 0.866133 23 2.5984 19C4.41316 15 7.09404 11.375 10.6411 8.125C14.1881 4.875 18.6425 2.16667 24.0043 0Z"
                  fill="#4B9BE8" />
              </svg>
              <div class="text">
                {% if locale == 'en'%}
                HKBN's promotional strategy makes great use of SleekFlow for targeted WhatsApp marketing. A great example is how we got 35% of inactive users—who were unreachable via the service hotline—to renew their contracts in a single broadcast campaign.
                {% elsif locale == 'zh-TW' %}
                HKBN 的推廣策略能夠充分利用 SleekFlow 來進行精準的 WhatsApp 行銷。 一個很好的例子是，我們在一次廣播活動中成功獲得了 35% 的非活躍用戶續約，這些用戶是無法透過服務熱線聯絡到的。
                {% elsif locale == 'zh-CN' %}
                HKBN 的推广策略能够充分利用 SleekFlow 来进行精准的 WhatsApp 行销。 一个很好的例子是，我们在一次群发信息活动中成功获得了 35% 的非活跃用户续约，这些用户是无法透过服务热线联络到的。
                {% elsif locale == 'pt' %}
                A estratégia promocional da HKBN utiliza bastante o SleekFlow para ações de marketing direcionadas no WhatsApp. Um ótimo exemplo disso é como conseguimos que 35% dos usuários inativos — inatingíveis por meio da linha direta de atendimento — renovassem seus contratos com uma única campanha de transmissão.
                {% elsif locale == 'id' %}
                Strategi promosi HKBN berhasil mengoptimalkan campaign WhatsApp targeted dengan SleekFlow. Contohnya, kami membuat 35% pengguna yang tidak aktif dan tidak dapat dijangkau melalui hotline layanan untuk memperbarui kontrak mereka dalam satu kali broadcast.
                {% elsif locale == 'de' %}
                Die Werbestrategie von HKBN nutzt SleekFlow hervorragend für gezieltes WhatsApp-Marketing. Ein tolles Beispiel ist, wie wir 35 % der inaktiven Nutzer—die über die Service-Hotline nicht erreichbar waren—dazu gebracht haben, ihre Verträge in einer einzigen Broadcast-Kampagne zu verlängern.
                {% elsif locale == 'it' %}
                La strategia promozionale di HKBN sfrutta al meglio SleekFlow per il marketing mirato su WhatsApp. Un ottimo esempio è il modo in cui siamo riusciti a far rinnovare i contratti al 35% degli utenti inattivi—irraggiungibili tramite la linea di assistenza—con una singola campagna di broadcast.
                {% endif %}
              </div>
              <div class="source">
                Kenneth She
              </div>
              <div class="source-position">
                {% if locale == 'en'%}
                CHIEF TRANSFORMATION OFFICER
                {% elsif locale == 'zh-TW' %}
                首席轉型總監
                {% elsif locale == 'zh-CN' %}
                首席转型总监
                {% elsif locale == 'pt' %}
                Diretor de Transformação
                {% elsif locale == 'id' %}
                Chief Transformation Officer (CTO)
                {% elsif locale == 'de' %}
                Chief Transformation Officer (CTO)
                {% elsif locale == 'it' %}
                Direttore della Trasformazione
                {% else %}
                CHIEF TRANSFORMATION OFFICER
                {% endif %}
              </div>
              <div class="logo-wrapper">
                <img
                  src="https://images.ctfassets.net/tu2uwzoyozk8/3zkyuMpArfs2I3EIR3Lsj9/70870a52bf8dd2137c053494c0e5ec52/HKBN_official_logo_1.png"
                  alt="HKBN" />
              </div>
            </div>
            {% endif %}
          </div>

          <div class="slide" data-slideIndex="2">
            {% if prompt.name == "login" or prompt.name == "login-id" %}
            <div class="slide-image-wrapper">
              <img src="{{templateImageUrl}}" />
            </div>
            <h1 class="title">
              {% if locale == 'en' %}
              Broadcast messages with WhatsApp Business API
              {% elsif locale == 'zh-TW' %}
              WhatsApp Business API 營銷群發
              {% elsif locale == 'zh-CN' %}
              WhatsApp Business API 营销群发
              {% elsif locale == 'pt' %}
              Envie mensagens em massa por meio da API do WhatsApp Business
              {% elsif locale == 'id' %}
              Kirim broadcast dengan WhatsApp Business API
              {% elsif locale == 'de' %}
              Senden Sie Nachrichten mit der WhatsApp Business API
              {% elsif locale == 'it' %}
              Trasmetti messaggi con l'API WhatsApp Business
              {% endif %}
            </h1>
            <div class="description">
              {% if locale == 'en' %}
              Blast personalized, promotional WhatsApp messages to customers. Automatically segment, filter, and label clients based on purchase behavior.
              {% elsif locale == 'zh-TW' %}
              一次性推廣並群發個人化宣傳訊息給儲存於資料庫裡逾256位的 WhatsApp 用戶。透過可靠的商業檔案與顧客溝通。自動分類、標籤顧客以進行更準確行銷，提供即時報告板助你追蹤客戶購買行為及監察 WhatsApp 群發推廣活動成效。
              {% elsif locale == 'zh-CN' %}
              一次性推广并群发个人化宣传讯息给储存于资料库里逾256位的 WhatsApp 用户。自动分类、标签顾客以进行更準确行销，提供即时报告板助你追踪客户购买行为及监察推广活动成效。
              {% elsif locale == 'pt' %}
              Dispare mensagens personalizadas e promocionais para clientes via WhatsApp. Segmente, filtre e classique os clientes automaticamente de acordo com o comportamento de compra.
              {% elsif locale == 'id' %}
              Sebar promo WhatsApp dengan pesan yang sudah dipersonalisasikan. Pesan akan disebarkan dengan segmentasi, filter dan jenis customer yang dibuat otomatis berdasarkan riwayat pembelian.
              {% elsif locale == 'de' %}
              Senden Sie personalisierte, werbende WhatsApp-Nachrichten an Kunden. Segmentieren, filtern und kennzeichnen Sie Kunden automatisch basierend auf ihrem Kaufverhalten.
              {% elsif locale == 'it' %}
              Invia messaggi WhatsApp personalizzati e promozionali ai clienti. Segmenta, filtra ed etichetta automaticamente i clienti in base al comportamento di acquisto.
              {% endif %}
            </div>
            {% elsif prompt.name == "signup" or prompt.name == "signup-id" %}
            <div class="testimonial">
              <svg width="56" height="48" viewBox="0 0 56 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M53.3291 0L55.68 5.125C50.8956 7.125 47.5136 9.33333 45.5339 11.75C43.5541 14.1667 42.5643 16.7917 42.5643 19.625C42.5643 21.375 42.8942 22.8333 43.5541 24C44.2965 25.1667 45.2039 26.2083 46.2763 27.125C47.3486 27.9583 48.421 28.8333 49.4933 29.75C50.5657 30.5833 51.4318 31.625 52.0917 32.875C52.8341 34.125 53.2053 35.75 53.2053 37.75C53.2053 40.5833 52.133 43 49.9883 45C47.926 47 45.2452 48 41.9456 48C39.9659 48 37.9861 47.4583 36.0064 46.375C34.1092 45.2917 32.5006 43.5833 31.1808 41.25C29.9435 38.8333 29.3248 35.6667 29.3248 31.75C29.3248 27.25 30.1909 23 31.9232 19C33.738 15 36.4188 11.375 39.9659 8.125C43.5129 4.875 47.9673 2.16667 53.3291 0ZM24.0043 0L26.3552 5.125C21.5708 7.125 18.1888 9.33333 16.2091 11.75C14.2293 14.1667 13.2395 16.7917 13.2395 19.625C13.2395 21.375 13.5694 22.8333 14.2293 24C14.9717 25.1667 15.8791 26.2083 16.9515 27.125C18.0238 27.9583 19.0962 28.8333 20.1685 29.75C21.2409 30.5833 22.107 31.625 22.7669 32.875C23.5093 34.125 23.8805 35.75 23.8805 37.75C23.8805 40.5833 22.8082 43 20.6635 45C18.6012 47 15.9204 48 12.6208 48C10.6411 48 8.66133 47.4583 6.6816 46.375C4.78436 45.2917 3.17582 43.5833 1.856 41.25C0.618667 38.8333 0 35.6667 0 31.75C0 27.25 0.866133 23 2.5984 19C4.41316 15 7.09404 11.375 10.6411 8.125C14.1881 4.875 18.6425 2.16667 24.0043 0Z"
                  fill="#4B9BE8" />
              </svg>
              <div class="text">
                {% if locale == 'en'%}
                SleekFlow has served a great purpose in helping us curate an omnichannel strategy for multiple markets, with up to 14X ROI achieved on WhatsApp marketing.
                {% elsif locale == 'zh-TW' %}
                SleekFlow 發揮了重要作用，協助我們為多個市場規劃全渠道策略，使 WhatsApp 行銷實現了高達 14 倍的投資回報率。
                {% elsif locale == 'zh-CN' %}
                SleekFlow 发挥了重要作用，协助我们为多个市场规划全渠道策略，使 WhatsApp 行销实现了高达 14 倍的投资回报率。
                {% elsif locale == 'pt' %}
                O SleekFlow teve um papel fundamental ao nos auxiliar na criação de uma estratégia omnichannel para diversos mercados, alcançando um ROI de até 14 vezes no marketing pelo WhatsApp.
                {% elsif locale == 'id' %}
                SleekFlow sangat membantu kami mengkurasi strategi omnichannel untuk market yang beragam hingga mencapai ROI 14X melalui WhatsApp Marketing.
                {% elsif locale == 'de' %}
                SleekFlow hat uns hervorragend dabei unterstützt, eine Omnichannel-Strategie für mehrere Märkte zu entwickeln, mit einem ROI von bis zu 14X im WhatsApp-Marketing.
                {% elsif locale == 'it' %}
                SleekFlow ci è stato di grande aiuto nel curare una strategia omnicanale per diversi mercati, ottenendo fino a 14 volte il ROI con il marketing su WhatsApp.
                {% endif %}
              </div>
              <div class="source">
                Lincoln Thong
              </div>
              <div class="source-position">
                {% if locale == 'en'%}
                BRAND DIRECTOR & CO-FOUNDER
                {% elsif locale == 'zh-TW' %}
                品牌總監兼聯合創辦人
                {% elsif locale == 'zh-CN' %}
                品牌总监兼联合创办人
                {% elsif locale == 'pt' %}
                Diretor de Marca e Co-fundador
                {% elsif locale == 'id' %}
                Brand Director & Co-Founder
                {% elsif locale == 'de' %}
                Brand Director & Mitbegründer
                {% elsif locale == 'it' %}
                Direttore del Brand e Co-Fondatore
                {% else %}
                BRAND DIRECTOR & CO-FOUNDER
                {% endif %}
              </div>
              <div class="logo-wrapper">
                <img
                  src="https://images.ctfassets.net/tu2uwzoyozk8/016e7cypLEFgc6l3idkyo4/2ec8fe4948df7d4d1381ee4a01f1e4e8/Pristine.png"
                  alt="Pristine Aroma" />
              </div>
            </div>
            {% endif %}
          </div>
          <div class="slide" data-slideIndex="3">

            {% if prompt.name == "login" or prompt.name == "login-id" %}
            <div class="slide-image-wrapper">
              <img src="{{paymentImageUrl}}" />
            </div>
            <h1 class="title">
              {% if locale == 'en' %}
              Get paid instantly in the chat
              {% elsif locale == 'zh-TW' %}
              對話中即時收款
              {% elsif locale == 'zh-CN' %}
              对话中即时收款
              {% elsif locale == 'pt' %}
              Receba pagamentos instantâneos via chat
              {% elsif locale == 'id' %}
              Proses pembayar lebih mudah lewat chat
              {% elsif locale == 'de' %}
              Lassen Sie sich sofort im Chat bezahlen
              {% elsif locale == 'it' %}
              Ricevi pagamenti all'istante nella chat
              {% endif %}
            </h1>
            <div class="description">
              {% if locale == 'en' %}
              Connect to your e-commerce shop or import catalogs to SleekFlow. Provide a friction-free buying experience by creating Payment Links for your customers in chats.
              {% elsif locale == 'zh-TW' %}
              連結電商網店或匯入產品目錄，為客人提供無縫購物體驗。於對話內即時創建收款連結並分享給客人，讓收款更方便快捷。
              {% elsif locale == 'zh-CN' %}
              连结电商网店或汇入产品目录，为客人提供无缝购物体验。于对话内即时创建收款链结并分享给客人，让收款更方便快捷。
              {% elsif locale == 'pt' %}
              Conecte sua loja e-commerce ou importe catálogos para o SleekFlow. Ofereça uma experiência de compra descomplicada ao criar Links de Pagamento para clientes nos chats.
              {% elsif locale == 'id' %}
              Sambungkan toko di e-commerce atau impor katalog ke SleekFlow untuk membuat Link Pembayaran dengan mudah. Proses transaksi lebih cepat karena kamu bisa kirim Link Pembayaran via chat.
              {% elsif locale == 'de' %}
              Verbinden Sie sich mit Ihrem E-Commerce-Shop oder importieren Sie Kataloge in SleekFlow. Sorgen Sie für ein reibungsloses Einkaufserlebnis, indem Sie Zahlungslinks für Ihre Kunden in Chats erstellen.
              {% elsif locale == 'it' %}
              Collegati al tuo negozio di e-commerce o importa cataloghi in SleekFlow. Offri un'esperienza di acquisto senza intoppi creando link di pagamento per i tuoi clienti nelle chat.
              {% endif %}
            </div>
            {% elsif prompt.name == 'signup' or prompt.name == "signup-id" %}
            <div class="testimonial">
              <svg width="56" height="48" viewBox="0 0 56 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M53.3291 0L55.68 5.125C50.8956 7.125 47.5136 9.33333 45.5339 11.75C43.5541 14.1667 42.5643 16.7917 42.5643 19.625C42.5643 21.375 42.8942 22.8333 43.5541 24C44.2965 25.1667 45.2039 26.2083 46.2763 27.125C47.3486 27.9583 48.421 28.8333 49.4933 29.75C50.5657 30.5833 51.4318 31.625 52.0917 32.875C52.8341 34.125 53.2053 35.75 53.2053 37.75C53.2053 40.5833 52.133 43 49.9883 45C47.926 47 45.2452 48 41.9456 48C39.9659 48 37.9861 47.4583 36.0064 46.375C34.1092 45.2917 32.5006 43.5833 31.1808 41.25C29.9435 38.8333 29.3248 35.6667 29.3248 31.75C29.3248 27.25 30.1909 23 31.9232 19C33.738 15 36.4188 11.375 39.9659 8.125C43.5129 4.875 47.9673 2.16667 53.3291 0ZM24.0043 0L26.3552 5.125C21.5708 7.125 18.1888 9.33333 16.2091 11.75C14.2293 14.1667 13.2395 16.7917 13.2395 19.625C13.2395 21.375 13.5694 22.8333 14.2293 24C14.9717 25.1667 15.8791 26.2083 16.9515 27.125C18.0238 27.9583 19.0962 28.8333 20.1685 29.75C21.2409 30.5833 22.107 31.625 22.7669 32.875C23.5093 34.125 23.8805 35.75 23.8805 37.75C23.8805 40.5833 22.8082 43 20.6635 45C18.6012 47 15.9204 48 12.6208 48C10.6411 48 8.66133 47.4583 6.6816 46.375C4.78436 45.2917 3.17582 43.5833 1.856 41.25C0.618667 38.8333 0 35.6667 0 31.75C0 27.25 0.866133 23 2.5984 19C4.41316 15 7.09404 11.375 10.6411 8.125C14.1881 4.875 18.6425 2.16667 24.0043 0Z"
                  fill="#4B9BE8" />
              </svg>
              <div class="text">
                {% if locale == 'en'%}
                By offering various entry points on WhatsApp, such as click-to WhatsApp ads and WhatsApp QR codes, we saw a 98% increase in enquiries and a 5X boost in WhatsApp leads, strengthen our foundation for future marketing strategies.
                {% elsif locale == 'zh-TW' %}
                我們在 WhatsApp 上提供多種進入點，例如點擊進入式 WhatsApp 廣告和 WhatsApp 二維碼，其後查詢數量增加了 98%，WhatsApp 潛在客戶提升了 5 倍，強化了未來行銷策略的基礎。
                {% elsif locale == 'zh-CN' %}
                我们在 WhatsApp 上提供多种进入点，例如点击进入式 WhatsApp 广告和 WhatsApp 二维码，其后查询数量增加了 98%，WhatsApp 潜在客户提升了 5 倍，强化了未来行销策略的基础。
                {% elsif locale == 'pt' %}
                Ao oferecer vários pontos de entrada no WhatsApp, como anúncios de Click-to WhatsApp e códigos QR do WhatsApp, vimos um aumento de 98% nas consultas e um aumento de 5 vezes nos leads do WhatsApp, fortalecendo nossa base para futuras estratégias de marketing.
                {% elsif locale == 'id' %}
                Dengan menyediakan berbagai entry points di WhatsApp, seperti iklan CTWA, dan WhatsApp QR codes, kami melihat peningkatan inquiries sebanayak 98% dan 5X leads di WhatsApp. Ini meningkatkan pondasi strategi marketing di masa mendatang.
                {% elsif locale == 'de' %}
                Durch das Anbieten verschiedener Einstiegspunkte auf WhatsApp, wie zum Beispiel Click-to-WhatsApp-Anzeigen und WhatsApp-QR-Codes, verzeichneten wir einen Anstieg der Anfragen um 98% und eine 5-fache Steigerung der WhatsApp-Leads, was unsere Grundlage für zukünftige Marketingstrategien stärkt.
                {% elsif locale == 'it' %}
                Offrendo diversi punti di accesso su WhatsApp, come gli annunci click-to-WhatsApp e i codici QR di WhatsApp, abbiamo registrato un aumento del 98% delle richieste e un incremento di 5 volte nei lead su WhatsApp, rafforzando così la nostra base per le future strategie di marketing.
                {% endif %}
              </div>
              <div class="source">
                {% if locale == 'en'%}
                Victor Gan
                {% elsif locale == 'zh-TW' %}
                Victor Gan
                {% elsif locale == 'zh-CN' %}
                Victor Gan
                {% elsif locale == 'pt' %}
                Victor Gan
                {% elsif locale == 'id' %}
                Victor Gan
                {% else %}
                Victor Gan
                {% endif %}

              </div>
              <div class="source-position">
                {% if locale == 'en'%}
                MARKETING DIRECTOR
                {% elsif locale == 'zh-TW' %}
                行銷總監
                {% elsif locale == 'zh-CN' %}
                行销总监
                {% elsif locale == 'pt' %}
                Diretor de Marketing
                {% elsif locale == 'id' %}
                Marketing Director
                {% elsif locale == 'de' %}
                Direktorin für Marketing
                {% elsif locale == 'it' %}
                Direttore Marketing
                {% else %}
                MARKETING DIRECTOR
                {% endif %}
              </div>
              <div class="logo-wrapper">
                <img
                  src="https://images.ctfassets.net/tu2uwzoyozk8/3wWOIUu8ZpwIofQeATD6tT/7d150bad949ac3aabcc3c0daad6618bb/natural_signature.png"
                  alt="Natural Signature" />
              </div>
            </div>
            {% endif %}
          </div>
          {% if prompt.name == 'signup' or prompt.name == "signup-id" %}
          <div class="slide" data-slideIndex="4">
            <div class="testimonial">
              <svg width="56" height="48" viewBox="0 0 56 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M53.3291 0L55.68 5.125C50.8956 7.125 47.5136 9.33333 45.5339 11.75C43.5541 14.1667 42.5643 16.7917 42.5643 19.625C42.5643 21.375 42.8942 22.8333 43.5541 24C44.2965 25.1667 45.2039 26.2083 46.2763 27.125C47.3486 27.9583 48.421 28.8333 49.4933 29.75C50.5657 30.5833 51.4318 31.625 52.0917 32.875C52.8341 34.125 53.2053 35.75 53.2053 37.75C53.2053 40.5833 52.133 43 49.9883 45C47.926 47 45.2452 48 41.9456 48C39.9659 48 37.9861 47.4583 36.0064 46.375C34.1092 45.2917 32.5006 43.5833 31.1808 41.25C29.9435 38.8333 29.3248 35.6667 29.3248 31.75C29.3248 27.25 30.1909 23 31.9232 19C33.738 15 36.4188 11.375 39.9659 8.125C43.5129 4.875 47.9673 2.16667 53.3291 0ZM24.0043 0L26.3552 5.125C21.5708 7.125 18.1888 9.33333 16.2091 11.75C14.2293 14.1667 13.2395 16.7917 13.2395 19.625C13.2395 21.375 13.5694 22.8333 14.2293 24C14.9717 25.1667 15.8791 26.2083 16.9515 27.125C18.0238 27.9583 19.0962 28.8333 20.1685 29.75C21.2409 30.5833 22.107 31.625 22.7669 32.875C23.5093 34.125 23.8805 35.75 23.8805 37.75C23.8805 40.5833 22.8082 43 20.6635 45C18.6012 47 15.9204 48 12.6208 48C10.6411 48 8.66133 47.4583 6.6816 46.375C4.78436 45.2917 3.17582 43.5833 1.856 41.25C0.618667 38.8333 0 35.6667 0 31.75C0 27.25 0.866133 23 2.5984 19C4.41316 15 7.09404 11.375 10.6411 8.125C14.1881 4.875 18.6425 2.16667 24.0043 0Z"
                  fill="#4B9BE8" />
              </svg>
              <div class="text">
                {% if locale == 'en'%}
                I would definitely recommend SleekFlow to financial services companies for managing customer inquiries, KYC processes, and application follow-ups. We saw a 23% higher response rate to insurance questionnaire follow-ups than email and SMS, accelerating the conversion process.
                {% elsif locale == 'zh-TW' %}
                我肯定會向金融服務公司推薦使用 SleekFlow 來管理客戶查詢、KYC 程序和申請跟進。 我們發現，其保險問卷跟進的回覆率比電郵和短訊高出 23%，加速了轉化過程。
                {% elsif locale == 'zh-CN' %}
                我肯定会向金融服务公司推荐使用 SleekFlow 来管理客户查询、KYC 程序和申请跟进。 我们发现，其保险问卷跟进的回复率比电邮和短讯高出 23%，加速了转化过程。
                {% elsif locale == 'pt' %}
                Eu certamente recomendaria o SleekFlow para empresas do setor financeiro no gerenciamento de consultas de clientes, processos de KYC e acompanhamento de solicitações. Observamos uma taxa de resposta 23% superior aos acompanhamentos de questionários de seguro em relação ao e-mail e SMS, acelerando o processo de conversão.
                {% elsif locale == 'id' %}
                Saya pasti akan merekomendasikan SleekFlow kepada perusahaan layanan keuangan untuk mengelola pertanyaan pelanggan, proses KYC, dan tindak lanjut aplikasi. Kami mendapatkan tingkat respons 23% lebih tinggi dalam menjawab kuesioner asuransi dibandingkan email dan SMS, sehingga mempercepat proses konversi.
                {% elsif locale == 'de' %}
                Ich würde SleekFlow auf jeden Fall Finanzdienstleistungsunternehmen empfehlen, um Kundenanfragen, KYC-Prozesse und Bewerbungsnachverfolgungen zu verwalten. Wir verzeichneten eine um 23 % höhere Antwortrate auf Nachfassaktionen zu Versicherungsfragebögen im Vergleich zu E-Mails und SMS, was den Konvertierungsprozess beschleunigte.
                {% elsif locale == 'it' %}
                Raccomanderei sicuramente SleekFlow alle società di servizi finanziari per la gestione delle richieste di informazioni da parte dei clienti, dei processi KYC e dei follow-up delle domande. Abbiamo registrato un tasso di risposta del 23% superiore ai follow-up dei questionari assicurativi rispetto alle e-mail e agli SMS, accelerando il processo di conversione.
                {% endif %}
              </div>
              <div class="source">
                {% if locale == 'en'%}
                Gabriel Kung
                {% elsif locale == 'zh-TW' %}
                Gabriel Kung
                {% elsif locale == 'zh-CN' %}
                Gabriel Kung
                {% elsif locale == 'pt' %}
                Gabriel Kung
                {% elsif locale == 'id' %}
                Gabriel Kung
                {% else %}
                Gabriel Kung
                {% endif %}

              </div>
              <div class="source-position">
                {% if locale == 'en'%}
                CHIEF COMMERCIAL OFFICER
                {% elsif locale == 'zh-TW' %}
                首席商務總監
                {% elsif locale == 'zh-CN' %}
                首席商务总监
                {% elsif locale == 'pt' %}
                Diretor Comercial
                {% elsif locale == 'id' %}
                Chief Commercial Officer
                {% elsif locale == 'de' %}
                Chief Commercial Officer (CCO)
                {% elsif locale == 'it' %}
                Direttore commerciale
                {% else %}
                CHIEF COMMERCIAL OFFICER
                {% endif %}
              </div>
              <div class="logo-wrapper">
                <img
                  src="https://images.ctfassets.net/tu2uwzoyozk8/6r4Gw1ELQPx2fKstEa3fmy/ea0052e70eb8daa75ab19b40c0951b0e/bowtie.png"
                  alt="Bowtie Life Insurance" />
              </div>
            </div>
          </div>
          <div class="slide" data-slideIndex="5">
            <div class="testimonial">
              <svg width="56" height="48" viewBox="0 0 56 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M53.3291 0L55.68 5.125C50.8956 7.125 47.5136 9.33333 45.5339 11.75C43.5541 14.1667 42.5643 16.7917 42.5643 19.625C42.5643 21.375 42.8942 22.8333 43.5541 24C44.2965 25.1667 45.2039 26.2083 46.2763 27.125C47.3486 27.9583 48.421 28.8333 49.4933 29.75C50.5657 30.5833 51.4318 31.625 52.0917 32.875C52.8341 34.125 53.2053 35.75 53.2053 37.75C53.2053 40.5833 52.133 43 49.9883 45C47.926 47 45.2452 48 41.9456 48C39.9659 48 37.9861 47.4583 36.0064 46.375C34.1092 45.2917 32.5006 43.5833 31.1808 41.25C29.9435 38.8333 29.3248 35.6667 29.3248 31.75C29.3248 27.25 30.1909 23 31.9232 19C33.738 15 36.4188 11.375 39.9659 8.125C43.5129 4.875 47.9673 2.16667 53.3291 0ZM24.0043 0L26.3552 5.125C21.5708 7.125 18.1888 9.33333 16.2091 11.75C14.2293 14.1667 13.2395 16.7917 13.2395 19.625C13.2395 21.375 13.5694 22.8333 14.2293 24C14.9717 25.1667 15.8791 26.2083 16.9515 27.125C18.0238 27.9583 19.0962 28.8333 20.1685 29.75C21.2409 30.5833 22.107 31.625 22.7669 32.875C23.5093 34.125 23.8805 35.75 23.8805 37.75C23.8805 40.5833 22.8082 43 20.6635 45C18.6012 47 15.9204 48 12.6208 48C10.6411 48 8.66133 47.4583 6.6816 46.375C4.78436 45.2917 3.17582 43.5833 1.856 41.25C0.618667 38.8333 0 35.6667 0 31.75C0 27.25 0.866133 23 2.5984 19C4.41316 15 7.09404 11.375 10.6411 8.125C14.1881 4.875 18.6425 2.16667 24.0043 0Z"
                  fill="#4B9BE8" />
              </svg>
              <div class="text">
                {% if locale == 'en'%}
                With SleekFlow, we can see our campaigns' effectiveness, gauge customer interest, and improve our sales strategies.  Since onboarding, we have seen a 30% increase in overall sales, with 70% partially attributed to its WhatsApp channel.
                {% elsif locale == 'zh-TW' %}
                有了 SleekFlow，我們可以了解行銷活動的成效、評估客戶興趣，並改進銷售策略。 自從使用其服務以來，我們的整體銷售額增加了 30%，其中 70% 要歸功於其 WhatsApp 渠道。
                {% elsif locale == 'zh-CN' %}
                有了 SleekFlow，我们可以了解行销活动的成效、评估客户兴趣，并改进销售策略。 自从使用其服务以来，我们的整体销售额增加了 30%，其中 70% 要归功于其 WhatsApp 渠道。
                {% elsif locale == 'pt' %}
                Com o SleekFlow, podemos ver a efetividade das nossas campanhas, avaliar o interesse dos clientes e melhorar nossas estratégias de venda. Desde a integração, vimos um aumento de 30% nas vendas totais, com 70% parcialmente atribuídos ao canal de WhatsApp.
                {% elsif locale == 'id' %}
                Dengan SleekFlow, kami dapat melihat efektivitas campaign, mengukur minat pelanggan, dan meningkatkan strategi penjualan. Sejak onboarding, kami telah melihat peningkatan 30% dalam penjualan secara keseluruhan, dengan 70% di antaranya lewat channel WhatsApp.
                {% elsif locale == 'de' %}
                Mit SleekFlow können wir die Effektivität unserer Kampagnen sehen, das Kundeninteresse messen und unsere Verkaufsstrategien verbessern. Seit der Einführung haben wir einen Anstieg des Gesamtverkaufs um 30 % verzeichnet, wobei 70 % teilweise dem WhatsApp-Kanal zugeschrieben werden.
                {% elsif locale == 'it' %}
                Con SleekFlow possiamo vedere l'efficacia delle nostre campagne, valutare l'interesse dei clienti e migliorare le nostre strategie di vendita. Da quando siamo entrati in azienda, abbiamo registrato un aumento del 30% delle vendite complessive, il 70% delle quali attribuite al canale WhatsApp.
                {% endif %}
              </div>
              <div class="source">
                {% if locale == 'en'%}
                Henry Lee
                {% elsif locale == 'zh-TW' %}
                Henry Lee
                {% elsif locale == 'zh-CN' %}
                Henry Lee
                {% elsif locale == 'pt' %}
                Henry Lee
                {% elsif locale == 'id' %}
                Henry Lee
                {% else %}
                Henry Lee
                {% endif %}

              </div>
              <div class="source-position">
                {% if locale == 'en'%}
                HEAD OF MARKETING & PR
                {% elsif locale == 'zh-TW' %}
                行銷及公關主管
                {% elsif locale == 'zh-CN' %}
                行销及公关主管
                {% elsif locale == 'pt' %}
                Chefe de Marketing e Relações Públicas
                {% elsif locale == 'id' %}
                Head of Marketing & PR
                {% elsif locale == 'de' %}
                Leiterin der Abteilung Marketing & PR
                {% elsif locale == 'it' %}
                Responsabile Marketing e PR
                {% else %}
                HEAD OF MARKETING & PR
                {% endif %}
              </div>
              <div class="logo-wrapper">
                <img
                  src="https://images.ctfassets.net/tu2uwzoyozk8/1uvbgGYe4Rr4IyeLGEAZMk/45f5487ac0b2e73fcce8c62cb2e9baa6/egl.png"
                  alt="EGL Tours Hong Kong" />
              </div>
            </div>
          </div>
          <div class="slide" data-slideIndex="6">
            <div class="testimonial">
              <svg width="56" height="48" viewBox="0 0 56 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M53.3291 0L55.68 5.125C50.8956 7.125 47.5136 9.33333 45.5339 11.75C43.5541 14.1667 42.5643 16.7917 42.5643 19.625C42.5643 21.375 42.8942 22.8333 43.5541 24C44.2965 25.1667 45.2039 26.2083 46.2763 27.125C47.3486 27.9583 48.421 28.8333 49.4933 29.75C50.5657 30.5833 51.4318 31.625 52.0917 32.875C52.8341 34.125 53.2053 35.75 53.2053 37.75C53.2053 40.5833 52.133 43 49.9883 45C47.926 47 45.2452 48 41.9456 48C39.9659 48 37.9861 47.4583 36.0064 46.375C34.1092 45.2917 32.5006 43.5833 31.1808 41.25C29.9435 38.8333 29.3248 35.6667 29.3248 31.75C29.3248 27.25 30.1909 23 31.9232 19C33.738 15 36.4188 11.375 39.9659 8.125C43.5129 4.875 47.9673 2.16667 53.3291 0ZM24.0043 0L26.3552 5.125C21.5708 7.125 18.1888 9.33333 16.2091 11.75C14.2293 14.1667 13.2395 16.7917 13.2395 19.625C13.2395 21.375 13.5694 22.8333 14.2293 24C14.9717 25.1667 15.8791 26.2083 16.9515 27.125C18.0238 27.9583 19.0962 28.8333 20.1685 29.75C21.2409 30.5833 22.107 31.625 22.7669 32.875C23.5093 34.125 23.8805 35.75 23.8805 37.75C23.8805 40.5833 22.8082 43 20.6635 45C18.6012 47 15.9204 48 12.6208 48C10.6411 48 8.66133 47.4583 6.6816 46.375C4.78436 45.2917 3.17582 43.5833 1.856 41.25C0.618667 38.8333 0 35.6667 0 31.75C0 27.25 0.866133 23 2.5984 19C4.41316 15 7.09404 11.375 10.6411 8.125C14.1881 4.875 18.6425 2.16667 24.0043 0Z"
                  fill="#4B9BE8" />
              </svg>
              <div class="text">
                {% if locale == 'en'%}
                The Broadcast feature enables us to sieve through responses from top funnel awareness right down to the consideration stage within a very short period, from days to minutes. This led to a 60% ROI from the SleekFlow platform.
                {% elsif locale == 'zh-TW' %}
                廣播功能使我們能夠在非常短的時間內（從幾天縮短到幾分鐘）篩選從上層漏斗意識階段至到考慮階段的回應。 這為我們帶來了 60% 的投資回報率。
                {% elsif locale == 'zh-CN' %}
                群发信息功能使我们能够在非常短的时间内（从几天缩短到几分钟）筛选从上层漏斗意识阶段至到考虑阶段的回应。 这为我们带来了 60% 的投资回报率。
                {% elsif locale == 'pt' %}
                O recurso de Transmissão nos permite filtrar as respostas desde a fase de conscientização do topo do funil até a fase de consideração em um período muito curto, de dias para minutos. Isso resultou em um ROI de 60% a partir da plataforma SleekFlow.
                {% elsif locale == 'id' %}
                Fitur Broadcast memungkinkan kami untuk menyaring awareness dari top funnel hingga ke tahap pertimbangan dalam waktu yang sangat singkat. Mulai dari hitungan hari, bahkan menit. Hal ini menghasilkan ROI sebesar 60% dari platform SleekFlow.
                {% elsif locale == 'de' %}
                Die Broadcast-Funktion ermöglicht es uns, Antworten vom oberen Trichter der Bekanntheit bis hin zur Überlegungsphase in sehr kurzer Zeit zu filtern, von Tagen auf Minuten. Dies führte zu einem ROI von 60 % durch die SleekFlow-Plattform.
                {% elsif locale == 'it' %}
                La funzione di Broadcast ci permette di passare al setaccio le risposte dall'awareness del top funnel fino alla fase di considerazione in un periodo molto breve, da giorni a minuti. Questo ha portato a un ROI del 60% dalla piattaforma SleekFlow.
                {% endif %}
              </div>
              <div class="source">
                {% if locale == 'en'%}
                Goh Doh Hau
                {% elsif locale == 'zh-TW' %}
                Goh Doh Hau
                {% elsif locale == 'zh-CN' %}
                Goh Doh Hau
                {% elsif locale == 'pt' %}
                Goh Doh Hau
                {% elsif locale == 'id' %}
                Goh Doh Hau
                {% else %}
                Goh Doh Hau
                {% endif %}

              </div>
              <div class="source-position">
                {% if locale == 'en'%}
                VP OF SALES AND MARKETING
                {% elsif locale == 'zh-TW' %}
                銷售及行銷副總裁
                {% elsif locale == 'zh-CN' %}
                销售及行销副总裁
                {% elsif locale == 'pt' %}
                VP de Vendas e Marketing
                {% elsif locale == 'id' %}
                VP of Sales and Marketing
                {% elsif locale == 'de' %}
                Vizepräsident für Vertrieb und Marketing
                {% elsif locale == 'it' %}
                Vicepresidente delle vendite e del marketing
                {% else %}
                VP OF SALES AND MARKETING
                {% endif %}
              </div>
              <div class="logo-wrapper">
                <img
                  src="https://images.ctfassets.net/tu2uwzoyozk8/7bBZwYOkHfEJgPwxfWvOxB/bafbd31604c403cae0ff36126f03859f/cats_college.png"
                  alt="International College of Creative Arts Technology" />
              </div>
            </div>
          </div>
          {% endif %}
        </div>
        <i id="forward-button" class="arrow right"></i>
        <div class="control-wrapper">
            <span class="indicator-wrapper">
              <div class="slide-indicator active"></div>
              <div class="slide-indicator"></div>
              <div class="slide-indicator"></div>
              <div class="slide-indicator"></div>
              {% if prompt.name == 'signup' or prompt.name == "signup-id" %}
              <div class="slide-indicator"></div>
              <div class="slide-indicator"></div>
              <div class="slide-indicator"></div>
              {% endif %}
            </span>

        </div>
      </div>
    </div>
  </div>
  {% endif %}
  {% assign custom_classes = '' %}
  {% if prompt.name != "login" and prompt.name != "signup" and prompt.name != "login-id" and prompt.name != "signup-id" %}
  {% assign custom_classes = 'full-width' %}
  {% endif %}
  {% if prompt.name == "signup" or prompt.name == "signup-id" %}
  {% assign custom_classes = 'sign-up' %}
  {% endif %}
  <div class="widget-logout-wrapper">
    {% if prompt.name contains "mfa" or prompt.name contains "Otp" %}
    <div>You are currently signing in as <span class="signing-in-as-email">{{ user.email }}</span></div>
    {% endif %}
    <div class="prompt-wrapper section {{ custom_classes }}">
      {%- auth0:widget -%}
    </div>
    <!-- v1 UAT -->
    {% if application.name == "sleekflow-client-web-app" and application.id == "rdqvMHVoagZzedB7GTPAwpMikI4EjWWb" %}
    {% assign auth0_returnTo = 'https%3A%2F%2Fv1-dev.sleekflow.io' %}
    <!-- v1 Staging -->
    {% elsif application.name == "sleekflow-client-web-app" and application.id == "2mLl29LnIvOJPNfmahpN892tDKRyRSeY" %}
    {% assign auth0_returnTo = 'https%3A%2F%2Fv1-staging.sleekflow.io' %}
    <!-- v1 Prod -->
    {% elsif application.name == "sleekflow-client-web-app" and application.id == "ClV0oPIDXP37fNoMkGP8sOma3ofi17M7" %}
    {% assign auth0_returnTo = 'https%3A%2F%2Fv1.sleekflow.io' %}
    <!-- v2 UAT -->
    {% elsif application.name == "sleekflow-client-web-v2-app" and application.id == "1w7Xc0q7GDNcEiYPBnR7dfHDd5ZbAyVV" %}
    {% assign auth0_returnTo = 'https%3A%2F%2Fdev.sleekflow.io' %}
    <!-- v2 Staging -->
    {% elsif application.name == "sleekflow-client-web-v2-app" and application.id == "e1lrLrffx83Z073tDkRqlQk7Wi0JnnP9" %}
    {% assign auth0_returnTo = 'https%3A%2F%2Fstaging.sleekflow.io' %}
    <!-- v2 Prod -->
    {% elsif application.name == "sleekflow-client-web-v2-app" and application.id == "JF6HeS1ExhYR8zig8L4TquIo1XKMVSiC" %}
    {% assign auth0_returnTo = 'https%3A%2F%2Fapp.sleekflow.io' %}
    <!-- mobile UAT -->
    {% elsif application.name == "sleekflow-client-mobile-app" and application.id == "ixziSViOYnhhCXEFlkORJzvLcfpbJ419" %}
    {% assign auth0_returnTo = 'https%3A%2F%2Fsso-dev.sf.chat%2Flogout%2Fcallback' %}
    <!-- mobile v2 UAT -->
    {% elsif application.name == "sleekflow-client-mobile-v2-app" and application.id == "LWqvABdMkmLjHVLuYXoXdPoEGmxcu2NR" %}
    {% assign auth0_returnTo = 'https%3A%2F%2Fsso-dev.sf.chat%2Flogout%2Fcallback' %}
    <!-- mobile Staging -->
    {% elsif application.name == "sleekflow-client-mobile-app" and application.id == "TpFetwowwhU0VnPJQaaMIl9DO70qTReD" %}
    {% assign auth0_returnTo = 'https%3A%2F%2Fsso-staging.sleekflow.io%2Flogout%2Fcallback' %}
    <!-- mobile Prod -->
    {% elsif application.name == "sleekflow-client-mobile-app" and application.id == "gC0NpG5snu67Jdvhgi7sZusAc6Khqilt" %}
    {% assign auth0_returnTo = 'https%3A%2F%2Fsso.sleekflow.io%2Flogout%2Fcallback' %}
    <!-- mobile v2 Prod -->
    {% elsif application.name == "sleekflow-client-mobile-v2-app" and application.id == "eUNerCIgFxMMK53Ve1ht5OhAgSgZWLIV" %}
    {% assign auth0_returnTo = 'https%3A%2F%2Fsso.sleekflow.io%2Flogout%2Fcallback' %}
    <!-- Reseller UAT -->
    {% elsif application.name == "sleekflow-client-reseller-portal-app" and application.id == "teQx3506TYIF6sBU8Zjx3lvkYxf7r5xS" %}
    {% assign auth0_returnTo = 'https%3A%2F%2Fpartner-uat.sleekflow.io' %}
    <!-- Reseller Prod -->
    {% elsif application.name == "sleekflow-client-reseller-portal-app" and application.id == "2h3lx3oaZO8g2I6v7kO9tcrcWJAoT0mY" %}
    {% assign auth0_returnTo = 'https%3A%2F%2Fpartner.sleekflow.io' %}
    <!-- Powerflow UAT -->
    {% elsif application.name == "sleekflow-client-powerflow-app" and application.id == "IRQqRRw0m9mPHhpfm3Gg68xv9lLmaSBQ" %}
    {% assign auth0_returnTo = 'https%3A%2F%2Fpowerflow.sleekflow.io' %}
    <!-- Powerflow Staging -->
    {% elsif application.name == "sleekflow-client-powerflow-app" and application.id == "StYdG2sHFsVeLjq9tNcuM6kREh1SEfFj" %}
    {% assign auth0_returnTo = 'https%3A%2F%2Fpowerflow.sleekflow.io' %}
    <!-- Powerflow Prod -->
    {% elsif application.name == "sleekflow-client-powerflow-app" and application.id == "T7GgXHOSi6oSVKiZSgYkoxilJcIp9obC" %}
    {% assign auth0_returnTo = 'https%3A%2F%2Fpowerflow.sleekflow.io' %}
    {% endif %}
    {% if prompt.name contains "mfa" or prompt.name contains "Otp" %}
    <a class="logout-button logout-button-desktop" href="/v2/logout?client_id={{ application.id }}&returnTo={{ auth0_returnTo }}">Return to Sign In</a>
    {% endif %}
  </div>
</div>
<footer class="footer">
  <div class="footer-overlay"></div>
  <div class="footer-group-wrapper">
    {% if prompt.name contains "mfa" or prompt.name contains "Otp" %}
    <a class="logout-button logout-button-mobile" href="/v2/logout?client_id={{ application.id }}&returnTo={{ auth0_returnTo }}">Return to Sign In</a>
    {% endif %}
    <ul class="toc-wrapper">
      <li><a href="https://sleekflow.io/{{ normalised_locale | downcase }}/terms" target="_blank">
        {% if locale == 'zh-HK' %}
        使用條款
        {% elsif locale == 'zh-CN' %}
        使用条款
        {% elsif locale == 'pt' %}
        Termos e Condições
        {% elsif locale == 'id' %}
        Syarat dan Ketentuan
        {% else %}
        Terms and Conditions
        {% endif %}
      </a></li>
      <li><a href="https://sleekflow.io/{{ normalised_locale | downcase }}/privacy" target="_blank">
        {% if locale == 'zh-HK' %}
        私隱政策
        {% elsif locale == 'zh-CN' %}
        私隐政策
        {% elsif locale == 'pt' %}
        Política de Privacidade
        {% elsif locale == 'id' %}
        Kebijakan Privasi
        {% else %}
        Privacy Policy
        {% endif %}
      </a></li>
    </ul>
  </div>
</footer>
<!-- use scr`+`ipt to workaround script break when you are using storybook-->
<!-- WARNING: remember to turn it back to <scr /> for production-->
<!-- ref: https://community.auth0.com/t/the-script-tag-breaks-the-auth0-cli-storybook/90526 -->
<script>
  function autoplayCarousel() {
    try {
      const carouselEl = document.getElementById("carousel");
      const slideContainerEl = carouselEl.querySelector("#slide-container");
      const slideEl = carouselEl.querySelector(".slide");
      let slideWidth = slideEl.offsetWidth;

      // Slide transition
      const getNewScrollPosition = (arg) => {
        const gap = 36;
        const maxScrollLeft = slideContainerEl.scrollWidth - slideWidth;
        if (arg === "forward") {
          const x = Math.round(slideContainerEl.scrollLeft + slideWidth + gap);
          return x <= maxScrollLeft ? x : 0;
        } else if (arg === "backward") {
          const x = slideContainerEl.scrollLeft - slideWidth
            - gap; return x >= 0 ? x : maxScrollLeft;
        } else if (typeof arg === "number") {
          const x = arg * (slideWidth + gap);
          return x;
        }
      }
      const navigate = (arg) => {
        slideContainerEl.scrollLeft = getNewScrollPosition(arg);
      }

      // Autoplay
      let autoplay = setInterval(() => navigate("forward"), 5000);
      carouselEl.addEventListener("mouseenter", () => {
        clearInterval(autoplay)
      });
      carouselEl.addEventListener("mouseleave", () => {
        autoplay = setInterval(() => navigate("forward"), 5000);
      });

      // Add click handlers
      document.querySelector("#back-button")
        .addEventListener("click", () => {
          navigate("backward")
        });
      document.querySelector("#forward-button")
        .addEventListener("click", () => navigate("forward"));
      document.querySelectorAll(".slide-indicator")
        .forEach((dot, index) => {
          dot.addEventListener("click", () => navigate(index));
        });
      // Add resize handler
      window.addEventListener('resize', () => {
        slideWidth = slideEl.offsetWidth;
      });

      // Slide indicators
      const slideObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach((entry, index) => {
          if (entry.isIntersecting) {
            const slideIndex = entry.target.dataset.slideindex;
            carouselEl.querySelector('.slide-indicator.active').classList.remove('active');
            carouselEl.querySelectorAll('.slide-indicator')[slideIndex].classList.add('active');
          }
        });
      }, { root: slideContainerEl, threshold: .1 });
      document.querySelectorAll('.slide').forEach((slide) => {
        slideObserver.observe(slide);
      });
    } catch (err) {
      //
    }
  }
  autoplayCarousel();

  function toggleDropdown() {
    try {
      const languageDropdownEl = document.getElementById("language-dropdown");
      if (languageDropdownEl.style.opacity === "1") {
        languageDropdownEl.style.opacity = "0";
        languageDropdownEl.style.display = "none";
      } else {
        languageDropdownEl.style.opacity = "1";
        languageDropdownEl.style.display = "initial";
      }
    } catch (err) {
      console.log('err: ', err);
      //
    }
  }

  function handleClickOutside() {
    try {
      const languageSelectEl = document.getElementById('language-select');

      document.addEventListener("click", event => {
        const languageDropdownEl = document.getElementById("language-dropdown");
        const isClickInside = languageSelectEl.contains(event.target);

        if (!isClickInside && languageDropdownEl.style.opacity === "1") {
          toggleDropdown()
        }
      })
    } catch (err) {
      //
    }
  }
  handleClickOutside();

  function handleSelectedLanguageLabelClick() {
    try {
      const selectedLanguageEl = document.getElementById('selected-language-label');
      selectedLanguageEl.addEventListener("click", event => {
        toggleDropdown();
      });
    } catch (err) {
      //
    }
  }
  handleSelectedLanguageLabelClick();

  function handleLanguageClick(languageCode) {
    try {
      const selectedLanguageEl = document.getElementById(languageCode);
      selectedLanguageEl.addEventListener("click", event => {
        const referrer = localStorage.getItem('documentReferrer') || 'https://localhost:5173/';
        const targetScreenQuery =
          window.location.pathname.indexOf('signup') !== -1
            ? '?screen_hint=signup'
            : window.location.pathname.indexOf('login') !== -1
              ? '?screen_hint=login'
              : '';
        window.location.replace(`${referrer}${languageCode}${targetScreenQuery}`);
      });
    } catch (err) {
      //
    }
  }

  ['en', 'zh-HK', 'zh-CN', 'pt-BR', 'id','de', 'it'].forEach((code) => handleLanguageClick(code));

  function isDeviceMobileOrTablet() {
    return (function (a) {
      const isMobile = /(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(a) || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(a.substr(0, 4));
      const isTablet = /(ipad|tablet|(android(?!.*mobile))|(windows(?!.*phone)(.*touch))|kindle|playbook|silk|(puffin(?!.*(IP|AP|WP))))/i.test(a);

      return isMobile || isTablet;
    })(navigator.userAgent || navigator.vendor || window.opera);
  }

  function isDeviceMobile() {
    let check = false;
    (function(a){if(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(a)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(a.substr(0,4))) check = true;})(navigator.userAgent||navigator.vendor||window.opera);
    return check;
  };


  function disableMobileSplashScreen () {
    const overlayEl = document.getElementById('overlay');
    overlayEl.classList.remove('enabled');
    overlayEl.classList.add('disabled');
    document.body.classList.remove('isMobileSplashScreen')
  }

  document
    .querySelector("#back-to-home")
    .addEventListener("click", () => {
      console.log('clicked');
      disableMobileSplashScreen();
    });


  function hideLogoutButton2FAScreen() {
    const logoutButton = document.querySelectorAll('.logout-button');
    const isMobile = isDeviceMobile();
    if (logoutButton && isMobile) {
      logoutButton.forEach((button) => {
        button.style.display = "none"
      })
    }
  }

  hideLogoutButton2FAScreen()

  // ref: https://stackoverflow.com/questions/11381673/detecting-a-mobile-browser
  function renderMobileSplashScreen() {
    const isMobile = isDeviceMobileOrTablet();

    const overlayEl = document.getElementById('overlay');
    if (isMobile) {
      overlayEl.classList.add('enabled');
      document.body.classList.add('isMobileSplashScreen')
    } else {
      overlayEl.classList.add('disabled')
    }
  };

  // renderMobileSplashScreen();

  function hideFieldsOnMobile() {
    const isMobile = isDeviceMobileOrTablet();
    if (isMobile) {
      document.body.classList.add('isMobile');

    }
  }

  hideFieldsOnMobile()

  function configDocuementReferrer() {
    const hostingDomainKeyword = 'sso' // currently 'https://sso-dev.sf.chat/', 'https://sso-staging.sf.chat/'
    if (!document.referrer.includes(hostingDomainKeyword) && !document.referrer.includes('sso-staging')) {
      localStorage.setItem('documentReferrer', document.referrer);
      return
    }
  }

  configDocuementReferrer();
</script>
</body>

</html>
