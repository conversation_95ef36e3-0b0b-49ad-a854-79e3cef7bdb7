using Sleekflow.IntelligentHub.Documents.Statistics;
using Sleekflow.IntelligentHub.Documents.Statistics.Abstractions;
using Sleekflow.IntelligentHub.Models.Documents.Statistics;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.StatisticsCalculators;

public class JpgStatisticsCalculator : IDocumentStatisticsCalculator
{
    private readonly IDocumentCounterService _documentCounterService;

    public JpgStatisticsCalculator(IDocumentCounterService documentCounterService)
    {
        _documentCounterService = documentCounterService;
    }

    public DocumentStatistics CalculateDocumentStatistics(Stream stream)
    {
        var count = Math.Max((int) stream.Length / 1000, 1000); // at minimum a jpg is considered 1000 characters

        const int totalPages = 1; // JPG/JPEG is considered a single page

        return new DocumentStatistics(
            count,
            count,
            count,
            totalPages,
            (int) stream.Length);
    }
}