using MassTransit;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Statistics;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.OnWorkflowExecutionEndedPostProcessRequestedEventConsumer.Response;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.Statistics;
using Sleekflow.FlowHub.StepExecutions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.Ids;

namespace Sleekflow.FlowHub.OnWorkflowExecutionEndedPostProcessRequestedEventConsumer.Consumers;

public class OnWorkflowExecutionEndedPostProcessRequestedEventConsumer
    : IConsumer<OnWorkflowExecutionEndedPostProcessRequestedEvent>
{
    private readonly IStepExecutionService _stepExecutionService;
    private readonly IWorkflowExecutionService _workflowExecutionService;
    private readonly IWorkflowStepCategoryProvider _workflowStepCategoryProvider;
    private readonly IBaseProxyStateService<BaseProxyState> _baseProxyStateService;
    private readonly IStateStepCategoryStatisticsService _stateStepCategoryStatisticsService;
    private readonly IIdService _idService;
    private readonly ILogger<OnWorkflowExecutionEndedPostProcessRequestedEventConsumer> _logger;

    public OnWorkflowExecutionEndedPostProcessRequestedEventConsumer(
        IStepExecutionService stepExecutionService,
        IWorkflowExecutionService workflowExecutionService,
        IWorkflowStepCategoryProvider workflowStepCategoryProvider,
        IBaseProxyStateService<BaseProxyState> baseProxyStateService,
        IStateStepCategoryStatisticsService stateStepCategoryStatisticsService,
        IIdService idService,
        ILogger<OnWorkflowExecutionEndedPostProcessRequestedEventConsumer> logger)
    {
        _stepExecutionService = stepExecutionService;
        _workflowExecutionService = workflowExecutionService;
        _workflowStepCategoryProvider = workflowStepCategoryProvider;
        _baseProxyStateService = baseProxyStateService;
        _stateStepCategoryStatisticsService = stateStepCategoryStatisticsService;
        _idService = idService;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<OnWorkflowExecutionEndedPostProcessRequestedEvent> context)
    {
        var @event = context.Message;

        try
        {
            var sleekflowCompanyId = @event.SleekflowCompanyId;
            var stateId = @event.StateId;

            var stepExecutions = await _stepExecutionService.UpdateStepTtlByStateIdAsync(
                sleekflowCompanyId,
                stateId,
                TimeToLiveConstants.NinetyDaysInSeconds,
                batchLimit: 60);

            var state = await _baseProxyStateService.GetProxyStateAsync(stateId);

            var workflowStepCategories =
                _workflowStepCategoryProvider.GetStepIdCategoryMapping(state.WorkflowContext.SnapshottedWorkflow.Steps);

            var stateStepCategoryCountDict = stepExecutions
                .Where(x => x.StepExecutionStatus == StepExecutionStatuses.Started)
                .GroupBy(x => x.StepId)
                .Select(
                    x =>
                    {
                        if (workflowStepCategories.TryGetValue(x.Key, out var category))
                        {
                            return new
                            {
                                Category = category, Count = x.Count()
                            };
                        }

                        return null;
                    })
                .Where(x => x is not null)
                .GroupBy(x => x!.Category)
                .ToDictionary(x => x.Key, g => g.Sum(y => y!.Count));

            if (stateStepCategoryCountDict.Count > 0)
            {
                await _stateStepCategoryStatisticsService.RecordStateStepCategoriesAsync(
                    new StateStepCategoryStatistics(
                        _idService.GetId(SysTypeNames.StateStepCategoryStatistics),
                        sleekflowCompanyId,
                        stateId,
                        state.Identity,
                        _workflowStepCategoryProvider.GetWorkflowTriggerStepCategory(
                            state.WorkflowContext.SnapshottedWorkflow.Triggers),
                        stateStepCategoryCountDict,
                        enrolledAt: state.CreatedAt,
                        DateTimeOffset.UtcNow,
                        DateTimeOffset.UtcNow),
                    sleekflowCompanyId);
            }

            var numOfNodesExecuted = stepExecutions.Count(
                x =>
                    !string.IsNullOrWhiteSpace(x.StepNodeId)
                    && x.StepId == x.StepNodeId
                    && x.StepExecutionStatus != StepExecutionStatuses.Started);

            if (numOfNodesExecuted > 0)
            {
                await _workflowExecutionService.SetWorkflowExecutionNodesCountAsync(
                    @event.WorkflowExecutionEndId,
                    state.Id,
                    state.Identity.WorkflowId,
                    numOfNodesExecuted);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error occurred while processing OnWorkflowExecutionEndedPostProcessRequestedEvent {Event}",
                JsonConvert.SerializeObject(@event));

            throw;
        }


        await context.RespondAsync(new PostProcessEventReceived(@event.WorkflowExecutionEndId));
    }
}