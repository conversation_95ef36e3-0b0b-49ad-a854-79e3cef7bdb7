using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Triggers.Internals;

[TriggerGroup(ControllerNames.Internals)]


public class GetTriggerCondition
    : ITrigger<GetTriggerCondition.GetTriggerConditionInput,
        GetTriggerCondition.GetTriggerConditionOutput>
{
    private readonly IWorkflowService _workflowService;

    private ILogger<GetTriggerCondition> _logger;

    public GetTriggerCondition(IWorkflowService workflowService, ILogger<GetTriggerCondition> logger)
    {
        _workflowService = workflowService;
        _logger = logger;
    }

    public class GetTriggerConditionInput
        : Models.Internals.GetTriggerConditionInput
    {
        [JsonConstructor]
        public GetTriggerConditionInput(
            string sleekflowCompanyId,
            string workflowVersionedId,
            string triggerId)
            : base(sleekflowCompanyId, workflowVersionedId, triggerId)
        {
        }
    }
    public class GetTriggerConditionOutput : Models.Internals.GetTriggerConditionOutput
    {
        [JsonConstructor]
        public GetTriggerConditionOutput(
            string sleekflowCompanyId,
            string workflowVersionedId,
            string triggerId,
            string condition,
            string workflowName)
            : base(sleekflowCompanyId, workflowVersionedId, triggerId, condition, workflowName)
        {
        }
    }

    public async Task<GetTriggerConditionOutput> F(GetTriggerConditionInput input)
    {
        _logger.LogInformation("GetTriggerConditionInput: {Input}", input);
        var proxyWorkflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
            input.SleekflowCompanyId,
            input.WorkflowVersionedId);

        var trigger = GetJsonPropertyNestedValue(proxyWorkflow, "triggers", input.TriggerId);
        _logger.LogInformation("Find trigger: {Trigger}", trigger);

        if (trigger == null)
        {
            throw new SfWorkflowWebhookTriggerNotFoundException();
        }

        var conditionProperty = trigger.GetType().GetProperty("Condition");
        if (conditionProperty == null)
        {
            _logger.LogError("trigger condition property is null. Trigger: {Trigger}", trigger);
            throw new SfWorkflowWebhookTriggerNotFoundException();
        }

        string condition = (string) conditionProperty.GetValue(trigger);
        _logger.LogInformation("Condition value: {Condition}", condition);

        return new GetTriggerConditionOutput(
            input.SleekflowCompanyId,
            input.WorkflowVersionedId,
            input.TriggerId,
            condition ?? string.Empty,
            proxyWorkflow.Name);
    }

    public object GetJsonPropertyNestedValue(object rootObject, string outerJsonPropertyName, string innerJsonPropertyName)

    {
        if (rootObject == null) return null;
        var outerValue = GetJsonPropertyValue(rootObject, outerJsonPropertyName);
        if (outerValue == null) return null;
        return GetJsonPropertyValue(outerValue, innerJsonPropertyName);
    }

    public object GetJsonPropertyValue(object obj, string propertyName)
    {
        if (obj == null) return null;

        foreach (var prop in obj.GetType().GetProperties())
        {
            var attr = prop.GetCustomAttributes(typeof(JsonPropertyAttribute), false)
                .OfType<JsonPropertyAttribute>()
                .FirstOrDefault();

            if (attr != null && attr.PropertyName == propertyName)
            {
                return prop.GetValue(obj);
            }
        }

        return null;
    }
}