using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Documents.WebCrawlingSessions;
using Sleekflow.IntelligentHub.WebCrawlingSessions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Workers.Activities;

public class PatchWebCrawlingSession
{
    private readonly ILogger<PatchWebCrawlingSession> _logger;
    private readonly IWebCrawlingSessionService _webCrawlingSessionService;

    public PatchWebCrawlingSession(
        ILogger<PatchWebCrawlingSession> logger,
        IWebCrawlingSessionService webCrawlingSessionService)
    {
        _logger = logger;
        _webCrawlingSessionService = webCrawlingSessionService;
    }

    public class PatchWebCrawlingSessionInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("session_id")]
        public string SessionId { get; set; }

        [Required]
        [JsonProperty("crawling_results")]
        [Validations.ValidateArray]
        public List<CrawlingResult> CrawlingResults { get; set; }

        [Required]
        [JsonProperty("urls_to_process")]
        [Validations.ValidateObject]
        public string[] UrlsToProcess { get; set; }

        [JsonProperty("status")]
        [Required]
        public string Status { get; set; }

        [JsonConstructor]
        public PatchWebCrawlingSessionInput(
            string sleekflowCompanyId,
            string sessionId,
            List<CrawlingResult> crawlingResults,
            string[] urlsToProcess,
            string status)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SessionId = sessionId;
            CrawlingResults = crawlingResults;
            UrlsToProcess = urlsToProcess;
            Status = status;
        }
    }

    public class PatchWebCrawlingSessionOutput
    {
        [JsonProperty("session")]
        public WebCrawlingSession Session { get; set; }

        [JsonConstructor]
        public PatchWebCrawlingSessionOutput(WebCrawlingSession session)
        {
            Session = session;
        }
    }

    [Function(nameof(PatchWebCrawlingSession))]
    public async Task<PatchWebCrawlingSessionOutput> Patch(
        [ActivityTrigger]
        PatchWebCrawlingSessionInput patchWebCrawlingSessionInput)
    {
        var updatedSession = await _webCrawlingSessionService.PatchWebCrawlingSessionAsync(
            patchWebCrawlingSessionInput.SleekflowCompanyId,
            patchWebCrawlingSessionInput.SessionId,
            patchWebCrawlingSessionInput.CrawlingResults,
            patchWebCrawlingSessionInput.UrlsToProcess,
            patchWebCrawlingSessionInput.Status);

        return new PatchWebCrawlingSessionOutput(updatedSession);
    }
}