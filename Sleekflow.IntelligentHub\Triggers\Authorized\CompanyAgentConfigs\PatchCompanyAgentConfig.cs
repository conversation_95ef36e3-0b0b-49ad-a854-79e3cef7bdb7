using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Events;
using Sleekflow.IntelligentHub.Models.Prompts;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.Models.Prompts;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.CompanyAgentConfigs;

[TriggerGroup(
    ControllerNames.CompanyAgentConfigs,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class PatchCompanyAgentConfig
    : ITrigger<PatchCompanyAgentConfig.PatchCompanyAgentConfigInput,
        PatchCompanyAgentConfig.PatchCompanyAgentConfigOutput>
{
    private readonly ISleekflowAuthorizationContext _authorizationContext;
    private readonly ICompanyAgentConfigService _companyAgentConfigService;
    private readonly IBus _bus;
    private readonly ILogger<PatchCompanyAgentConfig> _logger;

    public PatchCompanyAgentConfig(
        ISleekflowAuthorizationContext authorizationContext,
        ICompanyAgentConfigService companyAgentConfigService,
        IBus bus,
        ILogger<PatchCompanyAgentConfig> logger)
    {
        _authorizationContext = authorizationContext;
        _companyAgentConfigService = companyAgentConfigService;
        _bus = bus;
        _logger = logger;
    }

    public class PatchCompanyAgentConfigInput : IHasETag
    {
        [Required]
        [JsonProperty("id")]
        public string Id { get; set; }

        [Required]
        [JsonProperty("name")]
        public string? Name { get; set; }

        [JsonProperty(CompanyAgentConfig.PropertyNameDescription)]
        public string? Description { get; set; }

        [ValidateObject]
        [JsonProperty("is_chat_history_enabled_as_context")]
        public bool? IsChatHistoryEnabledAsContext { get; set; }

        [ValidateObject]
        [JsonProperty("is_contact_properties_enabled_as_context")]
        public bool? IsContactPropertiesEnabledAsContext { get; set; }

        [ValidateObject]
        [JsonProperty("number_of_previous_messages_in_chat_history_available_as_context")]
        public int? NumberOfPreviousMessagesInChatHistoryAvailableAsContext { get; set; }

        [AllowedStringValues(false, ChannelTypes.WhatsAppCloudApi)]
        [JsonProperty("channel_type")]
        public string? ChannelType { get; set; }

        [JsonProperty("channel_id")]
        public string? ChannelId { get; set; }

        [JsonProperty(CompanyAgentConfig.PropertyNameType)]
        [RegularExpression($"{CompanyAgentTypes.Sales}|{CompanyAgentTypes.Support}|{CompanyAgentTypes.Custom}")]
        public string? Type { get; set; }

        [Required]
        [ValidateObject]
        [JsonProperty("prompt_instruction")]
        public PromptInstructionDto? PromptInstruction { get; set; }

        [JsonProperty(CompanyAgentConfig.PropertyNameCollaborationMode)]
        [RegularExpression(
            $"{AgentCollaborationModes.Long}|{AgentCollaborationModes.Short}|{AgentCollaborationModes.Medium}|{AgentCollaborationModes.LeadNurturing}|{AgentCollaborationModes.ManagerLeadNurturing}|{AgentCollaborationModes.ReAct}")]
        public string? CollaborationMode { get; set; }

        [ValidateObject]
        [JsonProperty(CompanyAgentConfig.PropertyNameLeadNurturingTools)]
        public LeadNurturingTools? LeadNurturingTools { get; set; }

        [ValidateObject]
        [JsonProperty(CompanyAgentConfig.PropertyNameToolsConfig)]
        public ToolsConfig? ToolsConfig { get; set; }

        [ValidateArray]
        [JsonProperty(CompanyAgentConfig.PropertyNameEnricherConfigs)]
        public List<EnricherConfig>? EnricherConfigs { get; set; }

        [ValidateObject]
        [JsonProperty(CompanyAgentConfig.PropertyNameKnowledgeRetrievalConfig)]
        public KnowledgeRetrievalConfig? KnowledgeRetrievalConfig { get; set; }

        [ValidateObject]
        [JsonProperty(CompanyAgentConfig.PropertyNameActions)]
        public CompanyAgentConfigActionsDto? Actions { get; set; }

        [JsonProperty(CompanyAgentConfig.PropertyNameEditMode)]
        [RegularExpression($"{AgentEditModes.Basic}|{AgentEditModes.Advanced}")]
        public string? EditMode { get; set; }

        [Required]
        [JsonProperty(IHasETag.PropertyNameETag)]
        public string ETag { get; set; }

        [JsonConstructor]
        public PatchCompanyAgentConfigInput(
            string id,
            string eTag,
            string? name = null,
            string? description = null,
            PromptInstructionDto? promptInstruction = null,
            bool? isChatHistoryEnabledAsContext = null,
            bool? isContactPropertiesEnabledAsContext = null,
            int? numberOfPreviousMessagesInChatHistoryAvailableAsContext = null,
            string? channelType = null,
            string? channelId = null,
            string? collaborationMode = null,
            string? type = null,
            LeadNurturingTools? leadNurturingTools = null,
            ToolsConfig? toolsConfig = null,
            List<EnricherConfig>? enricherConfigs = null,
            KnowledgeRetrievalConfig? knowledgeRetrievalConfig = null,
            CompanyAgentConfigActionsDto? actions = null,
            string? editMode = null)
        {
            Id = id;
            Name = name;
            Description = description;
            ETag = eTag;
            PromptInstruction = promptInstruction;
            IsChatHistoryEnabledAsContext = isChatHistoryEnabledAsContext;
            IsContactPropertiesEnabledAsContext = isContactPropertiesEnabledAsContext;
            NumberOfPreviousMessagesInChatHistoryAvailableAsContext =
                numberOfPreviousMessagesInChatHistoryAvailableAsContext;
            ChannelType = channelType;
            ChannelId = channelId;
            CollaborationMode = collaborationMode;
            Type = type;
            LeadNurturingTools = leadNurturingTools;
            ToolsConfig = toolsConfig;
            EnricherConfigs = enricherConfigs;
            KnowledgeRetrievalConfig = knowledgeRetrievalConfig;
            Actions = actions;
            EditMode = editMode;
        }
    }

    public class PatchCompanyAgentConfigOutput
    {
        [JsonProperty("company_agent_config")]
        public CompanyAgentConfigDto CompanyAgentConfig { get; set; }

        [JsonConstructor]
        public PatchCompanyAgentConfigOutput(CompanyAgentConfigDto companyAgentConfig)
        {
            CompanyAgentConfig = companyAgentConfig;
        }
    }

    public async Task<PatchCompanyAgentConfigOutput> F(PatchCompanyAgentConfigInput input)
    {
        var companyAgentConfig = await _companyAgentConfigService.PatchAndGetAsync(
            input.Id,
            _authorizationContext.SleekflowCompanyId!,
            input.ETag,
            _authorizationContext.SleekflowStaffId!,
            _authorizationContext.SleekflowTeamIds,
            input.Name,
            GetPromptInstruction(input.PromptInstruction),
            input.IsChatHistoryEnabledAsContext,
            input.IsContactPropertiesEnabledAsContext,
            input.NumberOfPreviousMessagesInChatHistoryAvailableAsContext,
            input.ChannelType,
            input.ChannelId,
            input.Description,
            input.CollaborationMode,
            input.Type,
            input.LeadNurturingTools,
            input.ToolsConfig,
            input.EnricherConfigs,
            input.KnowledgeRetrievalConfig,
            input.Actions != null ? new CompanyAgentConfigActions(input.Actions) : null,
            firstWorkflowPublishedAt: null,
            input.EditMode);

        return new PatchCompanyAgentConfigOutput(new CompanyAgentConfigDto(companyAgentConfig));
    }

    private static PromptInstruction? GetPromptInstruction(PromptInstructionDto? promptInstruction)
    {
        if (promptInstruction is null)
        {
            return null;
        }

        return new PromptInstruction(
            promptInstruction.Objective,
            promptInstruction.Tone,
            promptInstruction.DiscloseLevel,
            promptInstruction.ResponseLevel,
            promptInstruction.RestrictivenessLevel,
            promptInstruction.GreetingMessage,
            promptInstruction.AdditionalInstructionCore,
            promptInstruction.AdditionalInstructionStrategy,
            promptInstruction.AdditionalInstructionResponse,
            promptInstruction.AdditionalInstructionKnowledgeRetrieval,
            promptInstruction.Guardrails);
    }
}