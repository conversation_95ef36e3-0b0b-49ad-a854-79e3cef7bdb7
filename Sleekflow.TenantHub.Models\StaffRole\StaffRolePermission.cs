using Sleekflow.Persistence.Abstractions;
using Newtonsoft.Json;

namespace Sleekflow.TenantHub.Models.StaffRole;

public class StaffRolePermission : IHasSleekflowCompanyId
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("sleekflow_role_id")]
    public string SleekflowRoleId { get; set; }

    [JsonProperty("sleekflow_role_name")]
    public string SleekflowRoleName { get; set; }

    [JsonProperty("rbac_role_permissions")]
    public List<string> RbacRolePermissions { get; set; }

    [JsonConstructor]
    public StaffRolePermission(
        string sleekflowCompanyId,
        string sleekflowRoleId,
        string sleekflowRoleName,
        List<string> rbacRolePermissions)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        SleekflowRoleId = sleekflowRoleId;
        SleekflowRoleName = sleekflowRoleName;
        RbacRolePermissions = rbacRolePermissions;
    }
}