# Sleekflow UserEventAnalyticsHub DataCompactor

## Overview

The DataCompactor service provides two main capabilities:
1. **Data Migration**: Migrates existing Parquet files from Azure Data Lake Storage to PostgreSQL to improve query performance
2. **Performance Testing**: Tests PostgreSQL-based SqlJobProcessingService to validate performance improvements

The current system suffers from performance issues due to many small Parquet files, causing DuckDB to be slow when processing user queries.

## Problem Statement

- Azure Data Lake Storage contains many small Parquet files with user event data
- DuckDB performance degrades when reading from many small files
- Total data volume is manageable (~100MB) but file fragmentation causes issues
- User SQL queries through `SqlJobProcessingService` are becoming slow

## Solution

### Migration Mode
This service migrates existing Parquet files to PostgreSQL using DuckDB as the migration engine:

1. **Discovers** Parquet files in Azure Data Lake Storage organized by company
2. **Migrates** data to PostgreSQL using DuckDB's PostgreSQL extension
3. **Tracks** migration progress to ensure no data loss
4. **Validates** data integrity after migration
5. **Updates** query routing to use PostgreSQL for faster performance

### Testing Mode
A PostgreSQL-based SqlJobProcessingService is included to test query performance:

1. **Tests** SQL queries directly against PostgreSQL data
2. **Measures** execution time and performance metrics
3. **Validates** that PostgreSQL approach is faster than parquet scanning
4. **Provides** configurable test scenarios for different query patterns

## Architecture

### Database Design
- Each company gets dedicated tables in PostgreSQL
- Data table: `events_{sleekflowCompanyId}` - stores the actual event data
- Tracking table: `file_migration_history_{sleekflowCompanyId}` - tracks migration progress

### Key Components

#### Migration Services
- **BlobDiscoveryService**: Scans Azure Data Lake Storage for Parquet files
- **PostgreSqlSchemaService**: Manages PostgreSQL schema and table creation
- **FileTrackingService**: Tracks which files have been migrated
- **ParquetCompactionService**: Orchestrates the migration process

#### Testing Services
- **SqlJobProcessingService**: PostgreSQL-based query processor for performance testing
- **MockSqlJobService**: Mock service for testing without full infrastructure
- **MockLockService**: Mock locking service for isolated testing

## Quick Start

### 🚀 Get Started in 5 Minutes

```bash
# 1. Set up environment variables
./setup-test-env.sh

# 2. Test connectivity
dotnet run

# 3. Discovery mode (safe testing)
export COMPACTOR_DISCOVERY_ONLY="true"
dotnet run

# 4. SQL performance testing
export COMPACTOR_SQL_JOB_TEST="true"
export TEST_COMPANY_ID="your-company-id"
dotnet run
```

See [QUICK_START_TESTING.md](./QUICK_START_TESTING.md) for detailed testing instructions.

## Usage Modes

### 1. Data Migration Mode (Default)
Migrates Parquet files to PostgreSQL:

```bash
# Run complete migration
dotnet run

# Limited migration for testing
export COMPACTOR_MAX_CONCURRENT_COMPANIES="1"
export COMPACTOR_FILE_BATCH_SIZE="10"
dotnet run
```

### 2. Discovery Mode
Explores data without processing:

```bash
export COMPACTOR_DISCOVERY_ONLY="true"
export COMPACTOR_SHOW_FILE_DETAILS="true"
export COMPACTOR_MAX_DISCOVERY_COMPANIES="5"
dotnet run
```

### 3. SQL Job Testing Mode
Tests PostgreSQL query performance:

```bash
export COMPACTOR_SQL_JOB_TEST="true"
export TEST_COMPANY_ID="company-123"
export TEST_SQL_QUERY="SELECT COUNT(*) FROM events WHERE eventType = 'user_action'"
dotnet run
```

## Configuration

### Environment Variables

#### Required (Migration Mode)
```bash
export ANALYTICS_STORAGE_ACCOUNT_NAME="your-storage-account"
export ANALYTICS_STORAGE_ACCOUNT_KEY="your-storage-key"
export EVENTS_CONTAINER_NAME="events"
export POSTGRES_CONNECTION_STRING="Host=host;Database=db;Username=user;Password=****"
export POSTGRES_SERVER_NAME="your-postgres-server"
export POSTGRES_DATABASE_NAME="sleekflow_events"
```

#### Required (Testing Mode)
```bash
export ANALYTICS_STORAGE_CONN_STR="your-storage-connection-string"
export RESULTS_CONTAINER_NAME="results"
# Plus all PostgreSQL variables above
```

#### Optional Processing Configuration
```bash
export COMPACTOR_MAX_CONCURRENT_COMPANIES="3"     # Parallel company processing
export COMPACTOR_FILE_BATCH_SIZE="100"            # Files per batch
export COMPACTOR_RETRY_ATTEMPTS="3"               # Retry failed operations
export COMPACTOR_PROCESSING_TIMEOUT_MINUTES="30"  # Processing timeout
```

#### Optional Testing Configuration
```bash
export TEST_COMPANY_ID="company-123"              # Company to test
export TEST_JOB_ID="job-456"                      # Job ID for testing
export TEST_SQL_QUERY="SELECT COUNT(*) FROM events" # SQL query to test
```

## Migration Process

### Phase 1: Discovery
1. Scans Azure Data Lake Storage for all Parquet files
2. Groups files by company ID
3. Extracts metadata (size, modification date, partition info)

### Phase 2: Schema Setup
1. Creates PostgreSQL tables for each company
2. Sets up proper indexes for query performance
3. Creates migration tracking tables

### Phase 3: Data Migration
1. Processes companies sequentially or in parallel
2. Uses DuckDB to read Parquet files and write to PostgreSQL
3. Tracks progress and handles failures gracefully
4. Validates data integrity after each batch

### Phase 4: Performance Testing
1. Tests SQL queries against PostgreSQL data
2. Compares performance with original parquet scanning
3. Validates query correctness and performance improvements

## Database Schema

Each company gets dedicated tables in PostgreSQL:

### Events Table: `events_{sleekflowCompanyId}`
```sql
CREATE TABLE events_{sleekflowCompanyId} (
    id VARCHAR PRIMARY KEY,
    eventType VARCHAR,
    sleekflowCompanyId VARCHAR NOT NULL,
    objectId VARCHAR,
    objectType VARCHAR,
    source VARCHAR,
    properties JSONB,
    metadata JSONB,
    timestamp BIGINT NOT NULL,
    year INTEGER NOT NULL,
    month INTEGER NOT NULL,
    day INTEGER NOT NULL,
    hour INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Migration Tracking: `file_migration_history_{sleekflowCompanyId}`
```sql
CREATE TABLE file_migration_history_{sleekflowCompanyId} (
    id SERIAL PRIMARY KEY,
    blob_path VARCHAR NOT NULL UNIQUE,
    blob_last_modified TIMESTAMP NOT NULL,
    blob_size_bytes BIGINT NOT NULL,
    records_count INTEGER NOT NULL,
    migration_started_at TIMESTAMP NOT NULL,
    migration_completed_at TIMESTAMP,
    migration_status VARCHAR(20) NOT NULL DEFAULT 'pending',
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);
```

## Monitoring and Validation

### Logging
The service provides detailed logging for:
- Migration progress per company and file
- SQL query performance metrics
- Error handling and retry attempts
- Data integrity validation results
- Resource usage and performance statistics

### Data Validation
```bash
# Check migrated data
psql -d sleekflow_events -c "SELECT COUNT(*) FROM events_company123;"

# Check migration status
psql -d sleekflow_events -c "SELECT migration_status, COUNT(*) FROM file_migration_history_company123 GROUP BY migration_status;"

# Performance testing results appear in application logs
```

## Performance Benefits

After migration, users should experience:
- **Faster SQL query execution**: Target <2s for typical queries vs 10-30s with parquet scanning
- **Reduced memory usage**: No more loading many small files into DuckDB memory
- **Consistent performance**: Database indexes provide predictable query performance
- **Better concurrency**: PostgreSQL handles multiple concurrent queries efficiently

## Testing

### Prerequisites
1. Azure PostgreSQL database instance
2. Access to Azure Data Lake Storage containing Parquet files
3. Appropriate environment variables configured

### Testing Workflow
1. **Connectivity Testing**: Verify all services connect properly
2. **Discovery Testing**: Explore data structure without processing
3. **Schema Testing**: Validate PostgreSQL table creation
4. **Migration Testing**: Process small batches of data
5. **Performance Testing**: Compare query speeds between parquet and PostgreSQL

See [QUICK_START_TESTING.md](./QUICK_START_TESTING.md) for step-by-step testing instructions.

## Documentation

- [QUICK_START_TESTING.md](./QUICK_START_TESTING.md) - Get started quickly with testing
- [PROJECT_STRUCTURE.md](./PROJECT_STRUCTURE.md) - Complete project architecture
- [IMPLEMENTATION_PLAN.md](./IMPLEMENTATION_PLAN.md) - Detailed implementation guidance
- [TESTING_GUIDE.md](./TESTING_GUIDE.md) - Comprehensive testing instructions

## Dependencies

### NuGet Packages
- .NET 8.0
- DuckDB.NET.Data.Full 1.2.0
- Azure.Storage.Files.DataLake 12.18.0
- Azure.Storage.Blobs 12.18.0
- CsvHelper 33.0.1
- Dapper 2.1.66
- Npgsql 8.0.3
- Polly 8.5.2
- Newtonsoft.Json 13.0.3

### Sleekflow Project References
- Sleekflow.csproj
- Sleekflow.Models.csproj
- Sleekflow.UserEventHub.Models.csproj

## Status

✅ **Core Implementation Complete** - Migration and testing services are implemented and ready for use.

Current capabilities:
- ✅ Data discovery and exploration
- ✅ PostgreSQL schema management
- ✅ File migration tracking
- ✅ Parquet to PostgreSQL migration
- ✅ SQL job performance testing
- ✅ Mock services for isolated testing

Next steps:
- Performance optimization based on testing results
- Production deployment configuration
- Automated monitoring and alerting