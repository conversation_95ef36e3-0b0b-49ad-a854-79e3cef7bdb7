using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.Snapshots;

public class PlaygroundRecommendReplySnapshotDto : IHasCreatedAt
{
    [JsonProperty("conversation_context")]
    public string ConversationContext { get; set; }

    [JsonProperty("knowledge_base_entries")]
    public string KnowledgeBaseEntries { get; set; }

    [JsonProperty("output_message")]
    public string OutputMessage { get; set; }

    [JsonProperty("collaboration_mode")]
    public string CollaborationMode { get; set; }

    [JsonProperty("playground_session_id")]
    public string? PlaygroundSessionId { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonConstructor]
    public PlaygroundRecommendReplySnapshotDto(
        string conversationContext,
        string knowledgeBaseEntries,
        string outputMessage,
        string collaborationMode,
        string? playgroundSessionId,
        DateTimeOffset createdAt)
    {
        ConversationContext = conversationContext;
        KnowledgeBaseEntries = knowledgeBaseEntries;
        OutputMessage = outputMessage;
        CollaborationMode = collaborationMode;
        PlaygroundSessionId = playgroundSessionId;
        CreatedAt = createdAt;
    }

    public PlaygroundRecommendReplySnapshotDto(PlaygroundRecommendReplySnapshot snapshot, DateTimeOffset createdAt)
        : this(
            snapshot.ConversationContext,
            snapshot.KnowledgeBaseEntries,
            snapshot.OutputMessage,
            snapshot.CollaborationMode,
            snapshot.PlaygroundSessionId,
            createdAt)
    {
    }
}