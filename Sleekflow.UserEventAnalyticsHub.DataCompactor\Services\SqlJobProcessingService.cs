using System.Data.Common;
using System.Globalization;
using System.Text;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using CsvHelper;
using CsvHelper.Configuration;
using Dapper;
using DuckDB.NET.Data;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.JsonConfigs;
using Sleekflow.Locks;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Configs;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Extensions;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Utils;
using Sleekflow.UserEventHub.Models.Cores;
using Sleekflow.UserEventHub.Models.SqlJobs;

namespace Sleekflow.UserEventAnalyticsHub.DataCompactor.Services;

public interface ISqlJobProcessingService
{
    Task ProcessSqlJobAsync(string jobId, string sleekflowCompanyId);

    Task<List<Dictionary<string, object>>> ProcessShortSqlJobAsync(
        string jobId,
        string sleekflowCompanyId,
        CancellationToken cancellationToken);
}

public class SqlJobProcessingService : ISqlJobProcessingService, IScopedService
{
    private readonly ILogger<SqlJobProcessingService> _logger;
    private readonly ICompactorConfig _compactorConfig;
    private readonly IPostgreSqlConfig _postgresConfig;
    private readonly ISqlJobService _sqlJobService;
    private readonly ILockService _lockService;

    private const int BufferSize = 1024 * 1024; // 1MB buffer
    private const int MaxSizeInBytes = 20 * 1024 * 1024; // 20MB limit

    public SqlJobProcessingService(
        ILogger<SqlJobProcessingService> logger,
        ICompactorConfig compactorConfig,
        IPostgreSqlConfig postgresConfig,
        ISqlJobService sqlJobService,
        ILockService lockService)
    {
        _logger = logger;
        _compactorConfig = compactorConfig;
        _postgresConfig = postgresConfig;
        _sqlJobService = sqlJobService;
        _lockService = lockService;
    }

    public async Task ProcessSqlJobAsync(string jobId, string sleekflowCompanyId)
    {
        var job = await _sqlJobService.GetOrDefaultAsync(jobId, sleekflowCompanyId);
        if (job == null)
        {
            _logger.LogInformation("Job {JobId} in Company {SleekflowCompanyId} not found", jobId, sleekflowCompanyId);
            return;
        }

        var @lock = await _lockService.LockAsync(
            [
                nameof(SqlJobProcessingService),
                nameof(ProcessSqlJobAsync),
                jobId
            ],
            TimeSpan.FromMinutes(15));
        if (@lock is null)
        {
            return;
        }

        await _sqlJobService.SetRunningAsync(job);

        try
        {
            // Initialize DuckDB with PostgreSQL connection
            await using var duckDbConnection = new DuckDBConnection("DataSource=:memory:;memory_limit=1536MB");
            await duckDbConnection.OpenAsync();

            var userProfileIds = job.Conditions.Count == 0 ? null : await GetUserProfileIdsAsync(job);

            await SetUpEventsDbAsync(
                sleekflowCompanyId,
                userProfileIds,
                duckDbConnection);

            await using var dbDataReader = await duckDbConnection.ExecuteReaderAsync(
                new CommandDefinition(
                    job.SqlQuery,
                    commandTimeout: 300));

            var blobId = GetResultBlobId(jobId, sleekflowCompanyId);

            await WriteToBlobAsync(dbDataReader, blobId);

            await _sqlJobService.CompleteJobAsync(
                job,
                blobId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing job {JobId}", job.Id);

            await _sqlJobService.FailJobAsync(
                job,
                ex.Message);
        }
        finally
        {
            if (@lock is not null)
            {
                await _lockService.ReleaseAsync(@lock);
            }
        }
    }

    public async Task<List<Dictionary<string, object>>> ProcessShortSqlJobAsync(
        string jobId,
        string sleekflowCompanyId,
        CancellationToken cancellationToken)
    {
        var job = await _sqlJobService.GetOrDefaultAsync(jobId, sleekflowCompanyId);
        if (job == null)
        {
            _logger.LogInformation("Job {JobId} in Company {SleekflowCompanyId} not found", jobId, sleekflowCompanyId);
            return new List<Dictionary<string, object>>();
        }

        var @lock = await _lockService.LockAsync(
            [
                nameof(SqlJobProcessingService),
                nameof(ProcessSqlJobAsync),
                jobId
            ],
            TimeSpan.FromMinutes(15));
        if (@lock is null)
        {
            return new List<Dictionary<string, object>>();
        }

        await _sqlJobService.SetRunningAsync(job);

        try
        {
            // Initialize DuckDB with PostgreSQL connection
            await using var duckDbConnection = new DuckDBConnection("DataSource=:memory:;memory_limit=1536MB");
            await duckDbConnection.OpenAsync(cancellationToken);

            var userProfileIds = job.Conditions.Count == 0 ? null : await GetUserProfileIdsAsync(job);

            await SetUpEventsDbAsync(
                sleekflowCompanyId,
                userProfileIds,
                duckDbConnection);

            await using var dbDataReader = await duckDbConnection.ExecuteReaderAsync(
                new CommandDefinition(
                    job.SqlQuery,
                    commandTimeout: 300));

            var records = await WriteToListAsync(dbDataReader);

            await _sqlJobService.CompleteJobAsync(
                job,
                null);

            return records;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing job {JobId}", job.Id);

            await _sqlJobService.FailJobAsync(
                job,
                ex.Message);
        }
        finally
        {
            if (@lock is not null)
            {
                await _lockService.ReleaseAsync(@lock);
            }
        }

        return new List<Dictionary<string, object>>();
    }

    private async Task<List<string>?> GetUserProfileIdsAsync(SqlJob job)
    {
        // TODO: For testing purposes, we'll skip user profile filtering
        // In a production environment, this would need to be implemented
        // with proper integration to the core system
        _logger.LogWarning("User profile filtering is not implemented in this test version");

        await Task.CompletedTask; // Remove async warning
        return null;
    }

    private async Task SetUpEventsDbAsync(
        string sleekflowCompanyId,
        List<string>? userProfileIds,
        DuckDBConnection duckDbConnection)
    {
        _logger.LogInformation("Setting up DuckDB with PostgreSQL backend for company: {CompanyId}", sleekflowCompanyId);

        // Configure DuckDB with PostgreSQL connection
        await DuckDbExtensions.ConfigureDuckDbAsync(duckDbConnection, _compactorConfig, _postgresConfig, _logger);

        // Create efficient user_profile_ids table with proper indexing if needed
        if (userProfileIds is not null && userProfileIds.Count > 0)
        {
            await using var userProfileTableCommand = duckDbConnection.CreateCommand();
            userProfileTableCommand.CommandText =
                """
                CREATE TEMPORARY TABLE user_profile_ids (
                    user_profile_id VARCHAR PRIMARY KEY
                );
                """;
            await userProfileTableCommand.ExecuteNonQueryAsync();

            // Insert user_profile_ids efficiently
            using (var appender = duckDbConnection.CreateAppender("user_profile_ids"))
            {
                foreach (var id in userProfileIds)
                {
                    var row = appender.CreateRow();
                    row.AppendValue(id).EndRow();
                }
            }

            _logger.LogInformation("Created user_profile_ids table with {Count} entries", userProfileIds.Count);
        }

        // Create optimized view with PostgreSQL backend
        await using var viewCommand = duckDbConnection.CreateCommand();

        var eventsTableName = PostgreSqlTableNameHelper.GetEventsTableName(sleekflowCompanyId);
        var userProfileJoin = userProfileIds is null || userProfileIds.Count == 0
            ? string.Empty
            : "JOIN user_profile_ids u ON u.user_profile_id = e.objectId";

        var sql = $"""
            CREATE VIEW events AS
            SELECT
                e.id,
                e.eventType,
                e.metadata,
                e.objectId,
                e.objectType,
                e.properties,
                e.sleekflowCompanyId,
                e.source,
                to_timestamp(e.timestamp / 1000.0) AS timestamp,
                e.year,
                e.month,
                e.day,
                e.hour
            FROM postgres_db.{eventsTableName} e
            {userProfileJoin};
            """;

        viewCommand.CommandText = sql;
        await viewCommand.ExecuteNonQueryAsync();

        _logger.LogInformation("Created events view for PostgreSQL table: {TableName}", eventsTableName);
    }

    private async Task WriteToBlobAsync(DbDataReader dbDataReader, string blobId)
    {
        var blobClient = new BlobContainerClient(_compactorConfig.StorageConnStr, _compactorConfig.ResultsContainerName)
            .GetBlobClient(blobId);

        var csvWriterConfiguration = new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            NewLine = Environment.NewLine,
            HasHeaderRecord = true,
            BufferSize = BufferSize
        };

        // Open blob stream for writing
        await using var blobStream = await blobClient.OpenWriteAsync(
            true,
            new BlobOpenWriteOptions
            {
                BufferSize = BufferSize
            });
        await using var streamWriter = new StreamWriter(blobStream, Encoding.UTF8, BufferSize);
        await using var csvWriter = new CsvWriter(streamWriter, csvWriterConfiguration);

        var recordCount = 0;
        var header = false;

        while (await dbDataReader.ReadAsync())
        {
            if (!header)
            {
                // Write headers only once
                for (int i = 0; i < dbDataReader.FieldCount; i++)
                {
                    csvWriter.WriteField(dbDataReader.GetName(i));
                }

                await csvWriter.NextRecordAsync();
                header = true;
            }

            // Write record
            for (int i = 0; i < dbDataReader.FieldCount; i++)
            {
                csvWriter.WriteField(dbDataReader.GetValue(i));
            }

            await csvWriter.NextRecordAsync();
            recordCount++;

            // Periodically flush to ensure data is written
            if (recordCount % 1000 == 0)
            {
                await streamWriter.FlushAsync();
            }

            // Check size limit
            if (blobStream.Position >= MaxSizeInBytes)
            {
                _logger.LogInformation("Data size exceeds maximum limit of {MaxSizeInBytes} bytes", MaxSizeInBytes);
                break;
            }
        }

        // Final flush
        await streamWriter.FlushAsync();

        _logger.LogInformation("Successfully wrote {RecordCount} records to blob: {BlobId}", recordCount, blobId);
    }

    private async Task<List<Dictionary<string, object>>> WriteToListAsync(DbDataReader dbDataReader)
    {
        var results = new List<Dictionary<string, object>>();
        var columnNames = new List<string>();
        var recordCount = 0;

        // Get column names
        for (int i = 0; i < dbDataReader.FieldCount; i++)
        {
            columnNames.Add(dbDataReader.GetName(i));
        }

        // Stream records directly from database
        while (await dbDataReader.ReadAsync())
        {
            var record = new Dictionary<string, object>();

            // Write record
            for (int i = 0; i < dbDataReader.FieldCount; i++)
            {
                record[columnNames[i]] = dbDataReader.GetValue(i);
            }

            results.Add(record);
            recordCount++;

            // Check size limit (optional)
            const int maxRecords = 1000;
            if (recordCount >= maxRecords)
            {
                _logger.LogInformation("Record count exceeds maximum limit of {MaxRecords} records", maxRecords);
                break;
            }
        }

        _logger.LogInformation("Successfully processed {RecordCount} records in memory", recordCount);
        return results;
    }

    private static string GetResultBlobId(string jobId, string sleekflowCompanyId)
    {
        var blobId = $"sleekflowCompanyId={sleekflowCompanyId}/jobId={jobId}/result.csv";
        return blobId;
    }
}