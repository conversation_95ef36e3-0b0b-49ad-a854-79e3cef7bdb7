﻿using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;

namespace Sleekflow.IntelligentHub.Models.Snapshots;

public class PlaygroundRecommendReplySnapshot : AgentRecordUsageSnapshot
{
    [JsonProperty("conversation_context")]
    public string ConversationContext { get; set; }

    [JsonProperty("knowledge_base_entries")]
    public string KnowledgeBaseEntries { get; set; }

    [JsonProperty("output_message")]
    public string OutputMessage { get; set; }

    [JsonProperty("token_counts")]
    public Dictionary<string, int> TokenCounts { get; set; }

    [JsonProperty("collaboration_mode")]
    public string CollaborationMode { get; set; }

    [JsonProperty("playground_session_id")]
    public string PlaygroundSessionId { get; set; }

    [JsonConstructor]
    public PlaygroundRecommendReplySnapshot(
        string conversationContext,
        string knowledgeBaseEntries,
        string outputMessage,
        Dictionary<string, int> tokenCounts,
        string collaborationMode,
        string agentId,
        string? agentVersionedId,
        string playgroundSessionId)
        : base(agentId, agentVersionedId)
    {
        ConversationContext = conversationContext;
        KnowledgeBaseEntries = knowledgeBaseEntries;
        OutputMessage = outputMessage;
        TokenCounts = tokenCounts;
        CollaborationMode = collaborationMode;
        PlaygroundSessionId = playgroundSessionId;
    }
}