﻿using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs.Snapshot;

public interface ICompanyAgentConfigSnapshotRepository : IRepository<CompanyAgentConfigSnapshot>
{
}

public class CompanyAgentConfigSnapshotRepository
    : BaseRepository<CompanyAgentConfigSnapshot>, ICompanyAgentConfigSnapshotRepository, IScopedService
{
    public CompanyAgentConfigSnapshotRepository(
        ILogger<BaseRepository<CompanyAgentConfigSnapshot>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}