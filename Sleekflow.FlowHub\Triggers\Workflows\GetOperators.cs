using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Workflows.Operators;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class GetOperators : ITrigger
{
    private readonly IOperatorService _operatorService;

    public GetOperators(IOperatorService operatorService)
    {
        _operatorService = operatorService;
    }

    public class GetOperatorsInput
    {
        [JsonProperty("variable_type")]
        [Required]
        public string VariableType { get; set; }

        [JsonProperty("trigger_id")]
        public string? TriggerId { get; set; }

        [JsonConstructor]
        public GetOperatorsInput(
            string variableType,
            string? triggerId)
        {
            VariableType = variableType;
            TriggerId = triggerId;
        }
    }

    public class OperatorDto
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("variable_type")]
        public string VariableType { get; set; }

        [JsonProperty("operator")]
        public string OperatorValue { get; set; }

        [JsonProperty("ui_copy")]
        public string UiCopy { get; set; }

        [JsonProperty("trigger_ids")]
        public List<string> TriggerIds { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonConstructor]
        public OperatorDto(
            string id,
            string variableType,
            string operatorValue,
            string uiCopy,
            List<string> triggerIds,
            string status)
        {
            Id = id;
            VariableType = variableType;
            OperatorValue = operatorValue;
            UiCopy = uiCopy;
            TriggerIds = triggerIds;
            Status = status;
        }
    }

    public class GetOperatorsOutput
    {
        [JsonProperty("operators")]
        public List<OperatorDto> Operators { get; set; }

        [JsonConstructor]
        public GetOperatorsOutput(List<OperatorDto> operators)
        {
            Operators = operators;
        }
    }

    public async Task<GetOperatorsOutput> F(GetOperatorsInput input)
    {
        var operators = await _operatorService.GetOperatorsAsync(
            input.VariableType,
            input.TriggerId);

        var operatorDtos = operators.Select(
            x => new OperatorDto(
                x.Id,
                x.VariableType,
                x.OperatorValue,
                x.UiCopy,
                x.TriggerIds,
                x.Status)).ToList();

        return new GetOperatorsOutput(operatorDtos);
    }
}