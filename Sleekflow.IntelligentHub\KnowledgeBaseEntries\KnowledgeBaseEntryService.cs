using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Documents.FileDocuments;
using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.KnowledgeBaseEntries;

namespace Sleekflow.IntelligentHub.KnowledgeBaseEntries;

public interface IKnowledgeBaseEntryService
{
    Task<(List<KnowledgeBaseEntry> KnowledgeBaseEntries, string? NextContinuationToken)>
        GetKnowledgeBaseEntriesAsync(
            string sleekflowCompanyId,
            GetKnowledgeBaseEntriesFilters filters,
            string? continuationToken,
            int limit);

    Task<KnowledgeBaseEntry> GetKnowledgeBaseEntryAsync(
        string sleekflowCompanyId,
        string knowledgeBaseEntryId);

    Task<KnowledgeBaseEntry> CreateKnowledgeBaseEntryAsync(
        string sleekflowCompanyId,
        KnowledgeBaseEntrySource knowledgeBaseEntrySource,
        string chunkId,
        string content,
        string contentEn,
        List<Category> categories);

    Task DeleteKnowledgeBaseEntriesByDocumentIdAsync(string sleekflowCompanyId, string documentId);
}

public class KnowledgeBaseEntryService : IScopedService, IKnowledgeBaseEntryService
{
    private readonly IKnowledgeBaseEntryRepository _knowledgeBaseEntryRepository;
    private readonly IIdService _idService;
    private readonly IFileDocumentChunkService _fileDocumentChunkService;

    public KnowledgeBaseEntryService(
        IKnowledgeBaseEntryRepository knowledgeBaseEntryRepository,
        IIdService idService,
        IFileDocumentChunkService fileDocumentChunkService)
    {
        _knowledgeBaseEntryRepository = knowledgeBaseEntryRepository;
        _idService = idService;
        _fileDocumentChunkService = fileDocumentChunkService;
    }

    public async Task<(List<KnowledgeBaseEntry> KnowledgeBaseEntries, string? NextContinuationToken)>
        GetKnowledgeBaseEntriesAsync(
            string sleekflowCompanyId,
            GetKnowledgeBaseEntriesFilters filters,
            string? continuationToken,
            int limit)
    {
        return await _knowledgeBaseEntryRepository.GetKnowledgeBaseEntriesByDocumentIdAsync(
            sleekflowCompanyId,
            filters.SourceId,
            filters.SourceType,
            continuationToken,
            limit);
    }

    public async Task DeleteKnowledgeBaseEntriesByChunkIdsAsync(string sleekflowCompanyId, List<string> chunkIds)
    {
        var entryIds =
            await _knowledgeBaseEntryRepository.GetKnowledgeBaseEntryIdByChunkIdsAsync(sleekflowCompanyId, chunkIds);

        await _knowledgeBaseEntryRepository.DeleteAsync(entryIds, sleekflowCompanyId);
    }

    public async Task DeleteKnowledgeBaseEntriesByDocumentIdAsync(string sleekflowCompanyId, string documentId)
    {
        var fileDocumentChunkIds =
            await _fileDocumentChunkService.GetFileDocumentChunkIdsAsync(sleekflowCompanyId, documentId);
        await _fileDocumentChunkService.DeleteFileDocumentChunkAsync(fileDocumentChunkIds, sleekflowCompanyId);

        await DeleteKnowledgeBaseEntriesByChunkIdsAsync(
            sleekflowCompanyId,
            fileDocumentChunkIds);
    }

    public async Task<KnowledgeBaseEntry> GetKnowledgeBaseEntryAsync(
        string sleekflowCompanyId,
        string knowledgeBaseEntryId)
    {
        return await _knowledgeBaseEntryRepository.GetAsync(knowledgeBaseEntryId, sleekflowCompanyId);
    }

    public async Task<KnowledgeBaseEntry> CreateKnowledgeBaseEntryAsync(
        string sleekflowCompanyId,
        KnowledgeBaseEntrySource knowledgeBaseEntrySource,
        string chunkId,
        string content,
        string contentEn,
        List<Category> categories)
    {
        var entries = await _knowledgeBaseEntryRepository.GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId
                && c.ChunkId == chunkId);
        if (entries.Any())
        {
            return entries.First();
        }

        var knowledgeBaseEntry = await _knowledgeBaseEntryRepository.CreateAndGetAsync(
            new KnowledgeBaseEntry(
                _idService.GetId(SysTypeNames.KnowledgeBaseEntry),
                sleekflowCompanyId,
                chunkId,
                content,
                contentEn,
                knowledgeBaseEntrySource,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                categories,
                new List<string>
                {
                    "Active"
                }),
            sleekflowCompanyId);

        return knowledgeBaseEntry;
    }
}