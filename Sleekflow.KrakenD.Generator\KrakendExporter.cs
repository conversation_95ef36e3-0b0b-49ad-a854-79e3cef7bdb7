using System.Text.RegularExpressions;

namespace Sleekflow.KrakenD.Generator;

internal class KrakendExporter
{
    private readonly string _fileExtension;
    private readonly List<Endpoint> _endpoints;
    private readonly bool _shouldLog;

    public KrakendExporter(
        string fileExtension,
        List<Endpoint> endpoints,
        bool shouldLog = true)
    {
        _endpoints = endpoints;
        _shouldLog = shouldLog;
        _fileExtension = fileExtension;
    }

    public void Export()
    {
        var krakendDocument = new KrakendDocument
        {
            Version = 3,
            Timeout = "60000ms",
            Endpoints = _endpoints.ToArray(),
            ExtraConfig = new KrakendDocumentExtraConfig
            {
                TelemetryLogging = new ImprovedLogging
                {
                    Level = LogLevel.Info, Prefix = "[KRAKEND]", Stdout = _shouldLog, Syslog = false,
                },
                Router = new RouterFlags()
                {
                    AutoOptions = true,
                    HideVersionHeader = true,
                    LoggerSkipPaths =
                    [
                        "/__health",
                        "/v1/tenant-hub/management/Rbac/IsRbacEnabled",
                        "/v1/audit-hub/SystemAuditLogs/CreateSystemAuditLog",
                        "/v1/user-event-hub/SignalRWebhook",
                        "/v1/user-event-hub/Messages/SendMessageToUsers",
                        "/v1/user-event-hub/Notifications/SendPushNotification",
                        "/v1/user-event-hub/ReliableMessage/negotiate",
                        "/v1/flow-hub/internals/CheckWorkflowContactEnrolmentConditionV2",
                    ]
                }
            },
            Plugin = new Plugin()
            {
                Pattern = ".so", Folder = "./plugins/"
            }
        };
        var krakendJson = krakendDocument.ToJson();
        var unescapedKrakendJson = krakendJson
            .Replace(@"\", string.Empty);

        var path = $"../Sleekflow.KrakenD/{_fileExtension}";
        if (File.Exists(path))
        {
            // Export the krakend.json
            var existingKrakendJson = File.ReadAllText(path);
            if (Regex.Replace(existingKrakendJson, @"\r\n|\n\r|\n|\r", "\r\n")
                != Regex.Replace(unescapedKrakendJson, @"\r\n|\n\r|\n|\r", "\r\n"))
            {
                Console.WriteLine(
                    $"::error title=KrakenDValidator::Please generate the latest krakend.json and commit it.");
            }
        }

        File.WriteAllText(_fileExtension, unescapedKrakendJson);
    }
}