# Development Standards

This document is part of [Sleekflow Project Practices](ProjectPractices.md).

## C# Coding Standards

Sleekflow follows specific coding standards enforced by linting tools and custom analyzers:

### Code Style

The project uses a combination of StyleCop, SonarAnalyzer, and custom analyzers to enforce consistent code style across the codebase. Key standards include:

- Four-space indentation for C# files
- Maximum line length of 120 characters
- PascalCase for class names, methods, and public members
- Braces placement follows C# standard (new line for braces)
- Expression-bodied members preferred for simple properties and accessors

These rules are enforced via `.editorconfig` and custom analyzers in the `Sleekflow.Analyzers` project.

### Nullability

The project leverages C# nullability features for better null handling:

```csharp
// Example of properly handled nullability
public class Request
{
    public string Id { get; set; }
    public string MachineName { get; set; }
    public DateTime StartTime { get; set; }
    public string Path { get; set; }
    public long? ElapsedMilliseconds { get; set; }
    public object? Input { get; set; }
    public object? Output { get; set; }
    public string? ExceptionStackTrace { get; set; }
    public bool? Success { get; set; }
}
```

### Code Formatting

For formatted code blocks (especially collections and JSON), the codebase uses formatter directives:

```csharp
// @formatter:off
var languageDefaultUnifyRules = new List<UnifyRule>
{
    new UnifyRule("Code", "time", new List<string> { "d365:new_code" }, true),
    new UnifyRule("Name", "time", new List<string> { "d365:new_name" }, true),
};
// @formatter:on
```