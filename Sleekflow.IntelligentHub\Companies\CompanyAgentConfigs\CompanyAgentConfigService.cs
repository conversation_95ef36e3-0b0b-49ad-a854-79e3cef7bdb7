using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs.Snapshot;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.Models.Prompts;
using Sleekflow.Persistence;

namespace Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;

public interface ICompanyAgentConfigService
{
    Task<CompanyAgentConfig> CreateAndGetAsync(
        string name,
        string type,
        string sleekflowCompanyId,
        bool isChatHistoryEnabledAsContext,
        bool isContactPropertiesEnabledAsContext,
        int numberOfPreviousMessagesInChatHistoryAvailableAsContext,
        string? channelType,
        string? channelId,
        string? collaborationMode,
        LeadNurturingTools? leadNurturingTools,
        ToolsConfig? toolsConfig,
        List<EnricherConfig>? enricherConfigs,
        KnowledgeRetrievalConfig? knowledgeRetrievalConfig,
        string sleekflowStaffId,
        List<string>? sleekflowTeamIds,
        string? description = null,
        CompanyAgentConfigActions? actions = null,
        PromptInstruction? promptInstruction = null,
        string? editMode = null);

    Task<List<CompanyAgentConfig>> GetObjectsAsync(string sleekflowCompanyId);

    Task<CompanyAgentConfig?> GetOrDefaultAsync(string id, string sleekflowCompanyId);

    Task DeleteAsync(string id, string sleekflowCompanyId, string eTag);

    Task<CompanyAgentConfig> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        string eTag,
        string sleekflowStaffId,
        List<string>? sleekflowTeamIds,
        string? name = null,
        PromptInstruction? promptInstruction = null,
        bool? isChatHistoryEnabledAsContext = null,
        bool? isContactPropertiesEnabledAsContext = null,
        int? numberOfPreviousMessagesInChatHistoryAvailableAsContext = null,
        string? channelType = null,
        string? channelId = null,
        string? description = null,
        string? collaborationMode = null,
        string? type = null,
        LeadNurturingTools? leadNurturingTools = null,
        ToolsConfig? toolsConfig = null,
        List<EnricherConfig>? enricherConfigs = null,
        KnowledgeRetrievalConfig? knowledgeRetrievalConfig = null,
        CompanyAgentConfigActions? actions = null,
        DateTimeOffset? firstWorkflowPublishedAt = null,
        string? editMode = null);
}

public class CompanyAgentConfigService : ICompanyAgentConfigService, IScopedService
{
    private readonly ILogger _logger;
    private readonly IIdService _idService;
    private readonly ICompanyAgentConfigRepository _companyAgentConfigRepository;
    private readonly ICompanyAgentConfigSnapshotService _snapshotService;

    public CompanyAgentConfigService(
        IIdService idService,
        ILogger<CompanyAgentConfigService> logger,
        ICompanyAgentConfigRepository companyAgentConfigRepository,
        ICompanyAgentConfigSnapshotService snapshotService)
    {
        _logger = logger;
        _idService = idService;
        _companyAgentConfigRepository = companyAgentConfigRepository;
        _snapshotService = snapshotService;
    }

    public async Task<CompanyAgentConfig> CreateAndGetAsync(
        string name,
        string type,
        string sleekflowCompanyId,
        bool isChatHistoryEnabledAsContext,
        bool isContactPropertiesEnabledAsContext,
        int numberOfPreviousMessagesInChatHistoryAvailableAsContext,
        string? channelType,
        string? channelId,
        string? collaborationMode,
        LeadNurturingTools? leadNurturingTools,
        ToolsConfig? toolsConfig,
        List<EnricherConfig>? enricherConfigs,
        KnowledgeRetrievalConfig? knowledgeRetrievalConfig,
        string sleekflowStaffId,
        List<string>? sleekflowTeamIds,
        string? description = null,
        CompanyAgentConfigActions? actions = null,
        PromptInstruction? promptInstruction = null,
        string? editMode = null)
    {
        var createdTime = DateTimeOffset.UtcNow;
        var staff = new AuditEntity.SleekflowStaff(
            sleekflowStaffId: sleekflowStaffId,
            sleekflowTeamIds: sleekflowTeamIds);

        var id = _idService.GetId(typeName: SysTypeNames.CompanyAgentConfig);

        var config = new CompanyAgentConfig(
            id: id,
            name: name,
            sleekflowCompanyId: sleekflowCompanyId,
            isChatHistoryEnabledAsContext: isChatHistoryEnabledAsContext,
            isContactPropertiesEnabledAsContext: isContactPropertiesEnabledAsContext,
            numberOfPreviousMessagesInChatHistoryAvailableAsContext: numberOfPreviousMessagesInChatHistoryAvailableAsContext,
            channelType: channelType,
            channelId: channelId,
            description: description,
            promptInstruction: promptInstruction,
            type: type,
            collaborationMode: collaborationMode ?? AgentCollaborationModes.Default,
            leadNurturingTools: leadNurturingTools,
            createdAt: createdTime,
            updatedAt: createdTime,
            createdBy: staff,
            updatedBy: staff,
            eTag: null,
            enricherConfigs: enricherConfigs,
            knowledgeRetrievalConfig: knowledgeRetrievalConfig,
            toolsConfig: toolsConfig,
            actions: actions,
            isTranscriptionEnabled: false,
            editMode: editMode ?? AgentEditModes.Default,
            agentVersionedId: _idService.GetId(typeName: "AgentVersionedId", parentId: id));

        _logger.LogInformation(message: "Creating companyAgentConfig with id {Config}", args: JsonConvert.SerializeObject(value: config));

        return await _companyAgentConfigRepository.CreateAndGetAsync(obj: config, partitionKey: sleekflowCompanyId);
    }

    public async Task<List<CompanyAgentConfig>> GetObjectsAsync(string sleekflowCompanyId)
    {
        return await _companyAgentConfigRepository.GetObjectsAsync(predicate: c => c.SleekflowCompanyId == sleekflowCompanyId);
    }

    public async Task<CompanyAgentConfig?> GetOrDefaultAsync(string id, string sleekflowCompanyId)
    {
        return await _companyAgentConfigRepository.GetOrDefaultAsync(id: id, partitionKey: sleekflowCompanyId);
    }

    public async Task DeleteAsync(string id, string sleekflowCompanyId, string eTag)
    {
        await _companyAgentConfigRepository.DeleteAsync(id: id, partitionKey: sleekflowCompanyId, eTag: eTag);
    }

    public async Task<CompanyAgentConfig> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        string eTag,
        string sleekflowStaffId,
        List<string>? sleekflowTeamIds,
        string? name = null,
        PromptInstruction? promptInstruction = null,
        bool? isChatHistoryEnabledAsContext = null,
        bool? isContactPropertiesEnabledAsContext = null,
        int? numberOfPreviousMessagesInChatHistoryAvailableAsContext = null,
        string? channelType = null,
        string? channelId = null,
        string? description = null,
        string? collaborationMode = null,
        string? type = null,
        LeadNurturingTools? leadNurturingTools = null,
        ToolsConfig? toolsConfig = null,
        List<EnricherConfig>? enricherConfigs = null,
        KnowledgeRetrievalConfig? knowledgeRetrievalConfig = null,
        CompanyAgentConfigActions? actions = null,
        DateTimeOffset? firstWorkflowPublishedAt = null,
        string? editMode = null)
    {
        _logger.LogInformation(
            message: "Patching companyAgentConfig with id {Id} {CompanyId} {SleekflowStaffId} {SleekflowTeamIds} {IsChatHistoryEnabledAsContext} {IsContactPropertiesEnabledAsContext} {NumberOfPreviousMessagesInChatHistoryAvailableAsContext} {CollaborationMode} {Type} {Actions}",
            args: [id, sleekflowCompanyId, sleekflowStaffId, JsonConvert.SerializeObject(value: sleekflowTeamIds), isChatHistoryEnabledAsContext, isContactPropertiesEnabledAsContext, numberOfPreviousMessagesInChatHistoryAvailableAsContext, collaborationMode, type, JsonConvert.SerializeObject(value: actions)]);

        // Retrieve the existing configuration
        var existingConfig = await _companyAgentConfigRepository.GetAsync(id: id, partitionKey: sleekflowCompanyId);

        // Verify ETag for optimistic concurrency control
        if (existingConfig.ETag != eTag)
        {
            throw new InvalidOperationException(message: "ETag mismatch. The object has been modified by another process.");
        }

        // Create a snapshot before patching
        var updatedStaff = new AuditEntity.SleekflowStaff(sleekflowStaffId: sleekflowStaffId, sleekflowTeamIds: sleekflowTeamIds);
        await _snapshotService.CreateSnapshotAsync(config: existingConfig, operationType: "Patch", createdBy: updatedStaff);

        // Update the properties that are provided (non-null values)
        var updatedTime = DateTimeOffset.UtcNow;

        var updatedConfig = new CompanyAgentConfig(
            id: existingConfig.Id,
            name: name ?? existingConfig.Name,
            sleekflowCompanyId: existingConfig.SleekflowCompanyId,
            isChatHistoryEnabledAsContext: isChatHistoryEnabledAsContext ?? existingConfig.IsChatHistoryEnabledAsContext,
            isContactPropertiesEnabledAsContext: isContactPropertiesEnabledAsContext ?? existingConfig.IsContactPropertiesEnabledAsContext,
            numberOfPreviousMessagesInChatHistoryAvailableAsContext: numberOfPreviousMessagesInChatHistoryAvailableAsContext ??
                                                                     existingConfig.NumberOfPreviousMessagesInChatHistoryAvailableAsContext,
            channelType: channelType ?? existingConfig.ChannelType,
            channelId: channelId ?? existingConfig.ChannelId,
            description: description ?? existingConfig.Description,
            promptInstruction: promptInstruction ?? existingConfig.PromptInstruction,
            type: type ?? existingConfig.Type,
            collaborationMode: collaborationMode ?? existingConfig.CollaborationMode,
            leadNurturingTools: leadNurturingTools ?? existingConfig.LeadNurturingTools,
            createdAt: existingConfig.CreatedAt,
            updatedAt: updatedTime,
            createdBy: existingConfig.CreatedBy,
            updatedBy: updatedStaff,
            eTag: eTag,
            enricherConfigs: enricherConfigs ?? existingConfig.EnricherConfigs,
            knowledgeRetrievalConfig: knowledgeRetrievalConfig ?? existingConfig.KnowledgeRetrievalConfig,
            toolsConfig: toolsConfig ?? existingConfig.ToolsConfig,
            actions: actions ?? existingConfig.Actions,
            firstWorkflowPublishedAt: firstWorkflowPublishedAt ?? existingConfig.FirstWorkflowPublishedAt,
            isTranscriptionEnabled: existingConfig.IsTranscriptionEnabled,
            editMode: editMode ?? existingConfig.EditMode,
            agentVersionedId: _idService.GetId(typeName: "AgentVersionedId", parentId: existingConfig.Id));

        // Upsert the updated configuration
        return await _companyAgentConfigRepository.UpsertAndGetAsync(obj: updatedConfig, partitionKey: sleekflowCompanyId);
    }
}