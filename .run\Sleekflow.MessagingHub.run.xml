﻿<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Sleekflow.MessagingHub" type="DotNetProject" factoryName=".NET Project">
    <option name="EXE_PATH" value="$PROJECT_DIR$/Sleekflow.MessagingHub/bin/Debug/net8.0/Sleekflow.MessagingHub.dll" />
    <option name="PROGRAM_PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Sleekflow.MessagingHub" />
    <option name="PASS_PARENT_ENVS" value="1" />
    <envs>
      <env name="SLEEKFLOW_BACKEND_AES256_KEY" value="cRfUjXn2r5u8x/A?D(G-KaPdSgVkYp3s" />
      <env name="ASPNETCORE_ENVIRONMENT" value="Development" />
      <env name="WORKER_FUNCTIONS_KEY" value="PLACEHOLDER" />
      <env name="COSMOS_MESSAGING_HUB_DB_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="FACEBOOK_WEBHOOK_VERIFICATION_TOKEN" value="n2r5u8xdAqD1GsKaPdSgVkYp3s6v9y4BdE6HAMbQeThWmZq4t7wGzACFOJaNdRfVE1HDMbQeShVmYq3t6w9zdC3F1JANcRfUjWnZr4u7xLAXDGoKaPdSgVkYp2s5VK" />
      <env name="WORKER_HOSTNAME" value="http://localhost:7078" />
      <env name="GET_BLAST_MESSAGES_STORAGE_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=s08b501d0;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="COSMOS_KEY" value="****************************************************************************************" />
      <env name="INTERNAL_STORAGE_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=s91c10634;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="EXTERNAL_STORAGE_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=d6i4c38s4s;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="FACEBOOK_SYSTEM_USER_ID" value="***************" />
      <env name="LOGGER_WORKSPACE_ID" value="f0ea3579-8e0a-483f-81bb-62617cdd75a6" />
      <env name="LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED" value="FALSE" />
      <env name="LOGGER_GOOGLE_CLOUD_PROJECT_ID" value="cool-phalanx-404402" />
      <env name="LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON" value="************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" />
      <env name="FACEBOOK_WABA_USER_LONG_ACCESS_TOKEN_SECRET" value="bQeThWmZq4t7w!z%C&amp;F)J@NcRfUjXnCA" />
      <env name="FACEBOOK_BUSINESS_INTEGRATION_SYSTEM_USER_ACCESS_TOKEN_SECRET" value="zF%U5ZM&amp;3B_+}.V.{Z?CFq,XKmL]bXC@[J%{" />
      <env name="MESSAGE_DATA_CONTAINER_NAME" value="message-data" />
      <env name="CACHE_PREFIX" value="Sleekflow.MessagingHub" />
      <env name="FACEBOOK_APP_SECRET" value="********************************" />
      <env name="REDIS_CONN_STR" value="sleekflow-redis739dbd6c.redis.cache.windows.net:6380,password=LSpaOPbm5b308TOUYaMDQwfDVUQZV7OODAzCaBAySj0=,ssl=True,abortConnect=False" />
      <env name="FACEBOOK_SYSTEM_USER_ACCESS_TOKEN" value="EAALi12GLcZCABAMrGOK00ulr54uMCIe6T0LAf5pmmcg4ve8ZCbedzKCRcyvQNyL9FnQPeHOIEK7AKPx31z0kR28TYmG97g5ZCtyZBsmYaiToUyufnQwmHKYMRPnRVgYKrM6SOZCrmu6GwEiaL7P0KZBsjFGeLz3g6uCumT6ayrMAg0aC6s75rI" />
      <env name="COSMOS_MESSAGING_HUB_DB_DATABASE_ID" value="messaginghubdb" />
      <env name="FACEBOOK_BUSINESS_ID" value="***************" />
      <env name="MESSAGE_DATA_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=lxg8d38o3e;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="FACEBOOK_APP_ID" value="***************" />
      <env name="LOGGER_IS_LOG_ANALYTICS_ENABLED" value="FALSE" />
      <env name="STRIPE_API_KEY" value="sk_test_W6snxTtlAExO9vFjvX5fbLdq00lAxGJMuk" />
      <env name="COSMOS_MESSAGING_HUB_DB_KEY" value="****************************************************************************************" />
      <env name="COSMOS_DATABASE_ID" value="db" />
      <env name="EXTERNAL_STORAGE_CONTAINER_NAME" value="external-container" />
      <env name="ASPNETCORE_URLS" value="https://localhost:7080;http://localhost:7081" />
      <env name="COSMOS_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="FACEBOOK_BUSINESS_CREDIT_LINE_ID" value="****************" />
      <env name="PAYMENT_GATEWAY_REDIRECT_URL" value="http://localhost:7078" />
      <env name="LOGGER_AUTHENTICATION_ID" value="PxOFmtDmfRvHYCoGsuItWHqipxqn72YE0WxgLy7msPitr3TMgvFFtX1RY7yvnP6Mu+lx0HUGy+Z5Un4oshm9Lw==" />
      <env name="WHATSAPP_CLOUD_API_OVERRIDE_WEBHOOK_URL" value="https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/messaging-hub/WhatsappCloudApiWebhook" />
      <env name="SERVICE_BUS_CONN_STR" value="Endpoint=sb://sleekflow-local.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ZKDnptWyDBxBPASM36H7o+NrnyDqtK7L3+ASbPJHFzw=" />
      <env name="EVENT_HUB_CONN_STR" value="Endpoint=sb://sleekflowlocal.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=jhlCshBxrz+WK7I90i8w1GKoVTOmSaGBO+AEhDcsaYU=" />
      <env name="SF_ENV_NAME" value="dev" />
      <env name="WHATSAPP_CLOUD_API_ERROR_ALERTS_WEBHOOK_URL" value="*******************************************************************************" />
      <env name="APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED" value="FALSE" />
      <env name="APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED" value="FALSE" />
    </envs>
    <option name="USE_EXTERNAL_CONSOLE" value="0" />
    <option name="USE_MONO" value="0" />
    <option name="RUNTIME_ARGUMENTS" value="" />
    <option name="PROJECT_PATH" value="$PROJECT_DIR$/Sleekflow.MessagingHub/Sleekflow.MessagingHub.csproj" />
    <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
    <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
    <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
    <option name="PROJECT_KIND" value="Unloaded" />
    <option name="PROJECT_TFM" value="net8.0" />
    <method v="2">
      <option name="Build" />
    </method>
  </configuration>
</component>