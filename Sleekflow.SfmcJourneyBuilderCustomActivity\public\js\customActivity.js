define(["postmonger"], function (Postmonger) {
    "use strict";

    const connection = new Postmonger.Session();
    let payload = {};
    let lastStepEnabled = false;
    let steps = [
        // initialize to the same value as what's set in config.json for consistency
        { label: "Send WhatsApp Message", key: "step1" },
    ];
    let currentStep = steps[0].key;
    let eventDefinitionKey;
    const schemas = [];

    var domainName;

    $(window).ready(onRender);

    connection.on("initActivity", initialize);
    connection.on("requestedTokens", onGetTokens);
    connection.on("requestedEndpoints", onGetEndpoints);

    connection.on("clickedNext", save);
    connection.on("requestedTriggerEventDefinition", onEventTrigger);
    connection.on("requestedInteractionDefaults", onInteractionDefaults);
    connection.on("requestedSchema", onDeSchemaTrigger);

    function onRender() {
        // JB will respond the first time 'ready' is called with 'initActivity'
        connection.trigger("ready");
        connection.trigger("requestTokens");
        connection.trigger("requestEndpoints");
        connection.trigger("requestTriggerEventDefinition");
        connection.trigger("requestInteractionDefaults");
        connection.trigger("requestSchema");
    }

    function initialize(data) {
        if (data) {
            payload = data;
        }

        const hasConfiurationArguments = Boolean(
            payload['configurationArguments'] &&
            payload['configurationArguments'].save &&
            payload['configurationArguments'].save.url &&
            payload['configurationArguments'].save.url.length > 0
        );

        domainName = hasConfiurationArguments
            ? payload['configurationArguments'].save.url.split(/\/JourneyBuilderCustomActivity\/save/)[0]
            : domainName;

        const hasInArguments = Boolean(
            payload["arguments"] &&
            payload["arguments"].execute &&
            payload["arguments"].execute.inArguments &&
            payload["arguments"].execute.inArguments.length > 0
        );

        const inArguments = hasInArguments
            ? payload["arguments"].execute.inArguments
            : {};

        connection.trigger("updateButton", {
            button: "next",
            text: "done",
            visible: true,
        });
    }

    function onGetTokens(tokens) {
        const dataExtension = document.getElementById("dataExtension");
        for (let i = 0; i < schemas.length; i++) {
            const data = document.createElement("li");
            data.classList.add("list-group-item");
            data.textContent = schemas[i]["name"];
            dataExtension.appendChild(data);
        }
    }

    function onGetEndpoints(endpoints) {
    }

    class Helper {
        constructor() {}

        static replaceVariables = (inputStr) => {
            const regex = /\{\{(.+?)\}\}/g;
            const newStr = inputStr.replace(regex, (match, variableName) => {
                const dataExtension = Helper.searchSchema(variableName);
                return dataExtension === null ? match : `{{${dataExtension["key"]}}}`;
            });
            return newStr;
        };

        static sanitizeParam = (str) => {
            if (str.startsWith('{{') && str.endsWith('}}')) {
                const paramValue = str.slice(2, -2);
                return "{{Event." + eventDefinitionKey + `."${paramValue}\"}}`;
            }
            return str;
        }

        static searchSchema = (schemaName) => {
            for (const ele of schemas) {
                if (ele["name"] && (ele["name"].toLowerCase() === schemaName.toLowerCase())) {
                    return ele;
                }
            }
            return null;
        };

        static getFields = () => {
            let messageType = $("#messageType").val();
            let apiKey = $("#apiKey").val();
            let whatsappPhoneNumber = $("#whatsappPhoneNumber").val();
            let recipientPhoneNumber = $("#recipientPhoneNumber").val();
            let analyticTagsAsString = $("#analyticTags").val();
            let messageContent = $("#messageContent").val();

            messageType = Helper.replaceVariables(messageType);
            apiKey = Helper.replaceVariables(apiKey);
            whatsappPhoneNumber = Helper.replaceVariables(whatsappPhoneNumber);
            recipientPhoneNumber = Helper.replaceVariables(recipientPhoneNumber);
            analyticTagsAsString = Helper.replaceVariables(analyticTagsAsString);
            messageContent = Helper.replaceVariables(messageContent);

            let analyticTags = Helper.getAnalyticTagsAsArray(analyticTagsAsString);

            let allFields = {
                messageType,
                apiKey,
                whatsappPhoneNumber,
                recipientPhoneNumber,
                analyticTags,
                messageContent,
            };
            if (messageType === "template") {
                const payloadJoson = $("#templatePayload").val();
                allFields['templatePayload'] = JSON.parse(payloadJoson);
            }
            return allFields;
        };

        static setPayload = async () => {
            connection.trigger("requestTriggerEventDefinition");
            const allFields = Helper.getFields();
            const taskType = allFields.messageType;
            const templatePayloadParam = [];
            const fileUrl = await Helper.getUploadUrl(allFields.apiKey);

            const allInArguments = {
                From: allFields.whatsappPhoneNumber,
                MessageType: allFields.messageType,
                MessageContent: allFields.messageContent,
                ApiKey: allFields.apiKey,
                To: allFields.recipientPhoneNumber,
                Channel: "whatsappcloudapi",
                AnalyticTags: allFields.analyticTags
            };

            if (taskType === "template") {
                const components = allFields.templatePayload.whatsappCloudApiTemplateMessageObject.components;
                let header = [];
                let body = [];
                components.forEach(e=>{
                    if(e['type'].toLowerCase() === 'header'){
                        header = e['parameters']
                    }else if (e['type'].toLowerCase() === 'body'){
                        body = e['parameters']
                    }else if (e['type'].toLowerCase() === 'button'){
                        Helper.setPayloadForButton(e)
                    }
                });

                // if have attachment, the count of file/fileUrl/header should be the same and always be 1
                const fileInput = document.getElementById("fileInput");
                for (let i = 0; i < fileInput.files.length ; i++) {
                    let type = Helper.getFileType(fileInput.files[i]);
                    header[i][type]["link"] = fileUrl[i];
                }

                body.forEach(e=>{
                    e['text'] = Helper.sanitizeParam(e["text"]);
                })
                
                console.log("Components: " + JSON.stringify(components));
                allInArguments['TemplatePayload'] = allFields.templatePayload;
            } else if (taskType === "file") {
                allInArguments['fileURLs'] = fileUrl;
            }

            payload["arguments"].execute.inArguments = [allInArguments];
            payload["metaData"].isConfigured = true;

            console.log("Payload on SAVE function: " + JSON.stringify(payload));
            connection.trigger("updateActivity", payload);
        };

        static getUploadUrl = async (apiKey) => {
            const fileInput = document.getElementById("fileInput");

            if (fileInput.files.length > 0) {
                const file = fileInput.files[0];

                let type = Helper.getFileType(file);

                const formData = new FormData();
                formData.append("file", file);
                formData.append("ExtendedMessageType", 101);
                formData.append("DisplayName", file.name);
                formData.append("MediaType", type);
                formData.append("Channel", "whatsappcloudapi");
                formData.append("IsTemplateFile", "false");
                try {
                    const response = await fetch(
                        `${domainName}/JourneyBuilderCustomActivity/upload/${apiKey}`,
                        {
                            method: "POST",
                            body: formData
                        }
                    );
                    const result = await response.json();
                    return result;
                } catch (error) {
                    console.error(error);
                    return [];
                }
            }
        };

        static getFileType = (file) => {
            let type;
            if (file.type.includes("image")) {
                type = "image";
            } else if (file.type.includes("video")) {
                type = "video";
            } else if (file.type.includes("audio")) {
                type = "audio";
            } else {
                type = "document";
            }

            return type;
        };

        static getAnalyticTagsAsArray = (analyticTagsAsString) => {
            return analyticTagsAsString.split(",").map((e) => e.trim());
        };

        static setPayloadForButton(button) {
            // sanitize text type parameters of button
            button.parameters?.forEach(p => {
                if (p.type === 'text' && p.text) {
                    p.text = Helper.sanitizeParam(p.text);
                }
            });
        }
    }

    async function save() {
        await Helper.setPayload();
    }

    function onEventTrigger(eventDefinitionModel) {
        if (eventDefinitionModel) {
            eventDefinitionKey = eventDefinitionModel.eventDefinitionKey;
        }
    }

    function onInteractionDefaults(settings) {
    }

    function onDeSchemaTrigger(data) {
        data["schema"].forEach((e) => schemas.push(e));
    }
});
