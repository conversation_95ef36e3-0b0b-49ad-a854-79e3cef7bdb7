﻿using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.KnowledgeBaseEntries;
using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.Models.Playgrounds;

public class PlaygroundRecommendedReply
{
    [JsonProperty("message_id")]
    public string MessageId { get; set; }

    [JsonProperty("conversation_context")]
    public List<SfChatEntry> ConversationContext { get; set; }

    [JsonProperty("recommended_reply")]
    public string RecommendedReply { get; set; }

    [JsonProperty("knowledge_citations")]
    public List<KnowledgeCitation>? KnowledgeCitations { get; set; }

    [JsonConstructor]
    public PlaygroundRecommendedReply(
        string messageId,
        List<SfChatEntry> conversationContext,
        string recommendedReply,
        List<KnowledgeCitation>? knowledgeCitations = null)
    {
        MessageId = messageId;
        ConversationContext = conversationContext;
        RecommendedReply = recommendedReply;
        KnowledgeCitations = knowledgeCitations;
    }
}