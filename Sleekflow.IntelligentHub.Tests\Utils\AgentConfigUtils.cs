using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.Tests.Utils;

public static class AgentConfigUtils
{
    public static CompanyAgentConfig GetAgentConfig(
        string? tone = null,
        LeadNurturingTools? leadNurturingTools = null,
        string? type = null)
    {
        var now = DateTimeOffset.Now;

        return new CompanyAgentConfig(
            id: Guid.NewGuid().ToString(),
            name: "Testing Agent",
            sleekflowCompanyId: string.Empty,
            isChatHistoryEnabledAsContext: true,
            isContactPropertiesEnabledAsContext: true,
            numberOfPreviousMessagesInChatHistoryAvailableAsContext: 50,
            channelType: null,
            channelId: null,
            description: null,
            promptInstruction: new PromptInstruction
            {
                Tone = tone ?? TargetToneTypes.Professional,
            },
            type: type ?? CompanyAgentTypes.Sales,
            collaborationMode: AgentCollaborationModes.Default,
            leadNurturingTools: leadNurturingTools,
            createdAt: now,
            updatedAt: now);
    }
}