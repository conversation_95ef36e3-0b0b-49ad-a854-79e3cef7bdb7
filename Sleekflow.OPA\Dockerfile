# Base image
FROM permitio/opal-client:latest

# Working directory
WORKDIR /opal

# Copy your custom fetcher files
COPY Sleekflow.OPA/Fetcher/custom_fetcher /opal/custom_fetcher
COPY Sleekflow.OPA/Fetcher/setup.py /opal/setup.py
COPY Sleekflow.OPA/Policies /opal/Policies

# Install required packages for blob storage
RUN pip install azure-storage-blob

# Install your custom fetcher package
WORKDIR /app
RUN pip install -e /opal
WORKDIR /opal

# Expose the necessary ports
EXPOSE 7000 8181

# Set environment variables
# Will be replace with the actual server endpoint in deployment
ENV OPAL_SERVER_URL="ws://host.docker.internal:7002"
ENV OPAL_FETCH_PROVIDER_MODULES="opal_common.fetcher.providers,custom_fetcher.provider"
ENV OPAL_DATA_CONFIG_SOURCES='{"config":{"entries":[{"url":"dummy","config":{"fetcher":"BlobFetcher"},"topics":["policy_data"],"dst_path":"company","polling_interval":30}]}}'
ENV OPAL_INLINE_DATA_UPDATES="true"
ENV OPAL_POLICY_UPDATER_AUTO_UPDATE="true"
ENV OPAL_DATA_UPDATER_AUTO_UPDATE="true"
ENV OPAL_INLINE_OPA_ENABLED="false"
ENV OPAL_INLINE_OPA_CONFIG='{"watch":"./Policies"}'
ENV OPAL_OFFLINE_MODE_ENABLED='true'
ENV OPA_BLOB_CONTAINER_NAME='opa-policies'
ENV OPA_DATA_PATH='DefaultEndpointsProtocol=https;AccountName=s3b945c67;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net'
# Add OPA run server command
CMD ["sh", "-c", "opa run -s /opal/Policies --log-level=error & opal-client run"]
#CMD ["sh", "-c", "opa run -s /opal/Policies -v=2 & OPA_LOG_LEVEL=debug opal-client run"]
