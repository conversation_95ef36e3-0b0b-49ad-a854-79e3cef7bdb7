﻿using System.ComponentModel.DataAnnotations;
using System.Globalization;
using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;
using Sleekflow.MessagingHub.Payments;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.WabaBalanceAutoTopup;
using Sleekflow.Persistence;
using Stripe.Checkout;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.WabaBalanceAutoTopUpProfiles;

public interface IWabaBalanceAutoTopUpProfileService
{
    Task<WabaBalanceAutoTopUpProfile?> GetWithFacebookWabaIdAsync(string facebookWabaId);

    Task<WabaBalanceAutoTopUpProfile?> GetWithFacebookBusinessAndWabaIdAsync(
        string facebookBusinessId,
        string facebookWabaId);

    Task<List<WabaBalanceAutoTopUpProfile>> GetWithFacebookBusinessIdAsync(string facebookBusinessId);

    Task<WabaBalanceAutoTopUpProfile> UpsertWabaBalanceAutoTopUpProfileAsync(
        string facebookWabaId,
        string facebookBusinessId,
        string? customerId,
        Money minimumBalance,
        StripeWhatsAppCreditTopUpPlan autoTopUpPlan,
        bool isAutoTopUpEnabled,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff? sleekflowStaff);

    Task<string> GenerateWabaBalanceAutoTopUpProfilePaymentLinkAsync(
        string sleekflowCompanyId,
        string facebookWabaId,
        string facebookBusinessId,
        string creditedBy,
        string? creditedByDisplayName,
        string redirectToUrl,
        string? phoneNumber,
        StripeWhatsAppCreditTopUpPlan stripeWhatsAppCreditTopUpPlan);

    Task<WabaBalanceAutoTopUpProfile> UpdateWabaBalanceAutoTopUpProfileCustomerIdAsync(string facebookWabaId, string customerId);
}

public class WabaBalanceAutoTopUpProfileService : IWabaBalanceAutoTopUpProfileService, ISingletonService
{
    private readonly IWabaBalanceAutoTopUpProfileRepository _wabaBalanceAutoTopUpProfileRepository;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly IStripeClient _stripeClient;
    private readonly IIdService _idService;
    private readonly ILogger<WabaBalanceAutoTopUpProfileService> _logger;
    private readonly IWabaBalanceAutoTopUpService _wabaBalanceAutoTopUpService;

    public WabaBalanceAutoTopUpProfileService(
        IWabaBalanceAutoTopUpProfileRepository wabaBalanceAutoTopUpProfileRepository,
        IBusinessBalanceService businessBalanceService,
        IStripeClient stripeClient,
        IIdService idService,
        ILogger<WabaBalanceAutoTopUpProfileService> logger,
        IWabaBalanceAutoTopUpService wabaBalanceAutoTopUpService)
    {
        _wabaBalanceAutoTopUpProfileRepository = wabaBalanceAutoTopUpProfileRepository;
        _businessBalanceService = businessBalanceService;
        _stripeClient = stripeClient;
        _idService = idService;
        _logger = logger;
        _wabaBalanceAutoTopUpService = wabaBalanceAutoTopUpService;
    }

    public async Task<WabaBalanceAutoTopUpProfile?> GetWithFacebookWabaIdAsync(string facebookWabaId)
    {
        return (await _wabaBalanceAutoTopUpProfileRepository.GetObjectsAsync(x => x.FacebookWabaId == facebookWabaId))
            .FirstOrDefault();
    }

    public async Task<WabaBalanceAutoTopUpProfile?> GetWithFacebookBusinessAndWabaIdAsync(string facebookBusinessId, string facebookWabaId)
    {
        return (await _wabaBalanceAutoTopUpProfileRepository
                .GetObjectsAsync(x => x.FacebookWabaId == facebookWabaId && x.FacebookBusinessId==facebookBusinessId))
            .FirstOrDefault();
    }

    public async Task<List<WabaBalanceAutoTopUpProfile>> GetWithFacebookBusinessIdAsync(string facebookBusinessId)
    {
        return await _wabaBalanceAutoTopUpProfileRepository.GetObjectsAsync(
            x => x.FacebookBusinessId == facebookBusinessId);
    }

    public async Task<WabaBalanceAutoTopUpProfile> UpsertWabaBalanceAutoTopUpProfileAsync(
        string facebookWabaId,
        string facebookBusinessId,
        string? customerId,
        Money minimumBalance,
        StripeWhatsAppCreditTopUpPlan autoTopUpPlan,
        bool isAutoTopUpEnabled,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var businessBalance = await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance is null)
        {
            _logger.LogError(
                "Unable to locate business account balance object with {FacebookBusinessId}",
                facebookBusinessId);

            throw new SfInternalErrorException(
                $"Unable to locate business account balance object with {facebookBusinessId}");
        }

        var wabaBalance = businessBalance.WabaBalances?.Find(x => x.FacebookWabaId == facebookWabaId);

        if (wabaBalance == null)
        {
            _logger.LogError(
                "Unable to locate waba account balance object with {FacebookBusinessId} {FacebookWabaId}",
                facebookBusinessId,
                facebookWabaId);

            throw new SfInternalErrorException(
                $"Unable to locate waba account balance object with {facebookBusinessId} {facebookWabaId}");
        }

        var wabaBalanceAutoTopUpProfile = await GetWithFacebookWabaIdAsync(facebookWabaId);

        if (wabaBalanceAutoTopUpProfile == null)
        {
            wabaBalanceAutoTopUpProfile = ConstructWabaBalanceAutoTopUpProfile(
                facebookWabaId,
                facebookBusinessId,
                customerId,
                minimumBalance,
                autoTopUpPlan,
                isAutoTopUpEnabled,
                sleekflowCompanyId,
                sleekflowStaff);
        }
        else
        {
            wabaBalanceAutoTopUpProfile.CustomerId = customerId;
            wabaBalanceAutoTopUpProfile.MinimumBalance = minimumBalance;
            wabaBalanceAutoTopUpProfile.AutoTopUpPlan = autoTopUpPlan;
            wabaBalanceAutoTopUpProfile.IsAutoTopUpEnabled = isAutoTopUpEnabled;
            wabaBalanceAutoTopUpProfile.UpdatedAt = DateTime.UtcNow;
        }

        var upsertedWabaBalanceAutoTopUpProfile = await _wabaBalanceAutoTopUpProfileRepository.UpsertAndGetAsync(
            wabaBalanceAutoTopUpProfile,
            facebookWabaId);

        var performAutoTopUp =
            _wabaBalanceAutoTopUpService.ShouldPerformAutoTopUp(
                facebookBusinessId,
                facebookWabaId,
                wabaBalance,
                upsertedWabaBalanceAutoTopUpProfile);

        if (performAutoTopUp)
        {
            await _wabaBalanceAutoTopUpService.PublishAutoTopUpEvent(
                facebookBusinessId,
                facebookWabaId,
                businessBalance.Id,
                upsertedWabaBalanceAutoTopUpProfile);
        }

        return upsertedWabaBalanceAutoTopUpProfile;
    }

    public async Task<string> GenerateWabaBalanceAutoTopUpProfilePaymentLinkAsync(
        string sleekflowCompanyId,
        string facebookWabaId,
        string facebookBusinessId,
        string creditedBy,
        string? creditedByDisplayName,
        string redirectToUrl,
        string? phoneNumber,
        StripeWhatsAppCreditTopUpPlan stripeWhatsAppCreditTopUpPlan)
    {
        var businessBalance = await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance is null)
        {
            throw new NullReferenceException();
        }

        if (businessBalance.WabaBalances == null || businessBalance.WabaBalances.TrueForAll(x => x.FacebookWabaId != facebookWabaId))
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        $"this FB business id {facebookBusinessId} business balance id {businessBalance.Id} hasn't yet enabled this FB waba id {facebookWabaId} waba level credit management",
                        new[]
                        {
                            "BusinessBalanceId",
                            "FacebookBusinessId",
                            "FacebookWabaId"
                        })
                });
        }

        var businessBalanceId = businessBalance.Id;

        var lineItem = new List<SessionLineItemOptions>
        {
            new ()
            {
                Price = stripeWhatsAppCreditTopUpPlan.Id, Quantity = 1
            }
        };

        var metadata = new Dictionary<string, string>
        {
            {
                "source", "messaging-hub"
            },
            {
                "sleekflow_company_id", sleekflowCompanyId
            },
            {
                "facebook_waba_id", facebookWabaId
            },
            {
                "facebook_business_id", facebookBusinessId
            },
            {
                "business_balance_id", businessBalanceId
            },
            {
                "type", TopUpTypes.WhatsappCloudApiAutoTopUp
            },
            {
                "whatsapp_cloud_api_top_up_plan_id", stripeWhatsAppCreditTopUpPlan.Id
            },
            {
                "whatsapp_cloud_api_top_up_plan_amount", stripeWhatsAppCreditTopUpPlan.Price.Amount.ToString(CultureInfo.InvariantCulture)
            },
            {
                "whatsapp_cloud_api_top_up_plan_currency", stripeWhatsAppCreditTopUpPlan.Price.CurrencyIsoCode
            },
            {
                "credited_by", creditedBy
            }
        };

        if (!string.IsNullOrEmpty(creditedByDisplayName))
        {
            metadata.Add("credited_by_display_name", creditedByDisplayName);
        }

        const bool isSetupFutureUsage = true;

        var successUrl =
            BuildAutoTopUpStripeRedirectToUrl(redirectToUrl, "success", "autoTopUp", phoneNumber);

        var cancelUrl =
            BuildAutoTopUpStripeRedirectToUrl(redirectToUrl, "cancel", "autoTopUp", phoneNumber);

        return (await _stripeClient.GeneratePaymentLink(
            null,
            successUrl,
            cancelUrl,
            lineItem,
            metadata,
            isSetupFutureUsage)).Url;
    }

    public async Task<WabaBalanceAutoTopUpProfile> UpdateWabaBalanceAutoTopUpProfileCustomerIdAsync(string facebookWabaId, string customerId)
    {
        var wabaBalanceAutoTopUpProfile = await GetWithFacebookWabaIdAsync(facebookWabaId);

        if (wabaBalanceAutoTopUpProfile is null)
        {
            throw new SfInternalErrorException(
                $"Waba Balance Auto Top Up Profile not found! facebook waba id:{facebookWabaId}");
        }

        var isInitialAutoTopUpProfileCustomerIdSetup = IsInitialAutoTopUpProfileCustomerIdSetup(wabaBalanceAutoTopUpProfile);

        if (isInitialAutoTopUpProfileCustomerIdSetup)
        {
            wabaBalanceAutoTopUpProfile.CustomerId = customerId;
            wabaBalanceAutoTopUpProfile.IsAutoTopUpEnabled = true;
            wabaBalanceAutoTopUpProfile.UpdatedAt = DateTimeOffset.UtcNow;

            return await _wabaBalanceAutoTopUpProfileRepository.UpsertAndGetAsync(
                    wabaBalanceAutoTopUpProfile,
                    facebookWabaId);
        }

        if (wabaBalanceAutoTopUpProfile.CustomerId != customerId)
        {
            wabaBalanceAutoTopUpProfile.CustomerId = customerId;
            wabaBalanceAutoTopUpProfile.UpdatedAt = DateTimeOffset.UtcNow;

            return await _wabaBalanceAutoTopUpProfileRepository.UpsertAndGetAsync(
                wabaBalanceAutoTopUpProfile,
                facebookWabaId);
        }

        return wabaBalanceAutoTopUpProfile;
    }

    private bool IsInitialAutoTopUpProfileCustomerIdSetup(WabaBalanceAutoTopUpProfile wabaBalanceAutoTopUpProfile)
    {
        return wabaBalanceAutoTopUpProfile is { IsAutoTopUpEnabled: false, CustomerId: null };
    }

    private WabaBalanceAutoTopUpProfile ConstructWabaBalanceAutoTopUpProfile(
        string facebookWabaId,
        string facebookBusinessId,
        string? customerId,
        Money minimumBalance,
        StripeWhatsAppCreditTopUpPlan autoTopUpPlan,
        bool isAutoTopUpEnabled,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        if (customerId is null)
        {
            isAutoTopUpEnabled = false;
        }

        var recordStatus = new List<string>
        {
            RecordStatuses.Active
        };

        var upsertWabaBalanceAutoTopUpProfile = new WabaBalanceAutoTopUpProfile(
            _idService.GetId(SysTypeNames.WabaBalanceAutoTopUpProfile),
            facebookWabaId,
            facebookBusinessId,
            customerId,
            minimumBalance,
            autoTopUpPlan,
            isAutoTopUpEnabled,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            sleekflowCompanyId,
            sleekflowStaff,
            sleekflowStaff,
            recordStatus);

        if (customerId is null)
        {
            return upsertWabaBalanceAutoTopUpProfile;
        }

        return upsertWabaBalanceAutoTopUpProfile;
    }

    private static string BuildAutoTopUpStripeRedirectToUrl(
        string baseUrl,
        string action,
        string info,
        string? phoneNumber = null)
    {
        var builder = new UriBuilder(baseUrl);

        // If baseUrl contain origin only, will provide a default path.
        if(builder.Path == "/")
        {
            builder.Path = $"/stripe/{action}";
        }

        var queryParams = new List<string>
        {
            $"info={info}",
        };

        if(phoneNumber != null)
        {
            queryParams.Add($"phoneNumber={phoneNumber}");
        }

        builder.Query = builder.Query == string.Empty
            ? string.Join("&", queryParams)
            : $"{builder.Query}&{string.Join("&", queryParams)}";

        return builder.ToString();
    }
}