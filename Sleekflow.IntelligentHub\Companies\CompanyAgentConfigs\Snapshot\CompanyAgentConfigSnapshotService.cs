using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs.Snapshot;

public interface ICompanyAgentConfigSnapshotService
{
    /// <summary>
    /// Creates a snapshot of the given CompanyAgentConfig.
    /// </summary>
    /// <param name="config">The configuration to snapshot.</param>
    /// <param name="operationType">The operation that triggered this snapshot.</param>
    /// <param name="createdBy">The staff member creating the snapshot.</param>
    /// <returns>The created snapshot.</returns>
    Task<CompanyAgentConfigSnapshot> CreateSnapshotAsync(
        CompanyAgentConfig config,
        string operationType,
        AuditEntity.SleekflowStaff createdBy);
}

public class CompanyAgentConfigSnapshotService : ICompanyAgentConfigSnapshotService, IScopedService
{
    private readonly ILogger _logger;
    private readonly IIdService _idService;
    private readonly ICompanyAgentConfigSnapshotRepository _companyAgentConfigSnapshotRepository;

    public CompanyAgentConfigSnapshotService(
        IIdService idService,
        ILogger<CompanyAgentConfigSnapshotService> logger,
        ICompanyAgentConfigSnapshotRepository companyAgentConfigSnapshotRepository)
    {
        _logger = logger;
        _idService = idService;
        _companyAgentConfigSnapshotRepository = companyAgentConfigSnapshotRepository;
    }

    public async Task<CompanyAgentConfigSnapshot> CreateSnapshotAsync(
        CompanyAgentConfig config,
        string operationType,
        AuditEntity.SleekflowStaff createdBy)
    {
        _logger.LogInformation(
            "Creating snapshot for CompanyAgentConfig {ConfigId} with operation type {OperationType}",
            config.Id,
            operationType);

        var snapshotId = _idService.GetId(SysTypeNames.CompanyAgentConfigSnapshot);
        var snapshot = CompanyAgentConfigSnapshot.CreateFromConfig(
            snapshotId,
            config,
            operationType,
            createdBy);

        var createdSnapshot =
            await _companyAgentConfigSnapshotRepository.CreateAndGetAsync(snapshot, config.SleekflowCompanyId);

        _logger.LogInformation(
            "Successfully created snapshot {SnapshotId} for CompanyAgentConfig {ConfigId}",
            createdSnapshot.Id,
            config.Id);

        return createdSnapshot;
    }
}