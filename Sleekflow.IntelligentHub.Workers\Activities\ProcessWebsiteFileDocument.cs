using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Workers.Services;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.IntelligentHub.Workers.Activities;

public class ProcessWebsiteFileDocument
{
    private readonly IWebsiteIngestionService _websiteIngestionService;

    public ProcessWebsiteFileDocument(IWebsiteIngestionService websiteIngestionService)
    {
        _websiteIngestionService = websiteIngestionService;
    }

    public class ProcessWebsiteFileDocumentInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("document_id")]
        public string DocumentId { get; set; }

        [Required]
        [JsonProperty("url")]
        public string Url { get; set; }

        [Required]
        [JsonProperty("mimeType")]
        public string MimeType { get; set; }

        [Required]
        [ValidateObject]
        [JsonProperty("file_ingestion_progress")]
        public object? FileIngestionProgress { get; set; }

        [JsonConstructor]
        public ProcessWebsiteFileDocumentInput(
            string sleekflowCompanyId,
            string documentId,
            string url,
            string mimeType,
            object? fileIngestionProgress)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DocumentId = documentId;
            Url = url;
            MimeType = mimeType;
            FileIngestionProgress = fileIngestionProgress;
        }
    }

    public class ProcessWebsiteFileDocumentOutput
    {
        [JsonProperty("is_completed")]
        public bool IsCompleted { get; set; }

        [JsonProperty("file_ingestion_progress")]
        public object FileIngestionProgress { get; set; }

        [JsonConstructor]
        public ProcessWebsiteFileDocumentOutput(bool isCompleted, object fileIngestionProgress)
        {
            IsCompleted = isCompleted;
            FileIngestionProgress = fileIngestionProgress;
        }
    }

    [Function(nameof(ProcessWebsiteFileDocument))]
    public async Task<ProcessWebsiteFileDocumentOutput> Process(
        [ActivityTrigger]
        ProcessWebsiteFileDocumentInput processWebsiteDocumentInput)
    {
        var fileIngestionProgress = await _websiteIngestionService.ProcessWebsiteFileDocument(
            processWebsiteDocumentInput.SleekflowCompanyId,
            processWebsiteDocumentInput.DocumentId,
            processWebsiteDocumentInput.Url,
            processWebsiteDocumentInput.MimeType,
            processWebsiteDocumentInput.FileIngestionProgress);

        return new ProcessWebsiteFileDocumentOutput(fileIngestionProgress.IsCompleted(), fileIngestionProgress);
    }
}