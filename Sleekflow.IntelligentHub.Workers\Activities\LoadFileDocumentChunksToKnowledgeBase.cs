using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.KnowledgeBaseEntries;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Workers.Activities;

public class LoadFileDocumentChunksToKnowledgeBase
{
    private readonly ILogger<LoadFileDocumentChunksToKnowledgeBase> _logger;
    private readonly IKnowledgeBaseEntryLoadingService _knowledgeBaseEntryLoadingService;

    public LoadFileDocumentChunksToKnowledgeBase(
        ILogger<LoadFileDocumentChunksToKnowledgeBase> logger,
        IKnowledgeBaseEntryLoadingService knowledgeBaseEntryLoadingService)
    {
        _logger = logger;
        _knowledgeBaseEntryLoadingService = knowledgeBaseEntryLoadingService;
    }

    public class LoadFileDocumentChunksToKnowledgeBaseInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("document_id")]
        public string DocumentId { get; set; }

        [JsonConstructor]
        public LoadFileDocumentChunksToKnowledgeBaseInput(
            string sleekflowCompanyId,
            string documentId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DocumentId = documentId;
        }
    }

    public class LoadFileDocumentChunksToKnowledgeBaseOutput
    {
        [JsonConstructor]
        public LoadFileDocumentChunksToKnowledgeBaseOutput()
        {
        }
    }

    [Function(nameof(LoadFileDocumentChunksToKnowledgeBase))]
    public async Task<LoadFileDocumentChunksToKnowledgeBaseOutput> Process(
        [ActivityTrigger]
        LoadFileDocumentChunksToKnowledgeBaseInput input)
    {
        _logger.LogInformation(
            "LoadFileDocumentChunksToKnowledgeBase {SleekflowCompanyId} {DocumentId}",
            input.SleekflowCompanyId,
            input.DocumentId);

        await _knowledgeBaseEntryLoadingService.LoadChunksToKnowledgeBaseAsync(
            input.SleekflowCompanyId,
            input.DocumentId);

        return new LoadFileDocumentChunksToKnowledgeBaseOutput();
    }
}