﻿using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.IntelligentHub.Blobs;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.Models.Workers;

namespace Sleekflow.IntelligentHub.Workers;

public interface IIntelligentHubWorkerService
{
    Task StartFileIngestionAsync(
        string sleekflowCompanyId,
        string documentId,
        string blobId);

    Task StartWebsiteIngestionAsync(
        string sleekflowCompanyId,
        string documentId);

    Task StartWebpageIngestionAsync(
        string sleekflowCompanyId,
        string documentId);

    Task StartBatchIngestionAsync(
        string sleekflowCompanyId,
        string[] documentIds);

    Task StartUploadToAgentKnowledgeBasesAsync(
        string sleekflowCompanyId,
        string documentId);

    Task StartWebCrawlingSessionAsync(
        string sleekflowCompanyId,
        string url,
        string sessionId);
}

public class IntelligentHubWorkerService : IIntelligentHubWorkerService, IScopedService
{
    private readonly ILogger<IntelligentHubWorkerService> _logger;
    private readonly IBlobService _blobService;
    private readonly IBus _bus;
    private readonly IKbDocumentRepository _kbDocumentRepository;

    public IntelligentHubWorkerService(
        ILogger<IntelligentHubWorkerService> logger,
        IBlobService blobService,
        IBus bus,
        IKbDocumentRepository kbDocumentRepository)
    {
        _logger = logger;
        _blobService = blobService;
        _bus = bus;
        _kbDocumentRepository = kbDocumentRepository;
    }

    public async Task StartFileIngestionAsync(
        string sleekflowCompanyId,
        string documentId,
        string blobId)
    {
        _logger.LogInformation("Starting file ingestion for document {DocumentId}", documentId);

        var blobUrl = await GetBlobDownloadSasUrl(sleekflowCompanyId, documentId, blobId);

        await _bus.Publish(
            new StartFileIngestionEvent(
                sleekflowCompanyId,
                documentId,
                blobUrl));
    }

    private async Task<string> GetBlobDownloadSasUrl(string sleekflowCompanyId, string documentId, string blobId)
    {
        _logger.LogInformation(
            "CreateBlobDownloadSasUrls for DocumentId: {DocumentId}, CompanyId: {CompanyId}",
            documentId,
            sleekflowCompanyId);

        var blobName = GetBlobNameFromId(blobId);
        var publicBlobs = await _blobService.CreateBlobDownloadSasUrls(
            sleekflowCompanyId,
            [blobName],
            "File");

        if (publicBlobs.Count != 1)
        {
            throw new InvalidOperationException($"Expected 1 blob, but got {publicBlobs.Count}");
        }

        var blobUrl = publicBlobs[0].Url;

        _logger.LogInformation(
            "CreateBlobDownloadSasUrls completed for DocumentId: {DocumentId}, URL created",
            documentId);
        return blobUrl;
    }

    private string GetBlobNameFromId(string blobId)
    {
        var splitId = blobId.Split('/');
        if (splitId.Length != 2)
        {
            throw new InvalidOperationException($"Invalid blob ID format: {blobId}");
        }

        return splitId[1];
    }

    public async Task StartWebsiteIngestionAsync(
        string sleekflowCompanyId,
        string documentId)
    {
        _logger.LogInformation("Starting website ingestion for document {DocumentId}", documentId);
        await _bus.Publish(
            new StartWebsiteIngestionEvent(
                sleekflowCompanyId,
                documentId));
    }

    public async Task StartWebpageIngestionAsync(
        string sleekflowCompanyId,
        string documentId)
    {
        _logger.LogInformation("Starting webpage ingestion for document {DocumentId}", documentId);
        await _bus.Publish(
            new StartWebpageIngestionEvent(
                sleekflowCompanyId,
                documentId));
    }

    public async Task StartBatchIngestionAsync(
        string sleekflowCompanyId,
        string[] documentIds)
    {
        _logger.LogInformation("Starting batch ingestion for {Count} documents", documentIds.Length);

        var fileDocumentIngestionParams = new List<FileDocumentIngestionParams>();
        var websiteDocumentIngestionParams = new List<WebsiteDocumentIngestionParams>();
        var webpageDocumentIngestionParams = new List<WebpageDocumentIngestionParams>();

        foreach (var documentId in documentIds)
        {
            var document = await _kbDocumentRepository.GetAsync(documentId, sleekflowCompanyId);
            if (document == null)
            {
                throw new SfNotFoundObjectException(documentId, sleekflowCompanyId);
            }

            switch (document.SysTypeName)
            {
                case SysTypeNames.FileDocument:
                    var fileDocument = (FileDocument) document;
                    var sasDownloadUrl = await GetBlobDownloadSasUrl(
                        sleekflowCompanyId,
                        documentId,
                        fileDocument.BlobId);
                    fileDocumentIngestionParams.Add(new FileDocumentIngestionParams(documentId, sasDownloadUrl));
                    break;

                case SysTypeNames.WebsiteDocument:
                    websiteDocumentIngestionParams.Add(new WebsiteDocumentIngestionParams(documentId));
                    break;

                case SysTypeNames.WebpageDocument:
                    webpageDocumentIngestionParams.Add(new WebpageDocumentIngestionParams(documentId));
                    break;

                default:
                    _logger.LogWarning(
                        "Unknown document type {SysTypeName} for document {DocumentId}, skipping",
                        document.SysTypeName,
                        documentId);
                    continue;
            }
        }

        await _bus.Publish(
            new StartBatchIngestionEvent(
                sleekflowCompanyId,
                fileDocumentIngestionParams.ToArray(),
                websiteDocumentIngestionParams.ToArray(),
                webpageDocumentIngestionParams.ToArray()));
    }

    public async Task StartUploadToAgentKnowledgeBasesAsync(
        string sleekflowCompanyId,
        string documentId)
    {
        _logger.LogInformation("StartUploadToAgentKnowledgeBasesAsync");
        await _bus.Publish(
            new StartUploadToAgentKnowledgeBasesEvent(
                sleekflowCompanyId,
                documentId));
    }

    public async Task StartWebCrawlingSessionAsync(
        string sleekflowCompanyId,
        string url,
        string sessionId)
    {
        _logger.LogInformation("StartWebCrawlingSessionAsync");
        await _bus.Publish(
            new StartWebCrawlingSessionEvent(
                sleekflowCompanyId,
                url,
                sessionId));
    }
}