using Microsoft.Extensions.Logging;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Documents.WebpageDocuments;
using Sleekflow.IntelligentHub.Documents.WebsiteDocuments.Ingestion;
using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.TextEnrichments;

namespace Sleekflow.IntelligentHub.Workers.Services;

public interface IWebpageIngestionService
{
    Task ProcessWebpageDocument(
        string sleekflowCompanyId,
        string documentId);
}

public class WebpageIngestionService : IScopedService, IWebpageIngestionService
{
    private readonly ILogger<WebpageIngestionService> _logger;
    private readonly IKbDocumentService _kbDocumentService;
    private readonly IWebpageDocumentChunkService _webpageDocumentChunkService;
    private readonly ITextTranslationService _textTranslationService;
    private readonly IWebsiteKnowledgeSource _websiteKnowledgeSource;

    public WebpageIngestionService(
        ILogger<WebpageIngestionService> logger,
        IKbDocumentService kbDocumentService,
        IWebpageDocumentChunkService webpageDocumentChunkService,
        ITextTranslationService textTranslationService,
        IWebsiteKnowledgeSource websiteKnowledgeSource)
    {
        _logger = logger;
        _kbDocumentService = kbDocumentService;
        _webpageDocumentChunkService = webpageDocumentChunkService;
        _textTranslationService = textTranslationService;
        _websiteKnowledgeSource = websiteKnowledgeSource;
    }

    public async Task ProcessWebpageDocument(
        string sleekflowCompanyId,
        string documentId)
    {
        var webpageDocument =
            (WebpageDocument) await _kbDocumentService.GetDocumentAsync(sleekflowCompanyId, documentId);

        _logger.LogInformation(
            "Processing webpage document: {DocumentId}, URL: {Url}",
            documentId,
            webpageDocument.Url);

        try
        {
            // Process the webpage URL
            var markdown = await _websiteKnowledgeSource.Ingest(webpageDocument.Url);

            _logger.LogInformation(
                "Successfully processed webpage document: {DocumentId}, URL: {Url}",
                documentId,
                webpageDocument.Url);

            // Store markdown as WebpageDocumentChunk
            if (!string.IsNullOrWhiteSpace(markdown))
            {
                var chunkId = await StoreMarkdownAsWebpageDocumentChunk(
                    sleekflowCompanyId,
                    documentId,
                    webpageDocument.Url,
                    markdown);

                _logger.LogInformation(
                    "Stored markdown chunk for webpage document {DocumentId} {ChunkId}",
                    documentId,
                    chunkId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error processing webpage document {DocumentId} with URL {Url}: {Error}",
                documentId,
                webpageDocument.Url,
                ex.Message);
            throw;
        }
    }

    private async Task<string> StoreMarkdownAsWebpageDocumentChunk(
        string sleekflowCompanyId,
        string documentId,
        string url,
        string markdown)
    {
        // Translate to English if needed
        var contentEn = await _textTranslationService.TranslateByLlmAsync("en", markdown);

        var documentChunk = await _webpageDocumentChunkService.CreateWebpageDocumentChunkAsync(
            sleekflowCompanyId,
            documentId,
            url,
            markdown,
            contentEn,
            new List<Category>(),
            new Dictionary<string, object?>());

        return documentChunk.Id;
    }
}