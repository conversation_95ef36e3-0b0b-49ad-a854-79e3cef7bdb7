using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Workers.Services;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Workers.Activities;

public class ProcessWebsiteDocument
{
    private readonly IWebsiteIngestionService _websiteIngestionService;

    public ProcessWebsiteDocument(IWebsiteIngestionService websiteIngestionService)
    {
        _websiteIngestionService = websiteIngestionService;
    }

    public class ProcessWebsiteDocumentInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("document_id")]
        public string DocumentId { get; set; }

        [JsonConstructor]
        public ProcessWebsiteDocumentInput(
            string sleekflowCompanyId,
            string documentId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DocumentId = documentId;
        }
    }

    public class ProcessWebsiteDocumentOutput
    {
        [JsonConstructor]
        public ProcessWebsiteDocumentOutput()
        {
        }
    }

    [Function(nameof(ProcessWebsiteDocument))]
    public async Task<ProcessWebsiteDocumentOutput> Process(
        [ActivityTrigger]
        ProcessWebsiteDocumentInput processWebsiteDocumentInput)
    {
        await _websiteIngestionService.ProcessWebsiteDocument(
            processWebsiteDocumentInput.SleekflowCompanyId,
            processWebsiteDocumentInput.DocumentId);

        return new ProcessWebsiteDocumentOutput();
    }
}