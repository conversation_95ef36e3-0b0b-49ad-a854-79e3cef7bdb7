using Azure.Messaging.ServiceBus;
using MassTransit;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.IntelligentHub.Workers.Orchestrators;

namespace Sleekflow.IntelligentHub.Workers.Triggers;

public class StartWebCrawlingSessionTrigger
{
    private const string QueueName = "start-web-crawling-session-event";
    private readonly ILogger<StartWebCrawlingSessionTrigger> _logger;
    private readonly IMessageReceiver _messageReceiver;

    public StartWebCrawlingSessionTrigger(
        ILogger<StartWebCrawlingSessionTrigger> logger,
        IMessageReceiver messageReceiver)
    {
        _logger = logger;
        _messageReceiver = messageReceiver;
    }

    [Function(nameof(StartWebCrawlingSessionTrigger))]
    public async Task RunAsync(
        [ServiceBusTrigger(QueueName, Connection = "SERVICE_BUS_CONN_STR")]
        ServiceBusReceivedMessage message,
        [DurableClient]
        DurableTaskClient starter,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("StartWebCrawlingSessionTrigger message: {Message}", message.Body.ToString());

            // Parse the full message to extract the nested "message" property
            var fullMessage = JObject.Parse(message.Body.ToString());
            var messageContent = fullMessage["message"]?.ToString();

            _logger.LogInformation(
                "StartWebCrawlingSessionTrigger message content: {Message}",
                messageContent);

            if (string.IsNullOrEmpty(messageContent))
            {
                _logger.LogError("Message content is missing or empty in the received message");
                return;
            }

            var input = JsonConvert.DeserializeObject<StartWebCrawlingSessionEvent>(messageContent);
            if (input == null)
            {
                _logger.LogError("Failed to deserialize StartWebCrawlingSessionEvent");
                return;
            }

            var instanceId = await starter.ScheduleNewOrchestrationInstanceAsync(
                nameof(StartWebCrawlingSessionOrchestrator),
                input);

            _logger.LogInformation($"Started StartWebCrawlingSessionOrchestrator with ID = [{instanceId}]");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Service Bus message for web crawling session");
            throw;
        }
    }
}