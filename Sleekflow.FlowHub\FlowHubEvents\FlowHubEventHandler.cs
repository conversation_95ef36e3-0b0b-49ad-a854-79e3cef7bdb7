using System.Net;
using MassTransit;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.Commons.Workflows;
using Sleekflow.FlowHub.Companies;
using Sleekflow.FlowHub.Configs;
using Sleekflow.FlowHub.EmailSentRecords;
using Sleekflow.FlowHub.EnrolmentMonetization;
using Sleekflow.FlowHub.FlowHubConfigs;
using Sleekflow.FlowHub.Limiters;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.WorkflowExecutions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.Steps;
using Sleekflow.FlowHub.Utils;
using Sleekflow.FlowHub.Triggers.Events;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Locks;
using JsonConfig = Sleekflow.FlowHub.JsonConfigs.JsonConfig;

namespace Sleekflow.FlowHub.FlowHubEvents;

public interface IFlowHubEventHandler
{
    Task HandleAsync(EventBody eventBody, string objectId, string objectType, string sleekflowCompanyId);

    Task<bool> HandleReenrollmentAsync(ProxyState state);
}

public class FlowHubEventHandler : IFlowHubEventHandler, IScopedService
{
    private readonly IStateService _stateService;
    private readonly IStateAggregator _stateAggregator;
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IWorkflowService _workflowService;
    private readonly IWorkflowExecutionService _workflowExecutionService;
    private readonly IWorkflowRuntimeService _workflowRuntimeService;
    private readonly IWorkflowEnrollmentFilter _workflowEnrollmentFilter;
    private readonly IStepExecutorActivator _stepExecutorActivator;
    private readonly IStateSubscriptionService _stateSubscriptionService;
    private readonly IFlowHubConfigService _flowHubConfigService;
    private readonly IWorkflowRateLimitConfig _workflowRateLimitConfig;
    private readonly IWorkflowEnrollmentRateLimitEmailNotificationConfig _workflowEnrollmentRateLimitEmailNotificationConfig;
    private readonly IRequestRateLimiter _requestRateLimiter;
    private readonly IBus _bus;
    private readonly IEmailSentRecordService _emailSentRecordService;
    private readonly ILockService _lockService;
    private readonly IWorkflowRecurringSettingsParser _workflowRecurringSettingsParser;
    private readonly ILogger<FlowHubEventHandler> _logger;
    private readonly IContactDataProvider _contactDataProvider;
    private readonly IWorkflowStateService _workflowStateService;
    private readonly IEnrolmentMonetizationService _enrolmentMonetizationService;
    private readonly IServiceBusManager _serviceBus;

    public FlowHubEventHandler(
        IStateService stateService,
        IStateAggregator stateAggregator,
        IStateEvaluator stateEvaluator,
        IWorkflowService workflowService,
        IWorkflowExecutionService workflowExecutionService,
        IWorkflowRuntimeService workflowRuntimeService,
        IWorkflowEnrollmentFilter workflowEnrollmentFilter,
        IStepExecutorActivator stepExecutorActivator,
        IStateSubscriptionService stateSubscriptionService,
        IFlowHubConfigService flowHubConfigService,
        IWorkflowRateLimitConfig workflowRateLimitConfig,
        IWorkflowEnrollmentRateLimitEmailNotificationConfig workflowEnrollmentRateLimitEmailNotificationConfig,
        IRequestRateLimiter requestRateLimiter,
        IBus bus,
        IEmailSentRecordService emailSentRecordService,
        ILockService lockService,
        IWorkflowRecurringSettingsParser workflowRecurringSettingsParser,
        ILogger<FlowHubEventHandler> logger,
        IContactDataProvider contactDataProvider,
        IWorkflowStateService workflowStateService,
        IEnrolmentMonetizationService enrolmentMonetizationService,
        IServiceBusManager serviceBus)
    {
        _stateService = stateService;
        _stateAggregator = stateAggregator;
        _stateEvaluator = stateEvaluator;
        _workflowService = workflowService;
        _workflowExecutionService = workflowExecutionService;
        _workflowRuntimeService = workflowRuntimeService;
        _workflowEnrollmentFilter = workflowEnrollmentFilter;
        _stepExecutorActivator = stepExecutorActivator;
        _stateSubscriptionService = stateSubscriptionService;
        _flowHubConfigService = flowHubConfigService;
        _workflowRateLimitConfig = workflowRateLimitConfig;
        _workflowEnrollmentRateLimitEmailNotificationConfig = workflowEnrollmentRateLimitEmailNotificationConfig;
        _requestRateLimiter = requestRateLimiter;
        _bus = bus;
        _emailSentRecordService = emailSentRecordService;
        _lockService = lockService;
        _workflowRecurringSettingsParser = workflowRecurringSettingsParser;
        _logger = logger;
        _contactDataProvider = contactDataProvider;
        _workflowStateService = workflowStateService;
        _enrolmentMonetizationService = enrolmentMonetizationService;
        _serviceBus = serviceBus;
    }

    public async Task HandleAsync(EventBody eventBody, string objectId, string objectType, string sleekflowCompanyId)
    {
        _logger.LogInformation("[FlowHubEventHandler: HandleAsync] eventBody: {EventBody}, ObjectId, {ObjectId}, ObjectType: {ObjectType}", eventBody, objectId, objectType);
        var flowHubConfig = await _flowHubConfigService.GetFlowHubConfigAsync(sleekflowCompanyId);

        if (!flowHubConfig.IsEnrolled)
        {
            return;
        }

        // Fetch contact data
        var contactData = await TryFetchContactDataAsync(objectId, objectType, sleekflowCompanyId, flowHubConfig);

        Dictionary<string, object?>? contactInfo = null;
        if (contactData != null && contactData.TryGetValue("contact", out var contactValue))
        {
            contactInfo = contactValue as Dictionary<string, object?>;
        }

        // Set the contact info on the event body if the property exists
        var contactProperty = eventBody.GetType().GetProperty("Contact");
        if (contactProperty != null)
        {
            contactProperty.SetValue(eventBody, contactInfo);
        }

        // If there exists a workflow executing for the contact, then aggregate the existing state
        var existingStates = await _stateService.GetRunningStatesAsync(
            objectId,
            objectType,
            sleekflowCompanyId,
            eventBody);

        foreach (var existingState in existingStates.Where(s => s.StateStatus == StateStatuses.Running))
        {
            await AggregateStateAndExecuteWorkflowSubscriptionsAsync(
                eventBody,
                existingState);
        }

        // Get workflows with matched conditions
        _logger.LogInformation("begin to match workflow");
        var matchedCompactWorkflows = await _workflowService.MatchWorkflowsAsync(
            sleekflowCompanyId,
            eventBody,
            flowHubConfig,
            existingStates);

        _logger.LogInformation("matched {MatchedWorkFlowCount} workflow(s)", matchedCompactWorkflows.Count);
        if (matchedCompactWorkflows is not { Count: > 0 }
            && existingStates is not { Count: > 0 })
        {
            return;
        }

        // Moving this usage check after matching workflows to avoid counting the workflow enrolments that are not eligible
        var isWithinUsageLimit = await _enrolmentMonetizationService.CheckWithinUsageLimitAsync(sleekflowCompanyId);

        var workflowEnrollmentFilterResult = await HandleWorkflowEnrollmentAsync(
            eventBody,
            objectId,
            objectType,
            existingStates,
            matchedCompactWorkflows,
            flowHubConfig,
            isWithinUsageLimit,
            isReenrollment: false);

        // Handle re-enrollment of workflows that trigger based on contact property
        if (eventBody is OnContactUpdatedEventBody onContactUpdatedEventBody)
        {
            await ReEnrollWorkflowsAsync(
                WorkflowScheduleTypes.ContactPropertyBasedDateTime,
                StateReasonCodes.ContactPropertyChanged,
                compactWorkflowToReEnrollPredicate: w =>
                    onContactUpdatedEventBody.ChangeEntries.Exists(
                        x => x.PropertyId == w.WorkflowScheduleSettings.ContactPropertyId),
                targetPropertyChangedPredicate: state =>
                    onContactUpdatedEventBody.ChangeEntries.Exists(
                    x => x.PropertyId == state.WorkflowContext.SnapshottedWorkflow.WorkflowScheduleSettings
                        .ContactPropertyId));
        }

        // Handle re-enrollment of workflows that trigger based on schemaful object property
        if (eventBody is OnSchemafulObjectUpdatedEventBody onSchemafulObjectUpdatedEventBody)
        {
            await ReEnrollWorkflowsAsync(
                WorkflowScheduleTypes.SchemafulObjectPropertyBasedDateTime,
                StateReasonCodes.SchemafulObjectPropertyChanged,
                compactWorkflowToReEnrollPredicate: w =>
                    onSchemafulObjectUpdatedEventBody.ChangeEntries.Exists(
                    x => x.PropertyId == w.WorkflowScheduleSettings.SchemafulObjectPropertyId),
                targetPropertyChangedPredicate: state =>
                    onSchemafulObjectUpdatedEventBody.ChangeEntries.Exists(
                    x => x.PropertyId == state.WorkflowContext.SnapshottedWorkflow.WorkflowScheduleSettings
                        .SchemafulObjectPropertyId));
        }

        async Task ReEnrollWorkflowsAsync(
            string scheduleType,
            string stateReasonCode,
            Func<CompactWorkflow, bool> compactWorkflowToReEnrollPredicate,
            Func<ProxyState, bool> targetPropertyChangedPredicate)
        {
            var compactWorkflowsToCheck = matchedCompactWorkflows
                .Where(w => w.WorkflowScheduleSettings.ScheduleType == scheduleType)
                .Except(workflowEnrollmentFilterResult.WorkflowsSkippedDueToCanEnrollOnlyOnce)
                .Except(workflowEnrollmentFilterResult.WorkflowsSkippedDueToCanEnrollOnlyOnFailure)
                .ToList();

            foreach (var state in existingStates)
            {
                /*
                 * The specialized sleep step will set the `scheduled_enrollment_started` flag after done, so that we can
                 * know that the current active enrollment has already passed the scheduled date and
                 * proceed with subsequent actions.
                 * If this happens, when user updates the contact property again at this moment,
                 * it shouldn't fail the current active enrollment.
                 */
                if (state.SysVarDict.ContainsKey(StateSystemVarNames.ScheduledEnrollmentStarted))
                {
                    continue;
                }

                var compactWorkflowToReEnroll = compactWorkflowsToCheck.Find(
                    w =>
                        w.WorkflowId == state.Identity.WorkflowId
                        && compactWorkflowToReEnrollPredicate(w));

                if (compactWorkflowToReEnroll is not null)
                {
                    await _workflowRuntimeService.AbandonWorkflowAsync(
                        state.Identity.SleekflowCompanyId,
                        state.Id,
                        stateReasonCode);

                    var workflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
                        compactWorkflowToReEnroll.SleekflowCompanyId,
                        compactWorkflowToReEnroll.WorkflowVersionedId);

                    if (workflow is not { ActivationStatus: WorkflowActivationStatuses.Active })
                    {
                        continue;
                    }

                    await _workflowStateService.CreateStateAndExecutedWorkflowAsync(
                        eventBody,
                        objectId,
                        objectType,
                        sleekflowCompanyId,
                        StateStatuses.Scheduled,
                        workflow,
                        flowHubConfig,
                        null,
                        false);
                }
                else if (state.WorkflowContext.SnapshottedWorkflow.WorkflowScheduleSettings.ScheduleType == scheduleType
                         && workflowEnrollmentFilterResult.WorkflowsSkippedDueToCanEnrollOnlyOnce
                             .All(x => x.WorkflowId != state.Identity.WorkflowId)
                         && workflowEnrollmentFilterResult.WorkflowsSkippedDueToCanEnrollOnlyOnFailure
                             .All(x => x.WorkflowId != state.Identity.WorkflowId)
                         && targetPropertyChangedPredicate(state))
                {
                    await _workflowRuntimeService.AbandonWorkflowAsync(
                        state.Identity.SleekflowCompanyId,
                        state.Id,
                        stateReasonCode);
                }
            }
        }
    }

    public async Task<bool> HandleReenrollmentAsync(ProxyState state)
    {
        var targetWorkflow =
            await _workflowService.GetVersionedWorkflowOrDefaultAsync(
                state.Identity.SleekflowCompanyId,
                state.Identity.WorkflowVersionedId);

        if (targetWorkflow?.ActivationStatus is not WorkflowActivationStatuses.Active)
        {
            return false;
        }

        var flowHubConfig = await _flowHubConfigService.GetFlowHubConfigAsync(state.Identity.SleekflowCompanyId);

        var isWithinUsageLimit =
            await _enrolmentMonetizationService.CheckWithinUsageLimitAsync(state.Identity.SleekflowCompanyId);

        if (!isWithinUsageLimit
            && (string.IsNullOrWhiteSpace(targetWorkflow.WorkflowScheduleSettings.ScheduleType)
                || targetWorkflow.WorkflowScheduleSettings.ScheduleType == WorkflowScheduleTypes.None))
        {
            _logger.LogWarning(
                "Reenrollment failed for company {CompanyId} workflow {WorkflowId} due to usage limit exceeded. StateId: {StateId}",
                state.Identity.SleekflowCompanyId,
                targetWorkflow.WorkflowId,
                state.Id);

            return false;
        }

        var existingStates = await _stateService.GetRunningStatesAsync(
            state.Identity.ObjectId,
            state.Identity.ObjectType,
            state.Identity.SleekflowCompanyId,
            state.TriggerEventBody);

        var workflowEnrollmentFilterResult = await HandleWorkflowEnrollmentAsync(
            state.TriggerEventBody,
            state.Identity.ObjectId,
            state.Identity.ObjectType,
            existingStates,
            [new CompactWorkflow(targetWorkflow)],
            flowHubConfig,
            isWithinUsageLimit,
            isReenrollment: true);

        return workflowEnrollmentFilterResult.WorkflowsToEnroll
            .Any(x => x.WorkflowVersionedId == state.Identity.WorkflowVersionedId);
    }

    private async Task<WorkflowEnrollmentFilterResult> HandleWorkflowEnrollmentAsync(
        EventBody eventBody,
        string objectId,
        string objectType,
        List<ProxyState> existingStates,
        List<CompactWorkflow> matchedCompactWorkflows,
        FlowHubConfig flowHubConfig,
        bool isWithinUsageLimit,
        bool isReenrollment)
    {
        _logger.LogInformation("[HandleWorkflowEnrollment] eventBody: {EventBoby}, objectId: {ObjectId}, objectType: {ObjectType}", eventBody, objectId, objectType);
        // Get running workflow ids
        var runningWorkflowIds = existingStates
            .Select(x => x.WorkflowContext.SnapshottedWorkflow.WorkflowId)
            .ToList();

        // Get workflows that the contact has enrolled in before this
        var completeExecutionWorkflowIds =
            await _workflowExecutionService.GetCompleteExecutionWorkflowIdsAsync(
                objectId,
                objectType,
                flowHubConfig.SleekflowCompanyId);

        // Filter workflows based on enrollment settings
        var workflowEnrollmentFilterResult = _workflowEnrollmentFilter.Filter(
            matchedCompactWorkflows,
            runningWorkflowIds,
            completeExecutionWorkflowIds);
        _logger.LogInformation("[HandleWorkflowEnrollment] workflowEnrollmentFilterResult: {WorkflowEnrollmentFilterResult}", workflowEnrollmentFilterResult);
        // Parallel flow enrollments
        foreach (var compactWorkflow in workflowEnrollmentFilterResult.WorkflowsToEnroll)
        {
            var workflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
                compactWorkflow.SleekflowCompanyId,
                compactWorkflow.WorkflowVersionedId);

            if (workflow is not { ActivationStatus: WorkflowActivationStatuses.Active })
            {
                continue;
            }

            var isWorkflowEnrollmentHitRateLimit = await IsWorkflowEnrollmentHitRateLimitAsync(
                workflow,
                objectId);

            if (isWorkflowEnrollmentHitRateLimit)
            {
                await HandleBlockedWorkflowEnrollmentAsync(
                    objectId,
                    objectType,
                    eventBody,
                    workflow,
                    flowHubConfig,
                    isReenrollment);
            }
            else if (!isWithinUsageLimit
                     && workflow.WorkflowScheduleSettings.IsNonOldScheduledWorkflowSchema())
            {
                // Create restricted state only for non-old-scheduled workflow trigger
                // New Scheduled Workflow Usage Limit Exceeded Handling (Original Handling is on ScheduledTriggerConditionsCheckStepExecutor)
                var state = await _stateService.CreateStateAsync(
                    objectId,
                    objectType,
                    workflow.SleekflowCompanyId,
                    StateStatuses.Restricted,
                    StateReasonCodes.EnrollmentUsageLimitExceeded,
                    TimeToLiveConstants.OneYearInSeconds,
                    isReenrollment,
                    workflow,
                    eventBody,
                    flowHubConfig);

                await _bus.Publish(
                    new OnWorkflowExecutionRestrictedEvent(
                        state.Identity.SleekflowCompanyId,
                        state.Id,
                        state.Identity,
                        state.WorkflowContext.SnapshottedWorkflow.WorkflowType));
            }
            else
            {
                var executionReasonCode = GetWorkflowExecutionReasonCode(workflow, workflowEnrollmentFilterResult);

                if (workflow.WorkflowScheduleSettings.IsNonOldScheduledWorkflowSchema())
                {
                    if ((workflow.WorkflowScheduleSettings.IsNewScheduledWorkflowSchema is true
                         || workflow.WorkflowScheduleSettings
                             .IsOldScheduledWorkflowSchemaFirstRecurringCompleted is true) && (
                        eventBody is OnScheduledWorkflowEnrollmentEventBody
                        || eventBody is OnDateAndTimeArrivedCommonEventBody))
                    {
                        ContactDetail CreateContactDetail(object evt)
                        {
                            if (evt is OnScheduledWorkflowEnrollmentEventBody enrollment)
                            {
                                return new ContactDetail(enrollment.Contact, enrollment.ContactOwner, enrollment.Lists, enrollment.Conversation);
                            }
                            if (evt is OnDateAndTimeArrivedCommonEventBody dateTime)
                            {
                                return new ContactDetail(dateTime.Contact, dateTime.ContactOwner, dateTime.Lists, dateTime.Conversation);
                            }

                            return null;
                        }

                        ContactDetail contactDetail = CreateContactDetail(eventBody);
                        await _workflowStateService.CreateStateAndExecutedWorkflowAsync(
                            eventBody,
                            objectId,
                            objectType,
                            flowHubConfig.SleekflowCompanyId,
                            StateStatuses.Running,
                            workflow,
                            flowHubConfig,
                            executionReasonCode,
                            isReenrollment,
                            contactDetail,
                            runningStates: existingStates);
                    }
                    else
                    {
                        await _workflowStateService.CreateStateAndExecutedWorkflowAsync(
                            eventBody,
                            objectId,
                            objectType,
                            flowHubConfig.SleekflowCompanyId,
                            StateStatuses.Running,
                            workflow,
                            flowHubConfig,
                            executionReasonCode,
                            isReenrollment,
                            runningStates: existingStates);
                    }
                }
                else if (eventBody is OnFbIgPostCommentReceivedEventBody fbIgPostCommentReceivedEventBody && fbIgPostCommentReceivedEventBody.EventName == "OnFbIgPostCommentReceivedEvent")
                {
                    var onFbIgPostCommentedEvent = new OnFbIgPostCommentedEvent(
                        fbIgPostCommentReceivedEventBody,
                        objectId,
                        "Contact",
                        flowHubConfig.SleekflowCompanyId);
                    await _serviceBus.PublishAsync(
                        onFbIgPostCommentedEvent);

                }
                else // Old Scheduled Workflow Schema && using in step enrollment condition check logic
                {
                    await _workflowStateService.CreateStateAndExecutedWorkflowAsync(
                        eventBody,
                        objectId,
                        objectType,
                        flowHubConfig.SleekflowCompanyId,
                        StateStatuses.Scheduled,
                        workflow,
                        flowHubConfig,
                        executionReasonCode,
                        isReenrollment);
                }
            }
        }

        foreach (var compactWorkflow in workflowEnrollmentFilterResult.WorkflowsSkippedDueToCanEnrollOnlyOnce)
        {
            var workflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
                compactWorkflow.SleekflowCompanyId,
                compactWorkflow.WorkflowVersionedId);

            if (workflow is not { ActivationStatus: WorkflowActivationStatuses.Active })
            {
                continue;
            }

            var state = await _stateService.CreateStateAsync(
                objectId,
                objectType,
                workflow.SleekflowCompanyId,
                StateStatuses.Blocked,
                StateReasonCodes.EnrollOnlyOncePolicy,
                TimeToLiveConstants.OneYearInSeconds,
                isReenrollment,
                workflow,
                eventBody,
                flowHubConfig);

            await _bus.Publish(
                new OnWorkflowExecutionBlockedEvent(
                    state.Identity.SleekflowCompanyId,
                    state.Id,
                    null,
                    state.Identity,
                    workflow.WorkflowType));
        }

        foreach (var compactWorkflow in workflowEnrollmentFilterResult.WorkflowsSkippedDueToCanEnrollOnlyOnFailure)
        {
            var workflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
                compactWorkflow.SleekflowCompanyId,
                compactWorkflow.WorkflowVersionedId);

            if (workflow is not { ActivationStatus: WorkflowActivationStatuses.Active })
            {
                continue;
            }

            var state = await _stateService.CreateStateAsync(
                objectId,
                objectType,
                workflow.SleekflowCompanyId,
                StateStatuses.Blocked,
                StateReasonCodes.EnrollAgainOnFailureOnlyPolicy,
                TimeToLiveConstants.OneYearInSeconds,
                isReenrollment,
                workflow,
                eventBody,
                flowHubConfig);

            await _bus.Publish(
                new OnWorkflowExecutionBlockedEvent(
                    state.Identity.SleekflowCompanyId,
                    state.Id,
                    null,
                    state.Identity,
                    workflow.WorkflowType));
        }

        return workflowEnrollmentFilterResult;
    }

    private static string? GetWorkflowExecutionReasonCode(
        ProxyWorkflow workflow,
        WorkflowEnrollmentFilterResult workflowEnrollmentFilterResult)
    {
        return workflowEnrollmentFilterResult.WorkflowsIncludedDueToCanEnrollInParallel
            .Any(w => w.WorkflowId == workflow.WorkflowId)
            ? WorkflowExecutionReasonCodes.ParallelismEnabled
            : null;
    }

    private async Task AggregateStateAndExecuteWorkflowSubscriptionsAsync(
        EventBody eventBody,
        ProxyState existingState)
    {
        var stateSubscriptions =
            await _stateSubscriptionService.GetStateSubscriptionsAsync(existingState.Id);
        var matchedStateSubscriptions = stateSubscriptions
            .Where(s => s.EventName == eventBody.EventName)
            .ToList();

        // Evaluate the condition for each state subscription one by one
        foreach (var subscription in matchedStateSubscriptions)
        {
            var hasFulfilledSubscriptionCondition = await _stateEvaluator.EvaluateExpressionAsync(
                existingState,
                subscription.Condition,
                eventBody);

            if (hasFulfilledSubscriptionCondition is true)
            {
                try
                {
                    // If the ETag is matched, then the following will be executed
                    await _stateSubscriptionService.UpdateStateSubscriptionIsExecutedAsync(
                        subscription,
                        true);
                }
                catch (CosmosException e) when (e.StatusCode == HttpStatusCode.PreconditionFailed)
                {
                    // This means the other instance has already executed based on the subscription
                    continue;
                }

                await _stateAggregator.AggregateStateStepBodyAsync(
                    existingState,
                    subscription.StepId,
                    JsonConvert.SerializeObject(eventBody, JsonConfig.DefaultJsonSerializerSettings));

                await _stepExecutorActivator.CompleteStepAsync(
                    existingState.Id,
                    subscription.StepId,
                    subscription.StackEntries,
                    StepExecutionStatuses.Complete,
                    eventBody);
            }
        }
    }

    private Task<bool> IsWorkflowEnrollmentHitRateLimitAsync(
        ProxyWorkflow workflow,
        string objectId)
    {
        return _requestRateLimiter.IsHitLimitAsync(
            $"workflow-enrollment-{workflow.SleekflowCompanyId}",
            $"workflow-enrollment:{workflow.SleekflowCompanyId}:{workflow.WorkflowVersionedId}:{objectId}",
            _workflowRateLimitConfig.EnrollmentRateLimitWindowSeconds,
            _workflowRateLimitConfig.MaxEnrollmentAllowedWithinWindow);
    }

    private Task<bool> IsSleekflowEmailNotificationSendingHitRateLimitAsync(ProxyWorkflow workflow)
    {
        return _requestRateLimiter.IsHitLimitAsync(
            $"workflow-enrollment-email-notification:{workflow.SleekflowCompanyId}",
            $"workflow-enrollment-email-notification:{workflow.SleekflowCompanyId}:{workflow.WorkflowVersionedId}",
            _workflowEnrollmentRateLimitEmailNotificationConfig.BlockedEnrollmentEmailNotificationRateLimitWindowSeconds,
            _workflowEnrollmentRateLimitEmailNotificationConfig.MaxBlockedEnrollmentEmailAllowedWithinWindow);
    }

    private async Task HandleBlockedWorkflowEnrollmentAsync(
        string objectId,
        string objectType,
        EventBody eventBody,
        ProxyWorkflow workflow,
        FlowHubConfig flowHubConfig,
        bool isReenrollment)
    {
        var state = await _stateService.CreateStateAsync(
            objectId,
            objectType,
            workflow.SleekflowCompanyId,
            StateStatuses.Blocked,
            StateReasonCodes.EnrollmentRateLimited,
            TimeToLiveConstants.OneYearInSeconds,
            isReenrollment,
            workflow,
            eventBody,
            flowHubConfig);

        await _bus.Publish(
            new OnWorkflowExecutionBlockedEvent(
                state.Identity.SleekflowCompanyId,
                state.Id,
                null,
                state.Identity,
                state.WorkflowContext.SnapshottedWorkflow.WorkflowType));

        var isSleekflowEmailNotificationSendingHitRateLimit =
            await IsSleekflowEmailNotificationSendingHitRateLimitAsync(workflow);

        if (!isSleekflowEmailNotificationSendingHitRateLimit)
        {
            await SleekflowEmailNotificationServiceCall.SendWorkflowInfiniteLoopEmailAsync(
                workflow.SleekflowCompanyId,
                workflow.WorkflowId,
                workflow.Name,
                flowHubConfig.Origin,
                DateTimeOffset.UtcNow);
        }
    }

    private async Task<Dictionary<string, object?>?> TryFetchContactDataAsync(
        string objectId,
        string objectType,
        string sleekflowCompanyId,
        FlowHubConfig flowHubConfig)
    {
        if (objectType?.ToLowerInvariant() == FlowHubEventObjectTypes.Contact &&
            !string.IsNullOrEmpty(objectId) &&
            !string.IsNullOrEmpty(sleekflowCompanyId))
        {
            try
            {
                _logger.LogInformation(
                    "Attempting to fetch contact data in FlowHubEventHandler for ObjectId: {ObjectId}, CompanyId: {CompanyId}, Origin: {Origin}",
                    objectId,
                    sleekflowCompanyId,
                    flowHubConfig.Origin);

                var fetchedContactData = await _contactDataProvider.GetContactDataFromOriginAsync(
                    flowHubConfig.Origin,
                    objectId,
                    sleekflowCompanyId);

                if (fetchedContactData != null && fetchedContactData.Count > 0)
                {
                    _logger.LogInformation(
                        "Successfully fetched contact data in FlowHubEventHandler for ObjectId: {ObjectId}, CompanyId: {CompanyId}. Data: {ContactData}",
                        objectId,
                        sleekflowCompanyId,
                        JsonConvert.SerializeObject(
                            fetchedContactData,
                            new JsonSerializerSettings
                            {
                                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                                NullValueHandling = NullValueHandling.Ignore,
                            }));
                    return fetchedContactData;
                }
                else
                {
                    _logger.LogWarning(
                        "Failed to fetch contact data or contact data was empty in FlowHubEventHandler for ObjectId: {ObjectId}, CompanyId: {CompanyId}",
                        objectId,
                        sleekflowCompanyId);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,
                    "Error fetching contact data in FlowHubEventHandler for ObjectId: {ObjectId}, CompanyId: {CompanyId}",
                    objectId,
                    sleekflowCompanyId);
                return null; // Return null on error
            }
        }
        else if (objectType?.ToLowerInvariant() != FlowHubEventObjectTypes.Contact)
        {
            _logger.LogInformation(
               "Skipping contact data fetch in FlowHubEventHandler as ObjectType ('{ObjectType}') is not '{ContactType}' for ObjectId: {ObjectId}",
               objectType,
               FlowHubEventObjectTypes.Contact,
               objectId);
        }
        else
        {
            _logger.LogWarning("ObjectId or SleekflowCompanyId is null or empty in FlowHubEventHandler. Skipping contact data fetch.");
        }
        return null; // Return null if not fetched
    }
}