using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.Snapshots;

public class AgentRecommendReplySnapshotDto : IHasCreatedAt
{
    [JsonProperty("conversation_context")]
    public string ConversationContext { get; set; }

    [JsonProperty("knowledge_base_entries")]
    public string KnowledgeBaseEntries { get; set; }

    [JsonProperty("output_message")]
    public string OutputMessage { get; set; }

    [JsonProperty("collaboration_mode")]
    public string CollaborationMode { get; set; }

    [JsonProperty("confidence_score")]
    public int ConfidenceScore { get; set; }

    [JsonProperty("agent_versioned_id")]
    public string? AgentVersionedId { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonConstructor]
    public AgentRecommendReplySnapshotDto(
        string conversationContext,
        string knowledgeBaseEntries,
        string outputMessage,
        string collaborationMode,
        int confidenceScore,
        string? agentVersionedId,
        DateTimeOffset createdAt)
    {
        ConversationContext = conversationContext;
        KnowledgeBaseEntries = knowledgeBaseEntries;
        OutputMessage = outputMessage;
        CollaborationMode = collaborationMode;
        ConfidenceScore = confidenceScore;
        AgentVersionedId = agentVersionedId;
        CreatedAt = createdAt;
    }

    public AgentRecommendReplySnapshotDto(AgentRecommendReplySnapshot snapshot, DateTimeOffset createdAt)
        : this(
            snapshot.ConversationContext,
            snapshot.KnowledgeBaseEntries,
            snapshot.OutputMessage,
            snapshot.CollaborationMode,
            snapshot.ConfidenceScore,
            snapshot.AgentVersionedId,
            createdAt)
    {
    }

}