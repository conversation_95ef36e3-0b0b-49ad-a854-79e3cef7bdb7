﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.TenantHub.Models.Authentications;
using Sleekflow.TenantHub.Models.Constants;
using Sleekflow.TenantHub.Models.Rbac;
using Sleekflow.TenantHub.Models.Users;
using Sleekflow.TenantHub.Rbac;
using Sleekflow.TenantHub.Roles;
using Sleekflow.TenantHub.Users;

namespace Sleekflow.TenantHub.Triggers.Internals;

[TriggerGroup(ControllerNames.Internals)]
public class GetUserAuthenticationDetails
    : ITrigger<
        GetUserAuthenticationDetails.GetUserAuthenticationDetailsInput,
        GetUserAuthenticationDetails.GetUserAuthenticationDetailsOutput>
{
    private readonly IUserService _userService;
    private readonly ILogger<GetUserAuthenticationDetails> _logger;
    private readonly IRbacService _rbacService;

    public GetUserAuthenticationDetails(IUserService userService, ILogger<GetUserAuthenticationDetails> logger, IRbacService rbacService)
    {
        _userService = userService;
        _logger = logger;
        _rbacService = rbacService;
    }

    public class GetUserAuthenticationDetailsInput
    {
        [JsonProperty("sleekflow_tenanthub_user_id")]
        public string? SleekflowTenantHubUserId { get; set; }

        [Required]
        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        [JsonProperty("sleekflow_email")]
        public string? SleekflowEmail { get; set; }

        [JsonProperty("sleekflow_company_id")]
        public string? SleekflowCompanyId { get; set; }

        [JsonProperty("targeted_company_id")]
        public string? TargetedCompanyId { get; set; }

        [JsonProperty("login_as_user")]
        [Validations.ValidateObjectAttribute]
        public LoginAsUser? LoginAsUser { get; set; }

        [JsonConstructor]
        public GetUserAuthenticationDetailsInput(
            string? sleekflowTenantHubUserId,
            string sleekflowUserId,
            string? sleekflowEmail,
            string? sleekflowCompanyId,
            string? targetedCompanyId,
            LoginAsUser? loginAsUser)
        {
            SleekflowTenantHubUserId = sleekflowTenantHubUserId;
            SleekflowUserId = sleekflowUserId;
            SleekflowEmail = sleekflowUserId;
            SleekflowCompanyId = sleekflowCompanyId;
            TargetedCompanyId = targetedCompanyId;
            LoginAsUser = loginAsUser;
        }
    }

    public class GetUserAuthenticationDetailsOutput
    {
        [JsonProperty("sleekflow_tenanthub_user_id")]
        public string? SleekflowTenantHubUserId { get; set; }

        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        [JsonProperty("sleekflow_staff_id")]
        public string SleekflowStaffId { get; set; }

        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("sleekflow_roles")]
        public List<string> SleekflowRoles { get; set; }

        [JsonProperty("sleekflow_role_ids")]
        public List<string> SleekflowRoleIds { get; set; }

        [JsonProperty("sleekflow_team_ids")]
        public List<string> SleekflowTeamIds { get; set; }

        [JsonProperty("sleekflow_impersonator")]
        public Impersonator? SleekflowImpersonator { get; set; }

        [JsonProperty("is_rbac_enabled")]
        public bool IsRbacEnabled { get; set; }

        [JsonProperty("rbac_role")]
        public List<string>? RbacRoles { get; set; }

        [JsonConstructor]
        public GetUserAuthenticationDetailsOutput(
            string sleekflowTenantHubUserId,
            string sleekflowUserId,
            string sleekflowStaffId,
            string sleekflowCompanyId,
            List<string> sleekflowRoles,
            List<string> sleekflowRoleIds,
            List<string> sleekflowTeamIds,
            Impersonator? sleekflowImpersonator)
        {
            SleekflowTenantHubUserId = sleekflowTenantHubUserId;
            SleekflowUserId = sleekflowUserId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowRoles = sleekflowRoles;
            SleekflowRoleIds = sleekflowRoleIds;
            SleekflowTeamIds = sleekflowTeamIds;
            SleekflowImpersonator = sleekflowImpersonator;
        }

        public GetUserAuthenticationDetailsOutput(
            string sleekflowTenantHubUserId,
            UserWorkspace userWorkspace,
            List<string> sleekflowRoles,
            Impersonator? sleekflowImpersonator)
            : this(
                sleekflowTenantHubUserId,
                userWorkspace.SleekflowUserId,
                userWorkspace.SleekflowStaffId,
                userWorkspace.SleekflowCompanyId,
                sleekflowRoles,
                userWorkspace.SleekflowRoleIds,
                userWorkspace.SleekflowTeamIds,
                sleekflowImpersonator)
        {
        }
    }

    // TODO : Check if the user able to impersonate
    public async Task<GetUserAuthenticationDetailsOutput> F(
        GetUserAuthenticationDetailsInput getUserAuthenticationDetailsInput)
    {
        Impersonator? sleekflowImpersonator = null;
        var loginAsUser = getUserAuthenticationDetailsInput.LoginAsUser;
        var sleekflowUserId = getUserAuthenticationDetailsInput.SleekflowUserId;
        var sleekflowCompanyId = getUserAuthenticationDetailsInput.SleekflowCompanyId;
        var targetedCompanyId = getUserAuthenticationDetailsInput.TargetedCompanyId;
        var sleekflowTenantHubUserId = getUserAuthenticationDetailsInput.SleekflowTenantHubUserId;

        if (loginAsUser != null && loginAsUser.ExpireAt >= DateTimeOffset.UtcNow)
        {
            _logger.LogInformation(
                "[{UserAuthenticationDetailsName}] User ({SleekflowUserId}) dive into the tenanthub_user: {TenantHubUserId}, sleekflow user: {SleekflowUserId}, company: {CompanyId}",
                nameof(GetUserAuthenticationDetails),
                sleekflowUserId,
                loginAsUser.TenantHubUserId,
                loginAsUser.SleekflowUserId,
                loginAsUser.CompanyId);
            // TODO: Need to ask team-ironman on TargetedCompanyId
            var impersonateUserId = string.IsNullOrEmpty(loginAsUser.TenantHubUserId)
                ? loginAsUser.SleekflowUserId
                : loginAsUser.TenantHubUserId;
            var impersonator = await _userService.GetUserAsync(
                impersonateUserId,
                loginAsUser.CompanyId);
            sleekflowImpersonator = new Impersonator
            {
                SleekflowUserId = impersonator.UserWorkspace.SleekflowUserId,
                SleekflowCompanyId = impersonator.UserWorkspace.SleekflowCompanyId,
                SleekflowStaffId = impersonator.UserWorkspace.SleekflowStaffId,
                SleekflowTenantHubUserId = impersonator.TenantHubUserId
            };
            sleekflowUserId = loginAsUser.SleekflowUserId;
            sleekflowTenantHubUserId = loginAsUser.TenantHubUserId;
            targetedCompanyId = loginAsUser.CompanyId;
        }

        _logger.LogInformation(
            "[{UserAuthenticationDetailsName}] Get user details for sleekflow id: {SleekflowUserId}, tenanthub id: {SleekflowTenantHubUserId}, company id {CompanyId}",
            nameof(GetUserAuthenticationDetails),
            sleekflowUserId,
            sleekflowTenantHubUserId,
            sleekflowCompanyId);

        var userId = string.IsNullOrEmpty(sleekflowTenantHubUserId) ? sleekflowUserId : sleekflowTenantHubUserId;
        var user = await _userService.GetUserAsync(
            userId,
            sleekflowCompanyId);

        if (!string.IsNullOrEmpty(user.UserWorkspace?.SleekflowCompanyId))
        {
            var isRbacEnabled = await _rbacService.IsRbacFeatureEnabledForCompanyAsync(user.UserWorkspace.SleekflowCompanyId);
            if (isRbacEnabled)
            {
                    var rbacRoles = await _rbacService.GetAllRolesInCompanyAsync(
                        user.UserWorkspace.SleekflowCompanyId);
                    var userRoles = rbacRoles
                        .Where(r => user.UserWorkspace.SleekflowRoleIds.Contains(r.Id))
                        .Select(r => r.Name)
                        .ToList();

                    return new GetUserAuthenticationDetailsOutput(
                        user.TenantHubUserId,
                        user.UserWorkspace,
                        userRoles,
                        sleekflowImpersonator)
                {
                    IsRbacEnabled = isRbacEnabled,
                    RbacRoles =
                        rbacRoles.Count == 0 ?[] : rbacRoles.Select(f => f.Name).ToList()
                };
            }
        }

        return new GetUserAuthenticationDetailsOutput(
            user.TenantHubUserId,
            user.UserWorkspace!,
            user.RoleNames,
            sleekflowImpersonator);
    }
}