<!DOCTYPE html>

<html lang="en">
<head>
  <meta charset="utf-8" />
  <title>Sleekflow Send Message Custom Activity</title>
  <link
    rel="stylesheet"
    href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css"
    integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T"
    crossorigin="anonymous"
  />
  <script
    type="text/javascript"
    src="//ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js"
  ></script>
  <script type="text/javascript" src="js/require.js"></script>
  <script type="text/javascript">
    (function () {
      var config = {
        baseUrl: "js",
      };

      var dependencies = ["customActivity"];

      require(config, dependencies);
    })();
  </script>

  <!--Styles-->
  <style type="text/css">
    body {
      padding: 20px;
      margin: 0;
    }

    #step1 {
      display: block;
    }
  </style>
</head>

<body>
<nav class="navbar navbar-expand-lg navbar-light">
  <div class="container px-5">
    <a href="https://sleekflow.io">
      <svg
        class="w-50 p-3"
        id="sleekflow-logo"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 3000 460.8"
        xml:space="preserve"
        class="w-32 sm:w-36 md:w-44"
      >
            <path
              fill="#0c122e"
              d="m230.6 380 27.5-35.5c21.7 22.3 52.6 36.6 90.4 36.6 38.3 0 67.5-14.9 67.5-41.2 0-28-32.6-36.6-71-45.8-48.1-12-104.7-26.9-104.7-88.1 0-56.7 51.5-85.8 111.6-85.8 46.3 0 82.4 17.7 107 40.1l-27.5 36.1c-19.4-18.9-46.9-31.5-79-31.5-33.8 0-61.8 13.2-61.8 38.3 0 28.6 33.2 36.6 72.1 45.8 48.1 11.4 104.2 25.8 104.2 86.4 0 57.8-52.1 90.4-117.9 90.4-51.4 0-91.5-20.1-118.4-45.8zM932.8 294.7H681.6c9.7 50.9 51.5 85.8 108.2 85.8 42.9 0 79.5-20.6 98.4-47.5l33.8 29.2c-29.2 38.9-77.3 63.5-133.9 63.5-91.6 0-159.7-66.4-159.7-153.9 0-86.4 65.8-151.6 153.4-151.6 87 0 152.2 65.2 152.2 152.8-.1 6.8-.7 14.8-1.2 21.7zm-249.5-44.6h200.9c-9.2-50.4-49.2-84.7-100.7-84.7-51 0-91.1 34.9-100.2 84.7zM1279 294.7h-251.2c9.7 50.9 51.5 85.8 108.2 85.8 42.9 0 79.5-20.6 98.4-47.5l33.8 29.2c-29.2 38.9-77.3 63.5-133.9 63.5-91.6 0-159.7-66.4-159.7-153.9 0-86.4 65.8-151.6 153.4-151.6 87 0 152.2 65.2 152.2 152.8 0 6.8-.6 14.8-1.2 21.7zm-251.8-44.6h200.9c-9.2-50.4-49.2-84.7-100.7-84.7-51 0-91 34.9-100.2 84.7zM1423.2 301.7l-37.8 40.1V419h-49.2V25.6l49.2-17v266.8L1525 127.2h64.1L1458 265.1 1593.2 419H1528l-104.8-117.3zM1754.6 0c20.6 0 34.9 4 50.4 12v43.5c-12-6.3-24-10.3-40.1-10.3-29.8 0-46.9 13.2-46.9 44.6V127h84.1v44.6H1718v247.2h-49.2V171.7h-53.2V127h53.2V86.4c-.1-54.9 29.1-86.4 85.8-86.4zM1907.4 6.9v412h-49.2v-395zM572.3 6.9v412H523v-395z"
            ></path>
        <path fill="#4b9be8" d="M0 376.6h174.5v49.2H0z"></path>
        <path
          fill="#0c122e"
          d="M1963.4 273c0-87 67-152.8 155.7-152.8 88.1 0 155.1 65.8 155.1 152.8 0 87-67 152.8-155.1 152.8-88.7 0-155.7-65.9-155.7-152.8zm259.8 0c0-60.1-44.6-105.3-104.1-105.3-60.1 0-104.7 45.2-104.7 105.3s44.6 105.3 104.7 105.3c59.5 0 104.1-45.2 104.1-105.3zM2774.9 127l-107 291.9h-51.5l-80.7-233.5-80.7 233.5h-51.5l-107-291.9h55.5l79 231.8 80.1-231.8h50.4l80.7 232.3 79-232.3h53.7z"
        ></path>
        <path fill="#4b9be8" d="M2815.4 0h174.5v174.5L2815.4 0"></path>
          </svg>
    </a>
  </div>
</nav>

<header
  class="py-5 border rounded w-80"
  style="background-color: rgba(13, 18, 43, 255)"
>
  <div class="container px-5">
    <div class="row gx-5 mr-4">
      <div class="col-lg">
        <div class="text-center my-5">
          <h1 class="display-5 fw-bolder text-white mb-2">
            Welcome to SleekFlow Journey Builder
          </h1>
          <p class="lead text-white-50 mb-4">
            Create a WhatsApp Cloud Api Template Message
          </p>
          <div class="d-grid gap-3 d-sm-flex justify-content-sm-center">
            <a
              class="btn btn-primary btn-lg px-4 me-sm-3"
              href="https://sleekflow.io"
            >Learn More</a
            >
          </div>
        </div>
      </div>
      <div class="col col-lg">
        <img
          src="./images/whats.png"
          alt="Girl in a jacket"
          class="img-fluid"
        />
      </div>
    </div>
  </div>
</header>

<div class="container row justify-content-center">
  <!-- data extension -->
  <div class="p-4 col-md">
    <div class="card mt-4" style="width: 18rem">
      <div class="card-header">Data Extension Options</div>
      <ul class="list-group list-group-flush" id="dataExtension"></ul>
    </div>
  </div>

  <!-- user input form -->
  <div class="py-4 col-md">
    <div class="w-100">
      <form id="myForm">
        <div class="form-group">
          <label for="apiKey" class="font-weight-bold">API Key:</label>
          <input
            type="text"
            class="form-control"
            id="apiKey"
            placeholder="Enter your SleekFlow SFMC API key"
            name="apiKey"
            value=""
          />
        </div>
        <div class="form-group">
          <label for="whatsappPhoneNumber" class="font-weight-bold"
          >WhatsApp Channel Number:</label
          >
          <input
            type="text"
            class="form-control"
            id="whatsappPhoneNumber"
            name="whatsappPhoneNumber"
            value=""
            placeholder="Enter the phone number to send the message"
          />
        </div>
        <div class="form-group">
          <label for="recipientPhoneNumber" class="font-weight-bold"
          >Recipient Phone Number:</label
          >
          <input
            type="text"
            class="form-control"
            id="recipientPhoneNumber"
            name="recipientPhoneNumber"
            value=""
            placeholder="Enter recipient's phone number"
          />
        </div>
        <div class="form-group">
          <label for="analyticTags" class="font-weight-bold"
          >Analytic Tags (separated by commas):</label
          >
          <input
            type="text"
            class="form-control"
            id="analyticTags"
            name="analyticTags"
            value=""
            placeholder="Enter analytic tags"
          />
        </div>
        <div class="form-group">
          <label for="messageType" class="font-weight-bold"
          >Message Type:</label
          >
          <select class="form-control" id="messageType" name="messageType">
            <option value="text">Text</option>
            <option value="template">Template</option>
            <option value="file">File</option>
          </select>
        </div>

        <div id="fileUpload" style="display: none">
          <input id="fileInput" type="file" name="file" />
          <hr />
        </div>

        <div class="form-group" id="content">
          <label for="messageContent" class="font-weight-bold"
          >Message Content:</label
          >
          <textarea
            class="form-control"
            id="messageContent"
            rows="3"
            name="messageContent"
            value=""
            placeholder="Enter the message content, not relevant for template messages"
          ></textarea>
        </div>
        <div class="form-group" id="payload" style="display: none">
          <label for="templatePayload" class="font-weight-bold"
          >Template Payload:</label
          >

          <textarea
            class="form-control"
            id="templatePayload"
            rows="10"
            name="templatePayload"
            value=""
            placeholder="Enter the payload for the template, copy it from SleekFlow platform"
          ></textarea>


        </div>
      </form>
    </div>
  </div>
</div>

<script>
  const messageTypeSelect = document.getElementById("messageType");
  const fileUpload = document.getElementById("fileUpload");
  // const templateConfig = document.getElementById("templateConfig");
  const messageContent = document.getElementById("content");
  const templatePayload = document.getElementById("payload");

  messageTypeSelect.addEventListener("change", (event) => {
    switch (event.target.value) {
      case "text":
        fileUpload.style.display = "none";
        fileUpload.style.display = "none";
        templatePayload.style.display = "none";
        messageContent.style.display = "block";
        break;
      case "file":
        fileUpload.style.display = "block";
        messageContent.style.display = "none";
        templatePayload.style.display = "none";
        break;
      case "template":
        fileUpload.style.display = "block";
        messageContent.style.display = "none";
        templatePayload.style.display = "block";
        break;
    }
  });
</script>

<!-- <script type="text/javascript" src="js/test.js"></script> -->
</body>
</html>
