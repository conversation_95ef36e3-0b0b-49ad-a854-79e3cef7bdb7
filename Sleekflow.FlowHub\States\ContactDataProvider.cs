using Sleekflow.DataProviders;
using Sleekflow.Models.Contact;
using Sleekflow.DependencyInjection;
using System.Text;
using Newtonsoft.Json;
using Polly;
using Sleekflow.FlowHub.Configs;
using Sleekflow.FlowHub.JsonConfigs;
using Sleekflow.Utils;
using Sleekflow.Attributes;
using static Sleekflow.FlowHub.States.Functions.SleekflowFunctions;

namespace Sleekflow.FlowHub.States;

[SwaggerInclude]
public class GetContactDetailInput
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; }

    [JsonProperty("sleekflow_contact_id")]
    public string SleekflowContactId { get; }

    [JsonConstructor]
    public GetContactDetailInput(string sleekflowCompanyId, string sleekflowContactId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        SleekflowContactId = sleekflowContactId;
    }
}

[SwaggerInclude]
public class GetContactDetailOutput
{
    [JsonProperty("contact")]
    public Dictionary<string, object?> Contact { get; }

    [JsonProperty("contact_owner")]
    public Dictionary<string, object?>? ContactOwner { get; }

    [JsonProperty("lists")]
    public ContactList[] Lists { get; }

    [JsonConstructor]
    public GetContactDetailOutput(
        Dictionary<string, object?> contact,
        Dictionary<string, object?>? contactOwner,
        ContactList[] lists)
    {
        Contact = contact;
        ContactOwner = contactOwner;
        Lists = lists;
    }
}
/// <summary>
/// Provider for retrieving contact data.
/// </summary>
public interface IContactDataProvider
{
    /// <summary>
    /// Gets the contact data as a dictionary suitable for Scriban template evaluation.
    /// </summary>
    /// <param name="contactId">The contact ID.</param>
    /// <param name="sleekflowCompanyId">The company ID.</param>
    /// <returns>Dictionary containing contact properties, or an empty dictionary if not found or an error occurs.</returns>
    Task<Dictionary<string, object?>> GetContactDataFromOriginAsync(string origin, string contactId, string sleekflowCompanyId);
    Task SetContactDataAsync(string contactId, string sleekflowCompanyId, ContactCache contactDetails);
    Task InvalidateContactDataAsync(string contactId, string sleekflowCompanyId);
}

/// <summary>
/// Implementation of IContactDataProvider that retrieves contact data using <see cref="BaseContactDataProvider"/>
/// and fetches from TravisBackend as the source.
/// </summary>
public class ContactDataProvider : BaseContactDataProvider, IContactDataProvider, IScopedService
{
    private readonly ILogger<ContactDataProvider> _logger;
    private readonly IAppConfig _appConfig;
    private readonly HttpClient _httpClient;
    private readonly IAsyncPolicy<HttpResponseMessage> _retryPolicy;

    public ContactDataProvider(
        IContactCacheService contactCacheService,
        ILogger<ContactDataProvider> logger,
        ILogger<BaseContactDataProvider> baseLogger,
        IAppConfig appConfig,
        IHttpClientFactory httpClientFactory,
        IAsyncPolicy<HttpResponseMessage> retryPolicy)
        : base(contactCacheService, baseLogger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _appConfig = appConfig ?? throw new ArgumentNullException(nameof(appConfig));
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _retryPolicy = retryPolicy ?? throw new ArgumentNullException(nameof(retryPolicy));
    }

    public new async Task<Dictionary<string, object?>> GetContactDataFromOriginAsync(string origin, string contactId, string sleekflowCompanyId)
    {
        var cachedResource = await base.GetContactDataAsync(origin, contactId, sleekflowCompanyId);

        if (cachedResource == null)
            return new Dictionary<string, object?>();

        var noContactData = cachedResource.Contact == null ||
                                   cachedResource.Contact.Count == 0;

        var noContactOwner = false;
        //Comment for now, re-apply in the next phase
        // !cachedResource.Contact.ContainsKey("ContactOwner") &&
        // !string.IsNullOrEmpty(cachedResource.Contact["ContactOwner"]?.ToString()) && !string.IsNullOrEmpty(cachedResource.Contact["ContactOwner"]?.ToString())

        var noContactList = cachedResource.Lists == null;

        var shouldRefreshData = noContactData || noContactOwner || noContactList;

        if (shouldRefreshData)
        {
            var refreshedResource = await FetchContactFromSourceAsync(origin, contactId, sleekflowCompanyId);

            if (refreshedResource != null)
            {
                await base.SetContactDataAsync(contactId, sleekflowCompanyId, refreshedResource);
                cachedResource = refreshedResource;
            }
            else
            {
                _logger.LogWarning("Failed to refresh contact data for {ResourceId} from source", contactId);
            }
        }

        // Build response with available data, ensuring Contact data exists
        if (cachedResource.Contact == null)
        {
            _logger.LogWarning("Contact data is still null for {ResourceId} after refresh attempt", contactId);
            return new Dictionary<string, object?>();
        }

        var response = new Dictionary<string, object?>
        {
            ["contact"] = cachedResource.Contact
        };

        if (cachedResource.ContactOwner != null)
        {

            response["contactOwner"] = cachedResource.ContactOwner;
        }

        if (cachedResource.Lists != null)
        {
            response["lists"] = cachedResource.Lists;
        }

        return response;
    }

    public async Task SetContactDataAsync(string contactId, string sleekflowCompanyId, ContactCache contactDetails)
    {
        try
        {
            await base.SetContactDataAsync(contactId, sleekflowCompanyId, contactDetails);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting contact data to redis for contact {ContactId} and company {CompanyId}.", contactId, sleekflowCompanyId);
        }
    }

    public async Task InvalidateContactDataAsync(string contactId, string sleekflowCompanyId)
    {
        try
        {
            await base.InvalidateContactDataAsync(contactId, sleekflowCompanyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidate contact data in redis for contact {ContactId} and company {CompanyId}.", contactId, sleekflowCompanyId);
        }
    }

    private async Task<GetContactDetailOutput> GetContactDetailAsync(string domain, GetContactDetailInput input)
    {
        _logger.LogInformation(
            "Getting contact detail for company {CompanyId} and contact {ContactId}",
            input.SleekflowCompanyId,
            input.SleekflowContactId);

        var targetUri = new Uri(domain + "/FlowHub/Internals/Functions/GetContactDetail");

        var inputJsonStr = JsonConvert.SerializeObject(input, JsonConfig.DefaultJsonSerializerSettings);

        var pollyContext = new Context();
        pollyContext["logger"] = _logger; // Key "logger" must match what onRetryAsync expects

        var resMsg = await _retryPolicy.ExecuteAsync(
            async (context) =>
            {
                var reqMsg = new HttpRequestMessage
                {
                    Method = HttpMethod.Post,
                    Content = new StringContent(inputJsonStr, Encoding.UTF8, "application/json"),
                    RequestUri = targetUri,
                    Headers = { { "X-Sleekflow-Flow-Hub-Authorization", InternalsTokenUtils.CreateJwt(_appConfig.CoreInternalsKey) } },
                };

                // Log attempt using the instance logger
                _logger.LogInformation(
                    "Attempting HTTP POST to {Uri} for company {CompanyId}, contact {ContactId}",
                    targetUri,
                    input.SleekflowCompanyId,
                    input.SleekflowContactId);

                return await _httpClient.SendAsync(reqMsg);
            }, pollyContext); // Pass the context to ExecuteAsync

        resMsg.EnsureSuccessStatusCode();
        var resStr = await resMsg.Content.ReadAsStringAsync();

        var output = JsonConvert.DeserializeObject<GetContactDetailOutput>(resStr, JsonConfig.DefaultJsonSerializerSettings)!;

        return output;
    }

    // / <summary>
    // / Fetches contact details from the TravisBackend and converts it to <see cref="ContactCache"/>.
    // / This implementation is specific to FlowHub's data source.
    // / </summary>
    protected override async Task<ContactCache?> FetchContactFromSourceAsync(string origin, string contactId, string sleekflowCompanyId)
    {
        _logger.LogDebug("Fetching contact {ContactId} for company {CompanyId} using origin {Origin} from TravisBackend (ContactDataProvider override).", contactId, sleekflowCompanyId, origin);
        try
        {
            var contactInput = new GetContactDetailInput(sleekflowCompanyId, contactId);
            var contactFromDb = await GetContactDetailAsync(origin, contactInput);

            if (contactFromDb == null)
            {
                _logger.LogWarning("Contact {ContactId} for company {CompanyId} with origin {Origin} not found in TravisBackend.", contactId, sleekflowCompanyId, origin);
                return null;
            }

            ContactList[] contactLists = Array.Empty<ContactList>();

            if (contactFromDb.Contact.TryGetValue("lists", out var listsObj) && listsObj is Newtonsoft.Json.Linq.JArray listsArray)
            {
                contactLists = listsArray.ToObject<ContactList[]>() ?? Array.Empty<ContactList>();
            }

            var contactCache = new ContactCache(
                contactFromDb.Contact,
                contactFromDb.ContactOwner,
                contactLists
            );

            _logger.LogDebug("Successfully fetched and mapped contact {ContactId} for company {CompanyId} with origin {Origin} from TravisBackend.", contactId, sleekflowCompanyId, origin);
            return contactCache;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching contact {ContactId} for company {CompanyId} with origin {Origin} from TravisBackend.", contactId, sleekflowCompanyId, origin);
            return null;
        }
    }
}