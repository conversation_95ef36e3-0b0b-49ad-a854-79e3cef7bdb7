using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Workflows.Operators;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Workflows.Operators;

public interface IOperatorRepository : IRepository<Operator>
{
    Task<List<Operator>> GetOperatorsAsync(
        string variableType,
        string? triggerId);
}

public class OperatorRepository : BaseRepository<Operator>, IOperatorRepository, IScopedService
{
    public OperatorRepository(
        ILogger<OperatorRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public async Task<List<Operator>> GetOperatorsAsync(
        string variableType,
        string? triggerId)
    {
        QueryDefinition queryDefinition = null;
        if (string.IsNullOrWhiteSpace(triggerId))
        {
            queryDefinition = new QueryDefinition(@"
            SELECT *
            FROM %%CONTAINER_NAME%% r
            WHERE r.variable_type = @variable_type
            AND r.trigger_ids = ''
            AND r.status = @status
            ORDER BY r.id ASC")
                .WithParameter("@variable_type", variableType)
                .WithParameter("@status", "active");
        }
        else
        {
            queryDefinition = new QueryDefinition(@"
            SELECT *
            FROM %%CONTAINER_NAME%% r
            WHERE r.variable_type = @variable_type
            AND (ARRAY_CONTAINS(r.trigger_ids, @trigger_id) OR r.trigger_ids = '')
            AND r.status = @status
            ORDER BY r.id ASC")
                .WithParameter("@variable_type", variableType)
                .WithParameter("@trigger_id", triggerId)
                .WithParameter("@status", "active");
        }


        var operators = await GetObjectsAsync(queryDefinition);
        return operators;
    }
}