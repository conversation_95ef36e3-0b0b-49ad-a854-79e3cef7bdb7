using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;

public class DebugTimestamps
{
    [JsonProperty("conversion_started")]
    public DateTimeOffset? ConversionStarted { get; set; }

    [JsonProperty("conversion_ended")]
    public DateTimeOffset? ConversionEnded { get; set; }

    [JsonProperty("upload_started")]
    public DateTimeOffset? UploadStarted { get; set; }

    [JsonProperty("upload_ended")]
    public DateTimeOffset? UploadEnded { get; set; }

    [JsonConstructor]
    public DebugTimestamps(
        DateTimeOffset? conversionStarted = null,
        DateTimeOffset? conversionEnded = null,
        DateTimeOffset? uploadStarted = null,
        DateTimeOffset? uploadEnded = null)
    {
        ConversionStarted = conversionStarted;
        ConversionEnded = conversionEnded;
        UploadStarted = uploadStarted;
        UploadEnded = uploadEnded;
    }
}