using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Workers.Activities;

public class PatchAgentAssignmentRagStatus
{
    private readonly IKbDocumentService _kbDocumentService;

    public PatchAgentAssignmentRagStatus(
        IKbDocumentService kbDocumentService)
    {
        _kbDocumentService = kbDocumentService;
    }

    public class PatchAgentAssignmentRagStatusInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("document_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string DocumentId { get; set; }

        [JsonProperty("agent_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string AgentId { get; set; }

        [JsonProperty("rag_status")]
        [System.ComponentModel.DataAnnotations.Required]
        public string RagStatus { get; set; }

        [JsonProperty("progress_percentage")]
        [System.ComponentModel.DataAnnotations.Required]
        [Range(0.0, 100.0)]
        public double ProgressPercentage { get; set; }

        [JsonConstructor]
        public PatchAgentAssignmentRagStatusInput(
            string sleekflowCompanyId,
            string documentId,
            string agentId,
            string ragStatus,
            double progressPercentage)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DocumentId = documentId;
            AgentId = agentId;
            RagStatus = ragStatus;
            ProgressPercentage = progressPercentage;
        }
    }

    public class PatchAgentAssignmentRagStatusOutput
    {
    }

    [Function(nameof(PatchAgentAssignmentRagStatus))]
    public async Task<PatchAgentAssignmentRagStatusOutput> Batch(
        [ActivityTrigger]
        PatchAgentAssignmentRagStatusInput input)
    {
        await _kbDocumentService.PatchFileDocumentAgentAssignmentRagStatusAsync(
            input.SleekflowCompanyId,
            input.DocumentId,
            input.AgentId,
            input.RagStatus,
            input.ProgressPercentage);

        return new PatchAgentAssignmentRagStatusOutput();
    }
}