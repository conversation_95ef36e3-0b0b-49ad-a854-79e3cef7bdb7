using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Models.WorkflowSteps;

public class SetAgentFirstWorkflowPublishedTimestampRequest : IHasSleekflowCompanyId
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("company_agent_config_id")]
    public string CompanyAgentConfigId { get; set; }

    [JsonConstructor]
    public SetAgentFirstWorkflowPublishedTimestampRequest(string sleekflowCompanyId, string companyAgentConfigId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        CompanyAgentConfigId = companyAgentConfigId;
    }
}