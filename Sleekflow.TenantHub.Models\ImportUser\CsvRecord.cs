using CsvHelper.Configuration.Attributes;

namespace Sleekflow.TenantHub.Models.ImportUser;

// Define CSV structure
public class CsvRecord
{
    // Required fields
    [Name("First Name")]
    public string FirstName { get; set; } = string.Empty;

    // Optional fields
    [Name("Last Name")]
    [Optional]
    public string? LastName { get; set; }

    // Optional fields
    [Optional]
    public string? Email { get; set; }

    // Required for non-enterprise user
    [Optional]
    public string? Password { get; set; }

    // Required if email is not provided
    [Optional]
    public string? Username { get; set; }

    // Required: Admin, Team Admin, Staff
    public string Role { get; set; } = string.Empty;

    // Optional, team names, separated by ";"
    [Optional]
    public string? Team { get; set; }

    // Optional fields
    [Optional]
    public string? Position { get; set; }
}