using Newtonsoft.Json;
using Sleekflow.UserEventHub.Models.Constants;

namespace Sleekflow.UserEventHub.Models.Notifications;

public class MobileNotificationSettings : PlatformSpecificNotificationSettings
{
    public const string PropertyNameBanner = "banner";
    public const string PropertyNameIosSound = "ios_sound";
    public const string PropertyNameAndroidSound = "android_sound";
    public const string PropertyNameBadge = "badge";
    public const string PropertyNameRegistrationId = "registration_id";
    public const string PropertyNameHandle = "handle";
    public const string PropertyNamePlatform = "platform";

    public const string PropertyNameHubName = "hub_name";

    [JsonProperty(PropertyNameRegistrationId)]
    public string? RegistrationId { get; set; }

    [JsonProperty(PropertyNameHandle)]
    public string? Handle { get; set; }

    [JsonProperty(PropertyNamePlatform)]
    public string? Platform { get; set; }

    [JsonProperty(PropertyNameBanner)]
    public bool Banner { get; set; }

    [JsonProperty(PropertyNameIosSound)]
    public string IosSound { get; set; }

    [JsonProperty(PropertyNameAndroidSound)]
    public string AndroidSound { get; set; }

    [JsonProperty(PropertyNameBadge)]
    public bool Badge { get; set; }

    [JsonProperty(PropertyNameHubName)]
    public string? HubName { get; set; }

    [JsonConstructor]
    public MobileNotificationSettings(
        bool isNotificationEnabled,
        List<string> enabledNotificationEvents,
        bool banner,
        string iosSound,
        string androidSound,
        bool badge,
        string? platform,
        string? hubName)
        : base(isNotificationEnabled, enabledNotificationEvents)
    {
        Banner = banner;
        IosSound = iosSound;
        AndroidSound = androidSound;
        Badge = badge;
        Platform = platform;
        HubName = hubName;
    }

    public MobileNotificationSettings(
        string handle,
        string registrationId,
        bool isNotificationEnabled,
        List<string> enabledNotificationEvents,
        bool banner,
        string iosSound,
        string androidSound,
        bool badge,
        string? platform,
        string? hubName)
        : base(isNotificationEnabled, enabledNotificationEvents)
    {
        Handle = handle;
        RegistrationId = registrationId;
        Banner = banner;
        IosSound = iosSound;
        AndroidSound = androidSound;
        Badge = badge;
        Platform = platform;
        HubName = hubName;
    }

    // create a new instance of MobileNotificationSettings with default values
    public static MobileNotificationSettings CreateDefault()
    {
        return new MobileNotificationSettings(
            isNotificationEnabled: true,
            enabledNotificationEvents: NotificationEvent.All,
            banner: true,
            iosSound: "default",
            androidSound: "default",
            badge: true,
            platform: "fcm",
            hubName: "sleekflowproduction");
    }

    public static MobileNotificationSettings CreateDefaultWithRegister(string handle, string registrationId, string? platform, string? hubName)
    {
        return new MobileNotificationSettings(
            handle: handle,
            registrationId: registrationId,
            isNotificationEnabled: true,
            enabledNotificationEvents: NotificationEvent.All,
            banner: true,
            iosSound: "default",
            androidSound: "default",
            badge: true,
            platform: platform,
            hubName: hubName ?? "sleekflowproduction");
    }

    // create a new instance of MobileNotificationSettings with updated values
    public MobileNotificationSettings Update(
        bool? isNotificationEnabled = null,
        List<string>? enabledNotificationEvents = null,
        bool? banner = null,
        string? iosSound = null,
        string? androidSound = null,
        bool? badge = null,
        string? platform = null,
        string? hubName = null)
    {
        if (Handle != null && RegistrationId != null)
        {
            return new MobileNotificationSettings(
                Handle,
                RegistrationId,
                isNotificationEnabled ?? IsNotificationEnabled,
                enabledNotificationEvents ?? EnabledNotificationEvents,
                banner ?? Banner,
                iosSound ?? IosSound,
                androidSound ?? AndroidSound,
                badge ?? Badge,
                platform ?? Platform,
                hubName ?? HubName);
        }

        return new MobileNotificationSettings(
            isNotificationEnabled ?? IsNotificationEnabled,
            enabledNotificationEvents ?? EnabledNotificationEvents,
            banner ?? Banner,
            iosSound ?? IosSound,
            androidSound ?? AndroidSound,
            badge ?? Badge,
            platform ?? Platform,
            hubName ?? HubName);
    }
}