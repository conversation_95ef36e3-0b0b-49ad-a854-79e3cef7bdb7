﻿using Pulumi.AzureNative.Portal.Inputs;

namespace Sleekflow.Infras.Components.Dashboards;

public static class DevDashboard
{
    public static List<DashboardPartsArgs> GetDashboardPartsArgs()
    {
        return new List<DashboardPartsArgs>()
        {
            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 0, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" } } }, { "name", "NormalizedRUConsumption" }, { "aggregationType", 3 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Normalized RU Consumption" } } } } } }, { "title", "Max Normalized RU Consumption for sleekflow2bd1537b" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" } } }, { "name", "NormalizedRUConsumption" }, { "aggregationType", 3 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Normalized RU Consumption" } } } } } }, { "title", "Max Normalized RU Consumption for sleekflow2bd1537b by DatabaseName where CollectionName = '<empty>'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "DatabaseName" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 6, Y = 0, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Cdn/profiles/sleekflow" } } }, { "name", "OriginHealthPercentage" }, { "aggregationType", 4 }, { "namespace", "microsoft.cdn/profiles" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Origin Health Percentage" } } } } } }, { "title", "Avg Origin Health Percentage for sleekflow by Origin" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "Origin" }, { "sort", 2 }, { "top", 10 } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Cdn/profiles/sleekflow" } } }, { "name", "OriginHealthPercentage" }, { "aggregationType", 4 }, { "namespace", "microsoft.cdn/profiles" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Origin Health Percentage" } } } } } }, { "title", "Avg Origin Health Percentage for sleekflow by Origin" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "Origin" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 12, Y = 0, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Cdn/profiles/sleekflow" } } }, { "name", "OriginLatency" }, { "aggregationType", 3 }, { "namespace", "microsoft.cdn/profiles" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Origin Latency" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Cdn/profiles/sleekflow" } } }, { "name", "OriginLatency" }, { "aggregationType", 4 }, { "namespace", "microsoft.cdn/profiles" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Origin Latency" } } } } } }, { "title", "Max Origin Latency and Avg Origin Latency for sleekflow" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Cdn/profiles/sleekflow" } } }, { "name", "TotalLatency" }, { "aggregationType", 4 }, { "namespace", "microsoft.cdn/profiles" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Total Latency" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Cdn/profiles/sleekflow" } } }, { "name", "TotalLatency" }, { "aggregationType", 3 }, { "namespace", "microsoft.cdn/profiles" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Total Latency" } } } } } }, { "title", "Avg Total Latency and Max Total Latency for sleekflow" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 18, Y = 0, ColSpan = 4, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Cdn/profiles/sleekflow" } } }, { "name", "RequestCount" }, { "aggregationType", 1 }, { "namespace", "microsoft.cdn/profiles" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Request Count" } } } } } }, { "title", "Sum Request Count for sleekflow" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Cdn/profiles/sleekflow" } } }, { "name", "RequestCount" }, { "aggregationType", 1 }, { "namespace", "microsoft.cdn/profiles" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Request Count" } } } } } }, { "title", "Sum Request Count for sleekflow by Http Status" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "HttpStatus" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 22, Y = 0, ColSpan = 8, RowSpan = 8
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "resourceTypeMode" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ComponentId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Scope" }, { "value", new Dictionary<string, object>() { { "resourceIds", new [] { "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourcegroups/sleekflow-resource-group-devf9af1d41/providers/microsoft.operationalinsights/workspaces/sleekflow283a57bc" } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartId" }, { "value", "51f9c716-bfed-459e-b8f8-c952d68d6c85" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Version" }, { "value", "2.0" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "TimeRange" }, { "value", "P1D" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DashboardId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DraftRequestParameters" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Query" }, { "value", "ContainerAppConsoleLogs_CL\n| project TimeGenerated, Log_s\n| where Log_s startswith \"[GIN]\"\n| where Log_s !has \"__health\"\n| parse Log_s with \"[GIN] \" Log_s_time \" | \" Log_s_status_code \" | \" Log_s_duration \" | \" Log_s_ip_addresss \" | \" Log_s_path\n| extend Log_s_duration_time = extract(@\"([0-9]+\\.[0-9]+)\", 1, Log_s_duration, typeof(real))\n| extend Log_s_duration_unit = extract(@\"([a-z|µ]+)\", 1, Log_s_duration, typeof(string))\n| extend Log_s_duration_ms = case(\n    Log_s_duration_unit == \"ms\", Log_s_duration_time,\n    Log_s_duration_unit == \"s\", Log_s_duration_time * 1000.0,\n    Log_s_duration_unit == \"µs\", Log_s_duration_time / 1000.0,\n    -1.0)\n| summarize Count=count(), \n    Avg_Duration = round(avg(Log_s_duration_ms), 2), \n    P95_Duration = round(percentile(Log_s_duration_ms, 95), 2), \n    Max_Duration =round(max(Log_s_duration_ms), 2) \n    by bin(TimeGenerated, 1h), tostring(Log_s_path), tostring(Log_s_status_code)\n| order by TimeGenerated desc, Max_Duration desc\n\n" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ControlType" }, { "value", "FrameControlChart" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "SpecificChart" }, { "value", "StackedColumn" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartTitle" }, { "value", "Analytics" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartSubTitle" }, { "value", "sleekflow283a57bc" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Dimensions" }, { "value", new Dictionary<string, object>() { { "xAxis", new Dictionary<string, object>() { { "name", "TimeGenerated" }, { "type", "datetime" } } }, { "yAxis", new [] { new Dictionary<string, object>() { { "name", "Count" }, { "type", "long" } } } }, { "splitBy", new [] { new Dictionary<string, object>() { { "name", "Log_s_path" }, { "type", "string" } } } }, { "aggregation", "Sum" } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "LegendOptions" }, { "value", new Dictionary<string, object>() { { "isEnabled", true }, { "position", "Bottom" } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "IsQueryContainTimeRange" }, { "value", false }, { "isOptional", true } } },
                    Type = "Extension/Microsoft_OperationsManagementSuite_Workspace/PartType/LogsDashboardPart",
                    Settings =
                    {
                    new Dictionary<string, object>() {  }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 30, Y = 0, ColSpan = 8, RowSpan = 8
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "resourceTypeMode" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ComponentId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Scope" }, { "value", new Dictionary<string, object>() { { "resourceIds", new [] { "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourcegroups/sleekflow-resource-group-devf9af1d41/providers/microsoft.operationalinsights/workspaces/sleekflow283a57bc" } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartId" }, { "value", "3a28a8e3-5cb6-4673-a915-94cfd5a102bf" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Version" }, { "value", "2.0" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "TimeRange" }, { "value", "PT12H" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DashboardId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DraftRequestParameters" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Query" }, { "value", "CDBDataPlaneRequests\n | summarize throttledOperations = dcountif(ActivityId, StatusCode == 429), totalOperations = dcount(ActivityId), totalConsumedRUPer15Minutes = sum(RequestCharge) by DatabaseName, CollectionName, OperationName, RequestResourceType, bin(TimeGenerated, 15min)\n | extend averageRUPerOperation = 1.0 * totalConsumedRUPer15Minutes / totalOperations\n | extend fractionOf429s = 1.0 * throttledOperations / totalOperations\n | order by TimeGenerated, totalConsumedRUPer15Minutes desc\n\n" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ControlType" }, { "value", "FrameControlChart" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "SpecificChart" }, { "value", "StackedColumn" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartTitle" }, { "value", "Analytics" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartSubTitle" }, { "value", "sleekflow283a57bc" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Dimensions" }, { "value", new Dictionary<string, object>() { { "xAxis", new Dictionary<string, object>() { { "name", "TimeGenerated" }, { "type", "datetime" } } }, { "yAxis", new [] { new Dictionary<string, object>() { { "name", "totalConsumedRUPer15Minutes" }, { "type", "real" } } } }, { "splitBy", new [] { new Dictionary<string, object>() { { "name", "CollectionName" }, { "type", "string" } } } }, { "aggregation", "Sum" } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "LegendOptions" }, { "value", new Dictionary<string, object>() { { "isEnabled", true }, { "position", "Bottom" } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "IsQueryContainTimeRange" }, { "value", false }, { "isOptional", true } } },
                    Type = "Extension/Microsoft_OperationsManagementSuite_Workspace/PartType/LogsDashboardPart",
                    Settings =
                    {
                    new Dictionary<string, object>() {  }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 38, Y = 0, ColSpan = 16, RowSpan = 8
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "resourceTypeMode" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ComponentId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Scope" }, { "value", new Dictionary<string, object>() { { "resourceIds", new [] { "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourcegroups/sleekflow-resource-group-devf9af1d41/providers/microsoft.operationalinsights/workspaces/sleekflow283a57bc" } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartId" }, { "value", "56491918-4183-486a-9b14-45dddc913899" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Version" }, { "value", "2.0" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "TimeRange" }, { "value", "P1D" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DashboardId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DraftRequestParameters" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Query" }, { "value", "ContainerAppConsoleLogs_CL \n" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ControlType" }, { "value", "AnalyticsGrid" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "SpecificChart" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartTitle" }, { "value", "Analytics" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartSubTitle" }, { "value", "sleekflow283a57bc" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Dimensions" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "LegendOptions" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "IsQueryContainTimeRange" }, { "value", false }, { "isOptional", true } } },
                    Type = "Extension/Microsoft_OperationsManagementSuite_Workspace/PartType/LogsDashboardPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "GridColumnsWidth", new Dictionary<string, object>() { { "Log_s", "1035px" }, { "Log_s_path", "484px" } } }, { "Query", "ContainerAppConsoleLogs_CL\n| project TimeGenerated, Log_s\n| where Log_s startswith \"[GIN]\"\n| where Log_s !has \"__health\"\n| parse Log_s with \"[GIN] \" Log_s_time \" | \" Log_s_status_code \" | \" Log_s_duration \" | \" Log_s_ip_addresss \" | \" Log_s_path\n| extend Log_s_duration_time = extract(@\"([0-9]+\\.[0-9]+)\", 1, Log_s_duration, typeof(real))\n| extend Log_s_duration_unit = extract(@\"([a-z|µ]+)\", 1, Log_s_duration, typeof(string))\n| extend Log_s_duration_ms = case(\n    Log_s_duration_unit == \"ms\", Log_s_duration_time,\n    Log_s_duration_unit == \"s\", Log_s_duration_time * 1000.0,\n    Log_s_duration_unit == \"µs\", Log_s_duration_time / 1000.0,\n    -1.0)\n| summarize Count=count(), \n            Avg_Duration = round(avg(Log_s_duration_ms), 2), \n            P95_Duration = round(percentile(Log_s_duration_ms, 95), 2), \n            Max_Duration =round(max(Log_s_duration_ms), 2) \n            by bin(TimeGenerated, 1h), tostring(Log_s_path), tostring(Log_s_status_code)\n| order by TimeGenerated desc, Max_Duration desc\n\n" } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 4, ColSpan = 4, RowSpan = 2
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Cache/Redis/sleekflow-redis739dbd6c" } } }, { "name", "serverLoad" }, { "aggregationType", 3 }, { "namespace", "microsoft.cache/redis" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Server Load" }, { "resourceDisplayName", "sleekflow-redis739dbd6c" } } } } } }, { "title", "Redis Server Load" }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", true }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Cache/Redis/sleekflow-redis739dbd6c" } } }, { "name", "serverLoad" }, { "aggregationType", 3 }, { "namespace", "microsoft.cache/redis" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Server Load" }, { "resourceDisplayName", "sleekflow-redis739dbd6c" } } } } } }, { "title", "Redis Server Load" }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 4, Y = 4, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" } } }, { "name", "ProvisionedThroughput" }, { "aggregationType", 3 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Provisioned Throughput" } } } } } }, { "title", "Max Provisioned Throughput for sleekflow2bd1537b by DatabaseName" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "DatabaseName" }, { "sort", 2 }, { "top", 10 } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" } } }, { "name", "ProvisionedThroughput" }, { "aggregationType", 3 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Provisioned Throughput" } } } } } }, { "title", "Max Provisioned Throughput for sleekflow2bd1537b by DatabaseName where CollectionName = '__Empty'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "DatabaseName" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 10, Y = 4, ColSpan = 8, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" } } }, { "name", "ProvisionedThroughput" }, { "aggregationType", 3 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Provisioned Throughput" } } } } } }, { "title", "Max Provisioned Throughput for sleekflow2bd1537b by DatabaseName" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "DatabaseName" }, { "sort", 2 }, { "top", 10 } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" } } }, { "name", "ProvisionedThroughput" }, { "aggregationType", 3 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Provisioned Throughput" } } } } } }, { "title", "Max Provisioned Throughput for sleekflow2bd1537b by CollectionName where CollectionName ≠ '__Empty'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "CollectionName" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 18, Y = 4, ColSpan = 4, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "filterCollection", new Dictionary<string, object>() { { "filters", new [] { new Dictionary<string, object>() { { "key", "StatusCode" }, { "operator", 0 }, { "values", new [] { "429" } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "CollectionName" }, { "sort", 2 }, { "top", 10 } } }, { "metrics", new [] { new Dictionary<string, object>() { { "aggregationType", 7 }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Throttled Requests (429s)" } } }, { "name", "TotalRequests" }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" } } } } } }, { "timespan", new Dictionary<string, object>() { { "grain", 1 }, { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false } } }, { "title", "Throttled Requests (429s) {DatabaseText} {ContainerText}" }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "axisType", 2 }, { "isVisible", true } } }, { "y", new Dictionary<string, object>() { { "axisType", 1 }, { "isVisible", true } } } } }, { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "hideSubtitle", false }, { "isVisible", true }, { "position", 2 } } } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" } } }, { "name", "TotalRequests" }, { "aggregationType", 7 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Throttled Requests (429s)" } } } } } }, { "title", "Throttled Requests (429s) sleekflow2bd1537b" }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "hideHoverCard", false }, { "hideLabelNames", true }, { "hideSubtitle", false }, { "isVisible", true }, { "position", 2 } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "axisType", 2 }, { "isVisible", true } } }, { "y", new Dictionary<string, object>() { { "axisType", 1 }, { "isVisible", true } } } } }, { "disablePinning", true } } }, { "filterCollection", new Dictionary<string, object>() { { "filters", new [] { new Dictionary<string, object>() { { "key", "StatusCode" }, { "operator", 0 }, { "values", new [] { "429" } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "CollectionName" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 6, ColSpan = 4, RowSpan = 2
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Cache/Redis/sleekflow-redis739dbd6c" } } }, { "name", "usedmemory" }, { "aggregationType", 3 }, { "namespace", "microsoft.cache/redis" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Used Memory" }, { "resourceDisplayName", "sleekflow-redis739dbd6c" } } } } } }, { "title", "Memory Usage" }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", true }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Cache/Redis/sleekflow-redis739dbd6c" } } }, { "name", "usedmemory" }, { "aggregationType", 3 }, { "namespace", "microsoft.cache/redis" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Used Memory" }, { "resourceDisplayName", "sleekflow-redis739dbd6c" } } } } } }, { "title", "Redis Memory Usage" }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 8, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.ServiceBus/namespaces/sleekflow05d81b8f" } } }, { "name", "Messages" }, { "aggregationType", 2 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Count of messages in a Queue/Topic." } } } } } }, { "title", "Min Count of messages in a Queue/Topic. for sleekflow05d81b8f by EntityName where EntityName ≠ 'sleekflow.auditlogs/onauditlogrequestedevent', 'sleekflow.crmhub.models.events/onobjectoperationevent', 'sleekflow.crmhub.models.events/onobjectpersistedevent', 'sleekflow.crmhub.models.events/onproviderinitializedevent', 'sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent', 'sleekflow.webhooks.events/onwebhookrequestedevent'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "filterCollection", new Dictionary<string, object>() { { "filters", new [] { new Dictionary<string, object>() { { "key", "EntityName" }, { "operator", 1 }, { "values", new [] { "sleekflow.auditlogs/onauditlogrequestedevent", "sleekflow.crmhub.models.events/onobjectoperationevent", "sleekflow.crmhub.models.events/onobjectpersistedevent", "sleekflow.crmhub.models.events/onproviderinitializedevent", "sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent", "sleekflow.webhooks.events/onwebhookrequestedevent" } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "EntityName" }, { "sort", 2 }, { "top", 10 } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.ServiceBus/namespaces/sleekflow-service-bus70e09db5" } } }, { "name", "CompleteMessage" }, { "aggregationType", 1 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Completed Messages" }, { "resourceDisplayName", "sleekflow-service-bus70e09db5" } } } } } }, { "title", "Sum Completed Messages for sleekflow-service-bus70e09db5 by EntityName where EntityName ≠ 'sleekflow.auditlogs/onauditlogrequestedevent', 'sleekflow.crmhub.models.events/onobjectoperationevent', 'sleekflow.crmhub.models.events/onobjectpersistedevent', 'sleekflow.crmhub.models.events/onproviderinitializedevent', 'sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent', 'sleekflow.webhooks.events/onwebhookrequestedevent'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "EntityName" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 6, Y = 8, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.ServiceBus/namespaces/sleekflow05d81b8f" } } }, { "name", "Messages" }, { "aggregationType", 2 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Count of messages in a Queue/Topic." } } } } } }, { "title", "Min Count of messages in a Queue/Topic. for sleekflow05d81b8f by EntityName where EntityName ≠ 'sleekflow.auditlogs/onauditlogrequestedevent', 'sleekflow.crmhub.models.events/onobjectoperationevent', 'sleekflow.crmhub.models.events/onobjectpersistedevent', 'sleekflow.crmhub.models.events/onproviderinitializedevent', 'sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent', 'sleekflow.webhooks.events/onwebhookrequestedevent'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "filterCollection", new Dictionary<string, object>() { { "filters", new [] { new Dictionary<string, object>() { { "key", "EntityName" }, { "operator", 1 }, { "values", new [] { "sleekflow.auditlogs/onauditlogrequestedevent", "sleekflow.crmhub.models.events/onobjectoperationevent", "sleekflow.crmhub.models.events/onobjectpersistedevent", "sleekflow.crmhub.models.events/onproviderinitializedevent", "sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent", "sleekflow.webhooks.events/onwebhookrequestedevent" } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "EntityName" }, { "sort", 2 }, { "top", 10 } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.ServiceBus/namespaces/sleekflow-service-bus70e09db5" } } }, { "name", "ActiveMessages" }, { "aggregationType", 3 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Count of active messages in a Queue/Topic." }, { "resourceDisplayName", "sleekflow-service-bus70e09db5" } } } } } }, { "title", "Max Count of active messages in a Queue/Topic. for sleekflow-service-bus70e09db5 by EntityName where EntityName ≠ 'sleekflow.auditlogs/onauditlogrequestedevent', 'sleekflow.crmhub.models.events/onobjectoperationevent', 'sleekflow.crmhub.models.events/onobjectpersistedevent', 'sleekflow.crmhub.models.events/onproviderinitializedevent', 'sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent', 'sleekflow.webhooks.events/onwebhookrequestedevent'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "EntityName" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 12, Y = 8, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.ServiceBus/namespaces/sleekflow05d81b8f" } } }, { "name", "ThrottledRequests" }, { "aggregationType", 1 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Throttled Requests." } } } } } }, { "title", "Sum Throttled Requests. for sleekflow05d81b8f" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.ServiceBus/namespaces/sleekflow-service-bus70e09db5" } } }, { "name", "ThrottledRequests" }, { "aggregationType", 1 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Throttled Requests." }, { "resourceDisplayName", "sleekflow-service-bus70e09db5" } } } } } }, { "title", "Sum Throttled Requests. for sleekflow-service-bus70e09db5 by EntityName" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "EntityName" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 18, Y = 8, ColSpan = 4, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.ServiceBus/namespaces/sleekflow-service-bus70e09db5" } } }, { "name", "NamespaceCpuUsage" }, { "aggregationType", 3 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.ServiceBus/namespaces/sleekflow-service-bus70e09db5" } } }, { "name", "NamespaceMemoryUsage" }, { "aggregationType", 3 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Usage" } } } } } }, { "title", "Max CPU and Max Memory Usage for sleekflow-service-bus70e09db5" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.ServiceBus/namespaces/sleekflow-service-bus70e09db5" } } }, { "name", "NamespaceCpuUsage" }, { "aggregationType", 3 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.ServiceBus/namespaces/sleekflow-service-bus70e09db5" } } }, { "name", "NamespaceMemoryUsage" }, { "aggregationType", 3 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Usage" } } } } } }, { "title", "Max CPU and Max Memory Usage for sleekflow-service-bus70e09db5" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 22, Y = 8, ColSpan = 18, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "resourceTypeMode" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ComponentId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Scope" }, { "value", new Dictionary<string, object>() { { "resourceIds", new [] { "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourcegroups/sleekflow-resource-group-devf9af1d41/providers/microsoft.operationalinsights/workspaces/sleekflow283a57bc" } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartId" }, { "value", "215b9ded-8af2-49f0-8124-8c957f12c6c9" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Version" }, { "value", "2.0" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "TimeRange" }, { "value", "PT1H" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DashboardId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DraftRequestParameters" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Query" }, { "value", "CDBDataPlaneRequests\n | summarize throttledOperations = dcountif(ActivityId, StatusCode == 429), totalOperations = dcount(ActivityId), totalConsumedRUPerMinute = sum(RequestCharge) by DatabaseName, CollectionName, OperationName, RequestResourceType, bin(TimeGenerated, 15min)\n | extend averageRUPerOperation = 1.0 * totalConsumedRUPerMinute / totalOperations\n | extend fractionOf429s = 1.0 * throttledOperations / totalOperations\n | order by TimeGenerated, totalConsumedRUPerMinute desc\n\n" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ControlType" }, { "value", "AnalyticsGrid" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "SpecificChart" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartTitle" }, { "value", "Analytics" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartSubTitle" }, { "value", "sleekflow283a57bc" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Dimensions" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "LegendOptions" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "IsQueryContainTimeRange" }, { "value", false }, { "isOptional", true } } },
                    Type = "Extension/Microsoft_OperationsManagementSuite_Workspace/PartType/LogsDashboardPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "Query", "CDBDataPlaneRequests\n | summarize throttledOperations = dcountif(ActivityId, StatusCode == 429), totalOperations = dcount(ActivityId), totalConsumedRUPer15Minutes = sum(RequestCharge) by DatabaseName, CollectionName, OperationName, RequestResourceType, bin(TimeGenerated, 15min)\n | extend averageRUPerOperation = 1.0 * totalConsumedRUPer15Minutes / totalOperations\n | extend fractionOf429s = 1.0 * throttledOperations / totalOperations\n | order by TimeGenerated, totalConsumedRUPer15Minutes desc\n\n" } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 40, Y = 8, ColSpan = 4, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "ComponentId" }, { "value", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "TimeContext" }, { "value", null }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ResourceIds" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ConfigurationId" }, { "value", "Community-Workbooks/CosmosDb/Resource Insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Type" }, { "value", "cosmosdb-insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "GalleryResourceType" }, { "value", "Microsoft.DocumentDb/databaseAccounts" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PinName" }, { "value", "Azure Cosmos DB Resource Insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "StepSettings" }, { "value", "{\"chartId\":\"workbookc851223d-75ec-41fe-8ea4-bc138f3c0125\",\"version\":\"MetricsItem/2.0\",\"size\":0,\"chartType\":2,\"resourceType\":\"microsoft.documentdb/databaseaccounts\",\"metricScope\":0,\"resourceParameter\":\"Resources\",\"resourceIds\":[\"{Resources}\"],\"timeContextFromParameter\":\"TimeRange\",\"timeContext\":{\"durationMs\":********},\"metrics\":[{\"namespace\":\"microsoft.documentdb/databaseaccounts\",\"metric\":\"microsoft.documentdb/databaseaccounts-Requests-ServerSideLatencyDirect\",\"aggregation\":4,\"splitBy\":\"Region\",\"columnName\":\"Direct Mode - Server Side Latency\"}],\"title\":\"Direct Connection  Mode: Server Side Latency (Avg) By Region {DatabaseText} {ContainerText} \",\"showOpenInMe\":true,\"filters\":[{\"id\":\"1\",\"key\":\"DatabaseName\",\"operator\":0,\"valueParam\":\"Database\"},{\"id\":\"2\",\"key\":\"CollectionName\",\"operator\":0,\"valueParam\":\"Container\"}],\"timeBrushParameterName\":\"TimeRange\",\"timeBrushExportOnlyWhenBrushed\":true,\"gridSettings\":{\"rowLimit\":10000}}" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ParameterValues" }, { "value", new Dictionary<string, object>() { { "Resources", new Dictionary<string, object>() { { "type", 5 }, { "value", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "Any one" }, { "displayName", "Azure Cosmos DB" }, { "specialValue", "value::1" }, { "formattedValue", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" } } }, { "EnabledApiTypes", new Dictionary<string, object>() { { "type", 1 }, { "value", "Sql" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "Sql" }, { "displayName", "EnabledApiTypes" }, { "formattedValue", "Sql" } } }, { "ApiDatabases", new Dictionary<string, object>() { { "type", 1 }, { "value", "sqlDatabases" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "sqlDatabases" }, { "displayName", "ApiDatabases" }, { "formattedValue", "sqlDatabases" } } }, { "TimeRange", new Dictionary<string, object>() { { "type", 4 }, { "value", new Dictionary<string, object>() { { "durationMs", ******** } } }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", true }, { "paramUxId", "ext-focus-5d3e01fb-f438-49cf-b4c3-50ed26b957f6" }, { "labelValue", "Last 4 hours" }, { "displayName", "Time Range" }, { "formattedValue", "Last 4 hours" } } }, { "Database", new Dictionary<string, object>() { { "type", 2 }, { "value", null }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<unset>" }, { "displayName", "Database" }, { "formattedValue", "" } } }, { "APIContainerType", new Dictionary<string, object>() { { "type", 1 }, { "value", "containers" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "containers" }, { "displayName", "APIContainerType" }, { "formattedValue", "containers" } } }, { "Container", new Dictionary<string, object>() { { "type", 2 }, { "value", null }, { "isPending", false }, { "isWaiting", false }, { "isFailed", true }, { "isGlobal", false }, { "labelValue", "<query failed>" }, { "displayName", "Container" }, { "formattedValue", "" } } }, { "selectedTab", new Dictionary<string, object>() { { "type", 1 }, { "value", "Latency" }, { "formattedValue", "Latency" } } }, { "DatabaseText", new Dictionary<string, object>() { { "type", 1 }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<unset>" }, { "displayName", "Database" }, { "formattedValue", "" } } }, { "ContainerText", new Dictionary<string, object>() { { "type", 1 }, { "isPending", false }, { "isWaiting", true }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<query pending>" }, { "displayName", "ContainerText" }, { "formattedValue", "" } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Location" }, { "isOptional", true } } },
                    Type = "Extension/AppInsightsExtension/PartType/PinnedNotebookMetricsPart"
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 44, Y = 8, ColSpan = 4, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "ComponentId" }, { "value", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "TimeContext" }, { "value", null }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ResourceIds" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ConfigurationId" }, { "value", "Community-Workbooks/CosmosDb/Resource Insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Type" }, { "value", "cosmosdb-insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "GalleryResourceType" }, { "value", "Microsoft.DocumentDb/databaseAccounts" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PinName" }, { "value", "Azure Cosmos DB Resource Insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "StepSettings" }, { "value", "{\"chartId\":\"workbookc851223d-75ec-41fe-8ea4-bc138f3c0125\",\"version\":\"MetricsItem/2.0\",\"size\":0,\"chartType\":2,\"resourceType\":\"microsoft.documentdb/databaseaccounts\",\"metricScope\":0,\"resourceParameter\":\"Resources\",\"resourceIds\":[\"{Resources}\"],\"timeContextFromParameter\":\"TimeRange\",\"timeContext\":{\"durationMs\":********},\"metrics\":[{\"namespace\":\"microsoft.documentdb/databaseaccounts\",\"metric\":\"microsoft.documentdb/databaseaccounts-Requests-ServerSideLatencyGateway\",\"aggregation\":4,\"splitBy\":\"Region\"}],\"title\":\"Gateway Connection Mode: Server Side Latency (Avg) By Region {DatabaseText} {ContainerText} \",\"showOpenInMe\":true,\"filters\":[{\"id\":\"1\",\"key\":\"DatabaseName\",\"operator\":0,\"valueParam\":\"Database\"},{\"id\":\"2\",\"key\":\"CollectionName\",\"operator\":0,\"valueParam\":\"Container\"}],\"timeBrushParameterName\":\"TimeRange\",\"timeBrushExportOnlyWhenBrushed\":true,\"gridSettings\":{\"rowLimit\":10000}}" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ParameterValues" }, { "value", new Dictionary<string, object>() { { "Resources", new Dictionary<string, object>() { { "type", 5 }, { "value", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "Any one" }, { "displayName", "Azure Cosmos DB" }, { "specialValue", "value::1" }, { "formattedValue", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" } } }, { "EnabledApiTypes", new Dictionary<string, object>() { { "type", 1 }, { "value", "Sql" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "Sql" }, { "displayName", "EnabledApiTypes" }, { "formattedValue", "Sql" } } }, { "ApiDatabases", new Dictionary<string, object>() { { "type", 1 }, { "value", "sqlDatabases" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "sqlDatabases" }, { "displayName", "ApiDatabases" }, { "formattedValue", "sqlDatabases" } } }, { "TimeRange", new Dictionary<string, object>() { { "type", 4 }, { "value", new Dictionary<string, object>() { { "durationMs", ******** } } }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", true }, { "paramUxId", "ext-focus-5d3e01fb-f438-49cf-b4c3-50ed26b957f6" }, { "labelValue", "Last 4 hours" }, { "displayName", "Time Range" }, { "formattedValue", "Last 4 hours" } } }, { "Database", new Dictionary<string, object>() { { "type", 2 }, { "value", null }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<unset>" }, { "displayName", "Database" }, { "formattedValue", "" } } }, { "APIContainerType", new Dictionary<string, object>() { { "type", 1 }, { "value", "containers" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "containers" }, { "displayName", "APIContainerType" }, { "formattedValue", "containers" } } }, { "Container", new Dictionary<string, object>() { { "type", 2 }, { "value", null }, { "isPending", false }, { "isWaiting", false }, { "isFailed", true }, { "isGlobal", false }, { "labelValue", "<query failed>" }, { "displayName", "Container" }, { "formattedValue", "" } } }, { "selectedTab", new Dictionary<string, object>() { { "type", 1 }, { "value", "Latency" }, { "formattedValue", "Latency" } } }, { "DatabaseText", new Dictionary<string, object>() { { "type", 1 }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<unset>" }, { "displayName", "Database" }, { "formattedValue", "" } } }, { "ContainerText", new Dictionary<string, object>() { { "type", 1 }, { "isPending", false }, { "isWaiting", true }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<query pending>" }, { "displayName", "ContainerText" }, { "formattedValue", "" } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Location" }, { "isOptional", true } } },
                    Type = "Extension/AppInsightsExtension/PartType/PinnedNotebookMetricsPart"
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 12, ColSpan = 16, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "ComponentId" }, { "value", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "TimeContext" }, { "value", null }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ResourceIds" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ConfigurationId" }, { "value", "Community-Workbooks/CosmosDb/Resource Insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Type" }, { "value", "cosmosdb-insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "GalleryResourceType" }, { "value", "Microsoft.DocumentDb/databaseAccounts" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PinName" }, { "value", "Azure Cosmos DB Resource Insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "StepSettings" }, { "value", "{\"chartId\":\"workbook9ee57fd6-b139-4730-ac8b-ae263ee8cb58\",\"version\":\"MetricsItem/2.0\",\"size\":1,\"chartType\":0,\"resourceType\":\"microsoft.documentdb/databaseaccounts\",\"metricScope\":0,\"resourceParameter\":\"Resources\",\"resourceIds\":[\"{Resources}\"],\"timeContextFromParameter\":\"TimeRange\",\"timeContext\":{\"durationMs\":********},\"metrics\":[{\"namespace\":\"microsoft.documentdb/databaseaccounts\",\"metric\":\"microsoft.documentdb/databaseaccounts-Requests-TotalRequests\",\"aggregation\":7,\"splitBy\":\"CollectionName\",\"splitBySortOrder\":-1,\"splitByLimit\":5},{\"namespace\":\"microsoft.documentdb/databaseaccounts\",\"metric\":\"microsoft.documentdb/databaseaccounts-Requests-DocumentCount\",\"aggregation\":4,\"splitBy\":\"CollectionName\",\"splitBySortOrder\":-1,\"splitByLimit\":5},{\"namespace\":\"microsoft.documentdb/databaseaccounts\",\"metric\":\"microsoft.documentdb/databaseaccounts-Requests-DataUsage\",\"aggregation\":4,\"splitBy\":\"CollectionName\",\"splitBySortOrder\":-1,\"splitByLimit\":5},{\"namespace\":\"microsoft.documentdb/databaseaccounts\",\"metric\":\"microsoft.documentdb/databaseaccounts-Requests-IndexUsage\",\"aggregation\":4,\"splitBy\":\"CollectionName\",\"splitBySortOrder\":-1,\"splitByLimit\":5},{\"namespace\":\"microsoft.documentdb/databaseaccounts\",\"metric\":\"microsoft.documentdb/databaseaccounts-Requests-ProvisionedThroughput\",\"aggregation\":3,\"splitBy\":\"CollectionName\",\"splitBySortOrder\":-1,\"splitByLimit\":5},{\"namespace\":\"microsoft.documentdb/databaseaccounts\",\"metric\":\"microsoft.documentdb/databaseaccounts-Requests-NormalizedRUConsumption\",\"aggregation\":3,\"splitBy\":\"CollectionName\",\"splitBySortOrder\":-1,\"splitByLimit\":5}],\"title\":\"Cosmos DB Account Metrics By Collection\",\"gridFormatType\":2,\"filters\":[{\"id\":\"1\",\"key\":\"CollectionName\",\"operator\":1,\"values\":[\"<empty>\"]},{\"id\":\"2\",\"key\":\"DatabaseName\",\"operator\":0,\"valueParam\":\"Database\"}],\"showExpandCollapseGrid\":true,\"gridSettings\":{\"formatters\":[{\"columnMatch\":\"$gen_group\",\"formatter\":13,\"formatOptions\":{\"linkTarget\":\"Resource\",\"showIcon\":true}},{\"columnMatch\":\"Subscription\",\"formatter\":5},{\"columnMatch\":\"Name\",\"formatter\":5},{\"columnMatch\":\"Segment\",\"formatter\":5},{\"columnMatch\":\"microsoft.documentdb/databaseaccounts-Requests-TotalRequests\",\"formatter\":8,\"formatOptions\":{\"min\":0,\"palette\":\"blue\",\"aggregation\":\"Sum\"},\"numberFormat\":{\"unit\":17,\"options\":{\"style\":\"decimal\",\"useGrouping\":false,\"maximumFractionDigits\":1}}},{\"columnMatch\":\"microsoft.documentdb/databaseaccounts-Requests-TotalRequests Timeline\",\"formatter\":9,\"formatOptions\":{\"min\":0,\"palette\":\"blue\",\"aggregation\":\"Sum\"}},{\"columnMatch\":\"microsoft.documentdb/databaseaccounts-Requests-DocumentCount\",\"formatter\":8,\"formatOptions\":{\"min\":0,\"palette\":\"yellow\",\"aggregation\":\"Sum\"},\"numberFormat\":{\"unit\":17,\"options\":{\"style\":\"decimal\",\"maximumFractionDigits\":1}}},{\"columnMatch\":\"microsoft.documentdb/databaseaccounts-Requests-DocumentCount Timeline\",\"formatter\":5},{\"columnMatch\":\"microsoft.documentdb/databaseaccounts-Requests-DataUsage\",\"formatter\":8,\"formatOptions\":{\"min\":0,\"palette\":\"green\",\"aggregation\":\"Sum\"},\"numberFormat\":{\"unit\":2,\"options\":{\"style\":\"decimal\",\"maximumFractionDigits\":1}}},{\"columnMatch\":\"microsoft.documentdb/databaseaccounts-Requests-DataUsage Timeline\",\"formatter\":5},{\"columnMatch\":\"microsoft.documentdb/databaseaccounts-Requests-IndexUsage\",\"formatter\":8,\"formatOptions\":{\"min\":0,\"palette\":\"green\",\"aggregation\":\"Sum\"},\"numberFormat\":{\"unit\":2,\"options\":{\"style\":\"decimal\",\"maximumFractionDigits\":1}}},{\"columnMatch\":\"microsoft.documentdb/databaseaccounts-Requests-IndexUsage Timeline\",\"formatter\":5},{\"columnMatch\":\"microsoft.documentdb/databaseaccounts-Requests-ProvisionedThroughput\",\"formatter\":8,\"formatOptions\":{\"min\":0,\"palette\":\"purple\",\"aggregation\":\"Max\"},\"numberFormat\":{\"unit\":17,\"options\":{\"style\":\"decimal\",\"useGrouping\":false,\"maximumFractionDigits\":1}}},{\"columnMatch\":\"microsoft.documentdb/databaseaccounts-Requests-ProvisionedThroughput Timeline\",\"formatter\":5},{\"columnMatch\":\"microsoft.documentdb/databaseaccounts-Requests-NormalizedRUConsumption\",\"formatter\":8,\"formatOptions\":{\"min\":0,\"palette\":\"blue\",\"aggregation\":\"Max\"},\"numberFormat\":{\"unit\":1,\"options\":{\"style\":\"decimal\",\"maximumFractionDigits\":1}}},{\"columnMatch\":\"microsoft.documentdb/databaseaccounts-Requests-NormalizedRUConsumption Timeline\",\"formatter\":5}],\"rowLimit\":10000,\"filter\":true,\"hierarchySettings\":{\"treeType\":1,\"groupBy\":[\"Name\"],\"expandTopLevel\":true,\"finalBy\":\"Segment\"},\"sortBy\":[{\"itemKey\":\"$gen_heatmap_microsoft.documentdb/databaseaccounts-Requests-DocumentCount_6\",\"sortOrder\":1}],\"labelSettings\":[{\"columnId\":\"Subscription\",\"label\":\"Subscription/Database/Collection\"},{\"columnId\":\"Name\",\"label\":\"Collections\"},{\"columnId\":\"Segment\",\"label\":\"Collection\"},{\"columnId\":\"microsoft.documentdb/databaseaccounts-Requests-TotalRequests\",\"label\":\"Total Requests (Count)\"},{\"columnId\":\"microsoft.documentdb/databaseaccounts-Requests-TotalRequests Timeline\",\"label\":\"Total Requests (Count) Timeline\"},{\"columnId\":\"microsoft.documentdb/databaseaccounts-Requests-DocumentCount\",\"label\":\"Document Count (Avg)\"},{\"columnId\":\"microsoft.documentdb/databaseaccounts-Requests-DocumentCount Timeline\",\"label\":\"Document Count (Avg) Timeline\"},{\"columnId\":\"microsoft.documentdb/databaseaccounts-Requests-DataUsage\",\"label\":\"Data Usage (Average)\"},{\"columnId\":\"microsoft.documentdb/databaseaccounts-Requests-DataUsage Timeline\",\"label\":\"Data Usage (Average) Timeline\"},{\"columnId\":\"microsoft.documentdb/databaseaccounts-Requests-IndexUsage\",\"label\":\"Index Usage (Average)\"},{\"columnId\":\"microsoft.documentdb/databaseaccounts-Requests-IndexUsage Timeline\",\"label\":\"Index Usage (Average) Timeline\"},{\"columnId\":\"microsoft.documentdb/databaseaccounts-Requests-ProvisionedThroughput\",\"label\":\"Provisioned Throughput (Max)\"},{\"columnId\":\"microsoft.documentdb/databaseaccounts-Requests-ProvisionedThroughput Timeline\",\"label\":\"Provisioned Throughput (Max) Timeline\"},{\"columnId\":\"microsoft.documentdb/databaseaccounts-Requests-NormalizedRUConsumption\",\"label\":\"Normalized RU Consumption (Max)\"},{\"columnId\":\"microsoft.documentdb/databaseaccounts-Requests-NormalizedRUConsumption Timeline\",\"label\":\"Normalized RU Consumption (Max) Timeline\"}]},\"sortBy\":[{\"itemKey\":\"$gen_heatmap_microsoft.documentdb/databaseaccounts-Requests-DocumentCount_6\",\"sortOrder\":1}],\"showExportToExcel\":true}" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ParameterValues" }, { "value", new Dictionary<string, object>() { { "Resources", new Dictionary<string, object>() { { "type", 5 }, { "value", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "Any one" }, { "displayName", "Azure Cosmos DB" }, { "specialValue", "value::1" }, { "formattedValue", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" } } }, { "EnabledApiTypes", new Dictionary<string, object>() { { "type", 1 }, { "value", "Sql" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "Sql" }, { "displayName", "EnabledApiTypes" }, { "formattedValue", "Sql" } } }, { "ApiDatabases", new Dictionary<string, object>() { { "type", 1 }, { "value", "sqlDatabases" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "sqlDatabases" }, { "displayName", "ApiDatabases" }, { "formattedValue", "sqlDatabases" } } }, { "TimeRange", new Dictionary<string, object>() { { "type", 4 }, { "value", new Dictionary<string, object>() { { "durationMs", ******** } } }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", true }, { "paramUxId", "ext-focus-5d3e01fb-f438-49cf-b4c3-50ed26b957f6" }, { "labelValue", "Last 4 hours" }, { "displayName", "Time Range" }, { "formattedValue", "Last 4 hours" } } }, { "Database", new Dictionary<string, object>() { { "type", 2 }, { "value", null }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<unset>" }, { "displayName", "Database" }, { "formattedValue", "" } } }, { "APIContainerType", new Dictionary<string, object>() { { "type", 1 }, { "value", "containers" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "containers" }, { "displayName", "APIContainerType" }, { "formattedValue", "containers" } } }, { "Container", new Dictionary<string, object>() { { "type", 2 }, { "value", null }, { "isPending", false }, { "isWaiting", false }, { "isFailed", true }, { "isGlobal", false }, { "labelValue", "<query failed>" }, { "displayName", "Container" }, { "formattedValue", "" } } }, { "selectedTab", new Dictionary<string, object>() { { "type", 1 }, { "value", "Overview" }, { "formattedValue", "Overview" } } }, { "DatabaseText", new Dictionary<string, object>() { { "type", 1 }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<unset>" }, { "displayName", "Database" }, { "formattedValue", "" } } }, { "ContainerText", new Dictionary<string, object>() { { "type", 1 }, { "isPending", false }, { "isWaiting", true }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<query pending>" }, { "displayName", "ContainerText" }, { "formattedValue", "" } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Location" }, { "isOptional", true } } },
                    Type = "Extension/AppInsightsExtension/PartType/PinnedNotebookMetricsPart"
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 16, Y = 12, ColSpan = 4, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "ComponentId" }, { "value", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "TimeContext" }, { "value", null }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ResourceIds" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ConfigurationId" }, { "value", "Community-Workbooks/CosmosDb/Resource Insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Type" }, { "value", "cosmosdb-insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "GalleryResourceType" }, { "value", "Microsoft.DocumentDb/databaseAccounts" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PinName" }, { "value", "Azure Cosmos DB Resource Insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "StepSettings" }, { "value", "{\"chartId\":\"workbooka8ccdd35-aa70-4f62-bd27-e90cb645287f\",\"version\":\"MetricsItem/2.0\",\"size\":1,\"chartType\":2,\"resourceType\":\"microsoft.documentdb/databaseaccounts\",\"metricScope\":0,\"resourceParameter\":\"Resources\",\"resourceIds\":[\"{Resources}\"],\"timeContextFromParameter\":\"TimeRange\",\"timeContext\":{\"durationMs\":********},\"metrics\":[{\"namespace\":\"microsoft.documentdb/databaseaccounts\",\"metric\":\"microsoft.documentdb/databaseaccounts-Requests-DataUsage\",\"aggregation\":4,\"splitBy\":null,\"columnName\":\"\"},{\"namespace\":\"microsoft.documentdb/databaseaccounts\",\"metric\":\"microsoft.documentdb/databaseaccounts-Requests-IndexUsage\",\"aggregation\":4}],\"title\":\"Data & Index Usage {DatabaseText} {ContainerText} \",\"showOpenInMe\":true,\"filters\":[{\"id\":\"1\",\"key\":\"DatabaseName\",\"operator\":0,\"valueParam\":\"Database\"},{\"id\":\"2\",\"key\":\"CollectionName\",\"operator\":0,\"valueParam\":\"Container\"}],\"timeBrushParameterName\":\"TimeRange\",\"timeBrushExportOnlyWhenBrushed\":true,\"gridSettings\":{\"rowLimit\":10000}}" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ParameterValues" }, { "value", new Dictionary<string, object>() { { "Resources", new Dictionary<string, object>() { { "type", 5 }, { "value", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "Any one" }, { "displayName", "Azure Cosmos DB" }, { "specialValue", "value::1" }, { "formattedValue", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" } } }, { "EnabledApiTypes", new Dictionary<string, object>() { { "type", 1 }, { "value", "Sql" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "Sql" }, { "displayName", "EnabledApiTypes" }, { "formattedValue", "Sql" } } }, { "ApiDatabases", new Dictionary<string, object>() { { "type", 1 }, { "value", "sqlDatabases" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "sqlDatabases" }, { "displayName", "ApiDatabases" }, { "formattedValue", "sqlDatabases" } } }, { "TimeRange", new Dictionary<string, object>() { { "type", 4 }, { "value", new Dictionary<string, object>() { { "durationMs", ******** } } }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", true }, { "paramUxId", "ext-focus-5d3e01fb-f438-49cf-b4c3-50ed26b957f6" }, { "labelValue", "Last 4 hours" }, { "displayName", "Time Range" }, { "formattedValue", "Last 4 hours" } } }, { "Database", new Dictionary<string, object>() { { "type", 2 }, { "value", null }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<unset>" }, { "displayName", "Database" }, { "formattedValue", "" } } }, { "APIContainerType", new Dictionary<string, object>() { { "type", 1 }, { "value", "containers" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "containers" }, { "displayName", "APIContainerType" }, { "formattedValue", "containers" } } }, { "Container", new Dictionary<string, object>() { { "type", 2 }, { "value", null }, { "isPending", false }, { "isWaiting", false }, { "isFailed", true }, { "isGlobal", false }, { "labelValue", "<query failed>" }, { "displayName", "Container" }, { "formattedValue", "" } } }, { "selectedTab", new Dictionary<string, object>() { { "type", 1 }, { "value", "Overview" }, { "formattedValue", "Overview" } } }, { "DatabaseText", new Dictionary<string, object>() { { "type", 1 }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<unset>" }, { "displayName", "Database" }, { "formattedValue", "" } } }, { "ContainerText", new Dictionary<string, object>() { { "type", 1 }, { "isPending", false }, { "isWaiting", true }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<query pending>" }, { "displayName", "ContainerText" }, { "formattedValue", "" } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Location" }, { "isOptional", true } } },
                    Type = "Extension/AppInsightsExtension/PartType/PinnedNotebookMetricsPart"
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 20, Y = 12, ColSpan = 16, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" } } }, { "name", "PhysicalPartitionSizeInfo" }, { "aggregationType", 3 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Physical Partition Size" } } } } } }, { "title", "Max Physical Partition Size for sleekflow2bd1537b by PhysicalPartitionId where CollectionName = 'entity'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "filterCollection", new Dictionary<string, object>() { { "filters", new [] { new Dictionary<string, object>() { { "key", "CollectionName" }, { "operator", 0 }, { "values", new [] { "entity" } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "PhysicalPartitionId" }, { "sort", 2 }, { "top", 10 } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" } } }, { "name", "PhysicalPartitionSizeInfo" }, { "aggregationType", 3 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Physical Partition Size" } } } } } }, { "title", "Max Physical Partition Size for sleekflow2bd1537b by PhysicalPartitionId where CollectionName = 'entity'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "PhysicalPartitionId" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 36, Y = 12, ColSpan = 8, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "ComponentId" }, { "value", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "TimeContext" }, { "value", null }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ResourceIds" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ConfigurationId" }, { "value", "Community-Workbooks/CosmosDb/Resource Insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Type" }, { "value", "cosmosdb-insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "GalleryResourceType" }, { "value", "Microsoft.DocumentDb/databaseAccounts" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PinName" }, { "value", "Azure Cosmos DB Resource Insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "StepSettings" }, { "value", "{\"chartId\":\"workbookba9902dd-aa8e-4ad3-b87c-548e83e9eb4a\",\"version\":\"MetricsItem/2.0\",\"size\":0,\"chartType\":2,\"resourceType\":\"microsoft.documentdb/databaseaccounts\",\"metricScope\":0,\"resourceParameter\":\"Resources\",\"resourceIds\":[\"{Resources}\"],\"timeContextFromParameter\":\"TimeRange\",\"timeContext\":{\"durationMs\":********},\"metrics\":[{\"namespace\":\"microsoft.documentdb/databaseaccounts\",\"metric\":\"microsoft.documentdb/databaseaccounts-Requests-ServiceAvailability\",\"aggregation\":4,\"splitBy\":null},{\"namespace\":\"microsoft.documentdb/databaseaccounts\",\"metric\":\"microsoft.documentdb/databaseaccounts-Requests-ServiceAvailability\",\"aggregation\":2},{\"namespace\":\"microsoft.documentdb/databaseaccounts\",\"metric\":\"microsoft.documentdb/databaseaccounts-Requests-ServiceAvailability\",\"aggregation\":3}],\"title\":\"Service Availability (min/max/avg in %)\",\"showOpenInMe\":true,\"timeBrushParameterName\":\"TimeRange\",\"timeBrushExportOnlyWhenBrushed\":true,\"gridSettings\":{\"formatters\":[{\"columnMatch\":\"Subscription\",\"formatter\":5},{\"columnMatch\":\"Name\",\"formatter\":13,\"formatOptions\":{\"linkTarget\":\"Resource\"}},{\"columnMatch\":\"microsoft.documentdb/databaseaccounts-Requests-ServiceAvailability Timeline\",\"formatter\":5},{\"columnMatch\":\"microsoft.documentdb/databaseaccounts-Requests-ServiceAvailability\",\"formatter\":1,\"numberFormat\":{\"unit\":1,\"options\":null}}],\"rowLimit\":10000,\"labelSettings\":[{\"columnId\":\"Subscription\",\"label\":\"Subscription/Database/Collection\"},{\"columnId\":\"microsoft.documentdb/databaseaccounts-Requests-ServiceAvailability\",\"label\":\"Service Availability (Average)\"},{\"columnId\":\"microsoft.documentdb/databaseaccounts-Requests-ServiceAvailability Timeline\",\"label\":\"Service Availability Timeline\"}]}}" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ParameterValues" }, { "value", new Dictionary<string, object>() { { "Resources", new Dictionary<string, object>() { { "type", 5 }, { "value", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "Any one" }, { "displayName", "Azure Cosmos DB" }, { "specialValue", "value::1" }, { "formattedValue", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow2bd1537b" } } }, { "EnabledApiTypes", new Dictionary<string, object>() { { "type", 1 }, { "value", "Sql" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "Sql" }, { "displayName", "EnabledApiTypes" }, { "formattedValue", "Sql" } } }, { "ApiDatabases", new Dictionary<string, object>() { { "type", 1 }, { "value", "sqlDatabases" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "sqlDatabases" }, { "displayName", "ApiDatabases" }, { "formattedValue", "sqlDatabases" } } }, { "TimeRange", new Dictionary<string, object>() { { "type", 4 }, { "value", new Dictionary<string, object>() { { "durationMs", ******** } } }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", true }, { "paramUxId", "ext-focus-5d3e01fb-f438-49cf-b4c3-50ed26b957f6" }, { "labelValue", "Last 4 hours" }, { "displayName", "Time Range" }, { "formattedValue", "Last 4 hours" } } }, { "Database", new Dictionary<string, object>() { { "type", 2 }, { "value", null }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<unset>" }, { "displayName", "Database" }, { "formattedValue", "" } } }, { "APIContainerType", new Dictionary<string, object>() { { "type", 1 }, { "value", "containers" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "containers" }, { "displayName", "APIContainerType" }, { "formattedValue", "containers" } } }, { "Container", new Dictionary<string, object>() { { "type", 2 }, { "value", null }, { "isPending", false }, { "isWaiting", false }, { "isFailed", true }, { "isGlobal", false }, { "labelValue", "<query failed>" }, { "displayName", "Container" }, { "formattedValue", "" } } }, { "selectedTab", new Dictionary<string, object>() { { "type", 1 }, { "value", "Availability" }, { "formattedValue", "Availability" } } }, { "DatabaseText", new Dictionary<string, object>() { { "type", 1 }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<unset>" }, { "displayName", "Database" }, { "formattedValue", "" } } }, { "ContainerText", new Dictionary<string, object>() { { "type", 1 }, { "isPending", false }, { "isWaiting", true }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<query pending>" }, { "displayName", "ContainerText" }, { "formattedValue", "" } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Location" }, { "isOptional", true } } },
                    Type = "Extension/AppInsightsExtension/PartType/PinnedNotebookMetricsPart"
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 16, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-crm-hub-appefe8db4d" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-crm-hub-appefe8db4d" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-hs-in-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-hs-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-sf-in-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-sf-in-app" } } } } } }, { "title", "Max Replica Count for sleekflow-crm-hub-appefe8db4d, sleekflow-hs-in-app, and sleekflow-sf-in-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-crm-hub-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-crm-hub-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-hs-in-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-hs-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-sf-in-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-sf-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-d365-in-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-d365-in-app" } } } } } }, { "title", "Max Replica Count for sleekflow-crm-hub-app, sleekflow-hs-in-app, and 2 other resources" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 6, Y = 16, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } } } }, { "title", "Max Replica Count for sleekflow-wh-app2b58762d" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-apigw-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-apigw-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-wh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-ah-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-ah-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-fh-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-fh-app" } } } } } }, { "title", "Max Replica Count for sleekflow-apigw-app, sleekflow-wh-app, and 2 other resources" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 12, Y = 16, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-mh-app2d66dcc1" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" } } } } } }, { "title", "Max Replica Count for sleekflow-mh-app2d66dcc1" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-mh-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-mh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-pagw-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-pagw-app" } } } } } }, { "title", "Max Replica Count for sleekflow-mh-app and sleekflow-pagw-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 18, Y = 16, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-mh-app2d66dcc1" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" } } } } } }, { "title", "Max Replica Count for sleekflow-mh-app2d66dcc1" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-commh-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-commh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-eh-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-eh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-th-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-th-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-ih-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-ih-app" } } } } } }, { "title", "Max Replica Count for sleekflow-commh-app, sleekflow-eh-app, and 2 other resources" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 24, Y = 16, ColSpan = 6, RowSpan = 9
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "resourceTypeMode" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ComponentId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Scope" }, { "value", new Dictionary<string, object>() { { "resourceIds", new [] { "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourcegroups/sleekflow-resource-group-devf9af1d41/providers/microsoft.operationalinsights/workspaces/sleekflow283a57bc" } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartId" }, { "value", "72b725d1-e2d0-41bf-95e1-7f8242c3f20c" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Version" }, { "value", "2.0" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "TimeRange" }, { "value", "PT12H" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DashboardId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DraftRequestParameters" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Query" }, { "value", "SleekflowAuditHub_CL\n| union SleekflowWebhookHub_CL\n| union\n    SleekflowCrmHub_CL,\n    SleekflowCrmHubWorker_CL,\n    SleekflowIntegratorSalesforce_CL,\n    SleekflowIntegratorHubspot_CL,\n    SleekflowIntegratorDynamics365_CL\n| union SleekflowCommerceHub_CL, SleekflowCommerceHubWorker_CL\n| union SleekflowMessagingHub_CL, SleekflowMessagingHubWorker_CL\n| union SleekflowFlowHub_CL, SleekflowFlowHubWorker_CL\n| union SleekflowEmailHub_CL\n| union SleekflowIntelligentHub_CL\n| union SleekflowTenantHub_CL\n| union SleekflowPublicApiGateway_CL\n| where LogLevel_s == 'Error'\n| project TimeGenerated, LogLevel_s, LogMessage_s, LogProperties_s, Type, LogException_Source_s, LogException_Message_s, LogException_ClassName_s, LogException_StackTrace_s, LogException_StackTraceString_s, LogException_SerializedContext_s, LogException_SerializedOutput_s\n| summarize count() by bin(TimeGenerated, 1h), Type, LogException_Source_s\n| order by TimeGenerated, Type, LogException_Source_s, count_\n\n" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ControlType" }, { "value", "FrameControlChart" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "SpecificChart" }, { "value", "Line" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartTitle" }, { "value", "Analytics" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartSubTitle" }, { "value", "sleekflow283a57bc" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Dimensions" }, { "value", new Dictionary<string, object>() { { "xAxis", new Dictionary<string, object>() { { "name", "TimeGenerated" }, { "type", "datetime" } } }, { "yAxis", new [] { new Dictionary<string, object>() { { "name", "count_" }, { "type", "long" } } } }, { "splitBy", new [] { new Dictionary<string, object>() { { "name", "Type" }, { "type", "string" } } } }, { "aggregation", "Sum" } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "LegendOptions" }, { "value", new Dictionary<string, object>() { { "isEnabled", true }, { "position", "Bottom" } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "IsQueryContainTimeRange" }, { "value", false }, { "isOptional", true } } },
                    Type = "Extension/Microsoft_OperationsManagementSuite_Workspace/PartType/LogsDashboardPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "Query", "SleekflowAuditHub_CL\n| union SleekflowWebhookHub_CL\n| union\n    SleekflowCrmHub_CL,\n    SleekflowCrmHubWorker_CL,\n    SleekflowIntegratorSalesforce_CL,\n    SleekflowIntegratorHubspot_CL,\n    SleekflowIntegratorDynamics365_CL\n| union SleekflowCommerceHub_CL, SleekflowCommerceHubWorker_CL\n| union SleekflowMessagingHub_CL, SleekflowMessagingHubWorker_CL\n| union SleekflowFlowHub_CL, SleekflowFlowHubWorker_CL\n| union SleekflowEmailHub_CL\n| union SleekflowIntelligentHub_CL\n| union SleekflowTenantHub_CL\n| union SleekflowPublicApiGateway_CL\n| where LogLevel_s == 'Error'\n| project TimeGenerated, LogLevel_s, LogMessage_s, LogProperties_s, Type, LogException_Source_s, LogException_Message_s, LogException_ClassName_s, LogException_StackTrace_s, LogException_StackTraceString_s, LogException_SerializedContext_s, LogException_SerializedOutput_s\n| summarize count() by bin(TimeGenerated, 1h), Type, LogException_Source_s\n| extend Type_Exception = strcat(Type, \"_\", LogException_Source_s)\n| order by TimeGenerated, Type, LogException_Source_s, count_\n\n" }, { "SpecificChart", "StackedColumn" }, { "Dimensions", new Dictionary<string, object>() { { "xAxis", new Dictionary<string, object>() { { "name", "TimeGenerated" }, { "type", "datetime" } } }, { "yAxis", new [] { new Dictionary<string, object>() { { "name", "count_" }, { "type", "long" } } } }, { "splitBy", new [] { new Dictionary<string, object>() { { "name", "Type_Exception" }, { "type", "string" } } } }, { "aggregation", "Sum" } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 30, Y = 16, ColSpan = 16, RowSpan = 9
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "resourceTypeMode" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ComponentId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Scope" }, { "value", new Dictionary<string, object>() { { "resourceIds", new [] { "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourcegroups/sleekflow-resource-group-devf9af1d41/providers/microsoft.operationalinsights/workspaces/sleekflow283a57bc" } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartId" }, { "value", "0354832f-54e0-4cb4-86a7-dc9a638e06c0" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Version" }, { "value", "2.0" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "TimeRange" }, { "value", "P1D" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DashboardId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DraftRequestParameters" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Query" }, { "value", "SleekflowCrmHub_CL \n| union SleekflowIntegratorSalesforce_CL\n| where LogLevel_s == 'Error'\n| project TimeGenerated, LogLevel_s, LogMessage_s, LogProperties_s, Type, LogException_Source_s, LogException_Message_s, LogException_ClassName_s, LogException_StackTrace_s, LogException_StackTraceString_s, LogException_SerializedContext_s, LogException_SerializedOutput_s\n" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ControlType" }, { "value", "AnalyticsGrid" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "SpecificChart" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartTitle" }, { "value", "Analytics" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartSubTitle" }, { "value", "sleekflow283a57bc" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Dimensions" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "LegendOptions" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "IsQueryContainTimeRange" }, { "value", false }, { "isOptional", true } } },
                    Type = "Extension/Microsoft_OperationsManagementSuite_Workspace/PartType/LogsDashboardPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "GridColumnsWidth", new Dictionary<string, object>() { { "LogProperties_s", "504px" }, { "LogException_StackTrace_s", "1122px" }, { "LogException_Source_s", "314px" } } }, { "Query", "SleekflowAuditHub_CL\n| union SleekflowWebhookHub_CL\n| union\n    SleekflowCrmHub_CL,\n    SleekflowCrmHubWorker_CL,\n    SleekflowIntegratorSalesforce_CL,\n    SleekflowIntegratorHubspot_CL,\n    SleekflowIntegratorDynamics365_CL\n| union SleekflowCommerceHub_CL, SleekflowCommerceHubWorker_CL\n| union SleekflowMessagingHub_CL, SleekflowMessagingHubWorker_CL\n| union SleekflowFlowHub_CL, SleekflowFlowHubWorker_CL\n| union SleekflowEmailHub_CL\n| union SleekflowIntelligentHub_CL\n| union SleekflowTenantHub_CL\n| union SleekflowPublicApiGateway_CL\n| where LogLevel_s == 'Error'\n| order by TimeGenerated desc \n| project\n    TimeGenerated,\n    LogLevel_s,\n    LogMessage_s,\n    LogProperties_s,\n    Type,\n    LogException_Source_s,\n    LogException_Message_s,\n    LogException_ClassName_s,\n    LogException_StackTrace_s,\n    LogException_StackTraceString_s,\n    LogException_SerializedContext_s,\n    LogException_SerializedOutput_s\n\n" } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 19, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-crm-hub-appefe8db4d" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-crm-hub-appefe8db4d" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-crm-hub-appefe8db4d" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-crm-hub-appefe8db4d" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-crm-hub-appefe8db4d" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-crm-hub-appefe8db4d" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-crm-hub-appefe8db4d" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-crm-hub-appefe8db4d" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-crm-hub-appefe8db4d" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-crm-hub-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-crm-hub-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-crm-hub-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-crm-hub-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-crm-hub-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-crm-hub-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-crm-hub-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-crm-hub-app" } } } } } }, { "title", "Avg CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-crm-hub-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 6, Y = 19, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-apigw-app4840259a" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-apigw-app4840259a" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-apigw-app4840259a" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-apigw-app4840259a" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-apigw-app4840259a" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-apigw-app4840259a" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-apigw-app4840259a" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-apigw-app4840259a" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-apigw-app4840259a" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-apigw-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-apigw-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-apigw-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-apigw-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-apigw-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-apigw-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-apigw-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-apigw-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-apigw-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 12, Y = 19, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-mh-app2d66dcc1" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-mh-app2d66dcc1" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-mh-app2d66dcc1" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-mh-app2d66dcc1" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-mh-app2d66dcc1" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-mh-app2d66dcc1" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-mh-app2d66dcc1" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-mh-app2d66dcc1" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-mh-app2d66dcc1" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-mh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-mh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-mh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-mh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-mh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-mh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-mh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-mh-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-mh-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 18, Y = 19, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-ch-appeb6f641c" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ch-appeb6f641c" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-ch-appeb6f641c" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ch-appeb6f641c" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-ch-appeb6f641c" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ch-appeb6f641c" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-ch-appeb6f641c" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ch-appeb6f641c" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-ch-appeb6f641c" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-commh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-commh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-commh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-commh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-commh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-commh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-commh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-commh-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-commh-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 22, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-sf-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-sf-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-sf-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-sf-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-sf-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-sf-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-sf-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-sf-in-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-sf-in-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-sf-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-sf-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-sf-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-sf-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-sf-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-sf-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-sf-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-sf-in-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-sf-in-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 6, Y = 22, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-wh-app2b58762d" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-wh-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 12, Y = 22, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Web/sites/sleekflow-mh-worker-appd6d9197c" } } }, { "name", "MemoryWorkingSet" }, { "aggregationType", 3 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory working set" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Web/sites/sleekflow-mh-worker-appd6d9197c" } } }, { "name", "FunctionExecutionUnits" }, { "aggregationType", 1 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Function Execution Units" } } } } } }, { "title", "Max Memory working set and Sum Function Execution Units for sleekflow-mh-worker-appd6d9197c" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Web/sites/sleekflow-mh-worker-appd6d9197c" } } }, { "name", "MemoryWorkingSet" }, { "aggregationType", 3 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory working set" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Web/sites/sleekflow-mh-worker-appd6d9197c" } } }, { "name", "FunctionExecutionUnits" }, { "aggregationType", 1 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Function Execution Units" } } } } } }, { "title", "Max Memory working set and Sum Function Execution Units for sleekflow-mh-worker-appd6d9197c" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 18, Y = 22, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Web/sites/sleekflow-mh-worker-appd6d9197c" } } }, { "name", "MemoryWorkingSet" }, { "aggregationType", 3 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory working set" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Web/sites/sleekflow-mh-worker-appd6d9197c" } } }, { "name", "FunctionExecutionUnits" }, { "aggregationType", 1 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Function Execution Units" } } } } } }, { "title", "Max Memory working set and Sum Function Execution Units for sleekflow-mh-worker-appd6d9197c" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Web/sites/sleekflow-commh-worker-app0afc8605" } } }, { "name", "MemoryWorkingSet" }, { "aggregationType", 3 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory working set" }, { "resourceDisplayName", "sleekflow-commh-worker-app0afc8605" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Web/sites/sleekflow-commh-worker-app0afc8605" } } }, { "name", "FunctionExecutionUnits" }, { "aggregationType", 1 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Function Execution Units" }, { "resourceDisplayName", "sleekflow-commh-worker-app0afc8605" } } } } } }, { "title", "Max Memory working set and Sum Function Execution Units for sleekflow-commh-worker-app0afc8605" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 25, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-d365-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-d365-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-d365-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-d365-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" } } } } } }, { "title", "Avg CPU Usage, Max CPU Usage, and 2 other metrics for sleekflow-d365-in-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-d365-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-d365-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-d365-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-d365-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" } } } } } }, { "title", "Avg CPU Usage, Max CPU Usage, and 2 other metrics for sleekflow-d365-in-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 6, Y = 25, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-ah-appd4f71ccd" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ah-appd4f71ccd" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-ah-appd4f71ccd" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ah-appd4f71ccd" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-ah-appd4f71ccd" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ah-appd4f71ccd" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-ah-appd4f71ccd" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ah-appd4f71ccd" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-ah-appd4f71ccd" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-ah-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ah-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-ah-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ah-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-ah-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ah-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-ah-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ah-app" } } } } } }, { "title", "Avg CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-ah-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 12, Y = 25, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-mh-app2d66dcc1" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-mh-app2d66dcc1" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-mh-app2d66dcc1" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-mh-app2d66dcc1" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-mh-app2d66dcc1" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-mh-app2d66dcc1" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-mh-app2d66dcc1" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-mh-app2d66dcc1" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-mh-app2d66dcc1" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-pagw-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-pagw-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-pagw-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-pagw-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-pagw-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-pagw-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-pagw-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-pagw-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-pagw-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 18, Y = 25, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-wh-app2b58762d" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-eh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-eh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-eh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-eh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-eh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-eh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-eh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-eh-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-eh-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 24, Y = 25, ColSpan = 22, RowSpan = 9
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "resourceTypeMode" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ComponentId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Scope" }, { "value", new Dictionary<string, object>() { { "resourceIds", new [] { "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourcegroups/sleekflow-resource-group-devf9af1d41/providers/microsoft.operationalinsights/workspaces/sleekflow283a57bc" } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartId" }, { "value", "56491918-4183-486a-9b14-45dddc913899" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Version" }, { "value", "2.0" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "TimeRange" }, { "value", "P1D" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DashboardId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DraftRequestParameters" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Query" }, { "value", "ContainerAppConsoleLogs_CL \n" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ControlType" }, { "value", "AnalyticsGrid" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "SpecificChart" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartTitle" }, { "value", "Analytics" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartSubTitle" }, { "value", "sleekflow283a57bc" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Dimensions" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "LegendOptions" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "IsQueryContainTimeRange" }, { "value", false }, { "isOptional", true } } },
                    Type = "Extension/Microsoft_OperationsManagementSuite_Workspace/PartType/LogsDashboardPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "GridColumnsWidth", new Dictionary<string, object>() { { "Log_s", "1035px" } } }, { "Query", "ContainerAppConsoleLogs_CL \n| where Log_s !has \"| 200 |\"\n| project TimeGenerated, RevisionName_s, ContainerAppName_s, ContainerName_s, Log_s, Stream_s, logtag_s, ContainerId_s, ContainerGroupName_s, ContainerGroupId_g, ContainerImage_s, EnvironmentName_s, Type\n| order by TimeGenerated desc\n" } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 28, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-hs-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-hs-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-hs-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-hs-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-hs-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-hs-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-hs-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-hs-in-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-hs-in-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-hs-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-hs-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-hs-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-hs-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-hs-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-hs-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-hs-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-hs-in-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-hs-in-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 6, Y = 28, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-wh-app2b58762d" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-fh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-fh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-fh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-fh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-fh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-fh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-fh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-fh-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-fh-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 18, Y = 28, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-wh-app2b58762d" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-th-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-th-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-th-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-th-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-th-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-th-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-th-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-th-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-th-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 31, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Web/sites/sleekflow-ch-worker-appb42948b6" } } }, { "name", "MemoryWorkingSet" }, { "aggregationType", 4 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory working set" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Web/sites/sleekflow-ch-worker-appb42948b6" } } }, { "name", "FunctionExecutionUnits" }, { "aggregationType", 1 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Function Execution Units" } } } } } }, { "title", "Avg Memory working set and Sum Function Execution Units for sleekflow-ch-worker-appb42948b6" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Web/sites/sleekflow-ch-worker-appb42948b6" } } }, { "name", "MemoryWorkingSet" }, { "aggregationType", 3 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory working set" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Web/sites/sleekflow-ch-worker-appb42948b6" } } }, { "name", "FunctionExecutionUnits" }, { "aggregationType", 1 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Function Execution Units" } } } } } }, { "title", "Max Memory working set and Sum Function Execution Units for sleekflow-ch-worker-appb42948b6" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 6, Y = 31, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Web/sites/sleekflow-mh-worker-appd6d9197c" } } }, { "name", "MemoryWorkingSet" }, { "aggregationType", 3 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory working set" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Web/sites/sleekflow-mh-worker-appd6d9197c" } } }, { "name", "FunctionExecutionUnits" }, { "aggregationType", 1 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Function Execution Units" } } } } } }, { "title", "Max Memory working set and Sum Function Execution Units for sleekflow-mh-worker-appd6d9197c" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Web/sites/sleekflow-fh-worker-appead178df" } } }, { "name", "FunctionExecutionUnits" }, { "aggregationType", 1 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Function Execution Units" }, { "resourceDisplayName", "sleekflow-fh-worker-appead178df" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.Web/sites/sleekflow-fh-worker-appead178df" } } }, { "name", "MemoryWorkingSet" }, { "aggregationType", 4 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory working set" }, { "resourceDisplayName", "sleekflow-fh-worker-appead178df" } } } } } }, { "title", "Sum Function Execution Units and Avg Memory working set for sleekflow-fh-worker-appead178df" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 18, Y = 31, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-wh-app2b58762d" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app2b58762d" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-wh-app2b58762d" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-ih-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ih-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-ih-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ih-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-ih-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ih-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-ih-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ih-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-ih-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },
            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 34, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-opa-app" } } }, { "name", "CpuPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opa-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-opal-app" } } }, { "name", "CpuPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opal-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-opa-app-seas" } } }, { "name", "CpuPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opa-app-seas" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-opal-app-seas" } } }, { "name", "CpuPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opal-app-seas" } } } } } }, { "title", "Max CPU Usage Percentage for OPA and OPAL" }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "hideHoverCard", false }, { "hideLabelNames", true }, { "isVisible", true }, { "position", 2 } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "axisType", 2 }, { "isVisible", true } } }, { "y", new Dictionary<string, object>() { { "axisType", 1 }, { "isVisible", true } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },
            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 6, Y = 34, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-opa-app" } } }, { "name", "MemoryPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opa-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-opal-app" } } }, { "name", "MemoryPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opal-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-opa-app-seas" } } }, { "name", "MemoryPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opa-app-seas" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-opal-app-seas" } } }, { "name", "MemoryPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opal-app-seas" } } } } } }, { "title", "Max Memory Percentage for OPA and OPAL" }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "hideHoverCard", false }, { "hideLabelNames", true }, { "isVisible", true }, { "position", 2 } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "axisType", 2 }, { "isVisible", true } } }, { "y", new Dictionary<string, object>() { { "axisType", 1 }, { "isVisible", true } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },
            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 12, Y = 34, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "aggregationType", 1 }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Requests" }, { "resourceDisplayName", "sleekflow-opa-app" } } }, { "name", "Requests" }, { "namespace", "microsoft.app/containerapps" }, { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-opa-app" } } } }, new Dictionary<string, object>() { { "aggregationType", 1 }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Requests" }, { "resourceDisplayName", "sleekflow-opal-app" } } }, { "name", "Requests" }, { "namespace", "microsoft.app/containerapps" }, { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-opal-app" } } } }, new Dictionary<string, object>() { { "aggregationType", 1 }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Requests" }, { "resourceDisplayName", "sleekflow-opa-app-seas" } } }, { "name", "Requests" }, { "namespace", "microsoft.app/containerapps" }, { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-opa-app-seas" } } } }, new Dictionary<string, object>() { { "aggregationType", 1 }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Requests" }, { "resourceDisplayName", "sleekflow-opal-app-seas" } } }, { "name", "Requests" }, { "namespace", "microsoft.app/containerapps" }, { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.App/containerApps/sleekflow-opal-app-seas" } } } } } }, { "title", "Request Count for OPA and OPAL" }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "axisType", 2 }, { "isVisible", true } } }, { "y", new Dictionary<string, object>() { { "axisType", 1 }, { "isVisible", true } } } } }, { "chartType", 2 }, { "disablePinning", true }, { "legendVisualization", new Dictionary<string, object>() { { "hideHoverCard", false }, { "hideLabelNames", true }, { "isVisible", true }, { "position", 2 } } } } } } } } } } } }
                    },
                }
            },
            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 20, Y = 34, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.ServiceBus/namespaces/sleekflow-high-traffic-service-bus47c666b9" } } }, { "name", "CompleteMessage" }, { "aggregationType", 1 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Completed Messages" }, { "resourceDisplayName", "sleekflow-high-traffic-service-bus47c666b9" } } } } } }, { "title", "Sum Completed Messages for sleekflow-high-traffic-service-bus47c666b9" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideHoverCard", false }, { "hideLabelNames", true } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 26, Y = 34, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-devf9af1d41/providers/Microsoft.ServiceBus/namespaces/sleekflow-high-traffic-service-bus47c666b9" } } }, { "name", "ActiveMessages" }, { "aggregationType", 3 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Count of active messages in a Queue/Topic." }, { "resourceDisplayName", "sleekflow-high-traffic-service-bus47c666b9" } } } } } }, { "title", "Max Count of active messages in a Queue/Topic. for sleekflow-high-traffic-service-bus47c666b9" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideHoverCard", false }, { "hideLabelNames", true } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            }
        };
    }
}