﻿using HashidsNet;
using IdGen;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Scripts;
using Microsoft.Extensions.Logging;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;

namespace Sleekflow.Ids;

public interface IIdService
{
    string GetId(string typeName);

    string GetId(string typeName, string parentId);

    (string TypeName, long SnowflakeId) DecodeId(string id);
}

public class IdService : ISingletonService, IIdService
{
    private readonly ILogger<IdService> _logger;
    private readonly Hashids _hashids;
    private readonly Container _container;

    private static readonly SemaphoreSlim InitSemaphore = new SemaphoreSlim(1, 1);
    private IdGenerator? _snowflakeIdGenerator;

    public IdService(
        ILogger<IdService> logger,
        IDbContainerResolver dbContainerResolver)
    {
        _logger = logger;
        _hashids = new Hashids("sleekflow");
        _container = dbContainerResolver.Resolve("db", "id_state");
    }

    private IdGenerator GetIdGenerator()
    {
        if (_snowflakeIdGenerator != null)
        {
            return _snowflakeIdGenerator!;
        }

        InitSemaphore.Wait();

        if (_snowflakeIdGenerator != null)
        {
            InitSemaphore.Release();

            return _snowflakeIdGenerator!;
        }

        try
        {
            _logger.LogInformation("Initializing the IdGenerator");

            var task = Task.Run<StoredProcedureExecuteResponse<string>>(
                async () =>
                    await _container.Scripts.ExecuteStoredProcedureAsync<string>(
                        "get-id-stored-procedure",
                        new PartitionKey("id"),
                        new dynamic[]
                        {
                            Environment.MachineName
                        }));

            var id = Convert.ToInt32(task.Result.Resource) % 1024;

            _snowflakeIdGenerator = new IdGenerator(id);

            _logger.LogInformation("Initialized the IdGenerator with id {Id}", id);

            return _snowflakeIdGenerator;
        }
        finally
        {
            InitSemaphore.Release();
        }
    }

    public string GetId(string typeName)
    {
        var typeId = GetTypeId(typeName);

        var snowflakeId = GetIdGenerator().CreateId();
        var encodeLong = _hashids.EncodeLong(typeId, snowflakeId);

        return encodeLong;
    }

    public string GetId(string typeName, string parentId)
    {
        var typeId = GetTypeId(typeName);

        var snowflakeId = GetIdGenerator().CreateId();
        var encodeLong = parentId + "-" + _hashids.EncodeLong(typeId, snowflakeId);

        return encodeLong;
    }

    private long GetTypeId(string typeName)
    {
        var typeId = typeName switch
        {
            "EntityEvent" => 1,
            "Contact" => 2,
            "Unify" => 3,
            "Webhook" => 4,
            "SyncObjectsProgressState" => 5,
            "Request" => 6,
            "WebhookEvent" => 7,
            "ProviderConfig" => 8,
            "Activity" => 9,
            "Company" => 10,
            "Lead" => 11,
            "Note" => 12,
            "Opportunity" => 13,
            "User" => 14,
            "State" => 15,
            "BlastMessagesBatch" => 16,
            "BlastMessagesProgressState" => 17,
            "AuditLog" => 18,
            "Product" => 19,
            "SalesforceSubscription" => 20,
            "HubspotSubscription" => 21,
            "SalesOrder" => 22,
            "SalesOrderItem" => 23,
            "Blob" => 24,
            "Waba" => 25,
            "Message" => 26,
            "MessagingHubAuditLog" => 27,
            "WabaPhoneNumber" => 28,
            "Store" => 29,
            "ProductVariant" => 30,
            "Category" => 31,
            "Order" => 32,
            "Payment" => 33,
            "PaymentConfig" => 34,
            "PaymentCurrencyMapping" => 35,
            "Campaign" => 36,
            "CampaignMember" => 37,
            "Language" => 38,
            "Currency" => 39,
            "Salesperson" => 40,
            "AwarenessSource" => 41,
            "BusinessBalance" => 42,
            "BusinessBalanceTransactionLog" => 43,
            "Link" => 44,
            "LinkClick" => 45,
            "Domain" => 46,
            "Cart" => 47,
            "ApiKeyConfig" => 48,
            "SleekflowCompany" => 49,
            "UserProfileAuditLog" => 50,
            "CustomCatalogConfig" => 51,
            "BusinessBalanceAutoTopUpProfile" => 52,
            "Workflow" => 53,
            "WorkflowExecution" => 54,
            "StateSubscription" => 55,
            "FlowHubEvent" => 56,
            "WorkflowVersioned" => 57,
            "StepExecution" => 58,
            "HubspotQueueItem" => 58,
            "WorkflowWebhookTrigger" => 59,
            "WabaProductCatalog" => 60,
            "FileDocument" => 61,
            "FileDocumentChunk" => 62,
            "Chunk" => 63,
            "WebPageDocument" => 64,
            "WebPageDocumentChunk" => 65,
            "KnowledgeBaseEntry" => 66,
            "CompanyEmailConfig" => 67,
            "Email" => 68,
            "DisposableLocalPart" => 69,
            "EmailHubOutlookSubscription" => 70,
            "UserEventHub.Message" => 71,
            "UserEventHub.Session" => 72,
            "Plan" => 73,
            "PlanDefinition" => 74,
            "Subscription" => 75,
            "WorkflowGroup" => 76,
            "LoopThroughObjectsProgressState" => 77,
            "SalesforceAuthentication" => 78,
            "SalesforceConnection" => 79,
            "Ticket" => 80,
            "SystemAuditLog" => 81,
            "WorkflowStepCategoryStatistics" => 82,
            "StateStepCategoryStatistics" => 83,
            "SalesforceUserMappingConfig" => 84,
            "ExperimentalFeature" => 85,
            "WabaBalance" => 87,
            "WabaBalanceAutoTopUpProfile" => 88,
            "CompanyConfig" => 89,
            "CompanyAgentConfig" => 90,
            "UserEvent" => 91,
            "UserEventType" => 92,
            "SqlJob" => 93,
            "GoogleSheetsAuthentication" => 94,
            "GoogleSheetsConnection" => 95,
            "GoogleSheetsSubscription" => 96,
            "IntegrationObject" => 97,
            "IntelligentHubUsage" => 98,
            "HubspotConnection" => 99,
            "HubspotAuthentication" => 100,
            "HubspotUserMappingConfig" => 101,
            "ZohoAuthentication" => 102,
            "ZohoConnection" => 103,
            "ZohoUserMappingConfig" => 104,
            "ZohoSubscription" => 105,
            "KnowledgeRetrievalCache" => 106,
            "TopicAnalyticsTopic" => 107,
            "FileContentCache" => 108,
            "AgentVersionedId" => 109,
            "CompanyAgentConfigSnapshot" => 110,
            _ => 0
        };
        if (typeId == 0)
        {
            _logger.LogWarning("The typeId {TypeId} is Unknown for typeName {TypeName}", typeId, typeName);
        }

        return typeId;
    }

    public (string TypeName, long SnowflakeId) DecodeId(string id)
    {
        var decodeLong = _hashids.DecodeLong(id);

        var typeName = GetTypeName(decodeLong[0]);
        var snowflakeId = decodeLong[1];

        return (typeName, snowflakeId);
    }

    private string GetTypeName(long typeId)
    {
        return typeId switch
        {
            53 => "Workflow",
            _ => "Unknown"
        };
    }
}