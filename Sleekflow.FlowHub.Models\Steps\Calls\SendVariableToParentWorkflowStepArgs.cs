using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class SendVariableToParentWorkflowStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.agent-send-variable-to-parent-workflow";

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Ai;

    [JsonProperty("contact_id__expr")]
    public string ContactIdExpr { get; set; }

    [JsonProperty("exit_condition__expr")]
    public string? ExitConditionExpr { get; set; }

    // variabels
    // [JsonProperty("variables__expr")]
    // public Dictionary<string, string> Variables { get; set; }

    [JsonConstructor]
    public SendVariableToParentWorkflowStepArgs(string contactIdExpr, string? exitConditionExpr = null)
    {
        ContactIdExpr = contactIdExpr;
        ExitConditionExpr = exitConditionExpr;
    }
}