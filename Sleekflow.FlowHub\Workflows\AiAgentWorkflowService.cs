using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.FlowHub.Blobs;
using Sleekflow.FlowHub.Internals.Agents;
using Sleekflow.FlowHub.Models.Agents;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using JsonConfig = Sleekflow.FlowHub.JsonConfigs.JsonConfig;

using static Sleekflow.Persistence.AuditEntity;

namespace Sleekflow.FlowHub.Workflows;

public interface IAiAgentWorkflowService
{
    Task<ProxyWorkflow> CreateAiAgentWorkflow(
        string sleekflowCompanyId,
        List<Dictionary<string, object>> channelConfigs,
        Dictionary<string, object> schemafulObject,
        List<Dictionary<string, object>> agents,
        Dictionary<string, object> contactProperty,
        string sleekflowStaffId,
        string aiStepId,
        string dependencyWorkflowId
    );

    Task<(
        List<string> AddWorkflowIds,
        List<string> RemoveWorkflowIds,
        List<string> UpdatedWorkflowIds
    )> UpdateAiAgentWorkflowsByAiNode(
        string sleekflowCompanyId,
        string workflowId,
        Dictionary<string, object> schemafulObject,
        Dictionary<string, object> contactProperty,
        SleekflowStaff updatedByStaff
    );
}

public partial class AiAgentWorkflowService : IAiAgentWorkflowService, IScopedService
{
    private readonly IWorkflowService _workflowService;
    private readonly IBlobService _blobService;
    private readonly ICacheService _cacheService;
    private readonly ILogger<AiAgentWorkflowService> _logger;
    private readonly IWorkflowStepValidator _workflowStepValidator;
    private readonly IIdService _idService;
    private readonly IAgentConfigService _agentConfigService;

    public AiAgentWorkflowService(
        IWorkflowService workflowService,
        IBlobService blobService,
        ICacheService cacheService,
        ILogger<AiAgentWorkflowService> logger,
        IWorkflowStepValidator workflowStepValidator,
        IIdService idService,
        IAgentConfigService agentConfigService
    )
    {
        _workflowService = workflowService;
        _blobService = blobService;
        _cacheService = cacheService;
        _logger = logger;
        _workflowStepValidator = workflowStepValidator;
        _idService = idService;
        _agentConfigService = agentConfigService;
    }

    private async Task<List<Dictionary<string, object>>> GetAiAgentWorkflowSteps()
    {
        var cacheKey = $"AiAgentWorkflowSteps";

        var aiAgentWorkflowSteps = await _cacheService.CacheAsync(
            cacheKey,
            async () =>
            {
                var aiAgentWorkflowSteps = await _blobService.DownloadJsonAsAsync<List<Dictionary<string, object>>>(
                    "ai-flow-template",
                    "flow_steps.json"
                );

                return aiAgentWorkflowSteps;
            },
            TimeSpan.FromMinutes(5)
        );

        return aiAgentWorkflowSteps ?? new();
    }

    private async Task<Dictionary<string, object>> GetAiAgentWorkflowMetaFormatted()
    {
        var cacheKey = $"AiAgentWorkflowMetaFormatted";

        var aiAgentWorkflowMetaFormatted = await _cacheService.CacheAsync(
            cacheKey,
            async () =>
            {
                var aiAgentWorkflowMetaFormatted = await _blobService.DownloadJsonAsAsync<Dictionary<string, object>>(
                    "ai-flow-template",
                    "flow_meta_formatted.json"
                );

                return aiAgentWorkflowMetaFormatted;
            },
            TimeSpan.FromMinutes(5)
        );

        return aiAgentWorkflowMetaFormatted ?? new();
    }

    private async Task<List<Dictionary<string, object>>> GetCalculateScoreSteps()
    {
        var cacheKey = $"AiAgentWorkflowCalculateScoreSteps";

        var calculateScoreSteps = await _cacheService.CacheAsync(
            cacheKey,
            async () =>
            {
                var calculateScoreSteps = await _blobService.DownloadJsonAsAsync<List<Dictionary<string, object>>>(
                    "ai-flow-template",
                    "calculate_score_steps.json"
                );

                return calculateScoreSteps;
            },
            TimeSpan.FromMinutes(5)
        );

        return calculateScoreSteps ?? new();
    }

    private bool IsHaveEvaluateScoreStep(AgentConfig agentConfig)
    {
        var calculateLeadScore = agentConfig.Actions?.CalculateLeadScore;
        return calculateLeadScore?.Enabled ?? false;
    }

    private bool IsHaveBulkMessageStep(AgentConfig agentConfig)
    {
        var sendMessage = agentConfig.Actions?.SendMessage;
        return sendMessage?.ResponseType == "intelligent";
    }

    private bool IsHaveAddLabelStep(AgentConfig agentConfig)
    {
        var addLabel = agentConfig.Actions?.AddLabel;
        return addLabel?.Enabled ?? false;
    }

    private async Task<List<Dictionary<string, object>>> InsertEvaluateScoreStep(List<Dictionary<string, object>> stepsJson)
    {
        var calculateScoreSteps = await GetCalculateScoreSteps();

        if (calculateScoreSteps == null || !calculateScoreSteps.Any())
        {
            return stepsJson;
        }

        var aggregateStep = stepsJson.FirstOrDefault(step =>
            step.ContainsKey("call") && step["call"].ToString() == AggregateStepArgs.CallName
        );

        if (aggregateStep == null)
        {
            return stepsJson;
        }

        var aggregateStepIndex = stepsJson.IndexOf(aggregateStep);

        var firstCalculateScoreStep = calculateScoreSteps.FirstOrDefault(step =>
            step.ContainsKey("call") && step["call"].ToString() == AgentCalculateLeadScoreStepArgs.CallName);

        if (firstCalculateScoreStep != null && firstCalculateScoreStep.ContainsKey("id"))
        {
            var targetId = firstCalculateScoreStep["id"].ToString();
            stepsJson[aggregateStepIndex]["next_step_id"] = targetId;
        }

        for (int i = 0; i < calculateScoreSteps.Count; i++)
        {
            stepsJson.Insert(aggregateStepIndex + 1 + i, calculateScoreSteps[i]);
        }

        return stepsJson;
    }

    private List<Dictionary<string, object>> RemoveBulkMessageStep(List<Dictionary<string, object>> stepsJson)
    {
        var bulkMessageStep = stepsJson.FirstOrDefault(step =>
            step.ContainsKey("call") && step["call"].ToString() == AggregateStepArgs.CallName
        );

        if (bulkMessageStep == null)
        {
            return stepsJson;
        }

        var bulkMessageStepId = bulkMessageStep["id"].ToString();
        var bulkMessageNextStepId = bulkMessageStep["next_step_id"].ToString();

        var previousSteps = stepsJson.Where(step =>
            step.ContainsKey("next_step_id") && step["next_step_id"]?.ToString() == bulkMessageStepId
        ).ToList();

        foreach (var previousStep in previousSteps)
        {
            previousStep["next_step_id"] = bulkMessageNextStepId;
        }

        var conditionBranchingSteps = stepsJson.Where(step =>
            step.ContainsKey("name") && step["name"].ToString() == "condition-branching"
        ).ToList();

        foreach (var conditionBranchingStep in conditionBranchingSteps)
        {
            if (conditionBranchingStep.ContainsKey("switch"))
            {
                var switchCases = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(
                    JsonConvert.SerializeObject(conditionBranchingStep["switch"], JsonConfig.DefaultJsonSerializerSettings),
                    JsonConfig.DefaultJsonSerializerSettings
                );

                foreach (var switchCase in switchCases)
                {
                    if (switchCase.ContainsKey("next_step_id") && switchCase["next_step_id"].ToString() == bulkMessageStepId)
                    {
                        switchCase["next_step_id"] = bulkMessageNextStepId;
                    }
                }
                conditionBranchingStep["switch"] = switchCases;
            }
        }

        stepsJson.Remove(bulkMessageStep);

        return stepsJson;
    }

    private List<Dictionary<string, object>> InsertAddLabelStep(List<Dictionary<string, object>> stepsJson, string agentConfigId, bool isHaveBulkMessageStep)
    {
        var addLabelStep = new CallStep<AgentAddLabelStepArgs>(
            id: Guid.NewGuid().ToString(),
            name: "Agent Add Label",
            assign: null,
            nextStepId: null,
            call: AgentAddLabelStepArgs.CallName,
            args: new AgentAddLabelStepArgs(
                contactIdExpr: "{{ trigger_event_body.contact_id }}",
                companyAgentConfigIdExpr: agentConfigId,
                retrievalWindowTimestampExpr: ""
            )
        );

        //Need hard code the step id to match the workflow template
        if (isHaveBulkMessageStep)
        {
            addLabelStep.Args.RetrievalWindowTimestampExpr = "{{ '{{ (sys_var_dict[\"cadb7a99-026c-4769-9337-f44d99cb9f3b\"] | json.deserialize).completed_timestamp ?? \"\" }}' | template.eval }}";
        }

        var addLabelId = addLabelStep.Id;

        var smartReplyStep = stepsJson.FirstOrDefault(step =>
            step.ContainsKey("name") && step["name"].ToString() == "Send Message"
        );

        var smartReplyStepIndex = stepsJson.IndexOf(smartReplyStep);
        var smartReplyNextStepId = smartReplyStep["next_step_id"]?.ToString();

        smartReplyStep["next_step_id"] = addLabelId;
        addLabelStep.NextStepId = smartReplyNextStepId;
        var addLabelStepDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(
            JsonConvert.SerializeObject(addLabelStep, JsonConfig.DefaultJsonSerializerSettings)
        );
        stepsJson.Insert(smartReplyStepIndex + 1, addLabelStepDict);

        return stepsJson;
    }


    private async Task<WorkflowTriggers> BuildAiAgentWorkflowTriggers(
        List<Dictionary<string, object>> channelConfigs
    )
    {
        var channelIdentityId = channelConfigs[0]["channelIdentityId"]?.ToString();
        var aiAgentCondition = "ctx?.additional_data?.can_proceed_with_ai_agent_workflow == true";

        var messageReceived = new WorkflowTrigger($"{{{{ ([\"{channelIdentityId}\"] | array.contains event_body.channel_id) && {aiAgentCondition} }}}}");
        var messageSent = new WorkflowTrigger($"{{{{ ([\"{channelIdentityId}\"] | array.contains event_body.channel_id) && (event_body.message?.message_delivery_type == \"Normal\" || event_body.message?.message_delivery_type == \"QuickReply\") && {aiAgentCondition} }}}}");

        var triggers = new WorkflowTriggers(
            null,
            null,
            null,
            null,
            null,
            messageReceived,
            messageSent,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null
        );
        return triggers;
    }

    private async Task<Workflow> BuildAiAgentWorkflow(
            string sleekflowCompanyId,
            List<Dictionary<string, object>> channelConfigs,
            Dictionary<string, object> schemafulObject,
            List<Dictionary<string, object>> agents,
            Dictionary<string, object> contactProperty,
            string sleekflowStaffId,
            string aiStepId,
            string dependencyWorkflowId
        )
    {
        var flowStepsJson = await GetAiAgentWorkflowSteps();
        var flowMetaFormattedJson = await GetAiAgentWorkflowMetaFormatted();

        if (flowStepsJson == null || flowMetaFormattedJson == null)
        {
            throw new SfInternalErrorException("Failed to load workflow template files");
        }

        var agentConfigId = agents[0]["id"].ToString();
        var agentConfig = await _agentConfigService.GetAsync(sleekflowCompanyId, agentConfigId);

        _logger.LogInformation("Create Ai Agent Workflow agentConfig: {agentConfig}", JsonConvert.SerializeObject(agentConfig, JsonConfig.DefaultJsonSerializerSettings));

        var isHaveEvaluateScoreStep = IsHaveEvaluateScoreStep(agentConfig);
        var isHaveBulkMessageStep = IsHaveBulkMessageStep(agentConfig);
        var isHaveAddLabelStep = IsHaveAddLabelStep(agentConfig);

        // Note: First insert evaluate score step, then remove bulk message step
        if (isHaveEvaluateScoreStep)
        {
            flowStepsJson = await InsertEvaluateScoreStep(flowStepsJson);
        }

        if (!isHaveBulkMessageStep)
        {
            flowStepsJson = RemoveBulkMessageStep(flowStepsJson);
        }

        if (isHaveAddLabelStep)
        {
            flowStepsJson = InsertAddLabelStep(flowStepsJson, agentConfigId, isHaveBulkMessageStep);
        }

        var idMapping = new Dictionary<string, string>();

        var updatedSteps = UpdateStepsJson(
            flowStepsJson,
            channelConfigs,
            schemafulObject,
            agents,
            contactProperty,
            idMapping,
            aiStepId,
            dependencyWorkflowId,
            isHaveBulkMessageStep
        );

        _logger.LogInformation(
            "updatedSteps: {updatedSteps}",
            JsonConvert.SerializeObject(updatedSteps, JsonConfig.DefaultJsonSerializerSettings)
        );

        // TODO: Update meta json, don't impact flow execution
        // var updatedMeta = UpdateMetaJson(
        //     flowMetaFormattedJson,
        //     channelConfigs,
        //     schemafulObject,
        //     agents,
        //     contactProperty,
        //     idMapping
        // );

        // _logger.LogInformation(
        //     "updatedMeta: {updatedMeta}",
        //     JsonConvert.SerializeObject(updatedMeta, JsonConfig.DefaultJsonSerializerSettings)
        // );

        WorkflowEnrollmentSettings workflowEnrollmentSettings = new WorkflowEnrollmentSettings(
            canEnrollOnlyOnce: false,
            canEnrollAgainOnFailureOnly: false,
            canEnrollInParallel: true
        );

        AuditEntity.SleekflowStaff? createdByStaff = null;
        if (!string.IsNullOrEmpty(sleekflowStaffId))
        {
            createdByStaff = new AuditEntity.SleekflowStaff(sleekflowStaffId, null);
        }

        var steps = JsonConvert.DeserializeObject<List<Step>>(
            JsonConvert.SerializeObject(updatedSteps, JsonConfig.DefaultJsonSerializerSettings),
            JsonConfig.DefaultJsonSerializerSettings
        );

        _logger.LogInformation(
            "final steps: {steps}",
            JsonConvert.SerializeObject(steps, JsonConfig.DefaultJsonSerializerSettings)
        );

        await _workflowStepValidator.AssertAllStepsAreValidAsync(steps, null, null);

        var workflowId = _idService.GetId("Workflow");
        var workflowVersionedId = _idService.GetId("WorkflowVersioned", workflowId);

        var workflowTriggers = await BuildAiAgentWorkflowTriggers(channelConfigs);

        var newWorkflow = new Workflow(
            workflowId: workflowId,
            workflowVersionedId: workflowVersionedId,
            name: $"AI Agent Workflow - {agentConfig.Id} - {dependencyWorkflowId} - {aiStepId}",
            workflowType: WorkflowType.AIAgent,
            workflowGroupId: null,
            triggers: workflowTriggers,
            workflowEnrollmentSettings: workflowEnrollmentSettings,
            workflowScheduleSettings: null,
            steps: steps,
            activationStatus: WorkflowActivationStatuses.Draft,
            id: workflowVersionedId,
            createdAt: DateTimeOffset.UtcNow,
            updatedAt: DateTimeOffset.UtcNow,
            sleekflowCompanyId: sleekflowCompanyId,
            createdBy: createdByStaff,
            updatedBy: null,
            metadata: RecoverMetaJson(flowMetaFormattedJson),
            dependencyWorkflowId: dependencyWorkflowId,
            version: "v1",
            enrolmentPricingType: EnrolmentPricingTypes.Basic,
            isDynamicVariableEnabled: false);

        _logger.LogInformation(
            "newWorkflow: {newWorkflow}",
            JsonConvert.SerializeObject(newWorkflow, JsonConfig.DefaultJsonSerializerSettings)
        );

        return newWorkflow;
    }

    public async Task<ProxyWorkflow> CreateAiAgentWorkflow(
        string sleekflowCompanyId,
        List<Dictionary<string, object>> channelConfigs,
        Dictionary<string, object> schemafulObject,
        List<Dictionary<string, object>> agents,
        Dictionary<string, object> contactProperty,
        string sleekflowStaffId,
        string aiStepId,
        string dependencyWorkflowId
    )
    {
        var newWorkflow = await BuildAiAgentWorkflow(
         sleekflowCompanyId,
         channelConfigs,
         schemafulObject,
         agents,
         contactProperty,
         sleekflowStaffId,
         aiStepId,
         dependencyWorkflowId);

        await _workflowService.CreateWorkflowAsync(newWorkflow, sleekflowCompanyId);

        var createdWorkflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
            sleekflowCompanyId,
            newWorkflow.WorkflowVersionedId
        );

        if (createdWorkflow == null)
        {
            throw new SfInternalErrorException("Failed to retrieve created workflow");
        }

        var workflow = await _workflowService.EnableWorkflowAsync(
            createdWorkflow.WorkflowVersionedId,
            createdWorkflow.SleekflowCompanyId,
            createdWorkflow.CreatedBy
        );

        var agentConfigId = agents[0]["id"].ToString();
        await _agentConfigService.SetAgentFirstWorkflowPublishedAtAsync(sleekflowCompanyId, agentConfigId);

        return workflow;
    }

    private async Task<ProxyWorkflow> UpdateAiAgentWorkflow(
        string sleekflowCompanyId,
        List<Dictionary<string, object>> channelConfigs,
        Dictionary<string, object> schemafulObject,
        List<Dictionary<string, object>> agents,
        Dictionary<string, object> contactProperty,
        SleekflowStaff updatedByStaff,
        string aiStepId,
        string dependencyWorkflowId,
        string workflowId
    )
    {
        var newWorkflow = await BuildAiAgentWorkflow(
         sleekflowCompanyId,
         channelConfigs,
         schemafulObject,
         agents,
         contactProperty,
         updatedByStaff.SleekflowStaffId,
         aiStepId,
         dependencyWorkflowId);

        var activeAgentWorkflow = await _workflowService.GetActiveWorkflowAsync(workflowId, sleekflowCompanyId);
        if (activeAgentWorkflow != null)
        {
            var disabledAgentWorkflow = await _workflowService.DisableWorkflowAsync(activeAgentWorkflow.WorkflowVersionedId, sleekflowCompanyId, updatedByStaff);
            _logger.LogInformation(
                "disabledAgentWorkflow: {disabledAgentWorkflowId} {disabledAgentWorkflowIdVersionedId}",
                disabledAgentWorkflow.WorkflowId,
                disabledAgentWorkflow.WorkflowVersionedId
            );
        }
        var updatedWorkflow = await _workflowService.UpdateWorkflowAsync(
            workflowId,
            sleekflowCompanyId,
            newWorkflow.Triggers,
            newWorkflow.WorkflowEnrollmentSettings,
            newWorkflow.WorkflowScheduleSettings,
            newWorkflow.Steps,
            newWorkflow.Name,
            newWorkflow.WorkflowGroupId,
            updatedByStaff,
            newWorkflow.Metadata,
            manualEnrollmentSource: null,
            enrolmentPricingType: EnrolmentPricingTypes.Basic,
            workflowType: WorkflowType.AIAgent,
            dependencyWorkflowId: dependencyWorkflowId
        );

        _logger.LogInformation(
            "updatedAgentWorkflow: {updatedAgentWorkflowId} {updatedAgentWorkflowIdVersionedId}",
            updatedWorkflow.WorkflowId,
            updatedWorkflow.WorkflowVersionedId
        );

        if (updatedWorkflow == null)
        {
            throw new SfInternalErrorException("Failed to retrieve updated workflow");
        }

        var workflow = await _workflowService.EnableWorkflowAsync(
            updatedWorkflow.WorkflowVersionedId,
            updatedWorkflow.SleekflowCompanyId,
            updatedWorkflow.CreatedBy
        );

        _logger.LogInformation(
            "enabledAgentWorkflow: {enabledAgentWorkflowId} {enabledAgentWorkflowIdVersionedId}",
            workflow.WorkflowId,
            workflow.WorkflowVersionedId
        );

        return workflow;
    }


    public async Task<(
        List<string> AddWorkflowIds,
        List<string> RemoveWorkflowIds,
        List<string> UpdatedWorkflowIds
    )> UpdateAiAgentWorkflowsByAiNode(
        string sleekflowCompanyId,
        string workflowId,
        Dictionary<string, object> schemafulObject,
        Dictionary<string, object> contactProperty,
        SleekflowStaff updatedByStaff
    )
    {
        var addWorkflowIds = new List<string>();
        var removeWorkflowIds = new List<string>();
        var updatedWorkflowIds = new List<string>();

        var stepIdToAiAgentWorkflowIdMap = new Dictionary<string, string>();

        var parentWorkflow = await _workflowService.GetLatestWorkflowAsync(workflowId, sleekflowCompanyId);
        var currentAiAgentSteps = parentWorkflow.Steps
            .Where(s => s is CallStep<EnterAiAgentStepArgs> callStep && callStep.Call == EnterAiAgentStepArgs.CallName)
            .ToList();

        var childWorkflows = await _workflowService.GetAllLatestWorkflowAndStatusTuplesAsync(
            sleekflowCompanyId,
            null,
            100,
            null,
            new WorkflowFilters(
                null,
                null,
                null,
                null,
                null,
                WorkflowType.AIAgent,
                workflowId)
        );

        var previousChildWorkflowIds = childWorkflows.Workflows
            .Select(w => w.WorkflowId)
            .ToList();

        var currentChildWorkflowIds = currentAiAgentSteps
            .Select(s => (s as CallStep<EnterAiAgentStepArgs>)?.Args.AiAgentWorkflowId)
            .Where(id => id != null)
            .Select(id => id.ToString())
            .ToList();

        var workflowsToRemove = previousChildWorkflowIds
            .Where(id => !currentChildWorkflowIds.Contains(id))
            .ToList();

        foreach (var id in workflowsToRemove)
        {
            await _workflowService.DeleteWorkflowAsync(
                id,
                sleekflowCompanyId,
                updatedByStaff
            );
            removeWorkflowIds.Add(id);
        }

        foreach (var step in currentAiAgentSteps)
        {
            var callStep = step as CallStep<EnterAiAgentStepArgs>;
            var args = callStep.Args;
            var ai_agent_workflow_id = args.AiAgentWorkflowId;
            var agents = new List<Dictionary<string, object>>
                {
                    new Dictionary<string, object>
                    {
                        { "id", args.AiAgentId },
                    }
                };
            var channelConfigs = new List<Dictionary<string, object>>
                {
                    new Dictionary<string, object>
                    {
                        { "channelIdentityId", args.ChannelIdentityId },
                        { "channelType", args.ChannelType },
                        { "name", "ai channel" }
                    }
                };
            if (ai_agent_workflow_id == null)
            {
                var addedWorkflow = await CreateAiAgentWorkflow(
                    sleekflowCompanyId,
                    channelConfigs,
                    schemafulObject,
                    agents,
                    contactProperty,
                    updatedByStaff.SleekflowStaffId,
                    callStep.Id,
                    workflowId
                );
                addWorkflowIds.Add(addedWorkflow.WorkflowId);
                stepIdToAiAgentWorkflowIdMap[callStep.Id] = addedWorkflow.WorkflowId;
            }



            if (ai_agent_workflow_id != null)
            {
                var updatedWorkflow = await UpdateAiAgentWorkflow(
                    sleekflowCompanyId,
                    channelConfigs,
                    schemafulObject,
                    agents,
                    contactProperty,
                    updatedByStaff,
                    callStep.Id,
                    workflowId,
                    ai_agent_workflow_id
                );
                updatedWorkflowIds.Add(ai_agent_workflow_id);
            }
        }

        if (stepIdToAiAgentWorkflowIdMap.Count > 0)
        {
            var clonedSteps = JsonConvert.DeserializeObject<List<Step>>(
                JsonConvert.SerializeObject(parentWorkflow.Steps));

            if (clonedSteps == null)
            {
                throw new InvalidOperationException("Failed to clone steps");
            }

            foreach (var step in clonedSteps)
            {
                if (step is CallStep<EnterAiAgentStepArgs> callStep && stepIdToAiAgentWorkflowIdMap.ContainsKey(callStep.Id))
                {
                    callStep.Args.AiAgentWorkflowId = stepIdToAiAgentWorkflowIdMap[callStep.Id];
                }
            }

            await _workflowService.UpdateWorkflowAsync(
                workflowId,
                sleekflowCompanyId,
                parentWorkflow.Triggers,
                parentWorkflow.WorkflowEnrollmentSettings,
                parentWorkflow.WorkflowScheduleSettings,
                clonedSteps,
                parentWorkflow.Name,
                parentWorkflow.WorkflowGroupId,
                updatedByStaff,
                parentWorkflow.Metadata,
                manualEnrollmentSource: null,
                enrolmentPricingType: EnrolmentPricingTypes.Advanced);
        }
        _logger.LogInformation(
            "For AI Workflow {workflowId}, addWorkflowIds: {addWorkflowIds}, removeWorkflowIds: {removeWorkflowIds}, updatedWorkflowIds: {updatedWorkflowIds}",
            workflowId,
            JsonConvert.SerializeObject(addWorkflowIds, JsonConfig.DefaultJsonSerializerSettings),
            JsonConvert.SerializeObject(removeWorkflowIds, JsonConfig.DefaultJsonSerializerSettings),
            JsonConvert.SerializeObject(updatedWorkflowIds, JsonConfig.DefaultJsonSerializerSettings)
        );
        return (addWorkflowIds, removeWorkflowIds, updatedWorkflowIds);
    }


    private Dictionary<string, object> UpdateMetaJson(
        Dictionary<string, object> metaJson,
        List<Dictionary<string, object>> channelConfigs,
        Dictionary<string, object> schemafulObject,
        List<Dictionary<string, object>> agents,
        Dictionary<string, object> contactProperty,
        Dictionary<string, string> idMapping
    )
    {
        if (metaJson.ContainsKey("v6"))
        {
            var v6Json = JsonConvert.SerializeObject(metaJson["v6"], JsonConfig.DefaultJsonSerializerSettings);
            var v6 = JsonConvert.DeserializeObject<Dictionary<string, object>>(
                v6Json,
                JsonConfig.DefaultJsonSerializerSettings
            );

            if (v6 != null)
            {
                UpdateNodeIds(v6, idMapping);

                UpdateParams(v6, channelConfigs, schemafulObject, agents, contactProperty);

                metaJson["v6"] = v6;
            }
        }

        return metaJson;
    }

    private List<Dictionary<string, object>> UpdateStepsId(
        List<Dictionary<string, object>> stepsJson,
        Dictionary<string, string> idMapping
    )
    {
        var fixedNodeIds = new[] { "setup-contact-and-conversation", "setup-contact-owner" };

        foreach (var step in stepsJson)
        {
            var oldId = step["id"].ToString();

            if (fixedNodeIds.Contains(oldId))
            {
                continue;
            }

            var newId = Guid.NewGuid().ToString();
            idMapping[oldId] = newId;
        }

        foreach (var step in stepsJson)
        {
            var oldId = step["id"].ToString();

            if (fixedNodeIds.Contains(oldId))
            {
                continue;
            }

            step["id"] = idMapping[oldId];

            if (step["next_step_id"] != null && idMapping.ContainsKey(step["next_step_id"].ToString()))
            {
                step["next_step_id"] = idMapping[step["next_step_id"].ToString()];
            }

            if (
                step.ContainsKey("name")
                && !string.IsNullOrEmpty(step["name"].ToString())
                && step["name"].ToString().StartsWith("jumpTo_")
            )
            {
                var oldJumpId = step["name"].ToString().Replace("jumpTo_", "");
                if (idMapping.ContainsKey(oldJumpId))
                {
                    step["name"] = $"jumpTo_{idMapping[oldJumpId]}";
                }
            }

            if (step["name"].ToString() == "condition-branching")
            {
                if (step.ContainsKey("switch") && step["switch"] != null)
                {
                    var switchCases = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(
                        JsonConvert.SerializeObject(step["switch"], JsonConfig.DefaultJsonSerializerSettings),
                        JsonConfig.DefaultJsonSerializerSettings
                    );

                    foreach (var condition in switchCases)
                    {
                        if (
                            condition["next_step_id"] != null
                            && idMapping.ContainsKey(condition["next_step_id"].ToString())
                        )
                        {
                            condition["next_step_id"] = idMapping[condition["next_step_id"].ToString()];
                        }

                        if (condition["condition"] != null)
                        {
                            foreach (var idKey in idMapping)
                            {
                                if (condition["condition"].ToString().Contains(idKey.Key))
                                {
                                    condition["condition"] = condition["condition"]
                                        .ToString()
                                        .Replace(idKey.Key, idKey.Value);
                                }
                            }
                        }
                    }
                    step["switch"] = switchCases;
                }
            }

            if (step.ContainsKey("args"))
            {
                var argsJsonStr = JsonConvert.SerializeObject(step["args"], JsonConfig.DefaultJsonSerializerSettings);
                foreach (var kvp in idMapping)
                {
                    if (argsJsonStr.Contains(kvp.Key))
                    {
                        argsJsonStr = argsJsonStr.Replace(kvp.Key, kvp.Value);
                    }
                }
                var args = JsonConvert.DeserializeObject<Dictionary<string, object>>(
                    argsJsonStr,
                    JsonConfig.DefaultJsonSerializerSettings
                );
                step["args"] = args;
            }
        }

        return stepsJson;
    }

    private List<Dictionary<string, object>> UpdateStepArgs(
        List<Dictionary<string, object>> stepsJson,
        List<Dictionary<string, object>> channelConfigs,
        Dictionary<string, object> schemafulObject,
        List<Dictionary<string, object>> agents,
        Dictionary<string, object> contactProperty,
        Dictionary<string, string> idMapping,
        string aiStepId,
        string dependencyWorkflowId,
        bool isHaveBulkMessageStep
    )
    {
        foreach (var step in stepsJson)
        {
            if (step.ContainsKey("args"))
            {
                var argsJsonStr = JsonConvert.SerializeObject(step["args"], JsonConfig.DefaultJsonSerializerSettings);

                argsJsonStr = argsJsonStr.Replace("AI_POC", $"AI_POC_{dependencyWorkflowId}_{aiStepId}");

                var args = JsonConvert.DeserializeObject<Dictionary<string, object>>(
                    argsJsonStr,
                    JsonConfig.DefaultJsonSerializerSettings
                );

                step["args"] = args;

                if (step.ContainsKey("call") && step["call"].ToString() == SendMessageV2StepArgs.CallName)
                {

                    var sendMsgStepArgs = JsonConvert.DeserializeObject<SendMessageV2StepArgs>(
                        JsonConvert.SerializeObject(step["args"], JsonConfig.DefaultJsonSerializerSettings),
                        JsonConfig.DefaultJsonSerializerSettings
                    );

                    var msgBody = sendMsgStepArgs.MessageExpr;

                    foreach (var kvp in idMapping)
                    {
                        if (msgBody.Contains(kvp.Key))
                        {
                            msgBody = msgBody.Replace(kvp.Key, kvp.Value);
                        }
                    }

                    sendMsgStepArgs.MessageExpr = msgBody;

                    var channelType = channelConfigs[0]["channelType"].ToString();
                    var channelIdentityId = channelConfigs[0]["channelIdentityId"].ToString();

                    sendMsgStepArgs.ChannelType = channelType;
                    sendMsgStepArgs.ChannelIdentityId = channelIdentityId;

                    if (channelType == "facebook")
                    {
                        sendMsgStepArgs.FacebookMessageParameters = new FacebookMessageParameters
                        {
                            SendWindow = "within_24_hours_window",
                            MessageTag = null,
                        };
                    }

                    step["args"] = sendMsgStepArgs;
                }

                if (
                    step.ContainsKey("call")
                    && (
                        step["call"].ToString() == UpdateSchemafulObjectStepArgs.CallName
                        || step["call"].ToString() == CreateSchemafulObjectStepArgs.CallName
                    )
                )
                {
                    var schemaIdExpr = args["schema_id__expr"].ToString();

                    schemaIdExpr = "{{ \"" + schemafulObject["id"] + "\" }}";

                    var propertyValuesKeyExprDict = JsonConvert.DeserializeObject<Dictionary<string, string>>(
                        JsonConvert.SerializeObject(
                            args["property_values__key_expr_dict"],
                            JsonConfig.DefaultJsonSerializerSettings
                        ),
                        JsonConfig.DefaultJsonSerializerSettings
                    );

                    var propertyMapping = new Dictionary<string, string>();

                    if (propertyValuesKeyExprDict != null && schemafulObject.ContainsKey("properties"))
                    {
                        var newPropertyValuesKeyExprDict = new Dictionary<string, string>();
                        var properties = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(
                            JsonConvert.SerializeObject(
                                schemafulObject["properties"],
                                JsonConfig.DefaultJsonSerializerSettings
                            ),
                            JsonConfig.DefaultJsonSerializerSettings
                        );

                        foreach (var kvp in propertyValuesKeyExprDict)
                        {
                            string value = kvp.Value;

                            if (
                                value.Contains("score")
                                || value.Contains("completed_timestamp")
                                || value.Contains("category")
                                || value.Contains("reason")
                            )
                            {
                                foreach (var prop in properties)
                                {
                                    if (prop.ContainsKey("unique_name") && prop.ContainsKey("id"))
                                    {
                                        string uniqueName = prop["unique_name"].ToString();

                                        if (
                                            (uniqueName.Contains("score") && value.Contains("score"))
                                            || (
                                                uniqueName.Contains("timestamp")
                                                && value.Contains("completed_timestamp")
                                            )
                                            || (uniqueName.Contains("category") && value.Contains("category"))
                                            || (uniqueName.Contains("reason") && value.Contains("reason"))
                                        )
                                        {
                                            foreach (var idKey in idMapping)
                                            {
                                                if (value.Contains(idKey.Key))
                                                {
                                                    value = value.Replace(idKey.Key, idKey.Value);
                                                }
                                            }
                                            newPropertyValuesKeyExprDict[prop["id"].ToString()] = value;
                                            break;
                                        }
                                    }
                                }
                            }
                            else
                            {
                                newPropertyValuesKeyExprDict[kvp.Key] = value;
                            }
                        }

                        args["property_values__key_expr_dict"] = newPropertyValuesKeyExprDict;
                    }

                    args["schema_id__expr"] = schemaIdExpr;
                    step["args"] = args;
                }

                if (step.ContainsKey("call") && step["call"].ToString() == UpdateContactPropertiesStepArgs.CallName)
                {
                    var propertyValuesKeyExprDict = new Dictionary<string, string>
                    {
                        { contactProperty["id"].ToString(), "{{ \"true\" }}" },
                    };

                    args["properties__key_expr_dict"] = propertyValuesKeyExprDict;
                    step["args"] = args;
                }

                if (
                    step.ContainsKey("call") && step["call"].ToString() == AgentRecommendReplyStepArgs.CallName
                    || step["call"].ToString() == AgentEvaluateScoreStepArgs.CallName
                    || step["call"].ToString() == EvaluateExitConditionsStepArgs.CallName
                    || step["call"].ToString() == AgentCalculateLeadScoreStepArgs.CallName
                )
                {
                    var agentConfigId = agents[0]["id"].ToString();
                    args["company_agent_config_id__expr"] = $"{agentConfigId}";
                    if (!isHaveBulkMessageStep)
                    {
                        args["retrieval_window_timestamp__expr"] = "";
                    }
                    step["args"] = args;
                }
            }

            if (step["name"].ToString() == "condition-branching")
            {
                if (step.ContainsKey("switch") && step["switch"] != null)
                {
                    var switchCases = JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(
                        JsonConvert.SerializeObject(step["switch"], JsonConfig.DefaultJsonSerializerSettings),
                        JsonConfig.DefaultJsonSerializerSettings
                    );

                    foreach (var condition in switchCases)
                    {
                        var isMatch = Regex.Match(
                            condition["condition"].ToString(),
                            @"usr_var_dict\.contact\[""[a-f0-9-]+""\]"
                        );

                        if (condition["condition"] != null && isMatch.Success)
                        {
                            condition["condition"] =
                                $"{{{{ (usr_var_dict.contact[\"{contactProperty["id"]}\"] | string.equals_ignore_case 'true') }}}}";
                        }
                    }
                    step["switch"] = switchCases;
                }
            }
        }
        return stepsJson;
    }

    private List<Dictionary<string, object>> UpdateStepsJson(
        List<Dictionary<string, object>> stepsJson,
        List<Dictionary<string, object>> channelConfigs,
        Dictionary<string, object> schemafulObject,
        List<Dictionary<string, object>> agents,
        Dictionary<string, object> contactProperty,
        Dictionary<string, string> idMapping,
        string aiStepId,
        string dependencyWorkflowId,
        bool isHaveBulkMessageStep
    )
    {
        stepsJson = UpdateStepsId(stepsJson, idMapping);

        stepsJson = UpdateStepArgs(
            stepsJson,
            channelConfigs,
            schemafulObject,
            agents,
            contactProperty,
            idMapping,
            aiStepId,
            dependencyWorkflowId,
            isHaveBulkMessageStep
        );

        return stepsJson;
    }

    private void UpdateNodeIds(Dictionary<string, object> v6, Dictionary<string, string> idMapping)
    {
        var fixedNodeIds = new[] { "setup-contact-and-conversation", "setup-contact-owner" };

        if (v6.ContainsKey("nodes"))
        {
            var nodesJson = JsonConvert.SerializeObject(v6["nodes"], JsonConfig.DefaultJsonSerializerSettings);
            var nodesList = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(
                nodesJson,
                JsonConfig.DefaultJsonSerializerSettings
            );

            if (nodesList == null)
                return;

            foreach (var node in nodesList)
            {
                if (!node.ContainsKey("id"))
                    continue;

                var oldId = node["id"].ToString();

                if (fixedNodeIds.Contains(oldId))
                {
                    continue;
                }

                if (node.ContainsKey("data"))
                {
                    var dataJson = JsonConvert.SerializeObject(node["data"], JsonConfig.DefaultJsonSerializerSettings);
                    foreach (var mapping in idMapping)
                    {
                        if (dataJson.Contains(mapping.Key))
                        {
                            dataJson = dataJson.Replace(mapping.Key, mapping.Value);
                        }
                    }
                    var updatedData = JsonConvert.DeserializeObject<Dictionary<string, object>>(
                        dataJson,
                        JsonConfig.DefaultJsonSerializerSettings
                    );
                    node["data"] = updatedData;
                }
            }

            foreach (var node in nodesList)
            {
                if (!node.ContainsKey("id"))
                    continue;

                var oldId = node["id"].ToString();

                if (fixedNodeIds.Contains(oldId))
                {
                    continue;
                }

                node["id"] = idMapping[oldId];

                if (node.ContainsKey("data"))
                {
                    var dataJson = JsonConvert.SerializeObject(node["data"], JsonConfig.DefaultJsonSerializerSettings);
                    foreach (var mapping in idMapping)
                    {
                        if (dataJson.Contains(mapping.Key))
                        {
                            dataJson = dataJson.Replace(mapping.Key, mapping.Value);
                        }
                    }
                    var updatedData = JsonConvert.DeserializeObject<Dictionary<string, object>>(
                        dataJson,
                        JsonConfig.DefaultJsonSerializerSettings
                    );
                    node["data"] = updatedData;
                }
            }

            v6["nodes"] = nodesList;

            if (v6.ContainsKey("edges"))
            {
                var edgesJson = JsonConvert.SerializeObject(v6["edges"], JsonConfig.DefaultJsonSerializerSettings);
                var edges = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(
                    edgesJson,
                    JsonConfig.DefaultJsonSerializerSettings
                );

                if (edges != null)
                {
                    foreach (var edge in edges)
                    {
                        edge["id"] = Guid.NewGuid().ToString();

                        if (edge.ContainsKey("source") && idMapping.ContainsKey(edge["source"].ToString()))
                        {
                            edge["source"] = idMapping[edge["source"].ToString()];
                        }
                        if (edge.ContainsKey("target") && idMapping.ContainsKey(edge["target"].ToString()))
                        {
                            edge["target"] = idMapping[edge["target"].ToString()];
                        }
                    }

                    v6["edges"] = edges;
                }
            }
        }
    }

    private List<Dictionary<string, object>> UpdateMetaCustomObjectRecord(
        Dictionary<string, object> schemafulObject,
        List<Dictionary<string, object>> properties
    )
    {
        var schemaProperties = new Dictionary<string, string>();
        var schemaPropsJson = JsonConvert.SerializeObject(
            schemafulObject["properties"],
            JsonConfig.DefaultJsonSerializerSettings
        );
        var schemaProps = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(
            schemaPropsJson,
            JsonConfig.DefaultJsonSerializerSettings
        );

        if (schemaProps != null)
        {
            foreach (var prop in schemaProps)
            {
                schemaProperties[prop["unique_name"].ToString()] = prop["id"].ToString();
            }
        }

        foreach (var prop in properties)
        {
            if (prop.ContainsKey("property"))
            {
                var propertyJson = JsonConvert.SerializeObject(
                    prop["property"],
                    JsonConfig.DefaultJsonSerializerSettings
                );
                var property = JsonConvert.DeserializeObject<Dictionary<string, object>>(
                    propertyJson,
                    JsonConfig.DefaultJsonSerializerSettings
                );

                if (property != null && property.ContainsKey("name"))
                {
                    var propName = property["name"].ToString();
                    if (schemaProperties.ContainsKey(propName))
                    {
                        property["id"] = schemaProperties[propName];
                        prop["property"] = property;
                    }
                }
            }
        }

        return properties;
    }

    private void UpdateParams(
        Dictionary<string, object> v6,
        List<Dictionary<string, object>> channelConfigs,
        Dictionary<string, object> schemafulObject,
        List<Dictionary<string, object>> agents,
        Dictionary<string, object> contactProperty
    )
    {
        if (v6.ContainsKey("nodes"))
        {
            var nodesJson = JsonConvert.SerializeObject(v6["nodes"], JsonConfig.DefaultJsonSerializerSettings);
            var nodes = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(
                nodesJson,
                JsonConfig.DefaultJsonSerializerSettings
            );

            if (nodes == null)
                return;

            foreach (var node in nodes)
            {
                if (node.ContainsKey("data"))
                {
                    var dataJson = JsonConvert.SerializeObject(node["data"], JsonConfig.DefaultJsonSerializerSettings);
                    var data = JsonConvert.DeserializeObject<Dictionary<string, object>>(
                        dataJson,
                        JsonConfig.DefaultJsonSerializerSettings
                    );

                    if (data != null && data.ContainsKey("formValues"))
                    {
                        var formValuesJson = JsonConvert.SerializeObject(
                            data["formValues"],
                            JsonConfig.DefaultJsonSerializerSettings
                        );
                        var formValues = JsonConvert.DeserializeObject<Dictionary<string, object>>(
                            formValuesJson,
                            JsonConfig.DefaultJsonSerializerSettings
                        );
                        if (formValues == null)
                            continue;

                        if (formValues.ContainsKey("channels"))
                        {
                            var channels = new List<Dictionary<string, object>>();
                            foreach (var configItem in channelConfigs)
                            {
                                var channelId = configItem["channelIdentityId"].ToString();
                                channels.Add(
                                    new Dictionary<string, object>
                                    {
                                        { "id", channelId },
                                        { "channelIdentityId", channelId },
                                        { "name", configItem["name"] },
                                        { "channel", configItem["channelType"] },
                                    }
                                );
                            }
                            formValues["channels"] = channels;
                        }

                        if (
                            formValues.ContainsKey("channelType")
                            && formValues["channelType"].ToString() == "SPECIFIC_CHANNEL"
                        )
                        {
                            if (formValues.ContainsKey("channel") && channelConfigs.Any())
                            {
                                formValues["channel"] = channelConfigs[0]["channelIdentityId"];
                            }
                        }

                        if (formValues.ContainsKey("conditions"))
                        {
                            var conditionsJson = JsonConvert.SerializeObject(
                                formValues["conditions"],
                                JsonConfig.DefaultJsonSerializerSettings
                            );
                            var conditions = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(
                                conditionsJson,
                                JsonConfig.DefaultJsonSerializerSettings
                            );

                            if (conditions != null)
                            {
                                foreach (var condition in conditions)
                                {
                                    if (
                                        condition.ContainsKey("conditionType")
                                        && condition["conditionType"].ToString() == "contactProperty"
                                    )
                                    {
                                        if (
                                            contactProperty != null
                                            && contactProperty.ContainsKey("id")
                                            && condition.ContainsKey("contactProperty")
                                        )
                                        {
                                            var contactPropertyDictJson = JsonConvert.SerializeObject(
                                                condition["contactProperty"],
                                                JsonConfig.DefaultJsonSerializerSettings
                                            );
                                            var contactPropertyDict = JsonConvert.DeserializeObject<
                                                Dictionary<string, object>
                                            >(contactPropertyDictJson, JsonConfig.DefaultJsonSerializerSettings);

                                            if (contactPropertyDict != null)
                                            {
                                                contactPropertyDict["id"] = contactProperty["id"];
                                                condition["contactProperty"] = contactPropertyDict;
                                            }
                                        }
                                    }
                                }
                                formValues["conditions"] = conditions;
                            }
                        }

                        if (formValues.ContainsKey("actionType"))
                        {
                            if (
                                formValues["actionType"].ToString() == "createCustomObjectRecord"
                                || formValues["actionType"].ToString() == "updateCustomObjectRecord"
                            )
                            {
                                if (formValues.ContainsKey("customObjectId") && schemafulObject != null)
                                {
                                    formValues["customObjectId"] = schemafulObject["id"];
                                }

                                if (
                                    formValues.ContainsKey("properties")
                                    && schemafulObject != null
                                    && schemafulObject.ContainsKey("properties")
                                )
                                {
                                    var propertiesJson = JsonConvert.SerializeObject(
                                        formValues["properties"],
                                        JsonConfig.DefaultJsonSerializerSettings
                                    );
                                    var properties = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(
                                        propertiesJson,
                                        JsonConfig.DefaultJsonSerializerSettings
                                    );

                                    if (properties != null)
                                    {
                                        formValues["properties"] = UpdateMetaCustomObjectRecord(
                                            schemafulObject,
                                            properties
                                        );
                                    }
                                }

                                if (
                                    formValues.ContainsKey("optionalProperties")
                                    && schemafulObject != null
                                    && schemafulObject.ContainsKey("properties")
                                )
                                {
                                    var propertiesJson = JsonConvert.SerializeObject(
                                        formValues["optionalProperties"],
                                        JsonConfig.DefaultJsonSerializerSettings
                                    );
                                    var properties = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(
                                        propertiesJson,
                                        JsonConfig.DefaultJsonSerializerSettings
                                    );

                                    if (properties != null)
                                    {
                                        formValues["optionalProperties"] = UpdateMetaCustomObjectRecord(
                                            schemafulObject,
                                            properties
                                        );
                                    }
                                }
                            }
                            else if (formValues["actionType"].ToString() == "updateContactProperties")
                            {
                                if (contactProperty != null && contactProperty.ContainsKey("id"))
                                {
                                    var propertyId = contactProperty["id"].ToString();
                                    var propertyName = contactProperty.ContainsKey("label")
                                        ? contactProperty["label"].ToString()
                                        : null;

                                    if (
                                        !string.IsNullOrEmpty(propertyId)
                                        && !string.IsNullOrEmpty(propertyName)
                                        && formValues.ContainsKey("property")
                                    )
                                    {
                                        var propertyJson = JsonConvert.SerializeObject(
                                            formValues["property"],
                                            JsonConfig.DefaultJsonSerializerSettings
                                        );
                                        var property = JsonConvert.DeserializeObject<Dictionary<string, object>>(
                                            propertyJson,
                                            JsonConfig.DefaultJsonSerializerSettings
                                        );

                                        if (property != null)
                                        {
                                            if (
                                                property.ContainsKey("name")
                                                && property["name"].ToString() == propertyName
                                            )
                                            {
                                                property["id"] = propertyId;
                                            }
                                            else
                                            {
                                                property["id"] = propertyId;
                                            }
                                            formValues["property"] = property;
                                        }
                                    }
                                }
                            }
                        }

                        if (
                            formValues.ContainsKey("companyAgentConfigId")
                            && formValues.ContainsKey("actionType")
                            && formValues["actionType"].ToString() == "agentRecommendReply"
                        )
                        {
                            if (agents != null)
                            {
                                formValues["companyAgentConfigId"] = agents[0]["id"];
                            }
                        }

                        data["formValues"] = formValues;
                        node["data"] = data;
                    }
                }
            }

            v6["nodes"] = nodes;
        }
    }

    private Dictionary<string, object> RecoverMetaJson(Dictionary<string, object> metaJson)
    {
        if (metaJson == null || !metaJson.ContainsKey("v6"))
        {
            return new Dictionary<string, object>();
        }

        var v6Str = JsonConvert.SerializeObject(metaJson["v6"], JsonConfig.DefaultJsonSerializerSettings);

        var newMetaJson = new Dictionary<string, object> { { "v6", v6Str } };

        return newMetaJson;
    }
}
