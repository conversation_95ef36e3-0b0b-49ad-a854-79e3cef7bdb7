﻿using System.IdentityModel.Tokens.Jwt;
using System.Net;
using Google.Apis.Auth.OAuth2;
using Google.Apis.Auth.OAuth2.Flows;
using Google.Apis.Auth.OAuth2.Responses;
using Google.Apis.PeopleService.v1;
using Google.Apis.Services;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.Integrator.GoogleSheets.Configs;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;
using Sleekflow.Utils;

namespace Sleekflow.Integrator.GoogleSheets.Authentications;

public interface IGoogleSheetsAuthenticationService
{
    Task<GoogleSheetsAuthentication?> GetAsync(
        string id,
        string sleekflowCompanyId);

    string GetAuthenticationUrl(
        string sleekflowCompanyId,
        string successUrl,
        string failureUrl,
        Dictionary<string, object?>? additionalDetails);

    Task<GoogleSheetsAuthentication> ReAuthenticateAndStoreAsync(
        string id,
        string sleekflowCompanyId);

    Task<(GoogleSheetsAuthentication Authentication, string Email, string SuccessUrl, string FailureUrl)>
        HandleAuthenticateCallbackAndStoreAsync(
            string code,
            string encryptedState);

    Task DeleteAsync(string id, string sleekflowCompanyId);
}

public class GoogleSheetsAuthenticationService : ISingletonService, IGoogleSheetsAuthenticationService
{
    private const string GoogleSheetsOauth2AuthBaseUrl = "https://accounts.google.com/o/oauth2/v2/auth";
    private const string GoogleSheetsOauth2TokenBaseUrl = "https://oauth2.googleapis.com/token";

    private readonly IGoogleSheetsAuthenticationRepository _googleSheetsAuthenticationRepository;
    private readonly IGoogleSheetsConfig _googleSheetsConfig;
    private readonly ILogger<GoogleSheetsAuthenticationService> _logger;
    private readonly HttpClient _httpClient;
    private readonly IIdService _idService;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;

    public GoogleSheetsAuthenticationService(
        IGoogleSheetsAuthenticationRepository googleSheetsAuthenticationRepository,
        IGoogleSheetsConfig googleSheetsConfig,
        ILogger<GoogleSheetsAuthenticationService> logger,
        IHttpClientFactory httpClientFactory,
        IIdService idService,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer)
    {
        _googleSheetsAuthenticationRepository = googleSheetsAuthenticationRepository;
        _googleSheetsConfig = googleSheetsConfig;
        _logger = logger;
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _idService = idService;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
    }

    public class State
    {
        public string? SleekflowCompanyId { get; set; }

        public string? SuccessUrl { get; set; }

        public string? FailureUrl { get; set; }

        public Dictionary<string, object?>? AdditionalDetails { get; set; }

        [JsonConstructor]
        public State(
            string? sleekflowCompanyId,
            string? successUrl,
            string? failureUrl,
            Dictionary<string, object?>? additionalDetails)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SuccessUrl = successUrl;
            FailureUrl = failureUrl;
            AdditionalDetails = additionalDetails;
        }
    }

    public class Oauth2TokenOutput
    {
        [JsonProperty("access_token")]
        public string? AccessToken { get; set; }

        [JsonProperty("expires_in")]
        public int? ExpiresIn { get; set; }

        [JsonProperty("token_type")]
        public string? TokenType { get; set; }

        [JsonProperty("scope")]
        public string? Scope { get; set; }

        [JsonProperty("refresh_token")]
        public string? RefreshToken { get; set; }

        [JsonProperty("id_token")]
        public string? IdToken { get; set; }
    }

    public async Task<GoogleSheetsAuthentication?> GetAsync(
        string id,
        string sleekflowCompanyId)
    {
        var authentication = await _googleSheetsAuthenticationRepository.GetOrDefaultAsync(
            id,
            sleekflowCompanyId);
        if (authentication == null)
        {
            return null;
        }

        if (DateTimeOffset.UtcNow.AddHours(1) < authentication.IssuedAt)
        {
            return authentication;
        }

        return await ReAuthenticateAndStoreAsync(id, sleekflowCompanyId);
    }

    public string GetAuthenticationUrl(
        string sleekflowCompanyId,
        string successUrl,
        string failureUrl,
        Dictionary<string, object?>? additionalDetails)
    {
        const string responseType = "code";
        const string accessType = "offline";
        const bool includeGrantedScopes = true;
        const string scope = "https://www.googleapis.com/auth/drive https://www.googleapis.com/auth/spreadsheets email";
        const string prompt = "consent select_account";

        var state = new State(
            sleekflowCompanyId,
            successUrl,
            failureUrl,
            additionalDetails);

        var encryptedState = AesUtils.AesEncryptBase64(
            JsonConvert.SerializeObject(state),
            _googleSheetsConfig.GoogleSheetsOauthStateEncryptionKey);

        return $"{GoogleSheetsOauth2AuthBaseUrl}?" +
               $"&prompt={WebUtility.UrlEncode(prompt)}" +
               $"&scope={WebUtility.UrlEncode(scope)}" +
               $"&access_type={accessType}" +
               $"&include_granted_scopes={includeGrantedScopes.ToString().ToLower()}" +
               $"&response_type={responseType}" +
               $"&redirect_uri={_googleSheetsConfig.GoogleSheetsOauthCallbackUrl}" +
               $"&state={WebUtility.UrlEncode(encryptedState)}" +
               $"&client_id={_googleSheetsConfig.GoogleSheetsClientId}";
    }

    public async Task<GoogleSheetsAuthentication> ReAuthenticateAndStoreAsync(
        string id,
        string sleekflowCompanyId)
    {
        var authentication = await _googleSheetsAuthenticationRepository.GetOrDefaultAsync(
            id,
            sleekflowCompanyId);
        if (authentication == null || authentication.RefreshToken == null)
        {
            throw new SfUnauthorizedException();
        }

        _logger.LogInformation(
            "Started ReAuthenticateAndStoreAsync. id {Id}",
            id);

        var getTokenRequestBody = new FormUrlEncodedContent(
            new Dictionary<string, string>
            {
                ["client_id"] = _googleSheetsConfig.GoogleSheetsClientId,
                ["client_secret"] = _googleSheetsConfig.GoogleSheetsClientSecret,
                ["refresh_token"] = authentication.RefreshToken,
                ["grant_type"] = "refresh_token",
            });
        getTokenRequestBody.Headers.Clear();
        getTokenRequestBody.Headers.Add("Content-Type", "application/x-www-form-urlencoded");
        var getTokenResponse = await _httpClient.PostAsync(GoogleSheetsOauth2TokenBaseUrl, getTokenRequestBody);
        var getTokenResponseStr = await getTokenResponse.Content.ReadAsStringAsync();

        if (!getTokenResponse.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.GoogleSheetsOauthApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", getTokenResponse.StatusCode.ToString() },
                    { "sleekflow_company_id", sleekflowCompanyId },
                });

            _logger.LogError(
                "Unable to get a success /oauth2/token response from Google. httpResponseMessage {HttpResponseMessage}, readAsStringAsync {ReadAsStringAsync}",
                getTokenResponse,
                getTokenResponseStr);

            throw new Exception("Unable to get a success /oauth2/token response from Google");
        }

        var oauth2TokenOutput = getTokenResponseStr.ToObject<Oauth2TokenOutput>();
        if (oauth2TokenOutput == null
            || oauth2TokenOutput.AccessToken == null)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.GoogleSheetsOauthApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", getTokenResponse.StatusCode.ToString() },
                    { "sleekflow_company_id", sleekflowCompanyId },
                });

            _logger.LogError(
                "Unable to get a success /oauth2/token response from Google. httpResponseMessage {HttpResponseMessage}, readAsStringAsync {ReadAsStringAsync}",
                getTokenResponse,
                getTokenResponseStr);

            throw new Exception("Unable to get a success /oauth2/token response from Google");
        }

        authentication.AccessToken = oauth2TokenOutput.AccessToken;
        authentication.IssuedAt = DateTimeOffset.UtcNow;
        authentication.RefreshRes = oauth2TokenOutput;

        var upsertAsync = await _googleSheetsAuthenticationRepository.UpsertAsync(authentication, authentication.SleekflowCompanyId);
        if (upsertAsync == 0)
        {
            throw new SfInternalErrorException("Unable to upsert GoogleSheetsAuthentication.");
        }

        _logger.LogInformation(
            "Completed ReAuthenticateAndStoreAsync. id {Id}. sleekflowCompanyId {SleekflowCompanyId}",
            id,
            sleekflowCompanyId);

        return authentication;
    }

    public async Task<(GoogleSheetsAuthentication Authentication, string Email, string SuccessUrl, string FailureUrl)>
        HandleAuthenticateCallbackAndStoreAsync(
            string code,
            string encryptedState)
    {
        var decryptStateStr =
            AesUtils.AesDecryptBase64(
                encryptedState,
                _googleSheetsConfig.GoogleSheetsOauthStateEncryptionKey);
        var state = decryptStateStr.ToObject<State>();
        if (state == null || string.IsNullOrEmpty(state.SleekflowCompanyId))
        {
            _logger.LogWarning("The SleekflowCompanyId is null or empty, or the state is invalid");

            throw new Exception("Unable to get a correct state.");
        }

        var nvc = new List<KeyValuePair<string, string>>
        {
            new ("grant_type", "authorization_code"),
            new ("code", code),
            new ("client_id", _googleSheetsConfig.GoogleSheetsClientId),
            new ("client_secret", _googleSheetsConfig.GoogleSheetsClientSecret),
            new ("redirect_uri", _googleSheetsConfig.GoogleSheetsOauthCallbackUrl),
        };
        var httpRequestMessage = new HttpRequestMessage(
            HttpMethod.Post,
            $"{GoogleSheetsOauth2TokenBaseUrl}")
        {
            Content = new FormUrlEncodedContent(nvc)
        };
        var httpResponseMessage = await _httpClient.SendAsync(httpRequestMessage);
        var readAsStringAsync = await httpResponseMessage.Content.ReadAsStringAsync();

        var oauth2TokenOutput = readAsStringAsync.ToObject<Oauth2TokenOutput>();
        if (oauth2TokenOutput == null
            || string.IsNullOrEmpty(oauth2TokenOutput.AccessToken)
            || string.IsNullOrEmpty(oauth2TokenOutput.RefreshToken)
            || string.IsNullOrEmpty(oauth2TokenOutput.IdToken)
            || string.IsNullOrEmpty(oauth2TokenOutput.Scope)
            || string.IsNullOrEmpty(oauth2TokenOutput.TokenType)
            || oauth2TokenOutput.ExpiresIn == null)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.GoogleSheetsOauthApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMessage.StatusCode.ToString() },
                    { "sleekflow_company_id", state.SleekflowCompanyId },
                });

            _logger.LogWarning(
                "The oauth2TokenOutput is null or has null. sleekflowCompanyId {SleekflowCompanyId}," +
                "readAsStringAsync {ReadAsStringAsync}," +
                "httpResponseMessage {HttpResponseMessage}",
                state.SleekflowCompanyId,
                readAsStringAsync,
                httpResponseMessage);

            throw new Exception("Unable to deserialize a success /oauth2/token response from Google");
        }

        var handler = new JwtSecurityTokenHandler();
        var decodedJwtToken = handler.ReadJwtToken(oauth2TokenOutput.IdToken);
        var email = decodedJwtToken.Claims.First(claim => claim.Type == "email").Value;
        if (email == null)
        {
            throw new Exception("Unable to obtain user email from Google");
        }

        var authentication = new GoogleSheetsAuthentication(
            _idService.GetId("GoogleSheetsAuthentication"),
            state.SleekflowCompanyId!,
            oauth2TokenOutput.AccessToken,
            oauth2TokenOutput.RefreshToken,
            oauth2TokenOutput.ExpiresIn.Value,
            oauth2TokenOutput.TokenType,
            oauth2TokenOutput.Scope,
            DateTimeOffset.UtcNow,
            oauth2TokenOutput,
            null);

        var upsertAsync =
            await _googleSheetsAuthenticationRepository.UpsertAsync(
                authentication,
                state.SleekflowCompanyId!);
        if (upsertAsync == 0)
        {
            throw new SfInternalErrorException("Unable to upsert GoogleSheetsAuthentication.");
        }

        _logger.LogInformation(
            "Completed HandleAuthenticateCallbackAndStoreAsync. code {Code}, encryptedState {EncryptedState}, sleekflowCompanyId {SleekflowCompanyId}",
            code,
            encryptedState,
            state.SleekflowCompanyId);

        return (authentication, email, state.SuccessUrl!, state.FailureUrl!);
    }

    public async Task DeleteAsync(string id, string sleekflowCompanyId)
    {
        var authentication = await _googleSheetsAuthenticationRepository.GetAsync(
            id,
            sleekflowCompanyId);
        if (authentication is null)
        {
            throw new SfNotFoundObjectException(id, sleekflowCompanyId);
        }

        var deleteAsync = await _googleSheetsAuthenticationRepository.DeleteAsync(
            id,
            sleekflowCompanyId);
        if (deleteAsync == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to delete the GoogleSheetsAuthentication with id {id}, sleekflowCompanyId {sleekflowCompanyId}");
        }
    }
}