﻿namespace Sleekflow.IntelligentHub.Models.Constants;

public static class ContainerNames
{
    public const string DatabaseId = "intelligenthubdb";
    public const string FileDocument = "file_document";
    public const string FileDocumentChunk = "file_document_chunk";
    public const string WebsiteDocumentChunk = "website_document_chunk";
    public const string WebpageDocumentChunk = "webpage_document_chunk";
    public const string WebCrawlingSession = "web_crawling_session";
    public const string KnowledgeBaseEntry = "knowledge_base_entry";
    public const string IntelligentHubConfig = "intelligent_hub_config";
    public const string IntelligentHubUsage = "intelligent_hub_usage";
    public const string BlobUploadHistory = "blob_upload_history";
    public const string CompanyConfig = "company_config";
    public const string CompanyAgentConfig = "company_agent_config";
    public const string CompanyAgentConfigSnapshot = "company_agent_config_snapshot";
    public const string Playground = "playground";
    public const string KnowledgeRetrievalCache = "knowledge_retrieval_cache";
    public const string TopicAnalyticsTopic = "topic_analytics_topic";
    public const string FileContentCache = "file_content_cache";
}