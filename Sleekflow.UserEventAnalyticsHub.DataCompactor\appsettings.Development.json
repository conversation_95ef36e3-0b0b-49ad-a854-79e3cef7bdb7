{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Sleekflow.UserEventAnalyticsHub.DataCompactor": "Debug"}}, "CompactorConfig": {"MaxConcurrentCompanies": 1, "FileBatchSize": 10, "ProcessingTimeoutMinutes": 5}, "ProcessingConfig": {"DuckDbMemoryLimitMB": 512, "MaxParallelCompanies": 1, "BatchCommitSize": 100, "ProgressReportingIntervalSeconds": 10}}