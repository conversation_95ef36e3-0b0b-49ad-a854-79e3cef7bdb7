using System.ComponentModel.DataAnnotations;
using Auth0.ManagementApi.Models;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.TenantHub.Auth0;
using Sleekflow.TenantHub.Auth0;
using Sleekflow.TenantHub.Models.Auth0s;
using Sleekflow.TenantHub.Models.Constants;
using Sleekflow.TenantHub.Rbac;
using Auth0Client = Auth0.ManagementApi.Models.Client;
using Auth0User = Auth0.ManagementApi.Models.User;

namespace Sleekflow.TenantHub.Triggers.Webhooks.Auth0;

[TriggerGroup(ControllerNames.Auth0, $"{BasePaths.Webhooks}")]
public class PostUserLogin : ITrigger<PostUserLogin.PostUserLoginInput, PostUserLogin.PostUserLoginOutput>
{
    private readonly IAuth0UserManagementService _auth0UserManagementService;
    private readonly ILogger<PostUserLogin> _logger;
    private readonly IRbacService _rbacService;

    public PostUserLogin(
        IAuth0UserManagementService auth0UserManagementService,
        ILogger<PostUserLogin> logger,
        IRbacService rbacService)
    {
        _auth0UserManagementService = auth0UserManagementService;
        _logger = logger;
        _rbacService = rbacService;
    }

    public class PostUserLoginInput
    {
        [Required]
        [JsonProperty("token")]
        public string Token { get; set; }

        [Required]
        [JsonProperty("user")]
        [Validations.ValidateObject]
        public Auth0User User { get; set; }

        [JsonProperty("connection_strategy")]
        public string? ConnectionStrategy { get; set; }

        [JsonProperty("connection_name")]
        public string? ConnectionName { get; set; }

        [JsonConstructor]
        public PostUserLoginInput(string token, Auth0User user, string? connectionStrategy, string? connectionName)
        {
            Token = token;
            User = user;
            ConnectionStrategy = connectionStrategy;
            ConnectionName = connectionName;
        }
    }

    public static class PostUserLoginResponseStatus
    {
        public const string Unchanged = "unchanged";
        public const string MergedByAuth0UserId = "merged_by_auth0_user_id";
        public const string MergedByEmail = "merged_by_email";
        public const string ErrorConflictUsername = "error_conflict_username";
        public const string ErrorConflictEmailAndNotVerified = "error_conflict_email_and_not_verified";
        public const string ErrorEmailAndNotVerified = "error_email_and_not_verified";
        public const string ErrorUserMustCompleteInvitation = "error_user_must_complete_invitation";
        public const string NewUser = "new_user";
        public const string ErrorNotValidEnterpriseUser = "error_not_valid_enterprise_user";
        public const string MergedByEnterpriseImportedUser = "merged_by_enterprise_imported_user";

        public static readonly string[] AllStatuses =
        {
            Unchanged,
            MergedByAuth0UserId,
            MergedByEmail,
            ErrorConflictUsername,
            ErrorConflictEmailAndNotVerified,
            ErrorEmailAndNotVerified,
            ErrorUserMustCompleteInvitation,
            NewUser,
            ErrorNotValidEnterpriseUser,
            MergedByEnterpriseImportedUser
        };
    }

    public class PostUserLoginOutput
    {
        [JsonProperty("auth0_user_id")]
        public string Auth0UserId { get; set; }

        [JsonProperty("app_metadata")]
        public Auth0AppMetadata? AppMetadata { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonConstructor]
        public PostUserLoginOutput(string auth0UserId, Auth0AppMetadata? appMetadata, string status)
        {
            Auth0UserId = auth0UserId;
            AppMetadata = appMetadata;
            Status = status;
        }
    }

    public async Task<PostUserLoginOutput> F(PostUserLoginInput input)
    {
        _logger.LogInformation(
            "PostUserLogin {Auth0EventUser}",
            JsonConvert.SerializeObject(input.User, Formatting.None));

        var isValid = await _auth0UserManagementService.ValidateAuth0ActionToken(input.Token);
        if (!isValid)
        {
            throw new SfInvalidEventTokenException();
        }

        try
        {
            // Map Imported Enterprise User by Connection and Email
            if (input.ConnectionStrategy == "adfs" ||
                input.ConnectionStrategy == "oidc" || input.ConnectionStrategy == "waad")
            {
                var possibleImportedEnterpriseUser = await _auth0UserManagementService.FindUserByEmailAsync(input.User.Email);
                if (possibleImportedEnterpriseUser is not null)
                {
                    var (updatedAuth0User, _) =
                        await _auth0UserManagementService.AssociateDbUserWithAuth0UserAsync(
                            input.User,
                            possibleImportedEnterpriseUser);

                    // Consider we don't need to update this, and SyncLoginDataAsync no need to call as well
                    // possibleImportedEnterpriseUser.LastLoginAt = DateTime.UtcNow;

                    // EmailConfirmed is default to true for enterprise user (User fields mapping will always set to true)
                    // possibleImportedEnterpriseUser.EmailConfirmed = input.User.EmailVerified.GetValueOrDefault();

                    var auth0AppMetadata = Auth0UserManagementService.GetAuth0AppMetadata(updatedAuth0User);

                    return
                        new PostUserLoginOutput(
                            input.User.UserId,
                            auth0AppMetadata,
                            PostUserLoginResponseStatus.MergedByEnterpriseImportedUser);
                }

                // The handling of not valid enterprise user
                _logger.LogInformation(
                    "PostUserLogin {Auth0EventUser}. Not valid enterprise user",
                    JsonConvert.SerializeObject(input.User, Formatting.None));

                throw new Exception(PostUserLoginResponseStatus.ErrorNotValidEnterpriseUser);
            }

            var tuple = await _auth0UserManagementService.FindAuth0UserAsync(input.User);
            if (tuple != null)
            {
                var applicationUser = tuple.Value.ConvertedUser;
                var auth0AppMetadata = tuple.Value.Auth0AppMetadata;

                // Make sure all invited user has completed the registration
                if (applicationUser.Username is not null && applicationUser.Username.StartsWith("invite."))
                {
                    _logger.LogInformation(
                        "PostUserLogin {Auth0EventUser}. User has not complete the invitation",
                        JsonConvert.SerializeObject(input.User, Formatting.None));

                    throw new Exception(PostUserLoginResponseStatus.ErrorUserMustCompleteInvitation);
                }

                // Need to make sure the email is verified to let the user login
                if (auth0AppMetadata?.LoginRequiresEmailVerification is true && input.User.EmailVerified is not true)
                {
                    _logger.LogInformation(
                        "PostUserLogin {Auth0EventUser}. You need to first verify your email",
                        JsonConvert.SerializeObject(input.User, Formatting.None));

                    await _auth0UserManagementService.SendVerifyEmailAsync(input.User);

                    throw new Exception(PostUserLoginResponseStatus.ErrorEmailAndNotVerified);
                }

                // Make sure the last login time has stored in user db.
                // await _auth0UserManagementService.SyncLoginDataAsync(input.User);

                // Rbac feature is enabled for the company (Only for migrated user)
                if (applicationUser.UserWorkspaces.Any())
                {
                    var isRbacEnabled = await _rbacService.IsRbacFeatureEnabledForCompanyAsync(
                        applicationUser.UserWorkspaces.FirstOrDefault()!.SleekflowCompanyId);
                    auth0AppMetadata!.IsRbacEnabled = isRbacEnabled;
                }
                else
                {
                    auth0AppMetadata!.IsRbacEnabled = false;
                }

                return new PostUserLoginOutput(
                    input.User.UserId,
                    tuple.Value.Auth0AppMetadata,
                    PostUserLoginResponseStatus.Unchanged);
            }

            // Start handle the case: The user is not found in the Auth0 database

            // Map Auth0User to DbUser if matched the id (the user is by import)
            // auth|0j2093jg90j90jsd90oii -> 0j2093jg90j90jsd90oii
            var possibleDbUserId = input.User.UserId.Split("|").ElementAtOrDefault(1);
            if (possibleDbUserId is not null)
            {
                var possibleDbUser = await _auth0UserManagementService.FindUserAsync(possibleDbUserId);
                if (possibleDbUser is not null)
                {
                    var (updatedAuth0User, _) =
                        await _auth0UserManagementService.AssociateDbUserWithAuth0UserAsync(input.User, possibleDbUser);

                    // Make sure the last login time has stored in user db. (Consider we will not need to use this)
                    // await _auth0UserManagementService.SyncLoginDataAsync(input.User);

                    // No need to verify email again
                    var auth0AppMetadata = Auth0UserManagementService.GetAuth0AppMetadata(updatedAuth0User);
                    auth0AppMetadata!.LoginRequiresEmailVerification = false;

                    return
                        new PostUserLoginOutput(
                            input.User.UserId,
                            auth0AppMetadata,
                            PostUserLoginResponseStatus.MergedByAuth0UserId);
                }
            }

            if (input.User.UserName is not null)
            {
                // Can't map because the Username is conflicted
                // This is unlikely to happen
                var existingDbUserWithUserName =
                    await _auth0UserManagementService.FindUserByUserNameAsync(input.User.UserName);
                if (existingDbUserWithUserName is not null)
                {
                    _logger.LogInformation(
                        "PostUserLogin {Auth0EventUser}. The username is already taken. Please choose another user name",
                        JsonConvert.SerializeObject(input.User, Formatting.None));

                    throw new Exception(PostUserLoginResponseStatus.ErrorConflictUsername);
                }
            }

            // Map Auth0User to SleekflowUser if matched the email (social login or user is by signup)
            var existingDbUser = await _auth0UserManagementService.FindUserByEmailAsync(input.User.Email);
            if (existingDbUser is not null)
            {
                // Considering the sleekflow_id is null, so we can safely assume the user is by signup if the email is not verified.
                // We need to make sure the email is verified to merge the user
                if (input.User.EmailVerified is not true)
                {
                    _logger.LogInformation(
                        "PostUserLogin {Auth0EventUser}. You need to first verify your email",
                        JsonConvert.SerializeObject(input.User, Formatting.None));

                    await _auth0UserManagementService.SendVerifyEmailAsync(input.User);

                    throw new Exception(PostUserLoginResponseStatus.ErrorConflictEmailAndNotVerified);
                }

                var (updatedAuth0User, _) =
                    await _auth0UserManagementService.AssociateDbUserWithAuth0UserAsync(input.User, existingDbUser);

                // Make sure the last login time has stored in user db.
                // await _auth0UserManagementService.SyncLoginDataAsync(input.User);

                // Need to make sure the email is verified to let the user login
                var auth0AppMetadata = Auth0UserManagementService.GetAuth0AppMetadata(updatedAuth0User);
                auth0AppMetadata!.LoginRequiresEmailVerification = true;

                return
                    new PostUserLoginOutput(
                        input.User.UserId,
                        auth0AppMetadata,
                        PostUserLoginResponseStatus.MergedByEmail);
            }

            var (newlyAssociatedAuth0User, _) =
                await _auth0UserManagementService.CreateNewDbUserAndAssociateWithAuth0User(input.User);

            return
                new PostUserLoginOutput(
                    input.User.UserId,
                    Auth0UserManagementService.GetAuth0AppMetadata(newlyAssociatedAuth0User),
                    PostUserLoginResponseStatus.NewUser);
        }
        catch (Exception e)
        {
            if (PostUserLoginResponseStatus.AllStatuses.Contains(e.Message))
            {
                return
                    new PostUserLoginOutput(
                        input.User.UserId,
                        null,
                        e.Message);
            }

            _logger.LogError(
                e,
                "PostUserLogin {Auth0EventUser} throw a unknown exception: {ErrorMessage}",
                JsonConvert.SerializeObject(input.User, Formatting.None),
                e.Message);

            throw new SfAuth0ActionFlowsEventException(e, e.Message);
        }
    }
}