using Microsoft.AspNetCore.Http;

namespace Sleekflow.Mvc.Configs;

public static class BypassPathConfig
{
    private static readonly string[] BypassPaths =
    {
        "/Internals/GetUserAuthenticationDetails",
        "/Internals/PropagateBatchToProvider",
        "/Management/EnabledFeatures/IsFeatureEnabledForCompany",
        "/Balances/GetWhatsappCloudApiBusinessBalances",
        "/Authorized/Rbac/IsRbacEnabled",
        "/Management/Rbac/IsRbacEnabled",
        "/CrmHubConfigs/GetCrmHubConfig",
        "/Management/Rbac/GetRolesByStaffId",
        "/Webhooks/Auth0/PostUserLogin",
        "/Webhooks/Auth0/RequiresMfa"
    };

    public static bool IsPathBypassed(PathString requestPath)
    {
        var requestPathValue = requestPath.Value?.TrimEnd('/') ?? string.Empty;

        // Check if path matches any of the bypass paths (case-insensitive)
        return Array.Exists(BypassPaths, path =>
            string.Equals(requestPathValue, path.TrimEnd('/'), StringComparison.OrdinalIgnoreCase));
    }
}
