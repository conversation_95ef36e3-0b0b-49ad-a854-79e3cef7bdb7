namespace Sleekflow.TenantHub.Models.Constants;

public static class TravisBackendPaths
{
    public const string IpWhiteListRefreshCache = "/auth0/tenant-hub/RefreshIpWhitelistCache";
    public const string IpWhiteListCleanCache = "/auth0/tenant-hub/CleanIpWhitelistCache";
    public const string RegisterCompany = "/auth0/tenant-hub/RegisterCompany";
    public const string GetApplicationUser = "/auth0/tenant-hub/GetApplicationUser";
    public const string GetUser = "/auth0/tenant-hub/GetUser";
    public const string SyncLoginData = "/auth0/tenant-hub/SyncLoginData";
    public const string AssociateDbUserToAuth0 = "/auth0/tenant-hub/AssociateDbUserToAuth0";
    public const string CreateNewDbUser = "/auth0/tenant-hub/CreateNewDbUser";
    public const string UpdateDbUser = "/auth0/tenant-hub/UpdateDbUser";

    /**
     * webhook URL for query userworkspaces lost
     */
    public const string GetUserLossInfo = "/auth0/tenant-hub/GetUserLossInfo";
    public const string DeleteUser = "/auth0/tenant-hub/DeleteDbUser";
    public const string OnUserChangePassword = "/auth0/tenant-hub/OnUserChangePassword";
    public const string InviteUserByEmail = "/auth0/tenant-hub/InviteUsersByEmail";
    public const string CompleteEmailInvitation = "/auth0/account/CompleteInvitation";
    public const string CompleteLinkInvitation = "/auth0/tenant-hub/AcceptShareableInviteLink";
    public const string ResendEmailInvitation = "/auth0/tenant-hub/ResendInvitationEmail";
    public const string GenerateShareableLink = "/auth0/tenant-hub/GenerateShareableLink";
    public const string IsCompanyRegistered = "/auth0/tenant-hub/IsCompanyRegistered";
    public const string DeleteCompanyStaff = "/auth0/tenant-hub/DeleteCompanyStaff";
    public const string GetUserStaff = "/auth0/tenant-hub/GetUserStaff";
    public const string GetCompanyOwner = "/auth0/tenant-hub/GetCompanyOwner";
    public const string UpdateStaffRole = "/auth0/tenant-hub/UpdateStaffRole";
    public const string IsRbacEnabled = "/auth0/tenant-hub/IsRbacEnabledFromServerEnvironment";
    public const string ImportUserFromCsv = "/auth0/tenant-hub/ImportUserFromCsv";
    public const string GetCompanyTeamIdsByNames = "/auth0/tenant-hub/GetCompanyTeamIdsByNames";
    public const string GetCompanyUsage = "/auth0/tenant-hub/GetCompanyUsage";
    public const string OnRbacFeatureEnabled = "/auth0/tenant-hub/OnRbacFeatureEnabled";
    public const string OnRbacFeatureDisabled = "/auth0/tenant-hub/OnRbacFeatureDisabled";
    public const string OnUserRoleAssigned = "/auth0/tenant-hub/OnUserRoleAssigned";
    public const string OnCompanyPoliciesSaved = "/auth0/tenant-hub/OnCompanyPoliciesSaved";
}