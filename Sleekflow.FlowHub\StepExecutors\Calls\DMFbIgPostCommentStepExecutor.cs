using System.ComponentModel;
using System.Net.Mime;
using System.Reflection;
using GraphApi.Client.ApiClients;
using GraphApi.Client.ApiClients.Exceptions;
using GraphApi.Client.Models.FacebookPageObjects;
using GraphApi.Client.Models.InstagramPageObjects;
using MimeDetective;
using MimeDetective.Definitions;
using Newtonsoft.Json;
using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IDmFbIgPostCommentStepExecutor : IStepExecutor
{
}

public class DmFbIgPostCommentStepExecutor(
    IWorkflowStepLocator workflowStepLocator,
    IWorkflowRuntimeService workflowRuntimeService,
    IServiceProvider serviceProvider,
    IStateEvaluator stateEvaluator,
    IHttpClientFactory httpClientFactory,
    IStateAggregator stateAggregator,
    ILogger<DmFbIgPostCommentStepExecutor> logger,
    ICoreCommander coreCommander)
    : GeneralStepExecutor<CallStep<DmFbIgPostCommentStepArgs>>(
            workflowStepLocator,
            workflowRuntimeService,
            serviceProvider),
        IDmFbIgPostCommentStepExecutor,
        IScopedService
{
    private readonly HttpClient _httpClient = httpClientFactory.CreateClient("default-flow-hub-handler");
    private readonly ILogger<DmFbIgPostCommentStepExecutor> _logger = logger;
    private static readonly string DmTypeMessage = "message";
    private static readonly string DmTypeMedia = "media";

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);
        var args = await GetArgs(callStep, state, step);
        object replyToCommentResponse = null;
        try
        {
            var updatedState = await stateAggregator.AggregateStateStepBodyAsync(
                state,
                step.Id,
                JsonConvert.SerializeObject(replyToCommentResponse));

            await onActivatedAsync(updatedState, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            if (e is GraphApiClientException g)
            {
                throw new SfFlowHubUserFriendlyException(
                    UserFriendlyErrorCodes.MetaGraphApiClientError,
                    $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                    g);
            }

            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }

        switch (args.Channel)
        {
            case ChannelTypes.Facebook:
            {
                await SendFacebookDmMessage(state, args, step);
                break;
            }

            case ChannelTypes.Instagram:
            {
                await SendInstagramDmMessage(state, args);
                break;
            }

            default:
            {
                _logger.LogError("illegal channel: {Channel}", args.Channel);
                throw new SfInvalidChannelException(args.Channel);
            }
        }

        await onActivatedAsync(state, StepExecutionStatuses.Complete);
    }

    private async Task<SendMessengerMessageResponse> SendInstagramDmMessage(ProxyState state, DmFbIgArgs args)
    {
        string pageAccessToken;
        var getInstagramPageAccessToken = JsonConvert.DeserializeObject<GetInstagramPageAccessTokenOutput>(
            await coreCommander.ExecuteAsync(
                state.Origin,
                "GetInstagramPageAccessToken",
                GetInstagramPageAccessTokenArgs(args.PageId, state)));

        if (getInstagramPageAccessToken == null ||
            string.IsNullOrEmpty(getInstagramPageAccessToken.InstagramPageAccessToken))
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to get args because of unable to get instagram page {args.PageId} access token");
        }

        pageAccessToken = getInstagramPageAccessToken.InstagramPageAccessToken;
        var pageCommentClient = new InstagramPageCommentClient(pageAccessToken, _httpClient);

        var sendMessengerMessageResponse = await pageCommentClient.InitiateDmFromCommentAsync(
            args.CommentId,
            new InstagramPageMessengerMessageObject
            {
                Text = args.DmText,
                Attachment = string.IsNullOrWhiteSpace(args.MediaUrl)
                    ? null
                    : new InstagramPageMessengerAttachmentDataObject
                    {
                        Type = args.MediaType,
                        Payload =
                            new InstagramPageMessengerPayloadObject
                            {
                                Url = args.MediaUrl,
                            }
                    }
            });
        return sendMessengerMessageResponse;
    }

    private async Task<SendMessengerMessageResponse> SendFacebookDmMessage(ProxyState state, DmFbIgArgs args, Step step)
    {
        string pageAccessToken;
        var getFacebookPageAccessToken = JsonConvert.DeserializeObject<GetFacebookPageAccessTokenOutput>(
            await coreCommander.ExecuteAsync(
                state.Origin,
                "GetFacebookPageAccessToken",
                GetFacebookPageAccessTokenArgs(args.PageId, state)));

        if (getFacebookPageAccessToken == null ||
            string.IsNullOrEmpty(getFacebookPageAccessToken.FacebookPageAccessToken))
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to get args because of unable to get facebook page {args.PageId} access token");
        }

        pageAccessToken = getFacebookPageAccessToken.FacebookPageAccessToken;

        var pageCommentClient = new FacebookPageCommentClient(pageAccessToken, _httpClient);

        // var facebookPageMessengerMessageObject = new FacebookPageMessengerMessageObject();
        // var facebookMessengerMessageObject = new FacebookMessengerMessageObject(
        //     "MESSAGE_TAG",
        //     "HUMAN_AGENT",
        //     facebookPageMessengerMessageObject);
        FacebookPageMessengerAttachmentObject? facebookPageMessengerAttachmentObject = null;
        string? dmText = null;
        if (DmTypeMedia.Equals(args.DmType))
        {
            var facebookPageMessengerPayloadObject =
                new FacebookPageMessengerPayloadObject()
                {
                    Url = args.MediaUrl
                };
            var pageMessengerAttachmentObject =
                new FacebookPageMessengerAttachmentObject()
                {
                    Type = args.MediaType, Payload = facebookPageMessengerPayloadObject
                };
            facebookPageMessengerAttachmentObject = pageMessengerAttachmentObject;
        }
        else
        {
            dmText = args.DmText;
        }

        var facebookPageMessengerMessageObject = new FacebookPageMessengerMessageObject()
        {
            Text = dmText, Attachment = facebookPageMessengerAttachmentObject,
        };
        try
        {
            _logger.LogInformation(
                "Try to send facebook dm, " +
                "PageId: {PageId}, CommentId: {CommentId}, MessageObject: {MessageObject}",
                args.PageId,
                args.CommentId,
                JsonConvert.SerializeObject(facebookPageMessengerMessageObject));
            var replyToCommentResponse = await pageCommentClient.InitiateFacebookDmFromCommentAsync(
                args.PageId,
                args.CommentId,
                facebookPageMessengerMessageObject);
            return replyToCommentResponse;
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to send instagram dm");
        }
    }

    private GetFacebookPageAccessTokenInput GetFacebookPageAccessTokenArgs(
        string facebookPageId,
        ProxyState state)
    {
        return new GetFacebookPageAccessTokenInput(state.Identity.SleekflowCompanyId, facebookPageId);
    }

    private GetInstagramPageAccessTokenInput GetInstagramPageAccessTokenArgs(
        string instagramPageId,
        ProxyState state)
    {
        return new GetInstagramPageAccessTokenInput(state.Identity.SleekflowCompanyId, instagramPageId);
    }

    private async Task<(string MediaUrl, string MediaType, string MediaName)> GetMessageBodyAndMessageTypeAsync(
        ProxyState state,
        DmFbIgPostCommentStepArgs callStepArgs)
    {
        if (string.IsNullOrWhiteSpace(callStepArgs.MediaParameters?.MediaUrlExpr))
        {
            return (string.Empty, string.Empty, string.Empty);
        }

        var mediaUrl = (string) (await stateEvaluator.EvaluateTemplateStringExpressionAsync(
                                     state,
                                     callStepArgs.MediaParameters!.MediaUrlExpr!)
                                 ?? throw new InvalidOperationException(
                                     $"No media url evaluated from expression {callStepArgs.MediaParameters.MediaUrlExpr}"));
        var mediaResponse = await _httpClient.GetAsync(
            mediaUrl,
            HttpCompletionOption.ResponseHeadersRead);
        if (!mediaResponse.IsSuccessStatusCode)
        {
            throw new InvalidOperationException(
                $"Couldn't read media from url {mediaUrl}. Status code: {mediaResponse.StatusCode}");
        }

        await using var mediaStream = await mediaResponse.Content.ReadAsStreamAsync();

        var inspector = new ContentInspectorBuilder
            {
                Definitions = DefaultDefinitions.All()
            }
            .Build();

        var fileType = inspector.Inspect(mediaStream)
            .Select(x => x.Definition.File)
            .FirstOrDefault();

        var mineType = fileType?.MimeType ?? MediaTypeNames.Application.Octet;

        var mediaType = mineType switch
        {
            _ when mineType.StartsWith("image") => "image",
            _ when mineType.StartsWith("video") => "video",
            _ when mineType.StartsWith("audio") => "audio",
            _ when mineType.StartsWith("file") => "file",
            _ when mineType.StartsWith("application") => "document",
            _ => throw new NotSupportedException($"Unsupported media type {mineType}")
        };

        var mediaName = (string?) await stateEvaluator.EvaluateTemplateStringExpressionAsync(
            state,
            callStepArgs.MediaParameters.MediaNameExpr ?? string.Empty);

        if (string.IsNullOrWhiteSpace(mediaName))
        {
            mediaName = Guid.NewGuid().ToString();
        }

        if (!string.IsNullOrWhiteSpace(fileType?.MimeType))
        {
            mediaName = $"{Path.GetFileNameWithoutExtension(mediaName)}.{fileType.Extensions.First()}";
        }

        return (mediaUrl, mediaType, mediaType);
    }


    private async Task<DmFbIgArgs> GetArgs(
        CallStep<DmFbIgPostCommentStepArgs> callStep,
        ProxyState state,
        Step step)
    {
        try
        {

            var pageId = (string) (await stateEvaluator.EvaluateExpressionAsync(
                                       state,
                                       "{{ trigger_event_body.page_id }}")
                                   ?? throw new InvalidOperationException("No page id found"));

            var commentId = (string) (await stateEvaluator.EvaluateExpressionAsync(
                                          state,
                                          "{{ trigger_event_body.comment_id }}")
                                      ?? throw new InvalidOperationException("No comment id found"));

            var channel = (string) (await stateEvaluator.EvaluateExpressionAsync(
                                        state,
                                        "{{ trigger_event_body.channel }}")
                                    ?? throw new InvalidOperationException("No channel found"));

            var dmType = (string) (await stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.DmType)
                                   ?? callStep.Args.DmType);

            var dmText = (string) (await stateEvaluator.EvaluateTemplateStringExpressionAsync(state, callStep.Args.DmText)
                                   ?? callStep.Args.DmText);

            _logger.LogInformation(
                "PageId = {PageId}, CommentId = {CommentId}, Channel = {Channel}, DmType = {DmType}, DmText = {DmText}",
                pageId,
                commentId,
                channel,
                dmType,
                dmText);

            var (mediaUrl, mediaType, mediaName) = await GetMessageBodyAndMessageTypeAsync(
                state,
                callStep.Args);
            _logger.LogInformation(
                "MediaUrl: {MediaUrl}, mediaType: {MediaType}, mediaName : {MediaName}",
                mediaUrl,
                mediaType,
                mediaName);

            var dmFbIgArgs = new DmFbIgArgs
            {
                PageId = pageId,
                CommentId = commentId,
                Channel = channel,
                DmType = dmType,
                DmText = dmText,
                MediaUrl = mediaUrl,
                MediaType = mediaType,
                MediaName = mediaName
            };
            ValidateArgs(dmFbIgArgs, step);
            return dmFbIgArgs;
        }
        catch (Exception e)
        {
            logger.LogInformation(e, "evaluate failed: {Exception}", e.Message);
            throw new SfScriptingException(e);
        }
    }

    private void ValidateArgs(DmFbIgArgs dmFbIgArgs, Step step)
    {
        _logger.LogInformation(
            "begin to validate executor args for step {StepId}. dmFbIgArgs: {DmFbIgArgs}",
            step.Id,
            JsonConvert.SerializeObject(dmFbIgArgs));

        if (dmFbIgArgs == null
            || string.IsNullOrWhiteSpace(dmFbIgArgs.PageId)
            || string.IsNullOrWhiteSpace(dmFbIgArgs.CommentId)
            || string.IsNullOrWhiteSpace(dmFbIgArgs.Channel)
            || string.IsNullOrWhiteSpace(dmFbIgArgs.DmType))
        {
            throw new SfUserFriendlyException("step arguments are invalid");
        }

        if (DmTypeMedia.Equals(dmFbIgArgs.DmType))
        {
            if (string.IsNullOrWhiteSpace(dmFbIgArgs.MediaUrl))
            {
                _logger.LogError("DmType is {DmType}, but media url is empty.", dmFbIgArgs.DmType);
                throw new SfUserFriendlyException("media url is empty");
            }
        }
        else if (DmTypeMessage.Equals(dmFbIgArgs.DmType))
        {
            if (string.IsNullOrWhiteSpace(dmFbIgArgs.DmText))
            {
                _logger.LogError("DmType is {DmType}, but text is empty.", dmFbIgArgs.DmType);
                throw new SfUserFriendlyException("text is empty");
            }
        }
    }

    public class DmFbIgArgs
    {
        public string PageId { get; set; }
        public string CommentId { get; set; }
        public string Channel { get; set; }
        public string DmType { get; set; }
        public string DmText { get; set; }
        public string MediaUrl { get; set; }
        public string MediaType { get; set; }
        public string MediaName { get; set; }
    }
}