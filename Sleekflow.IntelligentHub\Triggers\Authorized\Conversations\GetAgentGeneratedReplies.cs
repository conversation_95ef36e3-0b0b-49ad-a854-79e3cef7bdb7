﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Snapshots;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.Conversations;

[TriggerGroup(
    ControllerNames.Conversations,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class GetAgentGeneratedReplies
    : ITrigger<GetAgentGeneratedReplies.GetAgentGeneratedRepliesInput,
        GetAgentGeneratedReplies.GetAgentGeneratedRepliesOutput>
{
    private readonly ISleekflowAuthorizationContext _authorizationContext;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;

    public GetAgentGeneratedReplies(
        ISleekflowAuthorizationContext authorizationContext,
        IIntelligentHubUsageService intelligentHubUsageService)
    {
        _authorizationContext = authorizationContext;
        _intelligentHubUsageService = intelligentHubUsageService;
    }

    public class GetAgentGeneratedRepliesInput
    {
        [Required]
        [JsonProperty("agent_id")]
        public string AgentId { get; set; }

        [Required]
        [Range(1, 100)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [Required]
        [Validations.ValidateArray]
        [JsonProperty("generated_types")]
        public List<string> GeneratedTypes { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonConstructor]
        public GetAgentGeneratedRepliesInput(
            string agentId,
            int limit,
            string continuationToken,
            List<string> generatedTypes)
        {
            Limit = limit;
            AgentId = agentId;
            ContinuationToken = continuationToken;
            GeneratedTypes = generatedTypes;
        }
    }

    public class GetAgentGeneratedRepliesOutput
    {
        [JsonProperty("generated_replies")]
        public List<GeneratedReplySnapshot> GeneratedReplies { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonConstructor]
        public GetAgentGeneratedRepliesOutput(
            List<GeneratedReplySnapshot> generatedReplies,
            string? nextContinuationToken)
        {
            GeneratedReplies = generatedReplies;
            NextContinuationToken = nextContinuationToken;
        }

        public class GeneratedReplySnapshot
        {
            [JsonProperty("agent_recommend_reply_snapshot")]
            public AgentRecommendReplySnapshotDto? AgentRecommendReplySnapshot { get; set; }

            [JsonProperty("playground_recommend_reply_snapshot")]
            public PlaygroundRecommendReplySnapshotDto? PlaygroundRecommendReplySnapshot { get; set; }

            [JsonConstructor]
            public GeneratedReplySnapshot(
                AgentRecommendReplySnapshotDto? agentRecommendReplySnapshot,
                PlaygroundRecommendReplySnapshotDto? playgroundRecommendReplySnapshot)
            {
                AgentRecommendReplySnapshot = agentRecommendReplySnapshot;
                PlaygroundRecommendReplySnapshot = playgroundRecommendReplySnapshot;
            }
        }
    }

    public async Task<GetAgentGeneratedRepliesOutput> F(GetAgentGeneratedRepliesInput input)
    {
        // Build predicate based on GeneratedTypes
        var (usages, nextContinuationToken) = await _intelligentHubUsageService.GetAgentRecordUsagesAsync(
            _authorizationContext.SleekflowCompanyId!,
            input.AgentId,
            input.GeneratedTypes,
            input.ContinuationToken,
            input.Limit);

        return new GetAgentGeneratedRepliesOutput(
            usages.Select(
                u => new GetAgentGeneratedRepliesOutput.GeneratedReplySnapshot(
                    u.IntelligentHubUsageSnapshot is AgentRecommendReplySnapshot agentSnapshot
                        ? new AgentRecommendReplySnapshotDto(agentSnapshot, u.CreatedAt)
                        : null,
                    u.IntelligentHubUsageSnapshot is PlaygroundRecommendReplySnapshot playgroundSnapshot
                        ? new PlaygroundRecommendReplySnapshotDto(playgroundSnapshot, u.CreatedAt)
                        : null)).ToList(),
            nextContinuationToken);
    }
}