using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.IntelligentHub.Workers.Activities;
using Sleekflow.Persistence;

namespace Sleekflow.IntelligentHub.Workers.Orchestrators;

public class UploadFileDocumentToAllAssignedAgentsOrchestratorV1
{
    private readonly ILogger<UploadFileDocumentToAllAssignedAgentsOrchestratorV1> _logger;

    public UploadFileDocumentToAllAssignedAgentsOrchestratorV1(
        ILogger<UploadFileDocumentToAllAssignedAgentsOrchestratorV1> logger)
    {
        _logger = logger;
    }

    public class UploadFileDocumentToAllAssignedAgentsOrchestratorOutput
    {
        [JsonConstructor]
        public UploadFileDocumentToAllAssignedAgentsOrchestratorOutput()
        {
        }
    }

    [Function(nameof(UploadFileDocumentToAllAssignedAgentsOrchestratorV1))]
    public async Task<UploadFileDocumentToAllAssignedAgentsOrchestratorOutput> Process(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var input = context.GetInput<StartUploadToAgentKnowledgeBasesEvent>();
        _logger.LogInformation(
            "UploadFileDocumentToAllAssignedAgentsOrchestrator document {DocumentId}",
            input!.DocumentId);

        var taskOptions = new TaskOptions(new TaskRetryOptions(new RetryPolicy(5, TimeSpan.FromSeconds(60), 1)));

        var getKbDocumentOutput = await context
            .CallActivityAsync<GetKbDocument.GetKbDocumentOutput>(
                nameof(GetKbDocument),
                new GetKbDocument.GetKbDocumentInput(
                    input.SleekflowCompanyId,
                    input.DocumentId),
                taskOptions);

        var kbDocumentJObject = JObject.FromObject(getKbDocumentOutput.KbDocument);
        var sysTypeName = (string) kbDocumentJObject[Entity.PropertyNameSysTypeName]!;
        KbDocument kbDocument = sysTypeName switch
        {
            SysTypeNames.FileDocument => kbDocumentJObject.ToObject<FileDocument>()!,
            SysTypeNames.WebsiteDocument => kbDocumentJObject.ToObject<WebsiteDocument>()!,
            SysTypeNames.WebpageDocument => kbDocumentJObject.ToObject<WebpageDocument>()!,
            _ => throw new Exception($"Unknown system type: {sysTypeName}")
        };

        var chunkIds = getKbDocumentOutput.ChunkIds;
        var agentAssignments = kbDocument.AgentAssignments ?? [];

        if (kbDocument.FileDocumentProcessStatus != ProcessFileDocumentStatuses.ReadyToAssign &&
            kbDocument.FileDocumentProcessStatus != ProcessFileDocumentStatuses.Completed)
        {
            _logger.LogInformation(
                "UploadFileDocumentToAllAssignedAgentsOrchestratorOutput fileDocument is not ready to assign.");
            return new UploadFileDocumentToAllAssignedAgentsOrchestratorOutput();
        }

        // Record upload started timestamp
        await context
            .CallActivityAsync<UpdateDebugTimestamps.UpdateDebugTimestampsOutput>(
                nameof(UpdateDebugTimestamps),
                new UpdateDebugTimestamps.UpdateDebugTimestampsInput(
                    input.SleekflowCompanyId,
                    input.DocumentId,
                    uploadStarted: DateTimeOffset.UtcNow),
                taskOptions);

        // Fan-out pattern: Upload to all agent assignments in parallel
        var uploadTasks = new List<Task>();

        foreach (var agentAssignment in agentAssignments)
        {
            switch (agentAssignment.RagStatus)
            {
                case RagStatus.Pending:
                    // Create a task for each agent assignment upload
                    var uploadTask = UploadToAgentAssignmentAsync(
                        context,
                        input,
                        agentAssignment,
                        chunkIds,
                        taskOptions);
                    uploadTasks.Add(uploadTask);
                    break;

                case RagStatus.InProgress:
                case RagStatus.Completed:
                case RagStatus.Failed:
                    _logger.LogInformation(
                        "{DocumentId} skipping upload to assigned agent {AgentId}, rag status: {RagStatus}",
                        input.DocumentId,
                        agentAssignment.AgentId,
                        agentAssignment.RagStatus);
                    break;
            }
        }

        // Wait for all parallel uploads to complete
        await Task.WhenAll(uploadTasks);

        // Record upload ended timestamp
        await context
            .CallActivityAsync<UpdateDebugTimestamps.UpdateDebugTimestampsOutput>(
                nameof(UpdateDebugTimestamps),
                new UpdateDebugTimestamps.UpdateDebugTimestampsInput(
                    input.SleekflowCompanyId,
                    input.DocumentId,
                    uploadEnded: DateTimeOffset.UtcNow),
                taskOptions);

        return new UploadFileDocumentToAllAssignedAgentsOrchestratorOutput();
    }

    private async Task UploadToAgentAssignmentAsync(
        TaskOrchestrationContext context,
        StartUploadToAgentKnowledgeBasesEvent input,
        AgentAssignment agentAssignment,
        List<string> chunkIds,
        TaskOptions taskOptions)
    {
        try
        {
            _logger.LogInformation(
                "Uploading file document {DocumentId} to assigned agent {AgentId}",
                input.DocumentId,
                agentAssignment.AgentId);

            await context
                .CallActivityAsync<PatchAgentAssignmentRagStatus.PatchAgentAssignmentRagStatusOutput>(
                    nameof(PatchAgentAssignmentRagStatus),
                    new PatchAgentAssignmentRagStatus.PatchAgentAssignmentRagStatusInput(
                        input.SleekflowCompanyId,
                        input.DocumentId,
                        agentAssignment.AgentId,
                        RagStatus.InProgress,
                        0.0),
                    taskOptions);

            if (agentAssignment.AgentId == CompanyAgentTypes.SmartReply)
            {
                // an exceptional case - Smart Reply is still using the old key-value search
                await context
                    .CallActivityAsync<
                        LoadFileDocumentChunksToKnowledgeBase.LoadFileDocumentChunksToKnowledgeBaseOutput>(
                        nameof(LoadFileDocumentChunksToKnowledgeBase),
                        new LoadFileDocumentChunksToKnowledgeBase.
                            LoadFileDocumentChunksToKnowledgeBaseInput(
                                input.SleekflowCompanyId,
                                input.DocumentId),
                        taskOptions);
            }
            else
            {
                // Fan-out pattern: Upload all chunks in parallel with progress tracking
                var chunkUploadTasks = new List<Task>();

                foreach (var chunkId in chunkIds)
                {
                    var chunkUploadTask = context
                        .CallActivityAsync<UploadFileDocumentChunkToAgentKnowledgeBase.
                            UploadFileDocumentChunkToAgentKnowledgeBaseOutput>(
                            nameof(UploadFileDocumentChunkToAgentKnowledgeBase),
                            new UploadFileDocumentChunkToAgentKnowledgeBase.
                                UploadFileDocumentChunkToAgentKnowledgeBaseInput(
                                    input.SleekflowCompanyId,
                                    input.DocumentId,
                                    chunkId,
                                    agentAssignment.AgentId),
                            taskOptions);

                    chunkUploadTasks.Add(chunkUploadTask);
                }

                // Track progress as individual chunk uploads complete
                var remainingTasks = new List<Task>(chunkUploadTasks);
                var completedCount = 0;

                while (remainingTasks.Count > 0)
                {
                    var completedTask = await Task.WhenAny(remainingTasks);
                    remainingTasks.Remove(completedTask);
                    completedCount++;

                    // Update progress after each chunk completes
                    await context
                        .CallActivityAsync<
                            PatchAgentAssignmentRagStatus.PatchAgentAssignmentRagStatusOutput>(
                            nameof(PatchAgentAssignmentRagStatus),
                            new PatchAgentAssignmentRagStatus.PatchAgentAssignmentRagStatusInput(
                                input.SleekflowCompanyId,
                                input.DocumentId,
                                agentAssignment.AgentId,
                                RagStatus.InProgress,
                                completedCount * 100.0 / chunkIds.Count),
                            taskOptions);
                }
            }

            await context
                .CallActivityAsync<PatchAgentAssignmentRagStatus.PatchAgentAssignmentRagStatusOutput>(
                    nameof(PatchAgentAssignmentRagStatus),
                    new PatchAgentAssignmentRagStatus.PatchAgentAssignmentRagStatusInput(
                        input.SleekflowCompanyId,
                        input.DocumentId,
                        agentAssignment.AgentId,
                        RagStatus.Completed,
                        100.0),
                    taskOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError("UploadFileDocumentToAgentKnowledgeBase failed: {Message}", ex.Message);

            await context
                .CallActivityAsync<PatchAgentAssignmentRagStatus.PatchAgentAssignmentRagStatusOutput>(
                    nameof(PatchAgentAssignmentRagStatus),
                    new PatchAgentAssignmentRagStatus.PatchAgentAssignmentRagStatusInput(
                        input.SleekflowCompanyId,
                        input.DocumentId,
                        agentAssignment.AgentId,
                        RagStatus.Failed,
                        0.0),
                    taskOptions);
        }
    }
}