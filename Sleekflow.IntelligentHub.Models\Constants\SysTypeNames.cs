﻿namespace Sleekflow.IntelligentHub.Models.Constants;

public static class SysTypeNames
{
    public const string FileDocument = "FileDocument";
    public const string FileDocumentChunk = "FileDocumentChunk";
    public const string WebsiteDocument = "WebsiteDocument";
    public const string WebsiteDocumentChunk = "WebsiteDocumentChunk";
    public const string WebpageDocument = "WebpageDocument";
    public const string WebpageDocumentChunk = "WebpageDocumentChunk";
    public const string KnowledgeBaseEntry = "KnowledgeBaseEntry";
    public const string WebScraper = "WebScraper";
    public const string WebScraperRun = "WebScraperRun";
    public const string WebCrawlingSession = "WebCrawlingSession";
    public const string IntelligentHubConfig = "IntelligentHubConfig";
    public const string IntelligentHubUsage = "IntelligentHubUsage";
    public const string BlobUploadHistory = "BlobUploadHistory";
    public const string CompanyConfig = "CompanyConfig";
    public const string CompanyAgentConfig = "CompanyAgentConfig";
    public const string CompanyAgentConfigSnapshot = "CompanyAgentConfigSnapshot";
    public const string Playground = "Playground";
    public const string PlaygroundRecommendedReply = "PlaygroundRecommendedReply";
    public const string KnowledgeRetrievalCache = "KnowledgeRetrievalCache";
    public const string TopicAnalyticsTopic = "TopicAnalyticsTopic";
    public const string FileContentCache = "FileContentCache";
}