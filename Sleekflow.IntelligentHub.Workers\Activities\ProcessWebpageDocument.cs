using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Workers.Services;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Workers.Activities;

public class ProcessWebpageDocument
{
    private readonly IWebpageIngestionService _webpageIngestionService;

    public ProcessWebpageDocument(IWebpageIngestionService webpageIngestionService)
    {
        _webpageIngestionService = webpageIngestionService;
    }

    public class ProcessWebpageDocumentInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("document_id")]
        public string DocumentId { get; set; }

        [JsonConstructor]
        public ProcessWebpageDocumentInput(
            string sleekflowCompanyId,
            string documentId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DocumentId = documentId;
        }
    }

    public class ProcessWebpageDocumentOutput
    {
        [JsonConstructor]
        public ProcessWebpageDocumentOutput()
        {
        }
    }

    [Function(nameof(ProcessWebpageDocument))]
    public async Task<ProcessWebpageDocumentOutput> Process(
        [ActivityTrigger]
        ProcessWebpageDocumentInput processWebpageDocumentInput)
    {
        await _webpageIngestionService.ProcessWebpageDocument(
            processWebpageDocumentInput.SleekflowCompanyId,
            processWebpageDocumentInput.DocumentId);

        return new ProcessWebpageDocumentOutput();
    }
}