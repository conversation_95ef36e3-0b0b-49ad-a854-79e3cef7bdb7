openapi: 3.0.1
info:
  title: Sleekflow 1.0
  version: '1.0'
servers:
  - url: https://localhost:7070
    description: Local
  - url: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/share-hub
    description: Dev Apigw
  - url: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/share-hub
    description: Prod Apigw
paths:
  /Analytics/GetNumberOfClicks:
    post:
      tags:
        - Analytics
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetNumberOfClicksInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetNumberOfClicksOutputOutput'
  /CustomDomains/GetAfdCustomDomainValidationRecords:
    post:
      tags:
        - CustomDomains
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetAfdCustomDomainValidationRecordsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAfdCustomDomainValidationRecordsOutputOutput'
  /CustomDomains/GetCustomDomain:
    post:
      tags:
        - CustomDomains
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCustomDomainInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCustomDomainOutputOutput'
  /CustomDomains/GetCustomDomains:
    post:
      tags:
        - CustomDomains
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCustomDomainsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCustomDomainsOutputOutput'
  /CustomDomains/RefreshAfdCustomDomainValidationRecords:
    post:
      tags:
        - CustomDomains
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshAfdCustomDomainValidationRecordsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefreshAfdCustomDomainValidationRecordsOutputOutput'
  /CustomDomains/RegisterAfdCustomDomain:
    post:
      tags:
        - CustomDomains
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterAfdCustomDomainInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterAfdCustomDomainOutputOutput'
  /Links/Click:
    post:
      tags:
        - Links
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClickInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClickOutputOutput'
  /Links/GetLinkClicks:
    post:
      tags:
        - Links
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetLinkClicksInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLinkClicksOutputOutput'
  /Links/GetLinkQrCode:
    post:
      tags:
        - Links
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetLinkQrCodeInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLinkQrCodeOutputOutput'
  /Links/GetLinks:
    post:
      tags:
        - Links
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetLinksInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLinksOutputOutput'
  /Links/Shorten:
    post:
      tags:
        - Links
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ShortenInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ShortenOutputOutput'
  /QrCodes/GetUrlQrCode:
    post:
      tags:
        - QrCodes
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetUrlQrCodeInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetUrlQrCodeOutputOutput'
components:
  schemas:
    ClickInput:
      required:
        - link_click_analytics
        - short_url
      type: object
      properties:
        short_url:
          maxLength: 4096
          minLength: 0
          type: string
        link_click_analytics:
          $ref: '#/components/schemas/LinkClickAnalytics'
      additionalProperties: false
    ClickOutput:
      type: object
      properties:
        long_url:
          type: string
          nullable: true
      additionalProperties: false
    ClickOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ClickOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DnsRecord:
      type: object
      properties:
        host_name:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        data:
          type: string
          nullable: true
        expired_at:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    Domain:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        domain_name:
          type: string
          nullable: true
        domain_external_config:
          $ref: '#/components/schemas/DomainExternalConfig'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    DomainDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        domain_name:
          type: string
          nullable: true
        domain_external_config:
          $ref: '#/components/schemas/DomainExternalConfig'
      additionalProperties: false
    DomainExternalConfig:
      required:
        - provider_id
        - provider_name
      type: object
      properties:
        provider_name:
          minLength: 1
          type: string
        provider_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetAfdCustomDomainValidationRecordsInput:
      required:
        - id
        - sleekflow_company_id
      type: object
      properties:
        id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetAfdCustomDomainValidationRecordsOutput:
      type: object
      properties:
        domain:
          $ref: '#/components/schemas/Domain'
        dns_records:
          type: array
          items:
            $ref: '#/components/schemas/DnsRecord'
          nullable: true
      additionalProperties: false
    GetAfdCustomDomainValidationRecordsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetAfdCustomDomainValidationRecordsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetCustomDomainInput:
      required:
        - id
        - sleekflow_company_id
      type: object
      properties:
        id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetCustomDomainOutput:
      type: object
      properties:
        domain:
          $ref: '#/components/schemas/DomainDto'
      additionalProperties: false
    GetCustomDomainOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetCustomDomainOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetCustomDomainsInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetCustomDomainsOutput:
      type: object
      properties:
        domains:
          type: array
          items:
            $ref: '#/components/schemas/DomainDto'
          nullable: true
      additionalProperties: false
    GetCustomDomainsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetCustomDomainsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetLinkClicksInput:
      required:
        - link_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 36
          minLength: 36
          type: string
        link_id:
          minLength: 1
          type: string
        continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetLinkClicksOutput:
      type: object
      properties:
        continuation_token:
          type: string
          nullable: true
        records:
          type: array
          items:
            $ref: '#/components/schemas/LinkClick'
          nullable: true
        count:
          type: integer
          format: int64
      additionalProperties: false
    GetLinkClicksOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetLinkClicksOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetLinkQrCodeInput:
      required:
        - link_id
        - qr_code_config
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 36
          minLength: 36
          type: string
        link_id:
          minLength: 1
          type: string
        qr_code_config:
          $ref: '#/components/schemas/QrCodeConfig'
      additionalProperties: false
    GetLinkQrCodeOutput:
      type: object
      properties:
        url:
          type: string
          nullable: true
      additionalProperties: false
    GetLinkQrCodeOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetLinkQrCodeOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetLinksInput:
      required:
        - domain_id
        - long_url
        - sleekflow_company_id
        - title
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 36
          minLength: 36
          type: string
        domain_id:
          maxLength: 256
          minLength: 0
          type: string
        long_url:
          maxLength: 4096
          minLength: 0
          type: string
        title:
          maxLength: 512
          minLength: 0
          type: string
      additionalProperties: false
    GetLinksOutput:
      type: object
      properties:
        links:
          type: array
          items:
            $ref: '#/components/schemas/Link'
          nullable: true
      additionalProperties: false
    GetLinksOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetLinksOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetNumberOfClicksInput:
      required:
        - date
        - link_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 36
          minLength: 36
          type: string
        link_id:
          minLength: 1
          type: string
        date:
          type: string
          format: date
      additionalProperties: false
    GetNumberOfClicksOutput:
      type: object
      properties:
        count:
          type: integer
          format: int32
      additionalProperties: false
    GetNumberOfClicksOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetNumberOfClicksOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetUrlQrCodeInput:
      required:
        - qr_code_config
        - url
      type: object
      properties:
        icon_url:
          type: string
          nullable: true
        url:
          minLength: 1
          type: string
        qr_code_config:
          $ref: '#/components/schemas/QrCodeConfig'
      additionalProperties: false
    GetUrlQrCodeOutput:
      type: object
      properties:
        url:
          type: string
          nullable: true
      additionalProperties: false
    GetUrlQrCodeOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetUrlQrCodeOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Link:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        domain_name:
          type: string
          nullable: true
        domain_id:
          type: string
          nullable: true
        long_url:
          type: string
          nullable: true
        short_url:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        qr_code:
          $ref: '#/components/schemas/LinkQrCode'
        campaign:
          $ref: '#/components/schemas/LinkCampaign'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    LinkCampaign:
      type: object
      properties:
        campaign_id:
          type: string
          nullable: true
        sleekflow_user_id:
          type: string
          nullable: true
        sleekflow_campaign_id:
          type: string
          nullable: true
      additionalProperties: false
    LinkClick:
      type: object
      properties:
        link_id:
          type: string
          nullable: true
        domain_name:
          type: string
          nullable: true
        access_type:
          type: string
          nullable: true
        link_click_analytics:
          $ref: '#/components/schemas/LinkClickAnalytics'
        link_campaign:
          $ref: '#/components/schemas/LinkCampaign'
        sleekflow_company_id:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    LinkClickAnalytics:
      type: object
      properties:
        referrer:
          type: string
          nullable: true
        ip_country:
          type: string
          nullable: true
        ip_address:
          type: string
          nullable: true
        ip_type:
          type: string
          nullable: true
        fingerprintjs_id:
          type: string
          nullable: true
        access_type:
          type: string
          nullable: true
        query_params_str:
          type: string
          nullable: true
      additionalProperties: false
    LinkQrCode:
      type: object
      properties:
        blobs:
          type: array
          items:
            $ref: '#/components/schemas/LinkQrCodeBlob'
          nullable: true
      additionalProperties: false
    LinkQrCodeBlob:
      type: object
      properties:
        container_name:
          type: string
          nullable: true
        blob_name:
          type: string
          nullable: true
        blob_id:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        size:
          type: integer
          format: int32
        is_branded:
          type: boolean
      additionalProperties: false
    QrCodeConfig:
      type: object
      properties:
        size:
          type: integer
          format: int32
      additionalProperties: false
    RefreshAfdCustomDomainValidationRecordsInput:
      required:
        - id
        - sleekflow_company_id
      type: object
      properties:
        id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    RefreshAfdCustomDomainValidationRecordsOutput:
      type: object
      properties:
        domain:
          $ref: '#/components/schemas/Domain'
        dns_records:
          type: array
          items:
            $ref: '#/components/schemas/DnsRecord'
          nullable: true
      additionalProperties: false
    RefreshAfdCustomDomainValidationRecordsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RefreshAfdCustomDomainValidationRecordsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    RegisterAfdCustomDomainInput:
      required:
        - custom_domain_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        custom_domain_name:
          minLength: 1
          type: string
      additionalProperties: false
    RegisterAfdCustomDomainOutput:
      type: object
      properties:
        domain:
          $ref: '#/components/schemas/Domain'
        dns_records:
          type: array
          items:
            $ref: '#/components/schemas/DnsRecord'
          nullable: true
      additionalProperties: false
    RegisterAfdCustomDomainOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RegisterAfdCustomDomainOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ShortenInput:
      required:
        - long_url
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 36
          minLength: 36
          type: string
        domain_id:
          maxLength: 256
          minLength: 0
          type: string
          nullable: true
        long_url:
          maxLength: 4096
          minLength: 0
          type: string
        title:
          maxLength: 512
          minLength: 0
          type: string
          nullable: true
      additionalProperties: false
    ShortenOutput:
      type: object
      properties:
        link:
          $ref: '#/components/schemas/Link'
      additionalProperties: false
    ShortenOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ShortenOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false