﻿using System.ComponentModel.DataAnnotations;
using System.Net.Mime;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Https.Services;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Executions;

[TriggerGroup(ControllerNames.Executions)]
public class RedirectExecution : ITrigger
{
    private readonly IWebhookBridgeRedirectService _webhookBridgeRedirectService;

    public RedirectExecution(
        IWebhookBridgeRedirectService webhookBridgeRedirectService)
    {
        _webhookBridgeRedirectService = webhookBridgeRedirectService;
    }

    public class RedirectExecutionInput
    {
        [JsonProperty("url")]
        [Required]
        [ValidateObject]
        public string Url { get; set; }

        [JsonProperty("method")]
        [Required]
        [ValidateObject]
        public string Method { get; set; }

        [JsonProperty("headers")]
        [ValidateObject]
        public Dictionary<string, object?>? Headers { get; set; }

        [JsonProperty("body_str")]
        [ValidateObject]
        public string? BodyStr { get; set; }

        [JsonProperty("body_dict")]
        [ValidateObject]
        public Dictionary<string, object?>? BodyDict { get; set; }

        [JsonConstructor]
        public RedirectExecutionInput(
            string url,
            string method,
            Dictionary<string, object?>? headers,
            string? bodyStr,
            Dictionary<string, object?>? bodyDict)
        {
            Url = url;
            Method = method;
            Headers = headers;
            BodyStr = bodyStr;
            BodyDict = bodyDict;
        }
    }

    public class RedirectExecutionOutput
    {
        [JsonProperty("is_success")]
        [Required]
        [ValidateObject]
        public bool IsSuccess { get; set; }

        [JsonProperty("response")]
        [Required]
        [ValidateObject]
        public string Response { get; set; }

        [JsonConstructor]
        public RedirectExecutionOutput(bool isSuccess, string response)
        {
            IsSuccess = isSuccess;
            Response = response;
        }
    }

    public async Task<RedirectExecutionOutput> F(RedirectExecutionInput redirectExecutionInput)
    {
        var url = redirectExecutionInput.Url;
        var method = redirectExecutionInput.Method;

        var headerDict = redirectExecutionInput.Headers;

        var bodyDict = redirectExecutionInput.BodyDict;

        var bodyStr = redirectExecutionInput.BodyStr;

        var content = bodyStr ?? JsonConvert.SerializeObject(
            bodyDict ?? new object());


        var (isSuccess, response) = await _webhookBridgeRedirectService.RedirectAsync(
            url,
            method,
            headerDict,
            content,
            MediaTypeNames.Application.Json);

        return new RedirectExecutionOutput(isSuccess, response);
    }
}