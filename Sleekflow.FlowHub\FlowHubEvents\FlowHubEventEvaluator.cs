using System.Text.RegularExpressions;
using Scriban;
using Scriban.Functions;
using Scriban.Runtime;
using Scriban.Syntax;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Evaluations;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.ScribanExtensions;
using Sleekflow.FlowHub.States.Functions;

namespace Sleekflow.FlowHub.FlowHubEvents;

public interface IFlowHubEventEvaluator
{
    Task<bool> EvaluateConditionAsync(string condition, EventBody eventBody, EvaluationContext evaluationContext);

    Task<bool> EvaluateConditionAsync(
        string condition,
        EventBody eventBody,
        string sleekflowCompanyId,
        string? origin,
        EvaluationAdditionalData? additionalContext = null);
}

public partial class FlowHubEventEvaluator : IFlowHubEventEvaluator, ISingletonService
{
    public async Task<bool> EvaluateConditionAsync(
        string condition,
        EventBody eventBody,
        EvaluationContext evaluationContext)
    {
        try
        {
            var myRegex = MyRegex();
            var match = myRegex.Match(condition);

            // Simple String
            if (!match.Success)
            {
                return condition == "true";
            }

            condition = match.Groups[1].Value;

            var eventName = eventBody.EventName;

            var scriptObject = new ScriptObject
            {
                {
                    "ctx", evaluationContext
                },
                {
                    "event_body", eventBody
                },
                {
                    "event_name", eventName
                },
                {
                    "json", new JsonFunctions()
                },
                {
                    "array", new ExtendedArrayFunctions()
                },
                {
                    "date", new ExtendedDateTimeFunctions()
                },
                {
                    "sleekflow", new SleekflowFunctions()
                }
            };
            scriptObject.IsReadOnly = true;

            var templateContext = new AsyncTemplateContext();
            templateContext.PushGlobal(scriptObject);
            ((DateTimeFunctions) templateContext.BuiltinObject["date"]).Format = "%FT%T%Z";

            var evaluatedExpression = await Template.EvaluateAsync(
                condition.Trim(),
                templateContext);

            return evaluatedExpression is true;
        }
        catch (InvalidOperationException ex)
        {
            // TODO Log
            // throw new Exception($"Unable to evaluate expression: {expression}", ex);
            if (ex.Message.Contains("This template has errors."))
            {
                return false;
            }

            throw;
        }
        catch (ScriptRuntimeException ex)
        {
            throw new SfScriptingException(ex);
        }
    }

    public Task<bool> EvaluateConditionAsync(
        string condition,
        EventBody eventBody,
        string sleekflowCompanyId,
        string? origin,
        EvaluationAdditionalData? additionalContext = null)
    {
        return EvaluateConditionAsync(
            condition,
            eventBody,
            new EvaluationContext(
                sleekflowCompanyId,
                null,
                null,
                null,
                null,
                origin,
                additionalContext));
    }

    [GeneratedRegex("^{{(.*)}}$")]
    private static partial Regex MyRegex();
}