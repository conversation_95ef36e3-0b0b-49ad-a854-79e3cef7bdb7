using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Insights = Pulumi.AzureNative.Insights;
using ServiceBus = Pulumi.AzureNative.ServiceBus.V20220101Preview;

namespace Sleekflow.Infras.Components;

public class MyServiceBus
{
    private readonly ResourceGroup _resourceGroup;
    private readonly MyConfig _myConfig;

    public MyServiceBus(
        ResourceGroup resourceGroup,
        MyConfig myConfig)
    {
        _resourceGroup = resourceGroup;
        _myConfig = myConfig;
    }

    public class ServiceBusOutput
    {
        public Output<string> ResourceId { get; }

        public Output<string> CrmHubKedaPolicyKeyPrimaryConnStr { get; }

        public Output<string> CrmHubPolicyKeyPrimaryConnStr { get; }

        public ServiceBusOutput(
            Output<string> resourceId,
            Output<string> crmHubKedaPolicyKeyPrimaryConnStr,
            Output<string> crmHubPolicyKeyPrimaryConnStr)
        {
            ResourceId = resourceId;
            CrmHubKedaPolicyKeyPrimaryConnStr = crmHubKedaPolicyKeyPrimaryConnStr;
            CrmHubPolicyKeyPrimaryConnStr = crmHubPolicyKeyPrimaryConnStr;
        }
    }

    public ServiceBusOutput InitServiceBus(
        string? name = null,
        string? locationName = null,
        string? serviceBusConfigType = null)
    {
        // Determine the namespace name based on the ServiceBusConfigType
        var namespaceResourceName = serviceBusConfigType switch
        {
            ServiceBusConfigTypes.HighTrafficServiceBus => ConstructResourceName(
                "sleekflow-high-traffic-service-bus",
                name),
            _ => ConstructResourceName("sleekflow-service-bus", name)
        };

        // Determine the suffix for applying the serviceBusConfigType in authorization rule resource names
        // For DefaultServiceBus, retain the existing names without adding a suffix
        var crmHubKedaNamespaceAuthorizationRuleResourceName = serviceBusConfigType switch
        {
            ServiceBusConfigTypes.HighTrafficServiceBus => "sleekflow-crm-hub-high-traffic-keda-policy",
            _ => "sleekflow-crm-hub-keda-policy"
        };

        var sleekflowCrmHubNamespaceAuthorizationRuleResourceName = serviceBusConfigType switch
        {
            ServiceBusConfigTypes.HighTrafficServiceBus => "sleekflow-crm-hub-high-traffic-policy",
            _ => "sleekflow-crm-hub-policy"
        };

        var @namespace = new ServiceBus.Namespace(
            namespaceResourceName,
            new ServiceBus.NamespaceArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Location =
                    locationName is not null
                        ? LocationNames.GetAzureLocation(locationName)
                        : _resourceGroup.Location,
                Sku = _myConfig.Name == "production"
                    ? new ServiceBus.Inputs.SBSkuArgs
                    {
                        Name = ServiceBus.SkuName.Premium, Tier = ServiceBus.SkuTier.Premium, Capacity = 1,
                    }
                    : new ServiceBus.Inputs.SBSkuArgs
                    {
                        Name = ServiceBus.SkuName.Standard, Tier = ServiceBus.SkuTier.Standard,
                    },
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        var crmHubKedaNamespaceAuthorizationRule = new ServiceBus.NamespaceAuthorizationRule(
            ConstructResourceName(crmHubKedaNamespaceAuthorizationRuleResourceName, name),
            new ServiceBus.NamespaceAuthorizationRuleArgs
            {
                AuthorizationRuleName = "CrmHubKedaPolicy",
                NamespaceName = @namespace.Name,
                ResourceGroupName = _resourceGroup.Name,
                Rights =
                {
                    ServiceBus.AccessRights.Manage, ServiceBus.AccessRights.Listen, ServiceBus.AccessRights.Send,
                },
            },
            new CustomResourceOptions
            {
                Parent = @namespace,
            });
        var crmHubNamespaceAuthorizationRule = new ServiceBus.NamespaceAuthorizationRule(
            ConstructResourceName(sleekflowCrmHubNamespaceAuthorizationRuleResourceName, name),
            new ServiceBus.NamespaceAuthorizationRuleArgs
            {
                AuthorizationRuleName = "CrmHubPolicy",
                NamespaceName = @namespace.Name,
                ResourceGroupName = _resourceGroup.Name,
                Rights =
                {
                    ServiceBus.AccessRights.Manage, ServiceBus.AccessRights.Listen, ServiceBus.AccessRights.Send,
                },
            },
            new CustomResourceOptions
            {
                Parent = @namespace,
            });

        var crmHubKedaPolicyKeyPrimaryConnStr = ServiceBus.ListNamespaceKeys
            .Invoke(
                new ServiceBus.ListNamespaceKeysInvokeArgs
                {
                    AuthorizationRuleName = crmHubKedaNamespaceAuthorizationRule.Name,
                    NamespaceName = @namespace.Name,
                    ResourceGroupName = _resourceGroup.Name
                })
            .Apply(nk => nk.PrimaryConnectionString);
        var crmHubPolicyKeyPrimaryConnStr = ServiceBus.ListNamespaceKeys
            .Invoke(
                new ServiceBus.ListNamespaceKeysInvokeArgs
                {
                    AuthorizationRuleName = crmHubNamespaceAuthorizationRule.Name,
                    NamespaceName = @namespace.Name,
                    ResourceGroupName = _resourceGroup.Name
                })
            .Apply(nk => nk.PrimaryConnectionString);

        ServiceBusAutoScalingRule(@namespace.Id, name, serviceBusConfigType, locationName ?? LocationNames.EastAsia);

        return new ServiceBusOutput(
            @namespace.Id,
            crmHubKedaPolicyKeyPrimaryConnStr,
            crmHubPolicyKeyPrimaryConnStr);
    }

    private void ServiceBusAutoScalingRule(
        Output<string>? namespaceId,
        string? name = null,
        string? serviceBusConfigType = null,
        string? locationName = null)
    {
        // Only proceed for production environment
        if (_myConfig.Name != "production")
        {
            return;
        }

        var resourceNamePrefix = serviceBusConfigType switch
        {
            ServiceBusConfigTypes.HighTrafficServiceBus => "sleekflow-high-traffic-service-bus-autoscale-setting",
            _ => "sleekflow-service-bus-autoscale-setting"
        };

        var autoscaleSettingResourceName = ConstructResourceName(resourceNamePrefix, name);

        // Determine capacity settings based on config type
        var minCapacity = "1";
        var maxCapacity = "4";
        var defaultCapacity = "1";

        if (serviceBusConfigType == ServiceBusConfigTypes.HighTrafficServiceBus)
        {
            minCapacity = "2";
            defaultCapacity = "2";
        }

        // Adjust capacity settings for East Asia default service bus to 2 MU
        if (locationName == LocationNames.EastAsia
            && serviceBusConfigType == ServiceBusConfigTypes.DefaultServiceBus)
        {
            minCapacity = "2";
            defaultCapacity = "2";
        }

        // Create the autoscale setting
        var _ = new Insights.AutoscaleSetting(
            autoscaleSettingResourceName,
            new Insights.AutoscaleSettingArgs
            {
                TargetResourceUri = namespaceId,
                Enabled = true,
                Location = _resourceGroup.Location,
                ResourceGroupName = _resourceGroup.Name,
                Profiles = new List<Insights.Inputs.AutoscaleProfileArgs>
                {
                    new ()
                    {
                        Name = "Auto created default scale condition",
                        Capacity = new Insights.Inputs.ScaleCapacityArgs
                        {
                            Minimum = minCapacity, Maximum = maxCapacity, Default = defaultCapacity
                        },
                        Rules = CreateScalingRules(namespaceId)
                    }
                }
            });
    }

    private List<Insights.Inputs.ScaleRuleArgs> CreateScalingRules(
        Output<string>? namespaceId)
    {
        var scaleUpRule = new Insights.Inputs.ScaleRuleArgs()
        {
            ScaleAction = new Insights.Inputs.ScaleActionArgs
            {
                Direction = Insights.ScaleDirection.Increase,
                Type = Insights.ScaleType.ServiceAllowedNextValue,
                Value = "1",
                Cooldown = "PT5M"
            },
            MetricTrigger = new Insights.Inputs.MetricTriggerArgs
            {
                MetricName = "NamespaceCpuUsage",
                MetricNamespace = "microsoft.servicebus/namespaces",
                MetricResourceUri = namespaceId,
                Operator = Insights.ComparisonOperationType.GreaterThan,
                Statistic = Insights.MetricStatisticType.Max,
                Threshold = 70,
                TimeAggregation = Insights.TimeAggregationType.Maximum,
                TimeGrain = "PT1M",
                TimeWindow = "PT3M",
                DividePerInstance = false
            }
        };

        var scaleDownRule = new Insights.Inputs.ScaleRuleArgs()
        {
            ScaleAction = new Insights.Inputs.ScaleActionArgs
            {
                Direction = Insights.ScaleDirection.Decrease,
                Type = Insights.ScaleType.ServiceAllowedNextValue,
                Value = "1",
                Cooldown = "PT10M"
            },
            MetricTrigger = new Insights.Inputs.MetricTriggerArgs
            {
                MetricName = "NamespaceCpuUsage",
                MetricNamespace = "microsoft.servicebus/namespaces",
                MetricResourceUri = namespaceId,
                Operator = Insights.ComparisonOperationType.LessThan,
                Statistic = Insights.MetricStatisticType.Max,
                Threshold = 30,
                TimeAggregation = Insights.TimeAggregationType.Maximum,
                TimeGrain = "PT1M",
                TimeWindow = "PT3M",
                DividePerInstance = false
            }
        };

        return [scaleUpRule, scaleDownRule];
    }

    private static string ConstructResourceName(string resourceName, string? name)
    {
        return name is not null ? $"{resourceName}-{name}" : resourceName;
    }
}