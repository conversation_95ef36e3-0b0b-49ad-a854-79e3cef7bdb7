using Microsoft.Azure.NotificationHubs;
using Microsoft.Azure.NotificationHubs.Messaging;
using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.UserEventHub;
using Sleekflow.UserEventHub.Configs;
using Sleekflow.UserEventHub.Models.Notifications;

namespace Sleekflow.UserEventHub.Notifications;

/// <summary>
/// Mobile Push Notifications Service
/// </summary>
public interface INotificationHubService
{
    Task<HubResponse<NotificationOutcome>?> SendNotificationAsync(Models.Notifications.Notification newNotification);

    /// <summary>
    /// Register device using installation mode, which is the recommended approach for registering devices.
    /// Installation mode provides a more flexible and reliable way to manage device registrations.
    /// </summary>
    /// <param name="installationId">A unique identifier for the device installation (typically device ID)</param>
    /// <param name="deviceRegistration">Device registration information including handle (token) and tags</param>
    /// <returns>HubResponse indicating success or failure</returns>
    Task<HubResponse> RegisterDeviceInstallationAsync(string installationId, DeviceRegistration deviceRegistration);

    /// <summary>
    /// Delete device installation from Azure Notification Hub.
    /// </summary>
    /// <param name="installationId">A unique identifier for the device installation (typically device ID)</param>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    Task DeleteInstallationAsync(string installationId);

    Task RemoveAllRegistrationsByTagAsync(string companyId, string userId);

    Task<NotificationDetails> GetNotificationDeliveryStatus(string notificationId, string? hubName);

    Task<Installation?> GetInstallationAsync(string installationId);

    Task<List<string>> GetAllInstallationIdsAsync();

    Task<RegistrationDescription> GetRegistrationByRegistrationIdAsync(string registrationId);
}

public class NotificationHubService : INotificationHubService, ISingletonService
{
    private readonly NotificationHubClient _defaultNotificationHubClient;
    private readonly NotificationHubClient? _smartoneNotificationHubClient;

    private readonly INotificationSettingsRepository _notificationSettingsRepository;

    private readonly ICacheService _cacheService;

    private readonly ILogger<NotificationHubService> _logger;

    public NotificationHubService(
        ILogger<NotificationHubService> logger,
        INotificationHubConfig config,
        INotificationSettingsRepository notificationSettingsRepository,
        ICacheService cacheService)
    {
        _logger = logger;
        _notificationSettingsRepository = notificationSettingsRepository;
        _cacheService = cacheService;

        // Initialize default notification hub client
        _defaultNotificationHubClient = NotificationHubClient.CreateClientFromConnectionString(
            config.NotificationHubConnectionString,
            config.NotificationHubName);

        // Initialize Smartone notification hub client
        if (!string.IsNullOrEmpty(config.NotificationHubSmartoneConnectionString) && !string.IsNullOrEmpty(config.NotificationHubSmartoneName))
        {
            _smartoneNotificationHubClient = NotificationHubClient.CreateClientFromConnectionString(
                config.NotificationHubSmartoneConnectionString,
                config.NotificationHubSmartoneName);
        }
    }

    /// <summary>
    /// Determines which notification hub client to use based on company ID
    /// </summary>
    /// <param name="hubName">The hub name to check</param>
    /// <returns>The appropriate notification hub client</returns>
    private NotificationHubClient GetNotificationHubClient(string? hubName)
    {
        if (!string.IsNullOrEmpty(hubName) && "sleekflowsmartoneproduction".Equals(hubName.ToLower()))
        {
            _logger.LogInformation("Using Smartone notification hub for company: {CompanyId}", hubName);
            return _smartoneNotificationHubClient ?? throw new SfSendNotificationFailException("Smartone notification hub client not initialized");
        }

        _logger.LogDebug("Using default notification hub for company: {HubName}", hubName ?? "null");
        return _defaultNotificationHubClient;
    }

    /// <summary>
    /// Register device using installation mode, which provides better reliability and flexibility.
    /// </summary>
    /// <param name="installationId">Unique identifier for the device installation.</param>
    /// <param name="deviceRegistration">Device registration information including handle (token) and tags</param>
    /// <returns>HubResponse indicating success or failure.</returns>
    public async Task<HubResponse> RegisterDeviceInstallationAsync(
        string installationId,
        DeviceRegistration deviceRegistration)
    {
        try
        {
            var installation = new Installation
            {
                InstallationId = installationId,
                Platform =
                    deviceRegistration.Platform == "apns" ? NotificationPlatform.Apns : NotificationPlatform.FcmV1,
                PushChannel = deviceRegistration.Handle,
                Tags = deviceRegistration.Tags?.ToArray() ?? Array.Empty<string>(),
            };

            var notificationHubClient = GetNotificationHubClient(deviceRegistration.HubName);
            await notificationHubClient.CreateOrUpdateInstallationAsync(installation);

            _logger.LogInformation(
                "Successfully registered device installation. InstallationId: {InstallationId}, Platform: {Platform}, Tags: {Tags}, Hub: {Hub}",
                installationId,
                installation.Platform,
                string.Join(",", installation.Tags),
                deviceRegistration.HubName);

            return new HubResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to register device installation. InstallationId: {InstallationId}",
                installationId);

            return new HubResponse().AddErrorMessage(
                "Failed to register device installation. Please try again.");
        }
    }

    ///
    /// <summary>
    /// Send push notification to specific platform (Android, iOS or Windows).
    /// </summary>
    /// <param name="newNotification">The notification payload.</param>
    /// <returns>HubResponse.</returns>
    public async Task<HubResponse<NotificationOutcome>?> SendNotificationAsync(
        Models.Notifications.Notification newNotification)
    {
        if (newNotification.UserId == null || newNotification.CompanyId == null)
        {
            throw new ArgumentException("UserId and CompanyId are required.");
        }

        var mobileNotificationSettings = await _cacheService.CacheAsync(
            $"{nameof(NotificationHubService)}:{nameof(SendNotificationAsync)}:{newNotification.CompanyId}:{newNotification.UserId}",
            async () =>
            {
                var settings = await GetMobileNotificationSettings(
                    newNotification.UserId,
                    newNotification.CompanyId);
                if (settings != null)
                {
                    return settings;
                }

                _logger.LogInformation("Mobile notification settings not found. Creating default settings.");
                var defaultSettings = await _notificationSettingsRepository.CreateDefaultNotificationSettingsAsync(
                    newNotification.UserId,
                    newNotification.CompanyId);
                return defaultSettings.Mobile;
            },
            TimeSpan.FromHours(1));
        if (mobileNotificationSettings == null)
        {
            _logger.LogInformation("Mobile notification settings not found.");
            return null;
        }

        var isNotificationEnabled = mobileNotificationSettings.IsNotificationEnabled;
        var isNotificationEventEnabled =
            mobileNotificationSettings.EnabledNotificationEvents.Contains(newNotification.NotificationEvent);
        if (isNotificationEnabled == false || isNotificationEventEnabled == false)
        {
            return null;
        }

        var isBadgeEnabled = mobileNotificationSettings.Badge;
        var isBannerEnabled = mobileNotificationSettings.Banner;
        var badgeString = (newNotification.Badge ?? 0).ToString();

        // Notification hub only supports 4096 byte whole notification size
        // if newNotification.Body > 1024 byte, it will be truncated to 1024 byte
        var notificationBody = newNotification.Body?.Substring(0, Math.Min(newNotification.Body.Length, 1024)) ??
                               string.Empty;

        var notificationPlatform = mobileNotificationSettings.Platform;

        NotificationOutcome? outcome = null;

        if (notificationPlatform == "apns")
        {
            outcome = await SendApnsNotificationAsync(
                isBannerEnabled,
                isBadgeEnabled,
                newNotification,
                mobileNotificationSettings,
                notificationBody);
        }
        else
        {
            outcome = await SendFcmV1NotificationAsync(
                isBannerEnabled,
                isBadgeEnabled,
                newNotification,
                mobileNotificationSettings,
                notificationBody,
                badgeString);
        }

        if (outcome == null)
        {
            _logger.LogError("SendFcmV1NativeNotificationAsync returned null outcome");
            throw new SfSendNotificationFailException();
        }

        _logger.LogInformation("Notification id: {NotificationId}, Hub: {Hub}", outcome.NotificationId, mobileNotificationSettings.HubName);

        switch (outcome.State)
        {
            case NotificationOutcomeState.Completed:
            case NotificationOutcomeState.Processing:
            case NotificationOutcomeState.Enqueued:
                return new HubResponse<NotificationOutcome>(outcome, true);

            case NotificationOutcomeState.Abandoned:
            case NotificationOutcomeState.Unknown:
                throw new SfSendNotificationFailException();

            default:
                throw new SfSendNotificationFailException();
        }
    }

    /// <summary>
    /// Delete device installation from Azure Notification Hub.
    /// Since we don't know which hub the installation is in, we try both hubs.
    /// </summary>
    /// <param name="installationId">A unique identifier for the device installation (typically device ID)</param>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    public async Task DeleteInstallationAsync(string installationId)
    {
        try
        {
            var settings = await _notificationSettingsRepository.GetObjectsAsync(c => c.Mobile.RegistrationId == installationId, 1);
            if (settings.Count == 0)
            {
                _logger.LogInformation("Installation not found. InstallationId: {InstallationId}", installationId);
                return;
            }
            var mobileNotificationSettings = settings.FirstOrDefault(c => c.Mobile.RegistrationId == installationId)?.Mobile;
            if (mobileNotificationSettings == null)
            {
                _logger.LogInformation("Mobile notification settings not found. InstallationId: {InstallationId}", installationId);
                return;
            }

            _logger.LogInformation("Deleting installation. InstallationId: {InstallationId}, HubName: {HubName}", installationId, mobileNotificationSettings.HubName);

            var notificationHubClient = GetNotificationHubClient(mobileNotificationSettings.HubName);
            await notificationHubClient.DeleteInstallationAsync(installationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete installation. InstallationId: {InstallationId}", installationId);
        }
    }



    private async Task<MobileNotificationSettings?> GetMobileNotificationSettings(
        string userId,
        string sleekflowCompanyId)
    {
        var userNotificationSettings = await _notificationSettingsRepository.GetNotificationSettingsByUserIdAsync(
            userId,
            sleekflowCompanyId);
        return userNotificationSettings?.Mobile;
    }

    private async Task<List<RegistrationDescription>> GetAllRegistrationsByTagAsync(string companyId, string userId)
    {
        var registrations = new List<RegistrationDescription>();
        var continuationToken = string.Empty;
        do
        {
            var result = await _defaultNotificationHubClient.GetRegistrationsByTagAsync(
                companyId,
                continuationToken,
                100,
                CancellationToken.None);
            registrations.AddRange(result.Where(r => r.Tags.Contains(userId)));
            continuationToken = result.ContinuationToken;
        }
        while (continuationToken != null);

        return registrations;
    }

    public async Task RemoveAllRegistrationsByTagAsync(string companyId, string userId)
    {
        var registrations = await GetAllRegistrationsByTagAsync(companyId, userId);
        foreach (var registration in registrations)
        {
            await _defaultNotificationHubClient.DeleteRegistrationAsync(registration.RegistrationId);
        }
    }

    public async Task<NotificationDetails> GetNotificationDeliveryStatus(string notificationId, string? hubName)
    {
        var notificationHubClient = GetNotificationHubClient(hubName);
        return await notificationHubClient.GetNotificationOutcomeDetailsAsync(notificationId);
    }

    private async Task<NotificationOutcome?> SendFcmV1NotificationAsync(
        bool isBannerEnabled,
        bool isBadgeEnabled,
        Models.Notifications.Notification newNotification,
        MobileNotificationSettings mobileNotificationSettings,
        string notificationBody,
        string badgeString)
    {
        var fcmV1 = new FcmV1MessageWrapper
        {
            Message = new FcmV1Message
            {
                Notification = new Models.Notifications.FcmV1Notification
                {
                    Title = isBannerEnabled ? newNotification.Title : null,
                    Body = isBannerEnabled ? notificationBody : null,
                },
                Data = new FcmV1Data
                {
                    Message = newNotification.Title,
                    Badge = isBadgeEnabled ? badgeString : null,
                    ConversationId = newNotification.ConversationId,
                    CompanyId = newNotification.CompanyId
                },
                Android = new FcmV1Android
                {
                    Priority = "HIGH",
                    Notification = new FcmV1AndroidNotification
                    {
                        Sound = mobileNotificationSettings.AndroidSound,
                        NotificationCount = isBadgeEnabled ? (newNotification.Badge ?? 0) : 0
                    }
                },
                Apns = new FcmV1Apns
                {
                    Payload = new FcmV1ApnsPayload
                    {
                        Aps = new FcmV1ApnsPayloadAps
                        {
                            Sound = mobileNotificationSettings.IosSound,
                            Badge = isBadgeEnabled ? (newNotification.Badge ?? 0) : 0
                        }
                    }
                }
            }
        };

        _logger.LogInformation(
            "Sending FCM notification. Title: {Title}, UserId: {UserId}, CompanyId: {CompanyId}, Tags: {Tags}, Hub: {Hub}",
            newNotification.Title,
            newNotification.UserId,
            newNotification.CompanyId,
            string.Join(",", newNotification.Tags ?? Array.Empty<string>()),
            mobileNotificationSettings.HubName);

        var notificationHubClient = GetNotificationHubClient(mobileNotificationSettings.HubName);
        var outcome = await notificationHubClient.SendFcmV1NativeNotificationAsync(
            JsonConvert.SerializeObject(fcmV1),
            newNotification.Tags);

        return outcome;
    }

    private async Task<NotificationOutcome?> SendApnsNotificationAsync(
        bool isBannerEnabled,
        bool isBadgeEnabled,
        Models.Notifications.Notification newNotification,
        MobileNotificationSettings mobileNotificationSettings,
        string notificationBody)
    {
        var apns = new ApnsMessageWrapper
        {
            Aps = new ApsPayload
            {
                Alert = new ApsAlert
                {
                    Title = isBannerEnabled ? newNotification.Title : null,
                    Body = isBannerEnabled ? notificationBody : null,
                },
                Badge = isBadgeEnabled ? (newNotification.Badge ?? 0) : 0,
                Sound = mobileNotificationSettings.IosSound ?? "default"
            },
            Data = new ApnsData
            {
                ConversationId = newNotification.ConversationId,
                CompanyId = newNotification.CompanyId
            }
        };

        var apnsHeaders = new Dictionary<string, string>
        {
            {
                "apns-push-type", "alert"
            },
            {
                "apns-priority", "10"
            }
        };

        var appleNotification = new AppleNotification(JsonConvert.SerializeObject(apns), apnsHeaders);

        _logger.LogInformation(
            "Sending APNS notification. Title: {Title}, UserId: {UserId}, CompanyId: {CompanyId}, Tags: {Tags}, Hub: {Hub}",
            newNotification.Title,
            newNotification.UserId,
            newNotification.CompanyId,
            string.Join(",", newNotification.Tags ?? Array.Empty<string>()),
            mobileNotificationSettings.HubName);

        var notificationHubClient = GetNotificationHubClient(mobileNotificationSettings.HubName);
        var outcome = await notificationHubClient.SendNotificationAsync(
            appleNotification,
            newNotification.Tags);

        return outcome;
    }

    public async Task<Installation?> GetInstallationAsync(string installationId)
    {
        try
        {
            // get setting by installationId
            var settings = await _notificationSettingsRepository.GetObjectsAsync(c => c.Mobile.RegistrationId == installationId, 1);
            if (settings.Count == 0)
            {
                _logger.LogInformation("Mobile notification settings not found. InstallationId: {InstallationId}", installationId);
                return null;
            }

            var mobileNotificationSettings = settings.FirstOrDefault(c => c.Mobile.RegistrationId == installationId)?.Mobile;
            if (mobileNotificationSettings == null)
            {
                _logger.LogInformation("Mobile notification settings not found. InstallationId: {InstallationId}", installationId);
                return null;
            }

            var notificationHubClient = GetNotificationHubClient(mobileNotificationSettings.HubName);
            var installation = await notificationHubClient.GetInstallationAsync(installationId);
            return installation;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get installation. InstallationId: {InstallationId}", installationId);
            return null;
        }
    }

    public async Task<List<string>> GetAllInstallationIdsAsync()
    {
        var installations = await _notificationSettingsRepository.GetObjectsAsync(c => c.Mobile.RegistrationId != null, 50000);
        return installations.Select(i => i.Mobile?.RegistrationId ?? string.Empty).Where(i => i != string.Empty).ToList();
    }

    public async Task<RegistrationDescription> GetRegistrationByRegistrationIdAsync(string registrationId)
    {
        return await _defaultNotificationHubClient.GetRegistrationAsync<RegistrationDescription>(registrationId);
    }

}