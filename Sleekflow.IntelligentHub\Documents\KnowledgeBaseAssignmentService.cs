using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.IntelligentHub.Workers;

namespace Sleekflow.IntelligentHub.Documents;

public interface IKnowledgeBaseIngestionService
{
    Task StartKnowledgeBaseIngestion(
        string sleekflowCompanyId,
        string documentId);

    Task StartKnowledgeBaseBatchIngestion(
        string sleekflowCompanyId,
        string[] documentIds);
}

public class KnowledgeBaseIngestionService : IScopedService, IKnowledgeBaseIngestionService
{
    private readonly IKbDocumentRepository _kbDocumentRepository;
    private readonly IIntelligentHubWorkerService _intelligentHubWorkerService;
    private readonly ILogger<KnowledgeBaseIngestionService> _logger;

    public KnowledgeBaseIngestionService(
        IKbDocumentRepository kbDocumentRepository,
        IIntelligentHubWorkerService intelligentHubWorkerService,
        ILogger<KnowledgeBaseIngestionService> logger)
    {
        _kbDocumentRepository = kbDocumentRepository;
        _intelligentHubWorkerService = intelligentHubWorkerService;
        _logger = logger;
    }

    public async Task StartKnowledgeBaseIngestion(
        string sleekflowCompanyId,
        string documentId)
    {
        // First need to check the file status
        var document = await _kbDocumentRepository.GetAsync(documentId, sleekflowCompanyId);
        if (document == null)
        {
            throw new Exceptions.SfNotFoundObjectException(documentId, sleekflowCompanyId);
        }

        switch (document.FileDocumentProcessStatus)
        {
            case ProcessFileDocumentStatuses.Pending:
            case ProcessFileDocumentStatuses.Processing:
            case ProcessFileDocumentStatuses.NotConverted:
                // Determine which ingestion method to call based on content type
                switch (document.SysTypeName)
                {
                    case SysTypeNames.FileDocument:
                        var fileDocument = (FileDocument) document;

                        await _intelligentHubWorkerService.StartFileIngestionAsync(
                            sleekflowCompanyId,
                            documentId,
                            fileDocument.BlobId);
                        break;

                    case SysTypeNames.WebsiteDocument:
                        await _intelligentHubWorkerService.StartWebsiteIngestionAsync(
                            sleekflowCompanyId,
                            documentId);
                        break;

                    case SysTypeNames.WebpageDocument:
                        await _intelligentHubWorkerService.StartWebpageIngestionAsync(
                            sleekflowCompanyId,
                            documentId);
                        break;

                    default:
                        throw new ArgumentException(
                            $"Unknown SysTypeName type: {document.SysTypeName} for document {documentId}");
                }

                await _kbDocumentRepository.PatchFileDocumentProcessStatusAsync(
                    sleekflowCompanyId,
                    documentId,
                    ProcessFileDocumentStatuses.Converting,
                    0.0);
                break;

            case ProcessFileDocumentStatuses.Converting:
                _logger.LogInformation(
                    "UpdateKnowledgeBaseAssignment document {DocumentId} conversion already in progress",
                    documentId);

                // if the file is already converting, we don't need to do anything
                // after conversion, we will check the list of assigned agents and upload to kb
                break;

            case ProcessFileDocumentStatuses.Completed:
            case ProcessFileDocumentStatuses.ReadyToAssign:
                await _intelligentHubWorkerService.StartUploadToAgentKnowledgeBasesAsync(
                    sleekflowCompanyId,
                    documentId);
                break;

            case ProcessFileDocumentStatuses.Failed:
            case ProcessFileDocumentStatuses.FailedToConvert:
                // The file conversion has failed previously, cannot assign this file
                throw new Exception("Document conversion failed, cannot assign to agent.");

            default:
                throw new ArgumentOutOfRangeException($"Unknown status: {document.FileDocumentProcessStatus}");
        }
    }

    public async Task StartKnowledgeBaseBatchIngestion(
        string sleekflowCompanyId,
        string[] documentIds)
    {
        // For batch ingestion, we don't check the status here, do that in the orchestrator, instead we send an event via _intelligentHubWorkerService.StartBatchIngestionAsync
        await _intelligentHubWorkerService.StartBatchIngestionAsync(sleekflowCompanyId, documentIds);
    }
}