using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.UserEventHub.Configs;

public interface INotificationHubConfig
{
    public string NotificationHubConnectionString { get; }

    public string NotificationHubName { get; }

    public string NotificationHubSmartoneConnectionString { get; }

    public string NotificationHubSmartoneName { get; }
}

public class NotificationHubConfig : IConfig, INotificationHubConfig
{
    public string NotificationHubConnectionString { get; }

    public string NotificationHubName { get; }

    public string NotificationHubSmartoneConnectionString { get; }

    public string NotificationHubSmartoneName { get; }

    public NotificationHubConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        NotificationHubConnectionString = Environment.GetEnvironmentVariable("NOTIFICATION_HUB_CONNECTION_STRING", target)
            ?? throw new SfMissingEnvironmentVariableException("NOTIFICATION_HUB_CONNECTION_STRING");

        NotificationHubName = Environment.GetEnvironmentVariable("NOTIFICATION_HUB_NAME", target)
            ?? throw new SfMissingEnvironmentVariableException("NOTIFICATION_HUB_NAME");

        NotificationHubSmartoneConnectionString = Environment.GetEnvironmentVariable("NOTIFICATION_HUB_SMARTONE_CONNECTION_STRING", target)
            ?? throw new SfMissingEnvironmentVariableException("NOTIFICATION_HUB_SMARTONE_CONNECTION_STRING");

        NotificationHubSmartoneName = Environment.GetEnvironmentVariable("NOTIFICATION_HUB_SMARTONE_NAME", target)
            ?? throw new SfMissingEnvironmentVariableException("NOTIFICATION_HUB_SMARTONE_NAME");
    }
}