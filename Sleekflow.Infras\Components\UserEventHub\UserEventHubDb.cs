using Pulumi;
using Pulumi.AzureNative.DocumentDB.Inputs;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Components.Utils;
using DocumentDB = Pulumi.AzureNative.DocumentDB;

namespace Sleekflow.Infras.Components.UserEventHub;

public class UserEventHubDb
{
    private readonly ResourceGroup _resourceGroup;
    private readonly DocumentDB.DatabaseAccount _databaseAccount;
    private readonly MyConfig _myConfig;

    public UserEventHubDb(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount databaseAccount,
        MyConfig myConfig)
    {
        _resourceGroup = resourceGroup;
        _databaseAccount = databaseAccount;
        _myConfig = myConfig;
    }

    public class UserEventHubDbOutput
    {
        public Output<string> AccountName { get; }

        public Output<string> AccountKey { get; }

        public string DatabaseId { get; }

        public UserEventHubDbOutput(
            Output<string> accountName,
            Output<string> accountKey,
            string databaseId)
        {
            AccountName = accountName;
            AccountKey = accountKey;
            DatabaseId = databaseId;
        }
    }

    public UserEventHubDbOutput InitUserEventHubDb()
    {
        const string cosmosDbId = "usereventhubdb";
        var cosmosDb = new DocumentDB.SqlResourceSqlDatabase(
            cosmosDbId,
            new DocumentDB.SqlResourceSqlDatabaseArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = _databaseAccount.Name,
                Resource = new DocumentDB.Inputs.SqlDatabaseResourceArgs
                {
                    Id = cosmosDbId,
                },
                Options = new DocumentDB.Inputs.CreateUpdateOptionsArgs
                {
                    AutoscaleSettings = new DocumentDB.Inputs.AutoscaleSettingsArgs
                    {
                        MaxThroughput = 1000
                    }
                }
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        var containerParams = new ContainerParam[]
        {
            new (
                "notification_settings",
                "notification_settings",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                UniqueKeyArgsList: new List<UniqueKeyArgs>
                {
                    new DocumentDB.Inputs.UniqueKeyArgs
                    {
                        Paths = "/sleekflow_staff_id"
                    }
                }
            ),
            new (
                "recommended_reply_streaming_endpoint_saga_instances",
                "recommended_reply_streaming_endpoint_saga_instances",
                new List<string>
                {
                    "/id"
                },
                Ttl: -1
            ),
            new (
                "user_event",
                "user_event",
                new List<string>
                {
                    "/sleekflow_company_id", "/created_date", "/id",
                },
                MaxThroughput: _myConfig.Name == "production" ? 10000 : 1000,
                Ttl: -1
            ),
            new (
                "user_event_type",
                "user_event_type",
                new List<string>
                {
                    "/sleekflow_company_id",
                },
                MaxThroughput: _myConfig.Name == "production" ? 20000 : 1000
            ),
            new (
                "sql_job",
                "sql_job",
                new List<string>
                {
                    "/sleekflow_company_id",
                },
                MaxThroughput: _myConfig.Name == "production" ? 2000 : 1000
            )
        };

        var containerIdToContainer = ContainerUtils.CreateSqlResourceSqlContainers(
            _resourceGroup,
            _databaseAccount,
            cosmosDb,
            cosmosDbId,
            containerParams);

        var cosmosDbAccountKeys = DocumentDB.ListDatabaseAccountKeys.Invoke(
            new DocumentDB.ListDatabaseAccountKeysInvokeArgs
            {
                AccountName = _databaseAccount.Name, ResourceGroupName = _resourceGroup.Name
            });
        var cosmosDbAccountName = _databaseAccount.Name;
        var cosmosDbAccountKey = cosmosDbAccountKeys.Apply(accountKeys => accountKeys.PrimaryMasterKey);

        return new UserEventHubDbOutput(
            cosmosDbAccountName,
            cosmosDbAccountKey,
            cosmosDbId);
    }
}