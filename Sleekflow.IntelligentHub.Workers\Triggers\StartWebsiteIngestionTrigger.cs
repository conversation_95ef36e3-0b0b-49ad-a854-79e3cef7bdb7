using Azure.Messaging.ServiceBus;
using MassTransit;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.IntelligentHub.Workers.Orchestrators;

namespace Sleekflow.IntelligentHub.Workers.Triggers;

public class StartWebsiteIngestionTrigger
{
    private readonly ILogger<StartWebsiteIngestionTrigger> _logger;
    private const string QueueName = "start-website-ingestion-event";
    private readonly IMessageReceiver _messagereceiver;

    public StartWebsiteIngestionTrigger(
        ILogger<StartWebsiteIngestionTrigger> logger,
        IMessageReceiver messagereceiver)
    {
        _logger = logger;
        _messagereceiver = messagereceiver;
    }

    [Function(nameof(StartWebsiteIngestionTrigger))]
    public async Task RunAsync(
        [ServiceBusTrigger(QueueName, Connection = "SERVICE_BUS_CONN_STR")]
        ServiceBusReceivedMessage message,
        [DurableClient]
        DurableTaskClient starter,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("StartWebsiteIngestionTrigger message: {Message}", message.Body.ToString());

            // Parse the full message to extract the nested "message" property
            var fullMessage = JObject.Parse(message.Body.ToString());
            var messageContent = fullMessage["message"]?.ToString();

            _logger.LogInformation(
                "StartWebsiteIngestionTrigger message content: {Message}",
                messageContent);

            if (string.IsNullOrEmpty(messageContent))
            {
                _logger.LogError("Message content is missing or empty in the received message");
                return;
            }

            var input = JsonConvert.DeserializeObject<StartWebsiteIngestionEvent>(messageContent);
            if (input == null)
            {
                _logger.LogError("Failed to deserialize StartWebsiteIngestionEvent");
                return;
            }

            var instanceId = await starter.ScheduleNewOrchestrationInstanceAsync(
                nameof(StartWebsiteIngestionOrchestratorV1),
                input);

            _logger.LogInformation($"Started StartWebsiteIngestionOrchestrator with ID = [{instanceId}]");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Service Bus message for website ingestion");
            throw;
        }
    }
}