# Implementation Patterns

This document is part of [Sleekflow Project Practices](ProjectPractices.md).

## Service Lifetimes: IScopedService and ISingletonService

Sleekflow uses marker interfaces to control the lifetimes of services registered in the dependency injection (DI) container:

- **ISingletonService**: Services implementing this interface are registered as singletons. A single instance is created and shared throughout the application's lifetime. This is suitable for stateless services or those that maintain shared state (e.g., repositories, configuration providers).
- **IScopedService**: Services implementing this interface are registered with a scoped lifetime. A new instance is created per request or per scope, making it suitable for services that handle request-specific data or context.

### Registration Pattern

The registration is handled centrally (see `Sleekflow/Modules.cs`):

```csharp
b.<PERSON>an(
    scan => scan
        .FromAssemblies(hashSet)
        .AddClasses(classes => classes.AssignableTo<ISingletonService>())
        .UsingRegistrationStrategy(RegistrationStrategy.Throw)
        .AsMatchingInterface()
        .WithSingletonLifetime());

b.<PERSON><PERSON>(
    scan => scan
        .FromAssemblies(hashSet)
        .AddClasses(classes => classes.AssignableTo<IScopedService>())
        .UsingRegistrationStrategy(RegistrationStrategy.Throw)
        .AsMatchingInterface()
        .WithScopedLifetime());
```

### Example Usages

- **ISingletonService**: Used by most repository classes (e.g., `LockRepository`, `AuditLogRepository`, `EntityRepository`), time providers, and configuration services.
- **IScopedService**: Used by services that require per-request context, such as `CancellationTokenService`, `ShopifyProductService`, and certain workflow or integration services.

This approach ensures clear, maintainable, and testable service lifetimes across the entire hub ecosystem.

## Modular Architecture Patterns

### Automatic Dependency Injection

The system uses a marker interface pattern for automatic dependency registration. Key interfaces include:

1. **IConfig** - For configuration objects
   ```csharp
   public class AppConfig : IConfig, IAppConfig
   ```

2. **ISingletonService** - For singleton service registration
   ```csharp
   public class FuncService : ISingletonService, IFuncService
   ```

3. **ITrigger** - For API triggers
   ```csharp
   public class GetObjects : ITrigger
   ```

4. **IEventHandler** - For event handlers
   ```csharp
   public interface IOnObjectOperationEventHandler : IEventHandler
   public class OnObjectOperationEventHandler : ISingletonService, IOnObjectOperationEventHandler
   ```

5. **IChangeFeedHandler** - For change feed handlers
   ```csharp
   public class EntityChangeFeedHandler : ISingletonService, IEntityChangeFeedHandler
   ```

### Modular Dependencies

The codebase uses a centralized module registration approach for clean and consistent dependency setup:

```csharp
Modules.BuildCosmosServices(builder.Services, builder.Configuration);
Modules.BuildCosmosProcessorServices(builder.Services, typeof(Program));
Modules.BuildCacheServices(builder.Services);
Modules.BuildServiceBusServices(builder.Services);
Modules.BuildServiceBusProcessorServices(builder.Services, typeof(Program));
```

## Database Access Patterns

Sleekflow implements a robust repository pattern for data access, built around a base repository class that provides common functionality for all data repositories.

### Base Repository Implementation

The `BaseRepository<TObj>` class provides centralized data access with:

- Built-in error handling
- Consistent cancellation token propagation
- Automated retry policies for transient failures
- Strong typing for database objects
- Container resolution for multi-tenant scenarios

```csharp
public class BaseRepository<TObj>
{
    private readonly ILogger<BaseRepository<TObj>> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly Container _container;
    private readonly AsyncRetryPolicy _retryPolicy;

    protected BaseRepository(
        ILogger<BaseRepository<TObj>> logger,
        IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _container = InitContainer();
        _retryPolicy = GetRetryPolicy();
    }

    // Implementation details...
}
```

### Database Model Attributes

Each database model uses attributes to define its storage location:

```csharp
[Resolver(typeof(ICosmosContainerResolver))]
[DatabaseId("sleekflow-db")]
[ContainerId("users")]
public class User
{
    // User properties
}
```

### Hub-Specific Repository Implementations

Each hub implements its own repository classes that inherit from the base repository:

```csharp
public class EntityRepository : BaseRepository<CrmHubEntity>, IEntityRepository, ISingletonService
{
    public EntityRepository(
        ILogger<BaseRepository<CrmHubEntity>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}
```

### SQL Database Access

For SQL databases, Sleekflow uses Dapper for optimized data access:

```csharp
public class TravisBackendRepository : IScopedService, ITravisBackendRepository
{
    private readonly ILogger<TravisBackendRepository> _logger;
    private readonly TravisDatabase _travisDatabase;

    public async Task<List<Company>> GetCompanies(int offset, string location)
    {
        var sql = "SELECT id, CompanyName, CompanyCountry, CmsActivationOwnerId FROM CompanyCompanies WHERE IsDeleted = 0 ORDER BY CreatedAt OFFSET @offset ROWS FETCH NEXT 1000 ROWS ONLY";
        var connection = _travisDatabase.GetConnectionByRegion(location);
        if (connection == null)
        {
            return [];
        }

        return (await connection.QueryAsync<Company>(sql, new { offset })).AsList();
    }
}
```

## HTTP and API Implementation

Sleekflow employs a consistent approach to HTTP and API implementation across its service hubs.

### API Controllers

API controllers follow a standardized pattern with versioning, proper request validation, and consistent response formatting:

```csharp
[ApiController]
[ApiVersion("1.0")]
[Route("v{version:apiVersion}/[controller]")]
public class EntitiesController : ControllerBase
{
    private readonly IEntityService _entityService;

    public EntitiesController(IEntityService entityService)
    {
        _entityService = entityService;
    }

    [HttpGet("{id}")]
    [ProducesResponseType(typeof(Output<Entity>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(Output), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetEntityAsync(string id, CancellationToken cancellationToken)
    {
        var entity = await _entityService.GetEntityAsync(id, cancellationToken);

        if (entity == null)
        {
            return NotFound(Output.Error("Entity not found", "ENTITY_NOT_FOUND"));
        }

        return Ok(Output.Success(entity));
    }
}
```

### HTTP Clients

For hub-to-hub and external API communication, the project standardizes HTTP client configuration:

```csharp
// Program.cs or Startup.cs
Modules.BuildHttpClients(builder.Services);

// Implementation
public class HttpV2StepExecutor : GeneralStepExecutor<CallStep<HttpV2StepArgs>>, IHttpV2StepExecutor, IScopedService
{
    private readonly HttpClient _httpClient;

    public HttpV2StepExecutor(
        // Other dependencies...
        IHttpClientFactory httpClientFactory)
        : base(/* ... */)
    {
        _httpClient = httpClientFactory.CreateClient("default-flow-hub-handler");
    }

    // Implementation...
}
```

### Workflow HTTP Steps

The FlowHub implements reusable HTTP step executors for workflow automation:

```csharp
public class HttpV2StepArgs : TypedCallStepArgs
{
    [Required]
    [JsonProperty("method")]
    [JsonConverter(typeof(StringEnumConverter))]
    public HttpMethod Method { get; set; }

    [Required]
    [JsonProperty("url__expr")]
    public string UrlExpr { get; set; }

    [JsonProperty("headers__key_expr_set")]
    public HashSet<HttpHeaderKeyValuePair>? HeadersKeyExprSet { get; set; }

    // Other properties...
}
```

## Caching Patterns

Sleekflow implements multi-level caching strategies to optimize performance and reduce database load.

### Cache Service Abstraction

The `ICacheService` interface provides a consistent way to implement caching across the platform:

```csharp
public interface ICacheService
{
    Task<T> CacheAsync<T>(string key, Func<Task<T>> valueFactory, TimeSpan? expiry = null);
    Task RemoveCacheAsync(string key);
    // Other methods...
}
```

### Implementation-Specific Memory Cache

For in-memory caching, Sleekflow uses specialized memory cache implementations:

```csharp
// Configuration in Modules
public static void BuildFlowHubLruMemoryCacheServices(IServiceCollection b, string containerName, int capacity)
{
    b.AddSingleton<ISpecializedMemoryCacheService, LruMemoryCacheService>(
        _ => new LruMemoryCacheService(containerName, capacity));
}

// Usage in services
public async Task<Dictionary<string, object?>> GetWorkflowMetadataAsync(
    string sleekflowCompanyId,
    string workflowVersionedId)
{
    var cacheKey = $"WorkflowMetadata:{sleekflowCompanyId}:{workflowVersionedId}";

    var metadata = await _specializedMemoryCache.GetOrCreateAsync(
        cacheKey,
        () => _cacheService.CacheAsync(
            cacheKey,
            () => _blobService.DownloadJsonAsAsync<Dictionary<string, object?>>(
                WorkflowMetadataContainerName,
                GetWorkflowMetadataBlobName(
                    sleekflowCompanyId,
                    workflowVersionedId)),
            TimeSpan.FromMinutes(10)));

    return metadata ?? new();
}
```

### Distributed Caching

For distributed scenarios, Redis is used as a distributed cache:

```csharp
// Cache key generation
public static class FlowHubCacheKeyBuilder
{
    public static string BuildLatestActiveWorkflowSkeletonsKeyByCompany(string sleekflowCompanyId)
        => $"AllLatestActiveWorkflows:{sleekflowCompanyId}";
}

// Usage in services
public async Task<bool> IsUsageLimitExceeded(
    string sleekflowCompanyId,
    Dictionary<string, int> featureUsageLimits,
    IntelligentHubUsageFilter? intelligentHubUsageFilter)
{
    foreach (var featureUsageLimit in featureUsageLimits)
    {
        var featureName = featureUsageLimit.Key;
        var usageLimit = featureUsageLimit.Value;

        var usageCount = await _cacheService.CacheAsync(
            GetCacheKey(sleekflowCompanyId, featureName),
            async () => await _intelligentHubUsageRepository.CountFeatureUsageAsync(
                featureName,
                sleekflowCompanyId,
                intelligentHubUsageFilter),
            TimeSpan.FromDays(1));

        // Further implementation...
    }
}
```

## Event-Based Communication Implementation

The service bus pattern is implemented using Azure Service Bus and MassTransit:

```csharp
// From Sleekflow/Events/ServiceBus/ServiceBusManager.cs
public class ServiceBusManager : IServiceBusManager
{
    private readonly IServiceBusProvider _serviceBusProvider;

    public ServiceBusManager(IServiceBusProvider serviceBusProvider)
    {
        _serviceBusProvider = serviceBusProvider;
    }

    public async Task PublishAsync<T>(T @event, CancellationToken cancellationToken = default) where T : class
    {
        // Determine the service bus configuration type based on event type
        var serviceBusConfigType = DetermineServiceBusConfigType(typeof(T));

        // Resolve the appropriate Service Bus instance from the provider
        var bus = _serviceBusProvider.GetServiceBus(serviceBusConfigType);

        // Publish the event
        await bus.Publish(@event, cancellationToken);
    }
}
```

Example of hub-to-hub communication via events:

```csharp
// From Sleekflow.FlowHub/Steps/StepRequester.cs
public class StepRequester : IStepRequester, IScopedService
{
    private readonly IServiceBusManager _serviceBusManager;

    public StepRequester(IServiceBusManager serviceBusManager)
    {
        _serviceBusManager = serviceBusManager;
    }

    public virtual async Task RequestAsync(
        string stateId,
        string stepId,
        Stack<StackEntry> stackEntries,
        string? workerInstanceId)
    {
        await _serviceBusManager.PublishAsync<OnStepRequestedEvent>(
            new OnStepRequestedEvent(
                stateId,
                stepId,
                stackEntries,
                workerInstanceId));
    }
}
```

Configuration for high-traffic events:

```csharp
// From Sleekflow.Infras/Components/ServiceBus.cs
public ServiceBusOutput InitServiceBus(string? name = null, string? locationName = null, string? serviceBusConfigType = null)
{
    // Determine the namespace name based on the ServiceBusConfigType
    var namespaceResourceName = serviceBusConfigType == ServiceBusConfigTypes.HighTrafficServiceBus
        ? ConstructResourceName("sleekflow-high-traffic-service-bus", name)
        : ConstructResourceName("sleekflow-service-bus", name);

    // ... service bus configuration
}
```

## Gateway Implementations

### External API Gateway (KrakenD)

```csharp
// From Sleekflow.Infras/Components/ApiGateway.cs
public List<App.ContainerApp> InitApiGateway()
{
    // ...
    var krakenDImage = GetKrakenDImage(apigwImage);
    var globalKrakenDImage = GetGlobalKrakenDImage(globalApigwImage);

    // For each managed environment
    foreach (var managedEnvAndAppsTuple in _managedEnvAndAppsTuples)
    {
        var containerApps = managedEnvAndAppsTuple.ContainerApps;
        var managedEnvironment = managedEnvAndAppsTuple.ManagedEnvironment;
        var image = managedEnvAndAppsTuple is FilteredManagedEnvAndAppsTuple ? globalKrakenDImage : krakenDImage;
        var containerAppName = managedEnvAndAppsTuple.FormatContainerAppName("apigw");

        var containerApp = new App.ContainerApp(
            containerAppName,
            new App.ContainerAppArgs
            {
                // ... configuration
                Configuration = new App.Inputs.ConfigurationArgs
                {
                    Ingress = new App.Inputs.IngressArgs
                    {
                        External = true,
                        TargetPort = 8080,
                        // ... other configuration
                    },
                    // ... other configuration
                },
            }
        );
    }
}
```

KrakenD configuration example:

```json
// From Sleekflow.KrakenD/krakend.json
{
  "endpoint": "/v1/public-api-gateway/messaging-hub/whatsapp/cloudapi/Messages/{method}",
  "backend": [
    {
      "url_pattern": "/messaging-hub/whatsapp/cloudapi/Messages/{method}",
      "host": [
        "{{ env \"PUBLIC_API_GATEWAY_HOST\" }}"
      ],
      "encoding": "json",
      "method": "POST"
    }
  ],
  "extra_config": {
    "modifier/lua-endpoint": {
      "allow_open_libs": false,
      "live": false,
      "pre": "pre_endpoint()",
      "sources": [
        "lua/PublicApiGateway/ApiKey/pre_endpoint.lua"
      ]
    }
  },
  "input_headers": [
    "X-Sleekflow-Api-Key",
    "Content-Type",
    "Traceparent"
  ],
  "method": "POST"
}
```

### Internal Gateway

```csharp
// From Sleekflow.Infras/Components/InternalGateway.cs
public Dictionary<string, (App.ContainerApp ContainerApp, Output<string> ApplicationUrl)> InitInternalGateway()
{
    const string igwImage = "sleekflow-igw";
    var image = GetKrakenDImage(igwImage);
    var apps = new Dictionary<string, (App.ContainerApp, Output<string>)>();
    foreach (var managedEnvAndAppsTuple in _managedEnvAndAppsTuples)
    {
        // Only enabled the internal gateway for east asia
        if (managedEnvAndAppsTuple.LocationName != LocationNames.EastAsia)
        {
            continue;
        }

        // ... configure internal gateway
        var containerApp = new App.ContainerApp(
            containerAppName,
            new App.ContainerAppArgs
            {
                // ... configuration with routing for internal services
            }
        );
    }
    return apps;
}
```

### Azure Front Door Implementation

```csharp
// From Sleekflow.Infras/Components/FrontDoor.cs
public Cdn.AFDEndpoint InitFrontDoor()
{
    var profile = new Cdn.Profile(
        "sleekflow-front-door-profile",
        new Cdn.ProfileArgs
        {
            ResourceGroupName = _resourceGroup.Name,
            ProfileName = "sleekflow",
            Location = "Global",
            Sku = new Cdn.Inputs.SkuArgs
            {
                Name = Cdn.SkuName.Standard_AzureFrontDoor,
            },
            OriginResponseTimeoutSeconds = 60,
        },
        new CustomResourceOptions
        {
            Parent = _resourceGroup
        });

    // ... configure front door with routes and rules
}
```