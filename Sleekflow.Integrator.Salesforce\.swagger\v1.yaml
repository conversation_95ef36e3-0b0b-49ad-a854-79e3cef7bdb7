openapi: 3.0.1
info:
  title: Sleekflow 1.0
  version: '1.0'
servers:
  - url: https://localhost:7072
    description: Local
paths:
  /Integrations/CreateObject:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateObjectOutputOutput'
  /Integrations/CreateObjectV2:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateObjectV2Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateObjectV2OutputOutput'
  /Integrations/CreateUserMappingConfig:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserMappingConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateUserMappingConfigOutputOutput'
  /Integrations/DeactivateTypeSync:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeactivateTypeSyncInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeactivateTypeSyncOutputOutput'
  /Integrations/DeleteConnection:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteConnectionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteConnectionOutputOutput'
  /Integrations/DeleteObject:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteObjectOutputOutput'
  /Integrations/GetConnections:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetConnectionsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetConnectionsOutputOutput'
  /Integrations/GetCustomObjectTypes:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCustomObjectTypesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCustomObjectTypesOutputOutput'
  /Integrations/GetObjectDirectRefUrl:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetObjectDirectRefUrlInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetObjectDirectRefUrlOutputOutput'
  /Integrations/GetObjects:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetObjectsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetObjectsOutputOutput'
  /Integrations/GetObjectsCount:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetObjectsCountInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetObjectsCountOutputOutput'
  /Integrations/GetSubscriptions:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSubscriptionsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSubscriptionsOutputOutput'
  /Integrations/GetSupportedTypes:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSupportedTypesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSupportedTypesOutputOutput'
  /Integrations/GetTypeFields:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetTypeFieldsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTypeFieldsOutputOutput'
  /Integrations/GetTypeFieldsV2:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetTypeFieldsV2Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTypeFieldsV2OutputOutput'
  /Integrations/GetUserMappingConfig:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetUserMappingConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetUserMappingConfigOutputOutput'
  /Integrations/InitProvider:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitProviderInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InitProviderOutputOutput'
  /Integrations/InitProviderV2:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitProviderV2Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InitProviderV2OutputOutput'
  /Integrations/InitTypeSync:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitTypeSyncInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InitTypeSyncOutputOutput'
  /Integrations/InitTypeSyncV2:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitTypeSyncV2Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InitTypeSyncV2OutputOutput'
  /Integrations/LoopThroughAndEnrollObjectsToFlowHub:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoopThroughAndEnrollObjectsToFlowHubInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoopThroughAndEnrollObjectsToFlowHubOutputOutput'
  /Integrations/PreviewObjects:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PreviewObjectsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreviewObjectsOutputOutput'
  /Integrations/PreviewObjectsV2:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PreviewObjectsV2Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreviewObjectsV2OutputOutput'
  /Integrations/ReAuthenticate:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReAuthenticateInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReAuthenticateOutputOutput'
  /Integrations/ReInitConnection:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReInitConnectionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReInitConnectionOutputOutput'
  /Integrations/RenameConnection:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RenameConnectionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RenameConnectionOutputOutput'
  /Integrations/ResolveObjectId:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResolveObjectIdInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResolveObjectIdOutputOutput'
  /Integrations/SearchObjects:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchObjectsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchObjectsOutputOutput'
  /Integrations/SyncObject:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SyncObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SyncObjectOutputOutput'
  /Integrations/SyncObjects:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SyncObjectsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SyncObjectsOutputOutput'
  /Integrations/UpdateObject:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateObjectOutputOutput'
  /Integrations/UpdateObjectV2:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateObjectV2Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateObjectV2OutputOutput'
  /Integrations/UpdateUserMappingConfig:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserMappingConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateUserMappingConfigOutputOutput'
  /Internals/LoopThroughAndEnrollObjectsToFlowHubBatch:
    post:
      tags:
        - Internals
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoopThroughAndEnrollObjectsToFlowHubBatchInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoopThroughAndEnrollObjectsToFlowHubBatchOutputOutput'
  /Internals/SubscriptionsCheckBatch:
    post:
      tags:
        - Internals
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubscriptionsCheckBatchInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionsCheckBatchOutputOutput'
  /Internals/SyncObjectsBatch:
    post:
      tags:
        - Internals
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SyncObjectsBatchInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SyncObjectsBatchOutputOutput'
  /Migrations/PrepareMigrationToFlowHub:
    post:
      tags:
        - Migrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrepareMigrationToFlowHubInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrepareMigrationToFlowHubOutputOutput'
  /Public/healthz:
    get:
      tags:
        - Public
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /Public/AuthenticateCallback:
    get:
      tags:
        - Public
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: code
          in: query
          schema:
            type: string
        - name: state
          in: query
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /Public/ApexCallback:
    post:
      tags:
        - Public
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApexCallbackInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApexCallbackOutputOutput'
components:
  schemas:
    ApexCallbackInput:
      required:
        - key
        - new
        - old
        - sleekflow_company_id
        - type
        - user_id
      type: object
      properties:
        new:
          type: array
          items:
            type: object
            additionalProperties: { }
        old:
          type: array
          items:
            type: object
            additionalProperties: { }
        user_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        type:
          minLength: 1
          type: string
        key:
          minLength: 1
          type: string
      additionalProperties: false
    ApexCallbackOutput:
      type: object
      additionalProperties: false
    ApexCallbackOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ApexCallbackOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateObjectInput:
      required:
        - dict
        - entity_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        dict:
          type: object
          additionalProperties:
            nullable: true
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    CreateObjectOutput:
      type: object
      properties:
        is_async_operation:
          type: boolean
      additionalProperties: false
    CreateObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateObjectV2Input:
      required:
        - connection_id
        - dict
        - entity_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        dict:
          type: object
          additionalProperties:
            nullable: true
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    CreateObjectV2Output:
      type: object
      properties:
        is_async_operation:
          type: boolean
      additionalProperties: false
    CreateObjectV2OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateObjectV2Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserMappingConfigInput:
      required:
        - connection_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        user_mappings:
          type: array
          items:
            $ref: '#/components/schemas/UserMapping'
          nullable: true
      additionalProperties: false
    CreateUserMappingConfigOutput:
      type: object
      properties:
        user_mapping_config:
          $ref: '#/components/schemas/ProviderUserMappingConfigDto'
      additionalProperties: false
    CreateUserMappingConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateUserMappingConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CustomObjectType:
      type: object
      properties:
        api_name:
          type: string
          nullable: true
        display_name:
          type: string
          nullable: true
      additionalProperties: false
    DeactivateTypeSyncInput:
      required:
        - entity_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    DeactivateTypeSyncOutput:
      type: object
      additionalProperties: false
    DeactivateTypeSyncOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeactivateTypeSyncOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteConnectionInput:
      required:
        - connection_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
      additionalProperties: false
    DeleteConnectionOutput:
      type: object
      additionalProperties: false
    DeleteConnectionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteConnectionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteObjectInput:
      required:
        - entity_type_name
        - object_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        object_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    DeleteObjectOutput:
      type: object
      additionalProperties: false
    DeleteObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DurablePayload:
      type: object
      properties:
        id:
          type: string
          nullable: true
        statusQueryGetUri:
          type: string
          nullable: true
        sendEventPostUri:
          type: string
          nullable: true
        terminatePostUri:
          type: string
          nullable: true
        rewindPostUri:
          type: string
          nullable: true
        purgeHistoryDeleteUri:
          type: string
          nullable: true
        restartPostUri:
          type: string
          nullable: true
      additionalProperties: false
    GetConnectionsInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetConnectionsOutput:
      required:
        - connections
      type: object
      properties:
        connections:
          type: array
          items:
            $ref: '#/components/schemas/SalesforceConnectionDto'
      additionalProperties: false
    GetConnectionsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetConnectionsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetCustomObjectTypesInput:
      required:
        - connection_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetCustomObjectTypesOutput:
      required:
        - custom_object_types
      type: object
      properties:
        custom_object_types:
          type: array
          items:
            $ref: '#/components/schemas/CustomObjectType'
      additionalProperties: false
    GetCustomObjectTypesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetCustomObjectTypesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetObjectDirectRefUrlInput:
      required:
        - entity_type_name
        - object_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        object_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetObjectDirectRefUrlOutput:
      required:
        - object_direct_ref_url
      type: object
      properties:
        object_direct_ref_url:
          minLength: 1
          type: string
      additionalProperties: false
    GetObjectDirectRefUrlOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetObjectDirectRefUrlOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetObjectsCountInput:
      required:
        - entity_type_name
        - filter_groups
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilterGroup'
      additionalProperties: false
    GetObjectsCountOutput:
      required:
        - count
      type: object
      properties:
        count:
          type: integer
          format: int64
      additionalProperties: false
    GetObjectsCountOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetObjectsCountOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetObjectsInput:
      required:
        - connection_id
        - entity_type_name
        - filter_groups
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilterGroup'
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
      additionalProperties: false
    GetObjectsOutput:
      required:
        - objects
      type: object
      properties:
        objects:
          type: array
          items:
            type: object
            additionalProperties: { }
      additionalProperties: false
    GetObjectsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetObjectsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetSubscriptionsInput:
      required:
        - connection_id
        - entity_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    GetSubscriptionsOutput:
      required:
        - subscriptions
      type: object
      properties:
        subscriptions:
          type: array
          items:
            $ref: '#/components/schemas/SalesforceSubscriptionDto'
      additionalProperties: false
    GetSubscriptionsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetSubscriptionsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetSupportedTypesInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetSupportedTypesOutput:
      type: object
      properties:
        supported_types:
          type: array
          items:
            $ref: '#/components/schemas/SupportedEntityType'
          nullable: true
      additionalProperties: false
    GetSupportedTypesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetSupportedTypesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetTypeFieldsInput:
      required:
        - entity_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    GetTypeFieldsOutput:
      type: object
      properties:
        updatable_fields:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputFieldDto'
          nullable: true
        creatable_fields:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputFieldDto'
          nullable: true
        viewable_fields:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputFieldDto'
          nullable: true
      additionalProperties: false
    GetTypeFieldsOutputFieldDto:
      type: object
      properties:
        calculated:
          type: boolean
        compound_field_name:
          type: string
          nullable: true
        createable:
          type: boolean
        custom:
          type: boolean
        encrypted:
          type: boolean
        label:
          type: string
          nullable: true
        length:
          type: integer
          format: int32
        name:
          type: string
          nullable: true
        picklist_values:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputPicklistValue'
          nullable: true
        soap_type:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        unique:
          type: boolean
        updateable:
          type: boolean
        mandatory:
          type: boolean
          nullable: true
      additionalProperties: false
    GetTypeFieldsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetTypeFieldsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetTypeFieldsOutputPicklistValue:
      type: object
      properties:
        label:
          type: string
          nullable: true
        value:
          type: string
          nullable: true
      additionalProperties: false
    GetTypeFieldsV2Input:
      required:
        - connection_id
        - entity_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    GetTypeFieldsV2Output:
      type: object
      properties:
        updatable_fields:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputFieldDto'
          nullable: true
        creatable_fields:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputFieldDto'
          nullable: true
        viewable_fields:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputFieldDto'
          nullable: true
      additionalProperties: false
    GetTypeFieldsV2OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetTypeFieldsV2Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetUserMappingConfigInput:
      required:
        - connection_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetUserMappingConfigOutput:
      type: object
      properties:
        user_mapping_config:
          $ref: '#/components/schemas/ProviderUserMappingConfigDto'
      additionalProperties: false
    GetUserMappingConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetUserMappingConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    InitProviderInput:
      required:
        - return_to_url
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        return_to_url:
          minLength: 1
          type: string
        additional_details:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    InitProviderOutput:
      type: object
      properties:
        salesforce_authentication_url:
          type: string
          nullable: true
      additionalProperties: false
    InitProviderOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/InitProviderOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    InitProviderV2Input:
      required:
        - failure_url
        - sleekflow_company_id
        - success_url
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        success_url:
          minLength: 1
          type: string
        failure_url:
          minLength: 1
          type: string
        additional_details:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    InitProviderV2Output:
      type: object
      properties:
        salesforce_authentication_url:
          type: string
          nullable: true
      additionalProperties: false
    InitProviderV2OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/InitProviderV2Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    InitTypeSyncInput:
      required:
        - entity_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        sync_config:
          $ref: '#/components/schemas/SyncConfig'
      additionalProperties: false
    InitTypeSyncOutput:
      type: object
      additionalProperties: false
    InitTypeSyncOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/InitTypeSyncOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    InitTypeSyncV2Input:
      required:
        - connection_id
        - entity_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        sync_interval:
          type: integer
          format: int32
          nullable: true
        is_flows_based:
          type: boolean
          nullable: true
      additionalProperties: false
    InitTypeSyncV2Output:
      type: object
      additionalProperties: false
    InitTypeSyncV2OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/InitTypeSyncV2Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    LoopThroughAndEnrollObjectsToFlowHubBatchInput:
      required:
        - connection_id
        - entity_type_name
        - flow_hub_workflow_id
        - flow_hub_workflow_versioned_id
        - is_custom_object
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        is_custom_object:
          type: boolean
        next_records_url:
          type: string
          nullable: true
        flow_hub_workflow_id:
          minLength: 1
          type: string
        flow_hub_workflow_versioned_id:
          minLength: 1
          type: string
      additionalProperties: false
    LoopThroughAndEnrollObjectsToFlowHubBatchOutput:
      type: object
      properties:
        count:
          type: integer
          format: int64
        next_records_url:
          type: string
          nullable: true
      additionalProperties: false
    LoopThroughAndEnrollObjectsToFlowHubBatchOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/LoopThroughAndEnrollObjectsToFlowHubBatchOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    LoopThroughAndEnrollObjectsToFlowHubInput:
      required:
        - connection_id
        - entity_type_name
        - flow_hub_workflow_id
        - flow_hub_workflow_versioned_id
        - is_custom_object
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        is_custom_object:
          type: boolean
        flow_hub_workflow_id:
          minLength: 1
          type: string
        flow_hub_workflow_versioned_id:
          minLength: 1
          type: string
      additionalProperties: false
    LoopThroughAndEnrollObjectsToFlowHubOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
        statusQueryGetUri:
          type: string
          nullable: true
        sendEventPostUri:
          type: string
          nullable: true
        terminatePostUri:
          type: string
          nullable: true
        rewindPostUri:
          type: string
          nullable: true
        purgeHistoryDeleteUri:
          type: string
          nullable: true
        restartPostUri:
          type: string
          nullable: true
      additionalProperties: false
    LoopThroughAndEnrollObjectsToFlowHubOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/LoopThroughAndEnrollObjectsToFlowHubOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    PrepareMigrationToFlowHubInput:
      required:
        - entity_types_to_migrate
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_types_to_migrate:
          type: array
          items:
            type: string
      additionalProperties: false
    PrepareMigrationToFlowHubOutput:
      type: object
      properties:
        created_connection_id:
          type: string
          nullable: true
      additionalProperties: false
    PrepareMigrationToFlowHubOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/PrepareMigrationToFlowHubOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    PreviewObjectsInput:
      required:
        - entity_type_name
        - filter_groups
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilterGroup'
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
      additionalProperties: false
    PreviewObjectsOutput:
      required:
        - objects
      type: object
      properties:
        objects:
          type: array
          items:
            type: object
            additionalProperties: { }
      additionalProperties: false
    PreviewObjectsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/PreviewObjectsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    PreviewObjectsV2Input:
      required:
        - connection_id
        - entity_type_name
        - filter_groups
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilterGroup'
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
      additionalProperties: false
    PreviewObjectsV2Output:
      required:
        - objects
      type: object
      properties:
        objects:
          type: array
          items:
            type: object
            additionalProperties: { }
        next_records_url:
          type: string
          nullable: true
      additionalProperties: false
    PreviewObjectsV2OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/PreviewObjectsV2Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ProviderConnectionDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        organization_id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        environment:
          type: string
          nullable: true
        is_active:
          type: boolean
        is_api_request_limit_exceeded:
          type: boolean
          nullable: true
        connected_at:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    ProviderUserMappingConfigDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        connection_id:
          type: string
          nullable: true
        user_mappings:
          type: array
          items:
            $ref: '#/components/schemas/UserMapping'
          nullable: true
      additionalProperties: false
    ReAuthenticateInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    ReAuthenticateOutput:
      type: object
      additionalProperties: false
    ReAuthenticateOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ReAuthenticateOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ReInitConnectionInput:
      required:
        - connection_id
        - failure_url
        - sleekflow_company_id
        - success_url
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        success_url:
          minLength: 1
          type: string
        failure_url:
          minLength: 1
          type: string
        additional_details:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    ReInitConnectionOutput:
      type: object
      properties:
        provider_name:
          type: string
          nullable: true
        is_re_authentication_required:
          type: boolean
        salesforce_authentication_url:
          type: string
          nullable: true
      additionalProperties: false
    ReInitConnectionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ReInitConnectionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    RenameConnectionInput:
      required:
        - connection_id
        - name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        name:
          minLength: 1
          type: string
      additionalProperties: false
    RenameConnectionOutput:
      type: object
      properties:
        connection:
          $ref: '#/components/schemas/ProviderConnectionDto'
      additionalProperties: false
    RenameConnectionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RenameConnectionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ResolveObjectIdInput:
      required:
        - dict
      type: object
      properties:
        dict:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    ResolveObjectIdOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    ResolveObjectIdOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ResolveObjectIdOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SalesforceConnectionDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        organization_id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        environment:
          type: string
          nullable: true
        is_active:
          type: boolean
        is_api_request_limit_exceeded:
          type: boolean
      additionalProperties: false
    SalesforceSubscription:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        entity_type_name:
          type: string
          nullable: true
        interval:
          type: integer
          format: int32
        last_execution_start_time:
          type: string
          format: date-time
        last_object_modification_time:
          type: string
          format: date-time
          nullable: true
        durable_payload:
          $ref: '#/components/schemas/DurablePayload'
        is_flows_based:
          type: boolean
          nullable: true
        connection_id:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    SalesforceSubscriptionDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        entity_type_name:
          type: string
          nullable: true
        interval:
          type: integer
          format: int32
        is_flows_based:
          type: boolean
          nullable: true
        connection_id:
          type: string
          nullable: true
      additionalProperties: false
    SearchObjectCondition:
      required:
        - field_name
        - operator
      type: object
      properties:
        field_name:
          minLength: 1
          type: string
        operator:
          minLength: 1
          type: string
        value:
          nullable: true
      additionalProperties: false
    SearchObjectsInput:
      required:
        - conditions
        - connection_id
        - entity_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/SearchObjectCondition'
      additionalProperties: false
    SearchObjectsOutput:
      required:
        - objects
      type: object
      properties:
        objects:
          type: array
          items:
            type: object
            additionalProperties: { }
      additionalProperties: false
    SearchObjectsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SearchObjectsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SubscriptionsCheckBatchInput:
      required:
        - entity_type_name
        - filter_groups
        - last_object_modification_time
        - sleekflow_company_id
        - subscription
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        subscription:
          $ref: '#/components/schemas/SalesforceSubscription'
        entity_type_name:
          minLength: 1
          type: string
        last_object_modification_time:
          type: string
          format: date-time
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilterGroup'
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
        next_records_url:
          type: string
          nullable: true
      additionalProperties: false
    SubscriptionsCheckBatchOutput:
      type: object
      properties:
        count:
          type: integer
          format: int64
        next_last_object_modification_time:
          type: string
          format: date-time
        next_records_url:
          type: string
          nullable: true
      additionalProperties: false
    SubscriptionsCheckBatchOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SubscriptionsCheckBatchOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SupportedEntityType:
      type: object
      properties:
        name:
          type: string
          nullable: true
        is_auto_sync_supported:
          type: boolean
      additionalProperties: false
    SyncConfig:
      required:
        - interval
      type: object
      properties:
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilterGroup'
          nullable: true
        filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilter'
          nullable: true
          writeOnly: true
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
        interval:
          maximum: 86400
          minimum: 3600
          type: integer
          format: int32
        entity_type_name:
          type: string
          nullable: true
        sync_mode:
          type: string
          nullable: true
      additionalProperties: false
    SyncConfigFieldFilter:
      type: object
      properties:
        name:
          type: string
          nullable: true
      additionalProperties: false
    SyncConfigFilter:
      type: object
      properties:
        field_name:
          type: string
          nullable: true
        value:
          type: string
          nullable: true
        operator:
          type: string
          nullable: true
      additionalProperties: false
    SyncConfigFilterGroup:
      type: object
      properties:
        filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilter'
          nullable: true
      additionalProperties: false
    SyncObjectInput:
      required:
        - entity_type_name
        - object_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        object_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    SyncObjectOutput:
      type: object
      additionalProperties: false
    SyncObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SyncObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SyncObjectsBatchInput:
      required:
        - entity_type_name
        - filter_groups
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilterGroup'
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
        next_records_url:
          type: string
          nullable: true
      additionalProperties: false
    SyncObjectsBatchOutput:
      type: object
      properties:
        count:
          type: integer
          format: int64
        next_records_url:
          type: string
          nullable: true
      additionalProperties: false
    SyncObjectsBatchOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SyncObjectsBatchOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SyncObjectsInput:
      required:
        - entity_type_name
        - filter_groups
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilterGroup'
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
      additionalProperties: false
    SyncObjectsOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
        statusQueryGetUri:
          type: string
          nullable: true
        sendEventPostUri:
          type: string
          nullable: true
        terminatePostUri:
          type: string
          nullable: true
        rewindPostUri:
          type: string
          nullable: true
        purgeHistoryDeleteUri:
          type: string
          nullable: true
        restartPostUri:
          type: string
          nullable: true
      additionalProperties: false
    SyncObjectsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SyncObjectsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateObjectInput:
      required:
        - dict
        - entity_type_name
        - object_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        object_id:
          minLength: 1
          type: string
        dict:
          type: object
          additionalProperties:
            nullable: true
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    UpdateObjectOutput:
      type: object
      properties:
        is_async_operation:
          type: boolean
      additionalProperties: false
    UpdateObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateObjectV2Input:
      required:
        - connection_id
        - dict
        - entity_type_name
        - object_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        object_id:
          minLength: 1
          type: string
        dict:
          type: object
          additionalProperties:
            nullable: true
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    UpdateObjectV2Output:
      type: object
      properties:
        is_async_operation:
          type: boolean
      additionalProperties: false
    UpdateObjectV2OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateObjectV2Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateUserMappingConfigInput:
      required:
        - sleekflow_company_id
        - user_mapping_config_id
      type: object
      properties:
        user_mapping_config_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        user_mappings:
          type: array
          items:
            $ref: '#/components/schemas/UserMapping'
          nullable: true
      additionalProperties: false
    UpdateUserMappingConfigOutput:
      type: object
      properties:
        user_mapping_config:
          $ref: '#/components/schemas/ProviderUserMappingConfigDto'
      additionalProperties: false
    UpdateUserMappingConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateUserMappingConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UserMapping:
      type: object
      properties:
        provider_user_id:
          type: string
          nullable: true
        sleekflow_user_id:
          type: string
          nullable: true
      additionalProperties: false