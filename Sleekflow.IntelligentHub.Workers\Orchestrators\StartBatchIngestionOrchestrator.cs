using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.IntelligentHub.Workers.Activities;
using Sleekflow.Persistence;

namespace Sleekflow.IntelligentHub.Workers.Orchestrators;

public class StartBatchIngestionOrchestrator
{
    private readonly ILogger<StartBatchIngestionOrchestrator> _logger;
    private const int MaxConcurrentOrchestrators = 10;

    public StartBatchIngestionOrchestrator(ILogger<StartBatchIngestionOrchestrator> logger)
    {
        _logger = logger;
    }

    public class StartBatchIngestionOrchestratorOutput
    {
        [JsonProperty("processed_documents_count")]
        public int ProcessedDocumentsCount { get; set; }

        [JsonProperty("skipped_documents_count")]
        public int SkippedDocumentsCount { get; set; }

        [JsonConstructor]
        public StartBatchIngestionOrchestratorOutput(int processedDocumentsCount, int skippedDocumentsCount)
        {
            ProcessedDocumentsCount = processedDocumentsCount;
            SkippedDocumentsCount = skippedDocumentsCount;
        }
    }

    [Function(nameof(StartBatchIngestionOrchestrator))]
    public async Task<StartBatchIngestionOrchestratorOutput> RunOrchestrator(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var input = context.GetInput<StartBatchIngestionEvent>();

        // Combine all document ingestion parameters into a single collection
        var allDocumentIngestionParams = new List<IDocumentIngestionParams>();
        allDocumentIngestionParams.AddRange(input!.FileDocumentIngestionParams);
        allDocumentIngestionParams.AddRange(input.WebsiteDocumentIngestionParams);
        allDocumentIngestionParams.AddRange(input.WebpageDocumentIngestionParams);

        _logger.LogInformation(
            "StartBatchIngestionOrchestrator processing {DocumentCount} documents for company {CompanyId}",
            allDocumentIngestionParams.Count,
            input.SleekflowCompanyId);

        var taskOptions = new TaskOptions(new TaskRetryOptions(new RetryPolicy(1, TimeSpan.FromSeconds(10), 1.0)));

        // Step 1: Get all document information
        var getDocumentTasks = allDocumentIngestionParams.Select(
            documentParam =>
                context.CallActivityAsync<GetKbDocument.GetKbDocumentOutput>(
                    nameof(GetKbDocument),
                    new GetKbDocument.GetKbDocumentInput(input.SleekflowCompanyId, documentParam.DocumentId),
                    taskOptions)).ToArray();

        var allDocumentOutputs = await Task.WhenAll(getDocumentTasks);

        // Step 2: Create orchestrator tasks based on document status
        var orchestratorTasks = new List<Task>();
        var processedDocumentsCount = 0;
        var skippedDocumentsCount = 0;

        // Create a lookup dictionary for easy access to ingestion parameters
        var documentParamsLookup = allDocumentIngestionParams.ToDictionary(p => p.DocumentId);

        foreach (var documentOutput in allDocumentOutputs)
        {
            var kbDocumentJObject = JObject.FromObject(documentOutput.KbDocument);
            var sysTypeName = (string) kbDocumentJObject[Entity.PropertyNameSysTypeName]!;
            KbDocument kbDocument = sysTypeName switch
            {
                SysTypeNames.FileDocument => kbDocumentJObject.ToObject<FileDocument>()!,
                SysTypeNames.WebsiteDocument => kbDocumentJObject.ToObject<WebsiteDocument>()!,
                SysTypeNames.WebpageDocument => kbDocumentJObject.ToObject<WebpageDocument>()!,
                _ => throw new Exception($"Unknown system type: {sysTypeName}")
            };

            switch (kbDocument.FileDocumentProcessStatus)
            {
                case ProcessFileDocumentStatuses.Pending:
                case ProcessFileDocumentStatuses.Processing:
                case ProcessFileDocumentStatuses.NotConverted:
                    // Create orchestrator task based on document type
                    switch (sysTypeName)
                    {
                        case SysTypeNames.FileDocument:
                            var fileDocumentParams = (FileDocumentIngestionParams) documentParamsLookup[kbDocument.Id];
                            orchestratorTasks.Add(
                                context.CallSubOrchestratorAsync(
                                    nameof(StartFileIngestionOrchestratorV1),
                                    new StartFileIngestionEvent(
                                        input.SleekflowCompanyId,
                                        kbDocument.Id,
                                        fileDocumentParams.SasDownloadUrl),
                                    taskOptions));
                            break;

                        case SysTypeNames.WebsiteDocument:
                            orchestratorTasks.Add(
                                context.CallSubOrchestratorAsync(
                                    nameof(StartWebsiteIngestionOrchestratorV1),
                                    new StartWebsiteIngestionEvent(
                                        input.SleekflowCompanyId,
                                        kbDocument.Id),
                                    taskOptions));
                            break;

                        case SysTypeNames.WebpageDocument:
                            orchestratorTasks.Add(
                                context.CallSubOrchestratorAsync(
                                    nameof(StartWebpageIngestionOrchestrator),
                                    new StartWebpageIngestionEvent(
                                        input.SleekflowCompanyId,
                                        kbDocument.Id),
                                    taskOptions));
                            break;

                        default:
                            throw new Exception(
                                $"Unknown SysTypeName type: {sysTypeName} for document {kbDocument.Id}");
                    }

                    processedDocumentsCount++;
                    break;

                case ProcessFileDocumentStatuses.Converting:
                    // Do nothing - document is already being processed
                    _logger.LogInformation(
                        "Document {DocumentId} is already converting, skipping",
                        kbDocument.Id);
                    skippedDocumentsCount++;
                    break;

                case ProcessFileDocumentStatuses.Completed:
                case ProcessFileDocumentStatuses.ReadyToAssign:
                    // Create upload orchestrator task
                    orchestratorTasks.Add(
                        context.CallSubOrchestratorAsync(
                            nameof(UploadFileDocumentToAllAssignedAgentsOrchestratorV1),
                            new StartUploadToAgentKnowledgeBasesEvent(
                                input.SleekflowCompanyId,
                                kbDocument.Id),
                            taskOptions));
                    processedDocumentsCount++;
                    break;

                case ProcessFileDocumentStatuses.Failed:
                case ProcessFileDocumentStatuses.FailedToConvert:
                    // Document has failed - throw an error
                    throw new Exception(
                        $"Document {kbDocument.Id} has failed processing with status: {kbDocument.FileDocumentProcessStatus}");

                default:
                    throw new ArgumentOutOfRangeException(
                        $"Unknown document status: {kbDocument.FileDocumentProcessStatus} for document {kbDocument.Id}");
            }
        }

        // Step 3: Execute orchestrators with sliding window concurrency control
        if (orchestratorTasks.Any())
        {
            _logger.LogInformation(
                "Starting {OrchestratorCount} orchestrators with max concurrency of {MaxConcurrency}",
                orchestratorTasks.Count,
                MaxConcurrentOrchestrators);

            await ProcessTasksWithSlidingWindow(orchestratorTasks);
        }

        _logger.LogInformation(
            "StartBatchIngestionOrchestrator completed. Processed: {ProcessedCount}, Skipped: {SkippedCount}",
            processedDocumentsCount,
            skippedDocumentsCount);

        return new StartBatchIngestionOrchestratorOutput(processedDocumentsCount, skippedDocumentsCount);
    }

    // achieve a semaphore-like behaviour, but cannot use semaphores as it is indeterministic and cannot be used in azure durable functions
    private async Task ProcessTasksWithSlidingWindow(List<Task> allTasks)
    {
        var pendingTasks = new Queue<Task>(allTasks);
        var runningTasks = new List<Task>();
        var completedCount = 0;

        // Start initial batch of tasks up to maxConcurrency
        while (runningTasks.Count < MaxConcurrentOrchestrators && pendingTasks.Count > 0)
        {
            var task = pendingTasks.Dequeue();
            runningTasks.Add(task);
        }

        // Process remaining tasks using sliding window
        while (runningTasks.Count > 0)
        {
            // Wait for any task to complete
            var completedTask = await Task.WhenAny(runningTasks);
            runningTasks.Remove(completedTask);
            completedCount++;

            _logger.LogInformation(
                "Orchestrator completed. Progress: {CompletedCount}/{TotalCount}",
                completedCount,
                allTasks.Count);

            // Start the next task if available
            if (pendingTasks.Count > 0)
            {
                var nextTask = pendingTasks.Dequeue();
                runningTasks.Add(nextTask);
            }
        }

        _logger.LogInformation(
            "All {TotalCount} orchestrators completed",
            allTasks.Count);
    }
}