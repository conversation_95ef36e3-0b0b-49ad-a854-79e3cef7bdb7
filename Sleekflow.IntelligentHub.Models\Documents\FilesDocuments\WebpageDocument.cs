using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;

[Resolver(typeof(IIntelligentHubDbResolver))]
[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.FileDocument)]
public class WebpageDocument : KbDocument
{
    [JsonProperty("url")]
    public string Url { get; set; }

    [JsonProperty("title")]
    public string? Title { get; set; }

    [JsonProperty("character_count")]
    public int CharacterCount { get; set; }

    [JsonConstructor]
    public WebpageDocument(
        string id,
        string sleekflowCompanyId,
        string contentType,
        string url,
        string? title,
        int characterCount,
        Dictionary<string, object?> metadata,
        string fileDocumentProcessStatus,
        double? fileDocumentProcessPercentage,
        List<AgentAssignment> agentAssignments,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string uploadedBy,
        DebugTimestamps? debugTimestamps)
        : base(
            id,
            sleekflowCompanyId,
            contentType,
            metadata,
            fileDocumentProcessStatus,
            fileDocumentProcessPercentage,
            agentAssignments,
            createdAt,
            updatedAt,
            uploadedBy,
            debugTimestamps,
            SysTypeNames.WebpageDocument)
    {
        Url = url;
        Title = title;
        CharacterCount = characterCount;
    }

    public override int GetCharacterCount()
    {
        return CharacterCount;
    }
}