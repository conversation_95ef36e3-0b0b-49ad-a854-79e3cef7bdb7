using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.Models.Workers;

namespace Sleekflow.IntelligentHub.Documents;

public interface IKbDocumentService
{
    Task<KbDocument> GetDocumentAsync(
        string sleekflowCompanyId,
        string documentId);

    Task<KbDocument> PatchFileDocumentProcessStatusAsync(
        string sleekflowCompanyId,
        string documentId,
        string newStatus,
        double percentage);

    Task PatchFileDocumentAgentAssignmentRagStatusAsync(
        string sleekflowCompanyId,
        string documentId,
        string agentId,
        string ragStatus,
        double percentage);

    Task<string> GetFileDocumentProcessStatusAsync(
        string sleekflowCompanyId,
        string documentId);

    Task<List<KbDocument>> GetKbDocumentsByCompanyAsync(
        string sleekflowCompanyId,
        string? filterTrainingStatus = null,
        List<string>? filterExcludeTrainingStatus = null,
        string? filterAgent = null,
        string? filterExcludeAgent = null);

    Task<List<KbDocument>> SearchDocumentAsync(
        string sleekflowCompanyId,
        string searchInput);

    Task PatchAgentAssignmentsForAgentAsync(
        string sleekflowCompanyId,
        string agentId,
        List<string> documentIds);

    Task PatchAgentAssignmentsForDocumentAsync(
        string sleekflowCompanyId,
        string documentId,
        List<string> agentIds);

    Task RemoveAgentAssignmentFromAllDocuments(
        string sleekflowCompanyId,
        string agentId);

    Task UpdateDebugTimestampsAsync(
        string sleekflowCompanyId,
        string documentId,
        DateTimeOffset? conversionStarted,
        DateTimeOffset? conversionEnded,
        DateTimeOffset? uploadStarted,
        DateTimeOffset? uploadEnded);
}

public class KbDocumentService : IKbDocumentService, IScopedService
{
    private readonly IKbDocumentRepository _kbDocumentRepository;
    private readonly IKnowledgeBaseIngestionService _knowledgeBaseIngestionService;
    private readonly ICompanyAgentConfigService _companyAgentConfigService;
    private readonly IIntelligentHubCharacterCountService _intelligentHubCharacterCountService;
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;
    private readonly ILogger<KbDocumentService> _logger;

    public KbDocumentService(
        IKbDocumentRepository kbDocumentRepository,
        IKnowledgeBaseIngestionService knowledgeBaseIngestionService,
        ICompanyAgentConfigService companyAgentConfigService,
        IIntelligentHubCharacterCountService intelligentHubCharacterCountService,
        IIntelligentHubConfigService intelligentHubConfigService,
        IIntelligentHubUsageService intelligentHubUsageService,
        ILogger<KbDocumentService> logger)
    {
        _kbDocumentRepository = kbDocumentRepository;
        _knowledgeBaseIngestionService = knowledgeBaseIngestionService;
        _companyAgentConfigService = companyAgentConfigService;
        _intelligentHubCharacterCountService = intelligentHubCharacterCountService;
        _intelligentHubConfigService = intelligentHubConfigService;
        _intelligentHubUsageService = intelligentHubUsageService;
        _logger = logger;
    }

    public async Task<KbDocument> GetDocumentAsync(
        string sleekflowCompanyId,
        string documentId)
    {
        var document = await _kbDocumentRepository.GetAsync(documentId, sleekflowCompanyId);
        if (document == null)
        {
            throw new SfNotFoundObjectException(documentId, sleekflowCompanyId);
        }

        return document;
    }

    public async Task<KbDocument> PatchFileDocumentProcessStatusAsync(
        string sleekflowCompanyId,
        string documentId,
        string newStatus,
        double percentage)
    {
        return await _kbDocumentRepository.PatchFileDocumentProcessStatusAsync(
            sleekflowCompanyId,
            documentId,
            newStatus,
            percentage);
    }

    public async Task PatchFileDocumentAgentAssignmentRagStatusAsync(
        string sleekflowCompanyId,
        string documentId,
        string agentId,
        string ragStatus,
        double percentage)
    {
        var document = await _kbDocumentRepository.GetAsync(documentId, sleekflowCompanyId);
        if (document.AgentAssignments == null)
        {
            throw new Exception("Agent assignments not found");
        }

        var agentAssignment = document.AgentAssignments.First(agentAssignment => agentAssignment.AgentId == agentId);

        agentAssignment.RagStatus = ragStatus;
        agentAssignment.RagStatusUploadPercentage = percentage;

        await _kbDocumentRepository.PatchAgentAssignmentsAsync(
            sleekflowCompanyId,
            documentId,
            document.AgentAssignments);
    }

    public async Task<string> GetFileDocumentProcessStatusAsync(string sleekflowCompanyId, string documentId)
    {
        return await _kbDocumentRepository.GetFileDocumentProcessStatusAsync(
            sleekflowCompanyId,
            documentId);
    }

    public async Task<List<KbDocument>> GetKbDocumentsByCompanyAsync(
        string sleekflowCompanyId,
        string? filterTrainingStatus = null,
        List<string>? filterExcludeTrainingStatus = null,
        string? filterAgent = null,
        string? filterExcludeAgent = null)
    {
        // UI only use the new statues, we need to be backward compatible and respect the old statuses
        List<string> filterTrainingStatusList = [];
        List<string> filterExcludeTrainingStatusList = [];
        List<string> filterAgentList = [];
        List<string> filterExcludeAgentList = [];

        if (!string.IsNullOrEmpty(filterTrainingStatus))
        {
            filterTrainingStatusList.Add(filterTrainingStatus);
            filterTrainingStatusList.Add(ProcessFileDocumentStatuses.ToOldStatus(filterTrainingStatus));
        }

        if (filterExcludeTrainingStatus is { Count: > 0 })
        {
            foreach (var status in filterExcludeTrainingStatus)
            {
                filterExcludeTrainingStatusList.Add(status);
                filterExcludeTrainingStatusList.Add(ProcessFileDocumentStatuses.ToOldStatus(status));
            }
        }

        if (!string.IsNullOrEmpty(filterAgent))
        {
            filterAgentList.Add(filterAgent);
        }

        if (!string.IsNullOrEmpty(filterExcludeAgent))
        {
            filterExcludeAgentList.Add(filterExcludeAgent);
        }

        var results = await _kbDocumentRepository.GetKbDocumentsByCompanyAsync(
            sleekflowCompanyId,
            filterTrainingStatusList,
            filterExcludeTrainingStatusList,
            filterAgentList,
            filterExcludeAgentList);

        foreach (var document in results)
        {
            document.FileDocumentProcessStatus =
                ProcessFileDocumentStatuses.ToNewStatus(document.FileDocumentProcessStatus);
            document.AgentAssignments ??= [];
        }

        return results;
    }

    public async Task<List<KbDocument>> SearchDocumentAsync(
        string sleekflowCompanyId,
        string searchInput)
    {
        return await _kbDocumentRepository.SearchDocumentAsync(sleekflowCompanyId, searchInput);
    }

    public async Task PatchAgentAssignmentsForAgentAsync(
        string sleekflowCompanyId,
        string agentId,
        List<string> documentIds)
    {
        _logger.LogInformation(
            "Received request to update agent assignments for Agent {AgentId} in Company {CompanyId}",
            agentId,
            sleekflowCompanyId);

        // 1. Validate and get Agent Configuration to fetch the agent's name
        if (agentId != CompanyAgentTypes.SmartReply)
        {
            // Validate that the agent ID exists using the helper method
            await ValidateAgentIds(
                sleekflowCompanyId,
                new List<string>
                {
                    agentId
                });
        }

        // 2. Fetch documents CURRENTLY assigned to this agent for the company
        _logger.LogDebug("Fetching existing assignments for agent {AgentId}", agentId);
        var existingAssignedDocuments = await _kbDocumentRepository.GetObjectsAsync(
            d =>
                d.SleekflowCompanyId == sleekflowCompanyId &&
                d.AgentAssignments != null &&
                d.AgentAssignments.Any(a => a.AgentId == agentId));
        var existingAssignedDocumentIds = existingAssignedDocuments.Select(d => d.Id).ToHashSet();
        _logger.LogDebug(
            "Agent {AgentId} is currently assigned to {Count} documents.",
            agentId,
            existingAssignedDocumentIds.Count);

        // 3. Calculate differences (use set operations to calculate diff)
        var newDocumentIdsSet = new HashSet<string>(documentIds); // Target state
        var idsToAssign = newDocumentIdsSet.Except(existingAssignedDocumentIds).ToList();
        var idsToUnassign = existingAssignedDocumentIds.Except(newDocumentIdsSet).ToList();

        _logger.LogInformation(
            "Agent {AgentId}: Assignments to add: {AddCount}, Assignments to remove: {RemoveCount}",
            agentId,
            idsToAssign.Count,
            idsToUnassign.Count);

        if (!idsToAssign.Any() && !idsToUnassign.Any())
        {
            _logger.LogInformation("No assignment changes needed for agent {AgentId}.", agentId);
            return; // No changes required
        }

        // 4. Fetch documents to assign (only needed if there are documents to assign)
        var documentsToAssign = new List<KbDocument>();
        if (idsToAssign.Count > 0)
        {
            _logger.LogDebug(
                "Fetching {Count} documents to assign to agent {AgentId}.",
                idsToAssign.Count,
                agentId);
            documentsToAssign = await _kbDocumentRepository.GetObjectsAsync(
                d =>
                    d.SleekflowCompanyId == sleekflowCompanyId &&
                    idsToAssign.Contains(d.Id));
        }

        // 5. Get documents to unassign
        var documentsToUnassign = existingAssignedDocuments.Where(d => idsToUnassign.Contains(d.Id)).ToList();

        // Check character count limits for the agent
        if (agentId != CompanyAgentTypes.SmartReply)
        {
            var intelligentHubConfig =
                await _intelligentHubConfigService.GetIntelligentHubConfigAsync(sleekflowCompanyId);

            if (intelligentHubConfig == null)
            {
                throw new Exception("Intelligent Hub config not found");
            }

            // Check character count limits for each agent
            var characterCountLimit = _intelligentHubUsageService.GetFeatureTotalUsageLimit(
                intelligentHubConfig,
                PriceableFeatures.AiAgentsKnowledgeBaseTotalCharacterCount);
            var currentCharacterCount =
                await _intelligentHubCharacterCountService.GetCharacterCountForAgent(sleekflowCompanyId, agentId);
            var addingCharacterCount = documentsToAssign.Sum(doc => doc.GetCharacterCount());
            var deletingCharacterCount = documentsToUnassign.Sum(doc => doc.GetCharacterCount());

            var finalCharacterCount = currentCharacterCount + addingCharacterCount - deletingCharacterCount;

            if (finalCharacterCount > characterCountLimit)
            {
                throw new SfUserFriendlyException(
                    $"Agent {agentId} would exceed the character count limit of {characterCountLimit}. Count: {finalCharacterCount}");
            }
        }

        // 7. Perform the updates in a transaction through the repository
        await _kbDocumentRepository.ExecuteTransactionalBatchAsync(
            sleekflowCompanyId,
            transactionalBatch =>
            {
                // 7.1. Assign new documents
                if (documentsToAssign.Count > 0)
                {
                    foreach (var doc in documentsToAssign)
                    {
                        _logger.LogTrace(
                            "Preparing patch to add agent {AgentId} to document {DocumentId}",
                            agentId,
                            doc.Id);

                        // Create a copy of existing assignments and add the new agent
                        var updatedAssignments = new List<AgentAssignment>(doc.AgentAssignments ?? [])
                        {
                            new AgentAssignment(
                                agentId,
                                RagStatus.Pending,
                                0.0)
                        };

                        transactionalBatch.PatchItem(
                            doc.Id,
                            new List<PatchOperation>
                            {
                                PatchOperation.Set(
                                    $"/{KbDocument.PropertyNameAgentAssignments}",
                                    updatedAssignments),
                            });
                    }
                }

                // 7.2. Unassign documents
                if (documentsToUnassign.Count > 0)
                {
                    foreach (var doc in documentsToUnassign)
                    {
                        _logger.LogTrace(
                            "Preparing patch to remove agent {AgentId} from document {DocumentId}",
                            agentId,
                            doc.Id);

                        var updatedAssignments = doc.AgentAssignments?.Where(a => a.AgentId != agentId).ToList() ?? [];

                        transactionalBatch.PatchItem(
                            doc.Id,
                            new List<PatchOperation>
                            {
                                PatchOperation.Set(
                                    $"/{KbDocument.PropertyNameAgentAssignments}",
                                    updatedAssignments),
                            });
                    }
                }

                return Task.CompletedTask;
            });

        // 8. Trigger file ingestion
        if (documentsToAssign.Count > 0)
        {
            _logger.LogInformation(
                "Triggering file ingestion for {Count} newly assigned documents for agent {AgentId}",
                documentsToAssign.Count,
                agentId);

            await _knowledgeBaseIngestionService.StartKnowledgeBaseBatchIngestion(
                sleekflowCompanyId,
                documentsToAssign.Select(document => document.Id).ToArray());
        }

        _logger.LogInformation(
            "Successfully completed agent assignment update request for Agent {AgentId}",
            agentId);
    }

    public async Task PatchAgentAssignmentsForDocumentAsync(
        string sleekflowCompanyId,
        string documentId,
        List<string> agentIds)
    {
        _logger.LogInformation(
            "Received request to update agent assignments for Document {DocumentId} in Company {CompanyId}",
            documentId,
            sleekflowCompanyId);

        // Validate that agent IDs exist
        await ValidateAgentIds(sleekflowCompanyId, agentIds);

        // 1. Get the existing document and its agent assignments
        var document = await GetDocumentAsync(sleekflowCompanyId, documentId);
        var existingAssignments = document.AgentAssignments ?? [];
        var existingAgentIds = existingAssignments.Select(a => a.AgentId).ToHashSet();

        // 2. Calculate differences (use set operations to calculate diff)
        var newAgentIdsSet = new HashSet<string>(agentIds); // Target state
        var idsToAssign = newAgentIdsSet.Except(existingAgentIds).ToList();
        var idsToRemove = existingAgentIds.Except(newAgentIdsSet).ToList();

        _logger.LogInformation(
            "Document {DocumentId}: Assignments to add: {AddCount}, Assignments to remove: {RemoveCount}",
            documentId,
            idsToAssign.Count,
            idsToRemove.Count);

        if (!idsToAssign.Any() && !idsToRemove.Any())
        {
            _logger.LogInformation("No assignment changes needed for document {DocumentId}.", documentId);
            return; // No changes required
        }

        // 3. Build the final agent assignments list
        var updatedAssignments = new List<AgentAssignment>();

        // 3.1 Keep existing assignments that shouldn't be removed
        var assignmentsToKeep = existingAssignments.Where(a => !idsToRemove.Contains(a.AgentId)).ToList();
        updatedAssignments.AddRange(assignmentsToKeep);

        // 3.2 Add new assignments
        if (idsToAssign.Count > 0)
        {
            // Handle Smart Reply as a special case
            var smartReplyIds = idsToAssign.Where(id => id == CompanyAgentTypes.SmartReply).ToList();
            var otherAgentIds = idsToAssign.Where(id => id != CompanyAgentTypes.SmartReply).ToList();

            // Add Smart Reply agent assignments directly without fetching configs
            if (smartReplyIds.Count > 0)
            {
                updatedAssignments.Add(
                    new AgentAssignment(
                        CompanyAgentTypes.SmartReply,
                        RagStatus.Pending,
                        0.0));

                _logger.LogTrace(
                    "Added special Smart Reply agent to document {DocumentId}",
                    documentId);
            }

            // For non-special agents, fetch their configs as usual
            if (otherAgentIds.Count > 0)
            {
                // Fetch agent configs for new assignments
                var agentConfigTasks =
                    otherAgentIds.Select(id => _companyAgentConfigService.GetOrDefaultAsync(id, sleekflowCompanyId));
                var agentConfigs = await Task.WhenAll(agentConfigTasks);

                // Create and add new agent assignments
                var newAssignments = agentConfigs
                    .Where(config => config != null) // Filter out null configs (invalid IDs)
                    .Select(config => new AgentAssignment(config!.Id, RagStatus.Pending, 0.0))
                    .ToList();

                updatedAssignments.AddRange(newAssignments);
            }
        }

        var intelligentHubConfig = await _intelligentHubConfigService.GetIntelligentHubConfigAsync(sleekflowCompanyId);

        if (intelligentHubConfig == null)
        {
            throw new Exception("Intelligent Hub config not found");
        }

        var characterCountLimit = _intelligentHubUsageService.GetFeatureTotalUsageLimit(
            intelligentHubConfig,
            PriceableFeatures.AiAgentsKnowledgeBaseTotalCharacterCount);

        foreach (var agentId in idsToAssign.Where(id => id != CompanyAgentTypes.SmartReply))
        {
            // Check character count limits for each agent
            var currentCharacterCount =
                await _intelligentHubCharacterCountService.GetCharacterCountForAgent(sleekflowCompanyId, agentId);

            var finalCharacterCount = currentCharacterCount + document.GetCharacterCount();

            if (finalCharacterCount > characterCountLimit)
            {
                throw new SfUserFriendlyException(
                    $"Agent {agentId} would exceed the character count limit of {characterCountLimit}. Count: {finalCharacterCount}");
            }
        }

        _logger.LogDebug("Updating document with {Count} agent assignments", updatedAssignments.Count);

        // 4. Update the document with the updated agent assignments list
        await _kbDocumentRepository.PatchAgentAssignmentsAsync(
            sleekflowCompanyId,
            documentId,
            updatedAssignments);

        // 5. Trigger file ingestion
        if (idsToAssign.Count > 0)
        {
            await _knowledgeBaseIngestionService.StartKnowledgeBaseIngestion(
                sleekflowCompanyId,
                documentId);
        }

        _logger.LogInformation(
            "Successfully completed document agent assignment update request for Document {DocumentId}",
            documentId);
    }

    private async Task ValidateAgentIds(string sleekflowCompanyId, List<string> agentIds)
    {
        // Filter out SmartReply which is a special case
        var regularAgentIds = agentIds.Where(id => id != CompanyAgentTypes.SmartReply).ToList();

        if (!regularAgentIds.Any())
        {
            return; // No regular agent IDs to validate
        }

        // Fetch agent configs concurrently to validate they exist
        var agentConfigTasks =
            regularAgentIds.Select(id => _companyAgentConfigService.GetOrDefaultAsync(id, sleekflowCompanyId));
        var agentConfigs = await Task.WhenAll(agentConfigTasks);

        // Check for invalid agent IDs
        var invalidAgentIds = new List<string>();
        for (int i = 0; i < regularAgentIds.Count; i++)
        {
            if (agentConfigs[i] == null)
            {
                invalidAgentIds.Add(regularAgentIds[i]);
            }
        }

        if (invalidAgentIds.Any())
        {
            throw new SfNotFoundObjectException(
                $"The following agent IDs do not exist: {string.Join(", ", invalidAgentIds)}");
        }
    }

    public async Task RemoveAgentAssignmentFromAllDocuments(
        string sleekflowCompanyId,
        string agentId)
    {
        _logger.LogInformation(
            "Received request to remove agent {AgentId} from all documents in Company {CompanyId}",
            agentId,
            sleekflowCompanyId);

        // 1. Fetch all documents that currently have this agent assigned
        _logger.LogDebug("Fetching all documents assigned to agent {AgentId}", agentId);
        var documentsWithAgent = await _kbDocumentRepository.GetObjectsAsync(
            d =>
                d.SleekflowCompanyId == sleekflowCompanyId &&
                d.AgentAssignments != null &&
                d.AgentAssignments.Any(a => a.AgentId == agentId));

        if (!documentsWithAgent.Any())
        {
            _logger.LogInformation(
                "No documents found with agent {AgentId} assignments in company {CompanyId}",
                agentId,
                sleekflowCompanyId);
            return;
        }

        _logger.LogInformation(
            "Found {Count} documents with agent {AgentId} assignments to remove",
            documentsWithAgent.Count,
            agentId);

        // 2. Remove the agent assignment from all documents in a transaction
        await _kbDocumentRepository.ExecuteTransactionalBatchAsync(
            sleekflowCompanyId,
            transactionalBatch =>
            {
                foreach (var document in documentsWithAgent)
                {
                    _logger.LogTrace(
                        "Preparing patch to remove agent {AgentId} from document {DocumentId}",
                        agentId,
                        document.Id);

                    // Remove the agent from the document's agent assignments
                    var updatedAssignments = document.AgentAssignments?
                        .Where(a => a.AgentId != agentId)
                        .ToList() ?? [];

                    transactionalBatch.PatchItem(
                        document.Id,
                        new List<PatchOperation>
                        {
                            PatchOperation.Set(
                                $"/{KbDocument.PropertyNameAgentAssignments}",
                                updatedAssignments),
                        });
                }

                return Task.CompletedTask;
            });

        _logger.LogInformation(
            "Successfully removed agent {AgentId} from {Count} documents in company {CompanyId}",
            agentId,
            documentsWithAgent.Count,
            sleekflowCompanyId);
    }

    public async Task UpdateDebugTimestampsAsync(
        string sleekflowCompanyId,
        string documentId,
        DateTimeOffset? conversionStarted,
        DateTimeOffset? conversionEnded,
        DateTimeOffset? uploadStarted,
        DateTimeOffset? uploadEnded)
    {
        await _kbDocumentRepository.UpdateDebugTimestampsAsync(
            sleekflowCompanyId,
            documentId,
            conversionStarted,
            conversionEnded,
            uploadStarted,
            uploadEnded);
    }
}