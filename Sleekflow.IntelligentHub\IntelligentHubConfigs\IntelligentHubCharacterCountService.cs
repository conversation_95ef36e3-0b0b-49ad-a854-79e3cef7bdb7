using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Models.Workers;

namespace Sleekflow.IntelligentHub.IntelligentHubConfigs;

public interface IIntelligentHubCharacterCountService
{
    Task<int> GetCharacterCountForAgent(string companyId, string agentId);
}

public class IntelligentHubCharacterCountService : IIntelligentHubCharacterCountService, IScopedService
{
    private readonly IKbDocumentRepository _kbDocumentRepository;
    private readonly ILogger<IntelligentHubCharacterCountService> _logger;

    public IntelligentHubCharacterCountService(
        IKbDocumentRepository kbDocumentRepository,
        ILogger<IntelligentHubCharacterCountService> logger)
    {
        _kbDocumentRepository = kbDocumentRepository;
        _logger = logger;
    }

    public async Task<int> GetCharacterCountForAgent(string companyId, string agentId)
    {
        var documents = await _kbDocumentRepository.GetObjectsAsync(
            d =>
                d.SleekflowCompanyId == companyId &&
                d.AgentAssignments != null &&
                d.AgentAssignments.Any(a => a.AgentId == agentId));

        var characterCount = documents
            .Where(
                document => document.FileDocumentProcessStatus != ProcessFileDocumentStatuses.Failed &&
                            document.FileDocumentProcessStatus != ProcessFileDocumentStatuses.FailedToConvert)
            .Sum(document => document.GetCharacterCount());

        return characterCount;
    }
}