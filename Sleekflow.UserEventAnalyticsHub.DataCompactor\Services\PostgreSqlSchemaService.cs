using Microsoft.Extensions.Logging;
using Npgsql;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Configs;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Utils;

namespace Sleekflow.UserEventAnalyticsHub.DataCompactor.Services;

public class PostgreSqlSchemaService : IPostgreSqlSchemaService
{
    private readonly ILogger<PostgreSqlSchemaService> _logger;
    private readonly IPostgreSqlConfig _config;

    public PostgreSqlSchemaService(ILogger<PostgreSqlSchemaService> logger, IPostgreSqlConfig config)
    {
        _logger = logger;
        _config = config;
    }

    public async Task EnsureCompanyTablesExistAsync(string sleekflowCompanyId)
    {
        try
        {
            _logger.LogInformation("Ensuring tables exist for company: {CompanyId}", sleekflowCompanyId);

            await using var connection = new NpgsqlConnection(_config.ConnectionString);
            await connection.OpenAsync();

            // Create events table
            await CreateEventsTableAsync(connection, sleekflowCompanyId);

            // Create migration history table
            await CreateMigrationHistoryTableAsync(connection, sleekflowCompanyId);

            // Create indexes
            await CreateIndexesAsync(sleekflowCompanyId);

            _logger.LogInformation("Tables and indexes created successfully for company: {CompanyId}", sleekflowCompanyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ensuring tables exist for company: {CompanyId}", sleekflowCompanyId);
            throw;
        }
    }

    public async Task<bool> ValidateTableSchemaAsync(string sleekflowCompanyId)
    {
        try
        {
            var eventsTableName = PostgreSqlTableNameHelper.GetEventsTableName(sleekflowCompanyId);
            var historyTableName = PostgreSqlTableNameHelper.GetMigrationHistoryTableName(sleekflowCompanyId);

            var eventsTableExists = await TableExistsAsync(eventsTableName);
            var historyTableExists = await TableExistsAsync(historyTableName);

            var isValid = eventsTableExists && historyTableExists;

            _logger.LogInformation("Schema validation for company {CompanyId}: Events table exists: {EventsExists}, History table exists: {HistoryExists}",
                sleekflowCompanyId, eventsTableExists, historyTableExists);

            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating table schema for company: {CompanyId}", sleekflowCompanyId);
            return false;
        }
    }

    public async Task CreateIndexesAsync(string sleekflowCompanyId)
    {
        try
        {
            await using var connection = new NpgsqlConnection(_config.ConnectionString);
            await connection.OpenAsync();

            var eventsTableName = PostgreSqlTableNameHelper.GetEventsTableName(sleekflowCompanyId);
            var historyTableName = PostgreSqlTableNameHelper.GetMigrationHistoryTableName(sleekflowCompanyId);

            // Create indexes for events table
            var eventsIndexes = new[]
            {
                ($"timestamp", $"CREATE INDEX IF NOT EXISTS {PostgreSqlTableNameHelper.GetEventsTableIndexName(sleekflowCompanyId, "timestamp")} ON {eventsTableName} (timestamp);"),
                ($"eventtype", $"CREATE INDEX IF NOT EXISTS {PostgreSqlTableNameHelper.GetEventsTableIndexName(sleekflowCompanyId, "eventtype")} ON {eventsTableName} (eventType);"),
                ($"objectid", $"CREATE INDEX IF NOT EXISTS {PostgreSqlTableNameHelper.GetEventsTableIndexName(sleekflowCompanyId, "objectid")} ON {eventsTableName} (objectId);"),
                ($"partitions", $"CREATE INDEX IF NOT EXISTS {PostgreSqlTableNameHelper.GetEventsTableIndexName(sleekflowCompanyId, "partitions")} ON {eventsTableName} (year, month, day, hour);")
            };

            foreach (var (indexName, sql) in eventsIndexes)
            {
                await using var command = new NpgsqlCommand(sql, connection);
                await command.ExecuteNonQueryAsync();
                _logger.LogDebug("Created index {IndexName} for company {CompanyId}", indexName, sleekflowCompanyId);
            }

            // Create indexes for migration history table
            var historyIndexes = new[]
            {
                ($"status", $"CREATE INDEX IF NOT EXISTS {PostgreSqlTableNameHelper.GetMigrationHistoryIndexName(sleekflowCompanyId, "status")} ON {historyTableName} (migration_status);"),
                ($"blob_path", $"CREATE INDEX IF NOT EXISTS {PostgreSqlTableNameHelper.GetMigrationHistoryIndexName(sleekflowCompanyId, "blob_path")} ON {historyTableName} (blob_path);")
            };

            foreach (var (indexName, sql) in historyIndexes)
            {
                await using var command = new NpgsqlCommand(sql, connection);
                await command.ExecuteNonQueryAsync();
                _logger.LogDebug("Created index {IndexName} for company {CompanyId}", indexName, sleekflowCompanyId);
            }

            _logger.LogInformation("All indexes created successfully for company: {CompanyId}", sleekflowCompanyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating indexes for company: {CompanyId}", sleekflowCompanyId);
            throw;
        }
    }

    public async Task<bool> TableExistsAsync(string tableName)
    {
        try
        {
            await using var connection = new NpgsqlConnection(_config.ConnectionString);
            await connection.OpenAsync();

            var sql = """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = @schema
                    AND table_name = @tableName
                );
                """;

            await using var command = new NpgsqlCommand(sql, connection);
            command.Parameters.AddWithValue("@schema", _config.SchemaName);
            command.Parameters.AddWithValue("@tableName", tableName);

            var result = await command.ExecuteScalarAsync();
            return Convert.ToBoolean(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if table exists: {TableName}", tableName);
            return false;
        }
    }

    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            await using var connection = new NpgsqlConnection(_config.ConnectionString);
            await connection.OpenAsync();

            await using var command = new NpgsqlCommand("SELECT 1", connection);
            var result = await command.ExecuteScalarAsync();

            _logger.LogInformation("PostgreSQL connection test successful");
            return Convert.ToInt32(result) == 1;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PostgreSQL connection test failed");
            return false;
        }
    }

    public async Task EnsureDatabaseExistsAsync()
    {
        try
        {
            // Parse connection string to get database name
            var connectionStringBuilder = new NpgsqlConnectionStringBuilder(_config.ConnectionString);
            var databaseName = connectionStringBuilder.Database;

            // Create connection string without database name
            connectionStringBuilder.Database = "postgres"; // Connect to default database
            var masterConnectionString = connectionStringBuilder.ToString();

            await using var connection = new NpgsqlConnection(masterConnectionString);
            await connection.OpenAsync();

            // Check if database exists
            var checkSql = "SELECT 1 FROM pg_database WHERE datname = @databaseName";
            await using var checkCommand = new NpgsqlCommand(checkSql, connection);
            checkCommand.Parameters.AddWithValue("@databaseName", databaseName);

            var exists = await checkCommand.ExecuteScalarAsync();

            if (exists == null)
            {
                // Create database
                var createSql = $"CREATE DATABASE \"{databaseName}\"";
                await using var createCommand = new NpgsqlCommand(createSql, connection);
                await createCommand.ExecuteNonQueryAsync();

                _logger.LogInformation("Database created: {DatabaseName}", databaseName);
            }
            else
            {
                _logger.LogDebug("Database already exists: {DatabaseName}", databaseName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ensuring database exists");
            throw;
        }
    }

    private async Task CreateEventsTableAsync(NpgsqlConnection connection, string sleekflowCompanyId)
    {
        var tableName = PostgreSqlTableNameHelper.GetEventsTableName(sleekflowCompanyId);

        var sql = $"""
            CREATE TABLE IF NOT EXISTS {tableName} (
                id VARCHAR PRIMARY KEY,
                eventType VARCHAR,
                sleekflowCompanyId VARCHAR NOT NULL,
                objectId VARCHAR,
                objectType VARCHAR,
                source VARCHAR,
                properties JSONB,
                metadata JSONB,
                timestamp BIGINT NOT NULL,
                year INTEGER NOT NULL,
                month INTEGER NOT NULL,
                day INTEGER NOT NULL,
                hour INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT NOW()
            );
            """;

        await using var command = new NpgsqlCommand(sql, connection);
        await command.ExecuteNonQueryAsync();

        _logger.LogDebug("Created events table: {TableName}", tableName);
    }

    private async Task CreateMigrationHistoryTableAsync(NpgsqlConnection connection, string sleekflowCompanyId)
    {
        var tableName = PostgreSqlTableNameHelper.GetMigrationHistoryTableName(sleekflowCompanyId);

        var sql = $"""
            CREATE TABLE IF NOT EXISTS {tableName} (
                id SERIAL PRIMARY KEY,
                blob_path VARCHAR NOT NULL UNIQUE,
                blob_last_modified TIMESTAMP NOT NULL,
                blob_size_bytes BIGINT NOT NULL,
                records_count INTEGER NOT NULL,
                migration_started_at TIMESTAMP NOT NULL,
                migration_completed_at TIMESTAMP,
                migration_status VARCHAR(20) NOT NULL DEFAULT 'pending',
                error_message TEXT,
                retry_count INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT NOW()
            );
            """;

        await using var command = new NpgsqlCommand(sql, connection);
        await command.ExecuteNonQueryAsync();

        _logger.LogDebug("Created migration history table: {TableName}", tableName);
    }

    public async Task TestConnectivityAsync()
    {
        try
        {
            _logger.LogInformation("Testing PostgreSQL connectivity...");

            var connected = await TestConnectionAsync();
            if (!connected)
            {
                throw new InvalidOperationException("PostgreSQL connection test failed");
            }

            _logger.LogInformation("PostgreSQL connectivity test passed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PostgreSQL connectivity test failed");
            throw;
        }
    }

    public async Task EnsureCompanySchemaAsync(string sleekflowCompanyId)
    {
        try
        {
            _logger.LogInformation("Ensuring schema exists for company: {CompanyId}", sleekflowCompanyId);

            await EnsureCompanyTablesExistAsync(sleekflowCompanyId);
            await CreateIndexesAsync(sleekflowCompanyId);

            _logger.LogInformation("Schema ensured for company: {CompanyId}", sleekflowCompanyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ensuring schema for company: {CompanyId}", sleekflowCompanyId);
            throw;
        }
    }
}