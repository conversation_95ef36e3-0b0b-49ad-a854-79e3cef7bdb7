﻿using System.ComponentModel.DataAnnotations;
using System.Text;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.CrmHub.Workers.Configs;
using Sleekflow.CrmHub.Workers.Subscriptions;
using Sleekflow.Exceptions;
using Sleekflow.JsonConfigs;
using Sleekflow.Outputs;
using Sleekflow.Utils;

namespace Sleekflow.CrmHub.Workers.Triggers.Zoho;

public class SubscriptionsCheckBatch
{
    private readonly IAppConfig _appConfig;
    private readonly HttpClient _httpClient;
    private readonly IZohoSubscriptionRepository _zohoSubscriptionRepository;

    public SubscriptionsCheckBatch(
        IHttpClientFactory httpClientFactory,
        IAppConfig appConfig,
        IZohoSubscriptionRepository zohoSubscriptionRepository)
    {
        _appConfig = appConfig;
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _httpClient.Timeout = TimeSpan.FromMinutes(10);
        _zohoSubscriptionRepository = zohoSubscriptionRepository;
    }

    public class SubscriptionsCheckBatchInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("subscription")]
        [Required]
        public ZohoSubscription Subscription { get; set; }

        [JsonProperty("last_object_modification_time")]
        [Required]
        public DateTimeOffset LastObjectModificationTime { get; }

        [JsonProperty("next_page")]
        public int? NextPage { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckBatchInput(
            string sleekflowCompanyId,
            ZohoSubscription subscription,
            DateTimeOffset lastObjectModificationTime,
            int? nextPage = null)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Subscription = subscription;
            LastObjectModificationTime = lastObjectModificationTime;
            NextPage = nextPage;
        }
    }

    public class SubscriptionsCheckBatchOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("next_last_object_modification_time")]
        public DateTimeOffset NextLastObjectModificationTime { get; }

        [JsonProperty("next_page")]
        public int? NextPage { get; }

        [JsonConstructor]
        public SubscriptionsCheckBatchOutput(
            long count,
            DateTimeOffset nextLastObjectModificationTime,
            int? nextPage)
        {
            Count = count;
            NextLastObjectModificationTime = nextLastObjectModificationTime;
            NextPage = nextPage;
        }
    }

    /// <seealso cref="Sleekflow.Integrator.Zoho.Triggers.Internals.SubscriptionsCheckBatch"/>
    [Function("Zoho_SubscriptionsCheck_Batch")]
    public async Task<SubscriptionsCheckBatchOutput> Batch(
        [ActivityTrigger]
        SubscriptionsCheckBatchInput subscriptionsCheckBatchInput)
    {
        var inputJsonStr =
            JsonConvert.SerializeObject(subscriptionsCheckBatchInput, JsonConfig.DefaultJsonSerializerSettings);

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(inputJsonStr, Encoding.UTF8, "application/json"),
            RequestUri = new Uri(_appConfig.ZohoIntegratorInternalsEndpoint + "/SubscriptionsCheckBatch"),
            Headers =
            {
                {
                    "X-Sleekflow-Key", _appConfig.InternalsKey
                }
            },
        };
        var resMsg = await _httpClient.SendAsync(requestMessage);
        if (!resMsg.IsSuccessStatusCode)
        {
            await HandleExceptionalSubscriptionsCheckEnd(subscriptionsCheckBatchInput.Subscription);
        }

        var resStr = await resMsg.Content.ReadAsStringAsync();

        var output = resStr.ToObject<Output<dynamic>>();
        if (output == null)
        {
            await HandleExceptionalSubscriptionsCheckEnd(subscriptionsCheckBatchInput.Subscription);

            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        if (output.Success == false)
        {
            await HandleExceptionalSubscriptionsCheckEnd(subscriptionsCheckBatchInput.Subscription);

            throw new ErrorCodeException(output);
        }

        return ((JObject) output.Data).ToObject<SubscriptionsCheckBatchOutput>()!;
    }

    public async Task HandleExceptionalSubscriptionsCheckEnd(
        ZohoSubscription subscription)
    {
        await _zohoSubscriptionRepository.PatchAsync(
            subscription.Id,
            subscription.SleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace(
                    $"/{ZohoSubscription.PropertyNameDurablePayload}",
                    (HttpManagementPayload?)null)
            });
    }
}