# Security Practices

This document is part of [Sleekflow Project Practices](ProjectPractices.md).

## Authentication

- **Auth0** is used as the Identity-as-a-Service provider
- Token validation is performed at the API Gateway level
- Internal services use service-to-service authentication

## Configuration Security

1. **Azure App Configuration** - Manages configuration settings
2. **Azure Key Vault** - Secures credentials and secrets

## API Gateway Security

Sleekflow uses KrakenD as an API Gateway for securing access to microservices. The gateway handles several critical security functions:

### Gateway Architecture

The system implements a tiered gateway approach:

1. **External API Gateway (KrakenD)** - Primary entry point for all external requests
2. **Internal Gateway** - For service-to-service communication within the system
3. **Global Gateway** - For globally accessible services (primarily tenant management)

### Authentication Mechanisms

The KrakenD configuration supports multiple authentication patterns:

1. **JWT Validation**
   - Auth0 tokens are validated with RS256 algorithm
   - JWT claims are propagated to internal services via custom headers:
     ```json
     "PropagateClaims": [
       ["https://app.sleekflow.io/email", "X-Sleekflow-Email"],
       ["https://app.sleekflow.io/user_id", "X-Sleekflow-User-Id"],
       ["https://app.sleekflow.io/tenanthub_user_id", "X-Sleekflow-TenantHub-User-Id"],
       ["https://app.sleekflow.io/connection_strategy", "X-Sleekflow-Connection-Strategy"]
     ]
     ```

2. **API Key Authentication**
   - Public APIs use API keys for authentication
   - Different services have dedicated API keys (e.g., MESSAGING_HUB_KEY, FLOW_HUB_KEY)

3. **Service-to-Service Authentication**
   - Backend services communicate using specific authentication keys
   - Internal endpoints have restricted access via environment-specific keys

### Rate Limiting

To protect against denial of service attacks and abuse:

1. **IP-based rate limiting**
   ```json
   "QosRatelimitRouter": {
     "MaxRate": 100,
     "ClientMaxRate": 5,
     "Strategy": "ip",
     "Key": "X-Azure-ClientIP"
   }
   ```

2. **Token-based rate limiting**
   ```json
   "QosRatelimitRouter": {
     "Capacity": 1000,
     "ClientCapacity": 2,
     "ClientMaxRate": 1,
     "Every": "10s",
     "Key": "Authorization",
     "Strategy": "header"
   }
   ```

### Endpoint Security Patterns

Different endpoints implement various security patterns:

1. **Managed Endpoints** - Internal endpoints with API key validation
2. **Auth0 Managed Endpoints** - Endpoints requiring full JWT authentication
3. **Public API Gateway Endpoints** - External endpoints with various authentication types
4. **Webhook Endpoints** - Special endpoints for external integrations with signature validation

### Lua Script Protection

For additional security customization, Lua scripts are used to:

1. Validate custom headers and claims
2. Transform requests before they reach services
3. Apply custom security logic to specific endpoints

Example:
```json
"ModifierLuaEndpoint": {
  "Sources": ["lua/pre_endpoint.lua"],
  "Pre": "pre_endpoint('{{ env \"INTERNAL_INTEGRATION_HUB_NETSUITE_KEY\" }}')",
  "Live": false,
  "AllowOpenLibs": false
}
```

## Trigger Security Filters

In Sleekflow architecture, security is enforced at multiple layers. Sleekflow implements a custom `ITrigger` pattern that abstracts away the transport mechanism (HTTP, gRPC, or message bus), providing a more flexible approach than standard ASP.NET MVC controllers. Several specialized filter classes ensure appropriate authorization for these triggers. These filters are implemented in the `IFuncFilter` interface hierarchy and applied as attributes to trigger classes.

### The ITrigger Pattern

Unlike traditional ASP.NET MVC with controllers, Sleekflow uses the `ITrigger<TCommand, TResult>` pattern:

```csharp
public interface ITrigger<in TCommand, TResult>
{
    Task<TResult> ExecuteAsync(TCommand command);
}
```

This abstraction allows the same business logic to be exposed through different transport mechanisms:
- HTTP REST endpoints
- gRPC services
- Message bus consumers

Security filters are applied at the trigger level, ensuring consistent authorization regardless of how the trigger is invoked.

### Available Authorization Filters

The platform provides several authorization filters defined in various projects:

#### 1. HeadersAuthorizationFuncFilter

Located in `Sleekflow.Mvc.Func.Abstractions` namespace, this is the base authorization filter that:
- Extracts authentication information from HTTP headers
- Populates the `ISleekflowAuthorizationContext` with user, company, and role information
- Checks for basic required headers (staff ID and company ID)
- Handles impersonation scenarios

```csharp
// In Sleekflow.Mvc.Constants.AuthorizationFilterNames
public const string HeadersAuthorizationFuncFilter = "Sleekflow.Mvc.Func.Abstractions.IHeadersAuthorizationFuncFilter";

// Usage in a trigger class
[FuncFilter(
    AuthorizationFilterNames.HeadersAuthorizationFuncFilter)]
public class GetUserInfo : ITrigger<GetUserInfoCommand, UserInfoDto>
{
    private readonly ISleekflowAuthorizationContext _authContext;

    public GetUserInfo(ISleekflowAuthorizationContext authContext)
    {
        _authContext = authContext;
    }

    public async Task<UserInfoDto> ExecuteAsync(GetUserInfoCommand command)
    {
        // Access authorized user information
        var userId = _authContext.SleekflowUserId;

        // Implementation...
        return new UserInfoDto { /* ... */ };
    }
}
```

#### 2. Role-Based Filters

The TenantHub provides specialized filters for different role types:

```csharp
// In Sleekflow.TenantHub.Models.Constants.FilterNames
public const string SleekflowAdminFuncFilter = "Sleekflow.TenantHub.Func.ISleekflowAdminFuncFilter";
public const string InternalCmsUserFuncFilter = "Sleekflow.TenantHub.Func.IInternalCmsUserFuncFilter";
public const string InternalCmsSuperUserFuncFilter = "Sleekflow.TenantHub.Func.IInternalCmsSuperUserFuncFilter";
public const string SleekflowTeamAdminFuncFilter = "Sleekflow.TenantHub.Func.ISleekflowTeamAdminFuncFilter";
public const string SleekflowStaffFuncFilter = "Sleekflow.TenantHub.Func.ISleekflowStaffFuncFilter";
```

**SleekflowAdminFuncFilter**:
Ensures the user has Admin or SuperAdmin role when RBAC is not enabled.

```csharp
// Usage example combining multiple filters
[FuncFilter(
    AuthorizationFilterNames.HeadersAuthorizationFuncFilter,
    FilterNames.SleekflowAdminFuncFilter)]
public class CreateRole : ITrigger<CreateRoleCommand, RoleDto>
{
    // Implementation
}
```

#### 3. Registration Filter

For endpoints related to user registration and onboarding:

```csharp
// In Sleekflow.TenantHub.Models.Constants.FilterNames
public const string SleekflowRegistrationFuncFilter = "Sleekflow.TenantHub.Func.ISleekflowRegistrationFuncFilter";

// Usage example
[FuncFilter(
    FilterNames.SleekflowRegistrationFuncFilter)]
public class RegisterCompany : ITrigger<RegisterCompanyCommand, RegisterCompanyDto>
{
    // Implementation
}
```

### Implementing Security in Triggers

When creating new triggers, follow these guidelines:

1. **Choose the appropriate filter combination**:
   ```csharp
   // For basic authenticated endpoints
   [FuncFilter(AuthorizationFilterNames.HeadersAuthorizationFuncFilter)]

   // For admin-only endpoints
   [FuncFilter(
       AuthorizationFilterNames.HeadersAuthorizationFuncFilter,
       FilterNames.SleekflowAdminFuncFilter)]

   // For CMS user endpoints
   [FuncFilter(
       AuthorizationFilterNames.HeadersAuthorizationFuncFilter,
       FilterNames.InternalCmsUserFuncFilter)]
   ```

2. **Access the authorization context in your trigger**:
   ```csharp
   public class CreateSomething : ITrigger<CreateSomethingCommand, SomethingDto>
   {
       private readonly ISleekflowAuthorizationContext _authContext;

       public CreateSomething(ISleekflowAuthorizationContext authContext)
       {
           _authContext = authContext;
       }

       public async Task<SomethingDto> ExecuteAsync(CreateSomethingCommand command)
       {
           // Access user information
           var userId = _authContext.SleekflowUserId;
           var companyId = _authContext.SleekflowCompanyId;
           var roles = _authContext.SleekflowRoles;

           // Add custom authorization logic if needed
           if (command.SensitiveOperation && !roles.Contains("Admin"))
           {
               throw new SfUnauthorizedException("Admin role required for this operation");
           }

           // Process the command...
           return new SomethingDto { /* ... */ };
       }
   }
   ```

3. **Creating custom filters for specialized authorization**:
   ```csharp
   // Define interface
   public interface IMyCustomFuncFilter : IFuncFilter
   {
   }

   // Implement filter with custom logic
   public class MyCustomFuncFilter : IMyCustomFuncFilter, IScopedService
   {
       private readonly ISleekflowAuthorizationContext _authContext;
       private readonly IMyDomainService _domainService;

       public MyCustomFuncFilter(
           ISleekflowAuthorizationContext authContext,
           IMyDomainService domainService)
       {
           _authContext = authContext;
           _domainService = domainService;
       }

       public async Task FilterAsync(HttpRequest httpRequest)
       {
           // Get request-specific data
           var resourceId = httpRequest.Query["resourceId"].ToString();

           // Perform domain-specific authorization
           var hasAccess = await _domainService.CanUserAccessResourceAsync(
               _authContext.SleekflowUserId,
               resourceId);

           if (!hasAccess)
           {
               throw new SfUnauthorizedException();
           }
       }
   }
   ```

### Benefits of the ITrigger Pattern for Security

The `ITrigger` pattern provides several security advantages:

1. **Transport Agnostic Security**: The same security filters apply regardless of how the trigger is accessed (HTTP, gRPC, message bus).

2. **Separation of Concerns**: Business logic is cleanly separated from the transport mechanism, making security easier to implement and test.

3. **Reusable Security Logic**: Filters can be applied consistently across different transport types.

4. **Future-Proofing**: Easily switch between transport mechanisms without rewriting security logic.

### Best Practices for Trigger Security

1. **Layer your authorization**:
   - Use API Gateway for initial authentication
   - Apply trigger filters for role-based access control
   - Implement domain-specific authorization in your business logic

2. **Never skip the HeadersAuthorizationFuncFilter**:
   - It provides the basic security infrastructure
   - All other filters depend on the context it creates

3. **Choose the most restrictive applicable filter**:
   - For admin features, always use SleekflowAdminFuncFilter
   - For internal management, use the appropriate CMS filter

4. **Handle errors properly**:
   - Always throw SfUnauthorizedException for permission issues
   - Don't leak information about why authorization failed

5. **Test all authorization paths**:
   - Ensure endpoints require proper authentication
   - Verify role-based access controls work as expected

## Service Deployment Security

Sleekflow services are deployed with security considerations:

1. **Microservice Isolation**
   - Each hub is deployed as a separate service with its own authentication
   - Services like `tenant-hub`, `messaging-hub`, and `intelligent-hub` are isolated

2. **Layered Access Control**
   - Public endpoints have stricter authentication
   - Management endpoints require additional authorization
   - Internal endpoints are not exposed to external traffic

3. **Regional Deployment**
   - Services are deployed in multiple configurations:
     - Regional services (standard microservices)
     - Global services (tenant management, authentication)
     - Internal services (for system components)

## WebSocket Security

For real-time communication:

1. **SignalR Authentication**
   - Negotiation is protected with the same JWT authentication
   - Connection tokens are validated
   - Rate limiting is applied to connection attempts

## Webhook Validation

For external integrations (Facebook, WhatsApp, Stripe, etc.):

1. **Signature Verification**
   - Headers like `Stripe-Signature` and `X-HubSpot-Signature` are validated
   - Requests without valid signatures are rejected

2. **Challenge-Response Authentication**
   - Services like WhatsApp use challenge-response for webhook verification
   ```
   "InputQueryStrings": [
     "hub.mode",
     "hub.challenge",
     "hub.verify_token"
   ]
   ```

## Security Implementation Guidelines for Engineers

When implementing security features in Sleekflow, follow these guidelines to ensure consistent and robust security practices.

### Working with JWT Authentication

The system configures Auth0 JWT validation in KrakenD configuration (`Sleekflow.KrakenD.Generator/Program.cs`):

```csharp
// From Sleekflow.KrakenD.Generator/Program.cs
"AuthValidator": new JwtValidator
{
    Alg = Algorithm.Rs256,
    Audience = new[]
    {
        "AUTH_0_AUDIENCE"
    }.Select(a => "{{ env \"" + a + "\" }}").ToArray(),
    JwkUrl = "{{ env \"" + "AUTH_0_JWK_URL" + "\" }}",
    Cache = true,
    PropagateClaims = new[]
    {
        new[]
        {
            "https://app.sleekflow.io/email",
            "X-Sleekflow-Email"
        },
        new[]
        {
            "https://app.sleekflow.io/user_id",
            "X-Sleekflow-User-Id"
        },
        // Other claims...
    },
}
```

1. **Accessing user claims in your trigger**:
   ```csharp
   // In a trigger implementation
   public class GetResourceTrigger : ITrigger<GetResourceCommand, ResourceDto>
   {
       private readonly ISleekflowAuthorizationContext _authContext;

       public GetResourceTrigger(ISleekflowAuthorizationContext authContext)
       {
           _authContext = authContext;
       }

       public async Task<ResourceDto> ExecuteAsync(GetResourceCommand command)
       {
           // Use the authorization context which contains user information from headers
           var userId = _authContext.SleekflowUserId;
           var email = _authContext.SleekflowEmail;
           var roles = _authContext.SleekflowRoles;

           // Authorization logic using the claims
           if (string.IsNullOrEmpty(userId))
           {
               throw new SfUnauthorizedException();
           }

           // Continue with authorized operation...
           return new ResourceDto { /* ... */ };
       }
   }
   ```

2. **Implementing custom authorization middleware (when needed for HTTP transport)**:
   ```csharp
   // While the ITrigger pattern handles most authorization cases,
   // you might occasionally need middleware for cross-cutting concerns
   public class SleekflowAuthorizationMiddleware
   {
       private readonly RequestDelegate _next;

       public SleekflowAuthorizationMiddleware(RequestDelegate next)
       {
           _next = next;
       }

       public async Task InvokeAsync(HttpContext context, ISleekflowAuthorizationContext authContext)
       {
           // Extract info from headers only if not already set by filters
           if (string.IsNullOrEmpty(authContext.SleekflowUserId))
           {
               var userId = context.Request.Headers["X-Sleekflow-User-Id"].FirstOrDefault();
               if (string.IsNullOrEmpty(userId))
               {
                   context.Response.StatusCode = 401;
                   return;
               }

               // Populate the auth context
               // (This is normally done by HeadersAuthorizationFuncFilter)
           }

           await _next(context);
       }
   }
   ```

### Implementing Service-to-Service Authentication

1. **Calling another service securely from a trigger**:
   ```csharp
   public class CrossServiceTrigger : ITrigger<CrossServiceCommand, CrossServiceResult>
   {
       private readonly IHttpClientFactory _httpClientFactory;
       private readonly IConfiguration _configuration;

       public CrossServiceTrigger(
           IHttpClientFactory httpClientFactory,
           IConfiguration configuration)
       {
           _httpClientFactory = httpClientFactory;
           _configuration = configuration;
       }

       public async Task<CrossServiceResult> ExecuteAsync(CrossServiceCommand command)
       {
           var client = _httpClientFactory.CreateClient("OtherService");

           var request = new HttpRequestMessage(HttpMethod.Post, "/api/resource");

           // Add the service key for authentication
           request.Headers.Add("X-API-Key", _configuration["OtherService:ApiKey"]);

           // Add the data
           request.Content = new StringContent(
               JsonSerializer.Serialize(command.Data),
               Encoding.UTF8,
               "application/json");

           var response = await client.SendAsync(request);
           // Process response...

           return new CrossServiceResult { /* ... */ };
       }
   }
   ```

2. **Authenticating incoming service requests**:
   ```csharp
   // Create a custom FuncFilter for API Key validation
   public interface IApiKeyFuncFilter : IFuncFilter
   {
   }

   public class ApiKeyFuncFilter : IApiKeyFuncFilter, IScopedService
   {
       private readonly IConfiguration _configuration;

       public ApiKeyFuncFilter(IConfiguration configuration)
       {
           _configuration = configuration;
       }

       public Task FilterAsync(HttpRequest httpRequest)
       {
           if (!httpRequest.Headers.TryGetValue("X-API-Key", out var apiKey) ||
               apiKey != _configuration["MyService:ApiKey"])
           {
               throw new SfUnauthorizedException();
           }

           return Task.CompletedTask;
       }
   }

   // Usage in a service-to-service trigger
   [FuncFilter("MyService.IApiKeyFuncFilter")]
   public class InternalServiceTrigger : ITrigger<InternalCommand, InternalResult>
   {
       // Implementation
   }
   ```

### Securing Webhooks

1. **Validate webhook signatures in a webhook trigger**:
   ```csharp
   // For Stripe webhooks
   public class StripeWebhookTrigger : ITrigger<StripeWebhookCommand, WebhookResult>
   {
       private readonly IConfiguration _configuration;

       public StripeWebhookTrigger(IConfiguration configuration)
       {
           _configuration = configuration;
       }

       public async Task<WebhookResult> ExecuteAsync(StripeWebhookCommand command)
       {
           try
           {
               // Use the Stripe SDK to validate the signature
               var stripeEvent = EventUtility.ConstructEvent(
                   command.RawJson,
                   command.SignatureHeader,
                   _configuration["Stripe:WebhookSecret"]
               );

               // Process the validated event
               // ...

               return new WebhookResult { Success = true };
           }
           catch (StripeException)
           {
               throw new SfUnauthorizedException("Invalid Stripe signature");
           }
       }
   }
   ```

2. **Implement challenge-response for webhook verification**:
   ```csharp
   public class WhatsAppVerificationTrigger : ITrigger<WhatsAppVerificationCommand, string>
   {
       private readonly IConfiguration _configuration;

       public WhatsAppVerificationTrigger(IConfiguration configuration)
       {
           _configuration = configuration;
       }

       public Task<string> ExecuteAsync(WhatsAppVerificationCommand command)
       {
           // Verify this is a valid verification request
           if (command.Mode == "subscribe" &&
               command.Token == _configuration["WhatsApp:VerifyToken"])
           {
               // Return the challenge to confirm our endpoint
               return Task.FromResult(command.Challenge);
           }

           throw new SfUnauthorizedException();
       }
   }
   ```

### Handling Secrets and Configuration

1. **Accessing Key Vault secrets**:
   ```csharp
   // In Program.cs or Startup.cs
   public static IHostBuilder CreateHostBuilder(string[] args) =>
       Host.CreateDefaultBuilder(args)
           .ConfigureAppConfiguration((context, config) =>
           {
               // Add Azure Key Vault
               var builtConfig = config.Build();
               var keyVaultEndpoint = builtConfig["KeyVault:Endpoint"];

               if (!string.IsNullOrEmpty(keyVaultEndpoint))
               {
                   var azureServiceTokenProvider = new AzureServiceTokenProvider();
                   var keyVaultClient = new KeyVaultClient(
                       new KeyVaultClient.AuthenticationCallback(
                           azureServiceTokenProvider.KeyVaultTokenCallback));

                   config.AddAzureKeyVault(
                       keyVaultEndpoint,
                       keyVaultClient,
                       new DefaultKeyVaultSecretManager());
               }
           });
   ```

2. **Never hardcode secrets**:
   ```csharp
   // INCORRECT
   var apiKey = "sk_live_1234567890abcdef";

   // CORRECT
   var apiKey = _configuration["ServiceName:ApiKey"];
   ```

### Best Practices

1. **Always secure management endpoints**: Use appropriate filters with role validation for any trigger that can modify system configuration or access sensitive data.

2. **Rate limit public endpoints**: Apply rate limiting to all publicly accessible endpoints to prevent abuse or denial of service attacks.

3. **Use proper error handling**: Throw SfUnauthorizedException for authentication/authorization failures.

4. **Validate all incoming data**: Always validate command objects before processing.

5. **Log security events**: Record authentication failures, authorization violations, and other security-related events for auditing and monitoring.

6. **Defense in depth**: Don't rely solely on the API gateway for security. Implement security checks at the trigger level as well.

7. **Follow the principle of least privilege**: Services should only be granted the minimum permissions needed to function.