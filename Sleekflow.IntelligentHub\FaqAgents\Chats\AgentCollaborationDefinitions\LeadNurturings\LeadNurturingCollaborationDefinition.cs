using System.Net;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Microsoft.SemanticKernel.Agents.Chat;
using Microsoft.SemanticKernel.Agents.Orchestration.GroupChat;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Companies.CompanyConfigs;
using Sleekflow.IntelligentHub.FaqAgents.Chats.Enrichers;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.IntelligentHub.Plugins.Knowledges;
using Sleekflow.IntelligentHub.Utils;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;

public interface ILeadNurturingAgentCollaborationDefinition : IAgentCollaborationDefinition
{
}

public class LeadNurturingAgentCollaborationDefinition
    : BaseAgentCollaborationDefinition, ILeadNurturingAgentCollaborationDefinition, IScopedService
{
    private readonly ILogger<LeadNurturingAgentCollaborationDefinition> _logger;
    private readonly Kernel _kernel;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;
    private readonly ILeadNurturingCollaborationChatCacheService _leadNurturingCollaborationChatCacheService;
    private readonly ILeadNurturingAgentDefinitions _leadNurturingAgentDefinitions;
    private readonly ILeadNurturingAgentActionsDefinitions _leadNurturingAgentActionsDefinitions;
    private readonly IAgentRegistry _agentRegistry;
    private readonly ILeadNurturingAgentSessionManager _sessionManager;
    private readonly IAgenticKnowledgePlugin _agenticKnowledgePlugin;

    private bool _enableFollowupAgent;

    public LeadNurturingAgentCollaborationDefinition(
        ILogger<LeadNurturingAgentCollaborationDefinition> logger,
        Kernel kernel,
        ISummaryPlugin summaryPlugin,
        ILanguagePlugin languagePlugin,
        IAgentDurationTracker agentDurationTracker,
        ICompanyConfigService companyConfigService,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        ILeadNurturingCollaborationChatCacheService leadNurturingCollaborationChatCacheService,
        ILeadNurturingAgentDefinitions leadNurturingAgentDefinitions,
        ILeadNurturingAgentActionsDefinitions leadNurturingAgentActionsDefinitions,
        IAgentRegistry agentRegistry,
        ILeadNurturingAgentSessionManager sessionManager,
        IChatHistoryEnricherFactory enricherFactory,
        IAgenticKnowledgePlugin agenticKnowledgePlugin,
        IFileContentExtractionPlugin fileContentExtractionPlugin)
        : base(
            logger,
            kernel,
            summaryPlugin,
            languagePlugin,
            agentDurationTracker,
            companyConfigService,
            enricherFactory,
            fileContentExtractionPlugin)
    {
        _logger = logger;
        _kernel = kernel;
        _promptExecutionSettingsService = promptExecutionSettingsService;
        _leadNurturingCollaborationChatCacheService = leadNurturingCollaborationChatCacheService;
        _leadNurturingAgentDefinitions = leadNurturingAgentDefinitions;
        _leadNurturingAgentActionsDefinitions = leadNurturingAgentActionsDefinitions;
        _agentRegistry = agentRegistry;
        _sessionManager = sessionManager;
        _agenticKnowledgePlugin = agenticKnowledgePlugin;
    }

    public override Task<List<Agent>> CreateAgents(
        Kernel kernel,
        List<SfChatEntry> chatEntries,
        string sleekflowCompanyId,
        AgentCollaborationConfig agentCollaborationConfig,
        BaseInternalAgentCreationConfiguration? internalAgentCreationConfiguration = null)
    {
        // Store the followup agent configuration for use in termination strategy
        _enableFollowupAgent = agentCollaborationConfig.EnableFollowupAgent;

        var responseLanguage = agentCollaborationConfig.DetectedResponseLanguage ?? "English";
        var additionalInstructionStrategy = agentCollaborationConfig.AdditionalInstructionStrategy ?? string.Empty;
        var additionalInstructionResponse = agentCollaborationConfig.AdditionalInstructionResponse ?? string.Empty;

        // Create agents using the traditional approach
        var leadClassifierAgent = _leadNurturingAgentDefinitions.GetLeadClassifierAgent(
            kernel,
            GetPromptExecutionSettings(_leadNurturingAgentDefinitions.LeadClassifierAgentName));
        var decisionAgent = _leadNurturingAgentDefinitions.GetDecisionAgent(
            kernel,
            GetPromptExecutionSettings(_leadNurturingAgentDefinitions.DecisionAgentName));
        var strategyAgent = _leadNurturingAgentDefinitions.GetStrategyAgent(
            kernel,
            GetPromptExecutionSettings(_leadNurturingAgentDefinitions.StrategyAgentName),
            additionalInstructionStrategy);
        var responseCrafterAgent = _leadNurturingAgentDefinitions.GetResponseCrafterAgent(
            kernel,
            GetPromptExecutionSettings(_leadNurturingAgentDefinitions.ResponseCrafterAgentName),
            responseLanguage,
            _leadNurturingCollaborationChatCacheService,
            additionalInstructionResponse);
        var transitioningResponseCrafterAgent = _leadNurturingAgentDefinitions.GetTransitioningResponseCrafterAgent(
            kernel,
            GetPromptExecutionSettings(_leadNurturingAgentDefinitions.TransitioningResponseCrafterAgentName),
            responseLanguage,
            _leadNurturingCollaborationChatCacheService);
        var knowledgeRetrievalAgent = _leadNurturingAgentDefinitions.GetKnowledgeRetrievalAgent(
            kernel,
            _agenticKnowledgePlugin,
            GetPromptExecutionSettings(_leadNurturingAgentDefinitions.KnowledgeRetrievalAgentName),
            _leadNurturingCollaborationChatCacheService);
        var leadAssignmentPlanningAgent = _leadNurturingAgentDefinitions.GetLeadAssignmentPlanningAgent(
            kernel,
            GetPromptExecutionSettings(_leadNurturingAgentDefinitions.LeadAssignmentPlanningAgentName));
        var demoSchedulingPlanningAgent = _leadNurturingAgentDefinitions.GetDemoSchedulingPlanningAgent(
            kernel,
            GetPromptExecutionSettings(_leadNurturingAgentDefinitions.DemoSchedulingPlanningAgentName));
        var reviewerAgent = _leadNurturingAgentDefinitions.GetReviewerAgent(
            kernel,
            GetPromptExecutionSettings(_leadNurturingAgentDefinitions.ReviewerAgentName),
            responseLanguage,
            _leadNurturingCollaborationChatCacheService);
        var informationGatheringResponseCrafterAgent =
            _leadNurturingAgentDefinitions.GetInformationGatheringResponseCrafterAgent(
                kernel,
                GetPromptExecutionSettings(_leadNurturingAgentDefinitions.InformationGatheringResponseCrafterAgentName),
                responseLanguage,
                _leadNurturingCollaborationChatCacheService);
        var confirmationAgent = _leadNurturingAgentDefinitions.GetConfirmationAgent(
            kernel,
            GetPromptExecutionSettings(_leadNurturingAgentDefinitions.ConfirmationAgentName));
        var actionAgent = _leadNurturingAgentDefinitions.GetActionAgent(
            kernel,
            GetPromptExecutionSettings(_leadNurturingAgentDefinitions.ActionAgentName));
        // Register core agents with their capabilities
        _agentRegistry.RegisterAgent(leadClassifierAgent, AgentCapabilities.ClassifyLead);
        _agentRegistry.RegisterAgent(decisionAgent, AgentCapabilities.DecideAction);
        _agentRegistry.RegisterAgent(strategyAgent, AgentCapabilities.DefineStrategy);
        _agentRegistry.RegisterAgent(knowledgeRetrievalAgent, AgentCapabilities.RetrieveKnowledge);
        _agentRegistry.RegisterAgent(reviewerAgent, AgentCapabilities.ReviewResponse);

        _agentRegistry.RegisterAgent(leadAssignmentPlanningAgent, AgentCapabilities.AssignLead);
        _agentRegistry.RegisterAgent(demoSchedulingPlanningAgent, AgentCapabilities.ScheduleDemo);

        _agentRegistry.RegisterAgent(responseCrafterAgent, AgentCapabilities.CraftStandardResponse);
        _agentRegistry.RegisterAgent(informationGatheringResponseCrafterAgent, AgentCapabilities.GatherInfo);
        _agentRegistry.RegisterAgent(transitioningResponseCrafterAgent, AgentCapabilities.CraftTransitionResponse);

        _agentRegistry.RegisterAgent(confirmationAgent, AgentCapabilities.ConfirmActions);
        _agentRegistry.RegisterAgent(actionAgent, AgentCapabilities.ExecuteActions);

        var agents = new List<Agent>
        {
            // Core agents
            leadClassifierAgent,
            decisionAgent,
            strategyAgent,
            knowledgeRetrievalAgent,
            reviewerAgent,

            // Planning agents
            leadAssignmentPlanningAgent,
            demoSchedulingPlanningAgent,

            // Response agents
            responseCrafterAgent,
            informationGatheringResponseCrafterAgent,
            transitioningResponseCrafterAgent,

            // Action agents
            confirmationAgent,
            actionAgent
        };

        // Conditionally add followup agent if enabled
        if (agentCollaborationConfig.EnableFollowupAgent)
        {
            var followUpAgent = _leadNurturingAgentDefinitions.GetFollowUpAgent(
                kernel,
                GetPromptExecutionSettings("FollowUpAgent"));
            _agentRegistry.RegisterAgent(followUpAgent, AgentCapabilities.SendFollowUp);
            agents.Add(followUpAgent);
        }

        return Task.FromResult(agents);
    }

    public override SelectionStrategy? CreateSelectionStrategy(Kernel kernel)
    {
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, true);

        return new KernelFunctionSelectionStrategy(CreateCoordinatingFunction(), kernel)
        {
            HistoryReducer = new ChatHistoryTruncationReducer(1),
            HistoryVariableName = "history",
            ResultParser = (result) =>
            {
                try
                {
                    var capabilityName = result.GetValue<string>();
                    if (string.IsNullOrEmpty(capabilityName))
                    {
                        _logger.LogWarning(
                            "Empty capability name received from coordinating function. Using default capability.");
                        return _agentRegistry.GetAgentForCapability(AgentCapabilities.ClassifyLead).Name!;
                    }

                    // Handle generic CraftResponse capability - map to CraftStandardResponse
                    if (capabilityName == AgentCapabilities.CraftResponse)
                    {
                        capabilityName = AgentCapabilities.CraftStandardResponse;
                    }

                    if (_agentRegistry.HasCapability(capabilityName))
                    {
                        return _agentRegistry.GetAgentForCapability(capabilityName).Name!;
                    }
                    else
                    {
                        _logger.LogWarning($"Unknown capability: {capabilityName}. Using default capability.");
                        return _agentRegistry.GetAgentForCapability(AgentCapabilities.ClassifyLead).Name!;
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                    Console.WriteLine(result);

                    _logger.LogWarning(
                        e,
                        "Failed to parse result from coordinating function. Defaulting to ClassifyLead capability.");

                    return _agentRegistry.GetAgentForCapability(AgentCapabilities.ClassifyLead).Name!;
                }
            },
            Arguments = new KernelArguments(promptExecutionSettings),
            UseInitialAgentAsFallback = true
        };
    }

    public override RegexTerminationStrategy? CreateTerminationStrategy(List<Agent> agents)
    {
        var reviewerAgent = _agentRegistry.GetAgentForCapability(AgentCapabilities.ReviewResponse);

        // If followup agent is enabled, terminate only after followup is completed
        // Otherwise, terminate when reviewer approves
        if (_enableFollowupAgent && _agentRegistry.HasCapability(AgentCapabilities.SendFollowUp))
        {
            var followUpAgent = _agentRegistry.GetAgentForCapability(AgentCapabilities.SendFollowUp);
            return new RegexTerminationStrategy("\"followup_status\":\\s*\"completed\"")
            {
                MaximumIterations = 25,
                AutomaticReset = true,
                Agents = new List<Agent>
                {
                    followUpAgent
                }
            };
        }
        else
        {
            return new RegexTerminationStrategy("\"review\":\\s*\"approved\"")
            {
                MaximumIterations = 21,
                AutomaticReset = true,
                Agents = new List<Agent>
                {
                    reviewerAgent
                }
            };
        }
    }

    [method: JsonConstructor]
    public class ActionAgentOutput(string agentName, string actionType)
    {
        [JsonProperty("agent_name")]
        public string AgentName { get; set; } = agentName;

        [JsonProperty("plan_type")]
        public string ActionType { get; set; } = actionType;
    }

    public override async Task<bool> InterceptAgentReplyAsync(
        ChatMessageContent response,
        string groupChatIdStr)
    {
        await base.InterceptAgentReplyAsync(response, groupChatIdStr);

        // Cache the response if it's from a response agent
        var responseAgentNames = new HashSet<string?>
        {
            _agentRegistry.GetAgentForCapability(AgentCapabilities.CraftStandardResponse).Name,
            _agentRegistry.GetAgentForCapability(AgentCapabilities.CraftTransitionResponse).Name,
            _agentRegistry.GetAgentForCapability(AgentCapabilities.GatherInfo).Name
        };

        if (responseAgentNames.Contains(response.AuthorName) && response.Content != null)
        {
            // Assuming the actual reply is in a "response" field of the JSON content
            if (JsonUtils.TryParseJson<Dictionary<string, object>>(response.Content, out var jsonContent) &&
                jsonContent != null &&
                jsonContent.TryGetValue("response", out var replyObj) &&
                replyObj is string replyString)
            {
                await _leadNurturingCollaborationChatCacheService.SetLastResponseAgentReplyAsync(
                    groupChatIdStr,
                    replyString);
                _logger.LogInformation($"Cached last response agent reply for {groupChatIdStr}: {replyString}");
            }
        }

        if (response.AuthorName == _agentRegistry.GetAgentForCapability(AgentCapabilities.ClassifyLead).Name)
        {
            await _leadNurturingAgentActionsDefinitions.HandleLeadClassifierAction(
                response.Content ?? string.Empty,
                _kernel);
        }

        // Parse the group chat ID
        var groupChatId = GroupChatId.Parse(groupChatIdStr);

        // Check for action agents that perform actual operations
        if (response.AuthorName == _agentRegistry.GetAgentForCapability(AgentCapabilities.ExecuteActions).Name)
        {
            // Only mark actions as performed if we're in report phase (after successful execution)
            if (response.Content != null &&
                response.Content.Contains("\"executed_tools\":"))
            {
                // Mark this session as having performed actions that should not be canceled
                await _sessionManager.MarkSessionActionsPerformedAsync(groupChatId);
            }
        }

        if (_agentRegistry.HasCapability(AgentCapabilities.SendFollowUp)
            && response.AuthorName == _agentRegistry.GetAgentForCapability(AgentCapabilities.SendFollowUp).Name)
        {
            await _leadNurturingAgentActionsDefinitions.HandleFollowUpAction(
                response.Content ?? string.Empty,
                _kernel,
                groupChatIdStr);
        }

        // Check if we should cancel the session
        // This is used to prevent multiple sessions from processing the same conversation
        // Return false if the session should be stopped
        if (await _sessionManager.ShouldCancelSessionAsync(groupChatId))
        {
            _logger.LogInformation(
                "Canceling processing for agent session {GroupChatId} as newer session is handling the conversation",
                groupChatIdStr);
            return false;
        }

        return true;
    }

    public override string GetFinalReplyTag() => "response";

    public override string GetSourceTag() => "CONFIRMED_KNOWLEDGE";

    public override async Task<string> GetSourceAsync(ChatHistory chatHistory, string groupChatIdStr)
    {
        // Extract all source messages
        var allConfirmedKnowledges
            = await _leadNurturingCollaborationChatCacheService.GetAllConfirmedKnowledgesAsync(
                groupChatIdStr);

        var joinedAllSourceStr = string.Join("\n", allConfirmedKnowledges.Select(k => k.Value));

        return joinedAllSourceStr;
    }

    public override async Task<string> GetFinalReplyAsync(ChatHistory chatHistory)
    {
        // Extract final reply and source from JSON
        var finalReplyKey = GetFinalReplyTag();

        // Find the message containing the final reply
        var finalChatMessage = chatHistory
            .LastOrDefault(x =>
                x.Content != null
                && JsonUtils.TryParseJson<Dictionary<string, object>>(x.Content, out var json)
                && json != null
                && json.ContainsKey(finalReplyKey))
            ?.Content;

        if (finalChatMessage == null ||
            !JsonUtils.TryParseJson<Dictionary<string, object>>(finalChatMessage, out var finalJson))
        {
            return string.Empty;
        }

        var finalReplyToCustomer = finalJson?[finalReplyKey].ToString()?.Trim() ?? string.Empty;

        // We encountered a scenario where the final reply to the customer was not decoded properly.
        // The emoji is displayed as a string instead of the actual emoji.
        // e.g. "👍" is displayed as "&#128077;". We need to decode the HTML entities.
        finalReplyToCustomer = WebUtility.HtmlDecode(finalReplyToCustomer);

        // Convert HTML-style formatting tags to WhatsApp markdown format
        finalReplyToCustomer = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(finalReplyToCustomer);

        return finalReplyToCustomer;
    }

    private PromptExecutionSettings GetPromptExecutionSettings(string agentName)
    {
        if (agentName == _leadNurturingAgentDefinitions.ActionAgentName)
        {
            return _promptExecutionSettingsService.GetPromptExecutionSettings(
                SemanticKernelExtensions.S_GPT_4_1,
                true);
        }

        if (agentName == _leadNurturingAgentDefinitions.DecisionAgentName)
        {
            return _promptExecutionSettingsService.GetPromptExecutionSettings(
                SemanticKernelExtensions.S_FLASH_2_5,
                true);
        }

        if (agentName == _leadNurturingAgentDefinitions.ResponseCrafterAgentName
            || agentName == _leadNurturingAgentDefinitions.TransitioningResponseCrafterAgentName
            || agentName == _leadNurturingAgentDefinitions.InformationGatheringResponseCrafterAgentName)
        {
            return _promptExecutionSettingsService.GetPromptExecutionSettings(
                SemanticKernelExtensions.S_FLASH_2_5,
                true);
        }

        return _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, true);
    }

    private record ChatHistoryRecord(string Role, string Name, string Content);

    private KernelFunction CreateCoordinatingFunction()
    {
        // This coordination function manages the flow of the agent conversation using capabilities.
        // Each chat session is designed to generate exactly one response to the user:
        // - Regular response (via craft_response capability)
        // - Information gathering request (via gather_info capability)
        // - Transition message (via craft_response capability with TransitioningResponseCrafterAgent)
        //
        // After the review_response capability approves a response:
        // - If followup agent is enabled, it will be activated to send follow-up messages
        // - Otherwise, the chat session ends immediately
        // A new chat session begins when the customer responds.
        return KernelFunctionFactory.CreateFromMethod(
            (string history) =>
            {
                var messages = JsonConvert.DeserializeObject<ChatHistoryRecord[]>(history);

                if (messages == null || messages.Length == 0)
                {
                    return AgentCapabilities.ClassifyLead;
                }

                if (!JsonUtils.TryParseJson<Dictionary<string, object?>>(messages[0].Content, out var message))
                {
                    return AgentCapabilities.ClassifyLead;
                }

                // 1. First check for classification message, which starts the flow
                if (message!.ContainsKey("classification"))
                {
                    return AgentCapabilities.DecideAction;
                }

                // 2. Check for the DecisionAgent's decision
                if (message.TryGetValue("decision", out var value))
                {
                    string decisionStr = value as string ?? string.Empty;

                    if (decisionStr == "continue_nurturing")
                    {
                        return AgentCapabilities.DefineStrategy;
                    }

                    if (new[]
                        {
                            "assign_hot",
                            "assign_cold",
                            "assign_human",
                            "assign_drop_off",
                            "assign_insufficient_info"
                        }.Contains(decisionStr))
                    {
                        return AgentCapabilities.AssignLead;
                    }

                    if (decisionStr == "schedule_demo")
                    {
                        return AgentCapabilities.ScheduleDemo;
                    }
                }

                var agentName = message.TryGetValue("agent_name", out var agent)
                    ? agent as string ?? string.Empty
                    : string.Empty;

                // 3. Check for LeadAssignmentPlanningAgent's or DemoSchedulingPlanningAgent's output with plan_type
                if (message.TryGetValue("plan_type", out var actionTypeObj))
                {
                    var actionType = actionTypeObj as string ?? string.Empty;

                    // Check if information is sufficient for demo scheduling
                    if (actionType == "modification_required")
                    {
                        return AgentCapabilities.GatherInfo;
                    }

                    // If the action type is no_action or empty, we need to go back to the strategy agent
                    if (actionType == "no_action" || string.IsNullOrWhiteSpace(actionType))
                    {
                        return AgentCapabilities.DefineStrategy;
                    }

                    var actionsRequiringConfirmation = new string[]
                    {
                        // Uncomment these actions if they require confirmation
                        // "schedule_demo"
                        // "assign_lead"
                    };

                    // Check if the action type requires confirmation, if not, we can execute it directly
                    return actionsRequiringConfirmation.Contains(actionType)
                        ? AgentCapabilities.ConfirmActions
                        : AgentCapabilities.ExecuteActions;
                }

                // 4. Check for ConfirmationAgent output
                if (agentName == _agentRegistry.GetAgentForCapability(AgentCapabilities.ConfirmActions).Name)
                {
                    if (message.TryGetValue("confirmation_status", out var confirmationStatusObj) &&
                        confirmationStatusObj is string confirmationStatus)
                    {
                        switch (confirmationStatus)
                        {
                            // Check if the confirmation status is "confirmed" or "not confirmed" or "canceled"
                            case "confirmed":
                                return AgentCapabilities.ExecuteActions;
                            case "not confirmed":
                                return AgentCapabilities.CraftTransitionResponse;
                            case "cancelled":
                                return AgentCapabilities.CraftTransitionResponse;
                        }
                    }
                }

                // 5. Check for ActionAgent output
                if (agentName == _agentRegistry.GetAgentForCapability(AgentCapabilities.ExecuteActions).Name)
                {
                    if (message.TryGetValue("result", out var resultObj) &&
                        resultObj is string and ("success" or "failure"))
                    {
                        return AgentCapabilities.CraftTransitionResponse;
                    }
                }

                // 6. Handle InformationGatheringResponseCrafterAgent output - send to reviewer directly
                if (agentName == _agentRegistry.GetAgentForCapability(AgentCapabilities.GatherInfo).Name
                    && message.ContainsKey("response"))
                {
                    return AgentCapabilities.ReviewResponse;
                }

                // 7. Check for knowledge request
                if (message.ContainsKey("need_knowledge") && message["need_knowledge"] is not null)
                {
                    return AgentCapabilities.RetrieveKnowledge;
                }

                // 8. Check if we have knowledge to provide to craft_standard_response capability
                if (message.ContainsKey("knowledge_overview"))
                {
                    return AgentCapabilities.CraftStandardResponse;
                }

                // 9. Check if we have strategy to provide to craft_standard_response capability
                if (message.ContainsKey("strategy"))
                {
                    return AgentCapabilities.CraftStandardResponse;
                }

                // 10. Check if we have a response to review
                if (message.ContainsKey("response"))
                {
                    return AgentCapabilities.ReviewResponse;
                }

                // 11. Check if ReviewerAgent approved or rejected a response
                if (message.TryGetValue("review", out var reviewValue)
                    && reviewValue is string reviewStr)
                {
                    if (reviewStr.Contains("approved"))
                    {
                        // If followup agent is enabled, activate it after reviewer approval
                        if (_enableFollowupAgent && _agentRegistry.HasCapability(AgentCapabilities.SendFollowUp))
                        {
                            return AgentCapabilities.SendFollowUp;
                        }
                        // Otherwise, the conversation ends (handled by termination strategy)
                    }
                    else if (reviewStr.Contains("rejected"))
                    {
                        // Determine which response agent to use based on the message content
                        // If the message was from the TransitioningResponseCrafterAgent, keep using it
                        if (agentName ==
                            _agentRegistry.GetAgentForCapability(AgentCapabilities.CraftTransitionResponse).Name)
                        {
                            return AgentCapabilities.CraftTransitionResponse;
                        }

                        // If the message was from the InformationGatheringResponseCrafterAgent, keep using it
                        if (agentName == _agentRegistry.GetAgentForCapability(AgentCapabilities.GatherInfo).Name)
                        {
                            return AgentCapabilities.GatherInfo;
                        }

                        // Otherwise use the standard response crafter
                        return AgentCapabilities.CraftStandardResponse;
                    }
                }

                // Default to classify_lead if nothing else matches
                return AgentCapabilities.ClassifyLead;
            },
            "LeadNurturingCoordinatingFunction");
    }

    public override async Task<(string ChatHistoryStr, string Context)> InitializeChatHistoryAsync(
        AgentGroupChat? agentGroupChat,
        string groupChatIdStr,
        List<SfChatEntry> chatEntries,
        ReplyGenerationContext replyGenerationContext,
        AgentCollaborationConfig agentCollaborationConfig,
        CompanyAgentConfig? agentConfig)
    {
        // Parse the groupChatId using the utility class
        var groupChatId = GroupChatId.Parse(groupChatIdStr);

        // Register the agent group session so we can track its progress
        await _sessionManager.RegisterAgentSessionAsync(groupChatId);

        // Cancel any previous sessions that haven't performed actions
        await _sessionManager.CancelPreviousSessionsAsync(groupChatId);

        // Continue with normal initialization
        return await base.InitializeChatHistoryAsync(
            agentGroupChat,
            groupChatIdStr,
            chatEntries,
            replyGenerationContext,
            agentCollaborationConfig,
            agentConfig);
    }
}