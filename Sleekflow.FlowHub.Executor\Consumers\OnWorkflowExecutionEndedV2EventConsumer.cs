﻿using MassTransit;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Events.ServiceBus.HighTrafficServiceBus;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.WorkflowExecutions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;
using Sleekflow.OpenTelemetry.FlowHub;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnWorkflowExecutionEndedV2EventConsumerDefinition : ConsumerDefinition<OnWorkflowExecutionEndedV2EventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnWorkflowExecutionEndedV2EventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 4096;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.MaxMessageSizeInKilobytes = 2048;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(2);
            serviceBusReceiveEndpointConfiguration.UseMessageRetry(r => r.Interval(6, TimeSpan.FromSeconds(30)));
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnWorkflowExecutionEndedV2EventConsumer
    : IConsumer<OnWorkflowExecutionEndedV2Event>, IHighTrafficConsumer<OnWorkflowExecutionEndedV2Event>
{
    private readonly IServiceBusManager _serviceBusManager;
    private readonly IWorkflowExecutionService _workflowExecutionService;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;
    private readonly IFlowHubMeters _flowHubMeters;

    public OnWorkflowExecutionEndedV2EventConsumer(
        IServiceBusManager serviceBusManager,
        IWorkflowExecutionService workflowExecutionService,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer,
        IFlowHubMeters flowHubMeters)
    {
        _serviceBusManager = serviceBusManager;
        _workflowExecutionService = workflowExecutionService;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
        _flowHubMeters = flowHubMeters;
    }

    public async Task Consume(ConsumeContext<OnWorkflowExecutionEndedV2Event> context)
    {
        var @event = context.Message;

        var sleekflowCompanyId = @event.SleekflowCompanyId;
        var stateId = @event.StateId;
        var stateIdentity = @event.StateIdentity;
        var workflowExecutionStatus = @event.WorkflowExecutionStatus;
        var workflowExecutionReasonCode = @event.WorkflowExecutionReasonCode;
        var workflowType = @event.WorkflowType;
        var endedBy = @event.EndedBy;
        var endedAt = @event.EndedAt;
        var subWorkflowType = @event.SubWorkflowType;

        _applicationInsightsTelemetryTracer.TraceEvent(
            TraceEventNames.FlowHubWorkflowExecutionCompleted,
            new Dictionary<string, string>()
            {
                {
                    "sub_workflow_type", subWorkflowType
                },
            });

        if (workflowExecutionStatus.Equals(WorkflowExecutionStatuses.Complete))
        {
            _flowHubMeters.IncrementCounter(
                subWorkflowType,
                FlowHubMeterOptions.WorkflowComplete);
        }
        else if (workflowExecutionStatus.Equals(WorkflowExecutionStatuses.Failed))
        {
            _flowHubMeters.IncrementCounter(
                subWorkflowType,
                FlowHubMeterOptions.WorkflowFailed);
        }

        var workflowExecution = await _workflowExecutionService.CreateWorkflowExecutionAsync(
            stateId,
            stateIdentity,
            workflowExecutionStatus,
            workflowExecutionReasonCode,
            0,
            workflowType,
            endedBy,
            endedAt);

        await context.ConsumeCompleted;

        await _serviceBusManager.PublishAsync(
            new OnWorkflowExecutionEndedPostProcessRequestedEvent(
                sleekflowCompanyId,
                stateId,
                workflowExecution.Id));
    }
}