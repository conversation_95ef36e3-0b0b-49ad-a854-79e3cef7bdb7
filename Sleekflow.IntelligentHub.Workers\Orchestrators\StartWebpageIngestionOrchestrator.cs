using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.IntelligentHub.Workers.Activities;
using Sleekflow.IntelligentHub.Workers.Triggers;
using Exception = System.Exception;

namespace Sleekflow.IntelligentHub.Workers.Orchestrators;

public class StartWebpageIngestionOrchestrator
{
    private readonly ILogger<StartWebpageIngestionOrchestrator> _logger;

    public StartWebpageIngestionOrchestrator(ILogger<StartWebpageIngestionOrchestrator> logger)
    {
        _logger = logger;
    }

    public class ProcessWebpageDocumentOrchestratorOutput
    {
        [JsonConstructor]
        public ProcessWebpageDocumentOrchestratorOutput()
        {
        }
    }

    [Function(nameof(StartWebpageIngestionOrchestrator))]
    public async Task<ProcessWebpageDocumentOrchestratorOutput> RunOrchestrator(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var processWebpageDocumentInput = context.GetInput<StartWebpageIngestionEvent>();

        var taskOptions = new TaskOptions(new TaskRetryOptions(new RetryPolicy(5, TimeSpan.FromSeconds(120), 1)));

        // Record conversion started timestamp
        await context
            .CallActivityAsync<UpdateDebugTimestamps.UpdateDebugTimestampsOutput>(
                nameof(UpdateDebugTimestamps),
                new UpdateDebugTimestamps.UpdateDebugTimestampsInput(
                    processWebpageDocumentInput.SleekflowCompanyId,
                    processWebpageDocumentInput.DocumentId,
                    conversionStarted: DateTimeOffset.UtcNow),
                taskOptions);

        try
        {
            // Update status to converting
            await context
                .CallActivityAsync<PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusOutput>(
                    nameof(PatchProcessFileDocumentStatus),
                    new PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusInput(
                        processWebpageDocumentInput.SleekflowCompanyId,
                        processWebpageDocumentInput.DocumentId,
                        ProcessFileDocumentStatuses.Converting,
                        0.0),
                    taskOptions);

            // Get the webpage document
            var getWebpageDocumentOutput = await context
                .CallActivityAsync<GetWebpageDocument.GetWebpageDocumentOutput>(
                    nameof(GetWebpageDocument),
                    new GetWebpageDocument.GetWebpageDocumentInput(
                        processWebpageDocumentInput.SleekflowCompanyId,
                        processWebpageDocumentInput.DocumentId),
                    taskOptions);

            var webpageDocument = getWebpageDocumentOutput.WebpageDocument;

            _logger.LogInformation(
                "StartWebpageIngestionOrchestrator Start processing URL: {Url}",
                webpageDocument.Url);

            // Get HTTP head to determine if it's a file URL or web page URL
            var getWebpageHeadOutput = await context
                .CallActivityAsync<GetWebpageHead.GetWebpageHeadOutput>(
                    nameof(GetWebpageHead),
                    new GetWebpageHead.GetWebpageHeadInput(
                        processWebpageDocumentInput.SleekflowCompanyId,
                        webpageDocument.Url),
                    taskOptions);

            var webpageHead = getWebpageHeadOutput.WebpageHead;

            // Update progress to 50%
            await context
                .CallActivityAsync<PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusOutput>(
                    nameof(PatchProcessFileDocumentStatus),
                    new PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusInput(
                        processWebpageDocumentInput.SleekflowCompanyId,
                        processWebpageDocumentInput.DocumentId,
                        ProcessFileDocumentStatuses.Converting,
                        50.0),
                    taskOptions);

            // Process based on MIME type
            if (webpageHead.MimeType.Contains("text/html", StringComparison.OrdinalIgnoreCase))
            {
                // Process as HTML webpage
                await context
                    .CallActivityAsync<ProcessWebpageDocument.ProcessWebpageDocumentOutput>(
                        nameof(ProcessWebpageDocument),
                        new ProcessWebpageDocument.ProcessWebpageDocumentInput(
                            processWebpageDocumentInput.SleekflowCompanyId,
                            processWebpageDocumentInput.DocumentId),
                        taskOptions);
            }
            else
            {
                // Process as file document
                await ProcessWebpageFileDocument(
                    context,
                    processWebpageDocumentInput.SleekflowCompanyId,
                    processWebpageDocumentInput.DocumentId,
                    webpageDocument.Url,
                    webpageHead.MimeType,
                    taskOptions);
            }

            // Mark as completed
            await context
                .CallActivityAsync<PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusOutput>(
                    nameof(PatchProcessFileDocumentStatus),
                    new PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusInput(
                        processWebpageDocumentInput.SleekflowCompanyId,
                        processWebpageDocumentInput.DocumentId,
                        ProcessFileDocumentStatuses.ReadyToAssign,
                        100.0),
                    taskOptions);

            // Record conversion ended timestamp
            await context
                .CallActivityAsync<UpdateDebugTimestamps.UpdateDebugTimestampsOutput>(
                    nameof(UpdateDebugTimestamps),
                    new UpdateDebugTimestamps.UpdateDebugTimestampsInput(
                        processWebpageDocumentInput.SleekflowCompanyId,
                        processWebpageDocumentInput.DocumentId,
                        conversionEnded: DateTimeOffset.UtcNow),
                    taskOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error processing webpage document {DocumentId}",
                processWebpageDocumentInput.DocumentId);

            await context
                .CallActivityAsync<PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusOutput>(
                    nameof(PatchProcessFileDocumentStatus),
                    new PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusInput(
                        processWebpageDocumentInput.SleekflowCompanyId,
                        processWebpageDocumentInput.DocumentId,
                        ProcessFileDocumentStatuses.FailedToConvert,
                        0.0),
                    taskOptions);
        }

        // Start upload to agent knowledge bases
        await context
            .CallSubOrchestratorAsync(
                nameof(UploadFileDocumentToAllAssignedAgentsOrchestratorV1),
                new StartUploadToAgentKnowledgeBasesEvent(
                    processWebpageDocumentInput.SleekflowCompanyId,
                    processWebpageDocumentInput.DocumentId),
                taskOptions);

        return new ProcessWebpageDocumentOrchestratorOutput();
    }

    private async Task ProcessWebpageFileDocument(
        TaskOrchestrationContext context,
        string sleekflowCompanyId,
        string documentId,
        string url,
        string mimeType,
        TaskOptions taskOptions)
    {
        object? progressOutput = null;

        while (true)
        {
            _logger.LogInformation(
                "StartWebpageIngestionOrchestrator processing file from URL: {Url}",
                url);

            var processWebpageFileDocumentOutput = await context
                .CallActivityAsync<ProcessWebsiteFileDocument.ProcessWebsiteFileDocumentOutput>(
                    nameof(ProcessWebsiteFileDocument),
                    new ProcessWebsiteFileDocument.ProcessWebsiteFileDocumentInput(
                        sleekflowCompanyId,
                        documentId,
                        url,
                        mimeType,
                        progressOutput),
                    taskOptions);

            progressOutput = processWebpageFileDocumentOutput.FileIngestionProgress;

            if (processWebpageFileDocumentOutput.IsCompleted)
            {
                return;
            }
        }
    }
}