using Azure.Storage;
using Azure.Storage.Sas;
using DuckDB.NET.Data;
using Microsoft.Extensions.Logging;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Configs;

namespace Sleekflow.UserEventAnalyticsHub.DataCompactor.Extensions;

public class PostgreSqlConnectionParams
{
    public string Host { get; set; } = string.Empty;
    public int Port { get; set; } = 5432;
    public string Database { get; set; } = string.Empty;
    public string User { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
}

public static class DuckDbExtensions
{
    public static async Task ConfigureDuckDbAsync(DuckDBConnection connection, ICompactorConfig config, IPostgreSqlConfig pgConfig, ILogger logger)
    {
        logger.LogInformation("Configuring DuckDB with Azure and PostgreSQL for data migration");

        // Install and load required extensions
        await ExecuteNonQueryAsync(connection, "INSTALL postgres;");
        await ExecuteNonQueryAsync(connection, "LOAD postgres;");
        await ExecuteNonQueryAsync(connection, "INSTALL azure;");
        await ExecuteNonQueryAsync(connection, "LOAD azure;");

        // Set memory limits (using hardcoded values for simplicity)
        await ExecuteNonQueryAsync(connection, "SET memory_limit='1536MB';");
        await ExecuteNonQueryAsync(connection, "SET max_memory='2GB';");

        // Set thread configuration
        await ExecuteNonQueryAsync(connection, "SET threads TO 16;");

        // Optimize for bulk operations
        await ExecuteNonQueryAsync(connection, "SET enable_progress_bar=false;");
        await ExecuteNonQueryAsync(connection, "SET preserve_insertion_order=false;");

        // Set up security configuration
        await ExecuteNonQueryAsync(connection, "SET allow_unredacted_secrets = false;");
        await ExecuteNonQueryAsync(connection, "SET allow_persistent_secrets = false;");
        await ExecuteNonQueryAsync(connection, "SET allow_community_extensions = false;");
        await ExecuteNonQueryAsync(connection, "SET azure_transport_option_type = 'curl';");

        // Generate SAS token for Azure access
        var sasToken = GenerateHierarchicalSasToken(
            config.StorageAccountName,
            config.StorageAccountKey,
            config.EventsContainerName,
            DateTimeOffset.UtcNow.AddHours(1));

        // Create Azure secret for authentication
        var azureSecretCommand = $@"
            CREATE SECRET azure_secret (
                TYPE AZURE,
                CONNECTION_STRING 'DefaultEndpointsProtocol=https;AccountName={config.StorageAccountName};EndpointSuffix=core.windows.net;SharedAccessSignature={sasToken}'
            );";
        await ExecuteNonQueryAsync(connection, azureSecretCommand);

        // Parse PostgreSQL connection parameters from connection string
        var pgParams = ParsePostgreSqlConnectionString(pgConfig.ConnectionString);

        // Create PostgreSQL secret for authentication
        var pgSecretCommand = $@"
            CREATE SECRET postgres_secret (
                TYPE POSTGRES,
                HOST '{pgParams.Host}',
                PORT {pgParams.Port},
                DATABASE '{pgParams.Database}',
                USER '{pgParams.User}',
                PASSWORD '{pgParams.Password}'
            );";
        await ExecuteNonQueryAsync(connection, pgSecretCommand);

        // Attach PostgreSQL database using the secret
        await ExecuteNonQueryAsync(connection, "ATTACH '' AS postgres_db (TYPE postgres, SECRET postgres_secret);");

        // Lock configuration after all setup is complete
        await ExecuteNonQueryAsync(connection, "SET lock_configuration = true;");

        logger.LogInformation("DuckDB configuration with Azure and PostgreSQL completed successfully");
    }

    public static string BuildParquetScanQuery(List<string> filePaths)
    {
        if (filePaths.Count == 1)
        {
            return $"SELECT * FROM parquet_scan('{filePaths[0]}')";
        }

        var unionQueries = filePaths.Select(path => $"SELECT * FROM parquet_scan('{path}')");
        return string.Join(" UNION ALL ", unionQueries);
    }

    public static string BuildBulkInsertQuery(string postgresTable, List<string> parquetFilePaths)
    {
        var parquetQuery = BuildParquetScanQuery(parquetFilePaths);
        return $"INSERT INTO {postgresTable} ({parquetQuery})";
    }

    public static async Task<long> GetParquetRowCountAsync(DuckDBConnection connection, string filePath)
    {
        using var command = new DuckDBCommand($"SELECT COUNT(*) FROM parquet_scan('{filePath}')", connection);
        var result = await command.ExecuteScalarAsync();
        return Convert.ToInt64(result);
    }

    public static async Task<long> GetParquetBatchRowCountAsync(DuckDBConnection connection, List<string> filePaths)
    {
        var query = BuildParquetScanQuery(filePaths);
        using var command = new DuckDBCommand($"SELECT COUNT(*) FROM ({query})", connection);
        var result = await command.ExecuteScalarAsync();
        return Convert.ToInt64(result);
    }

    private static async Task ExecuteNonQueryAsync(DuckDBConnection connection, string sql)
    {
        using var command = new DuckDBCommand(sql, connection);
        await command.ExecuteNonQueryAsync();
    }

    private static string GenerateHierarchicalSasToken(
        string storageAccountName,
        string storageAccountKey,
        string containerName,
        DateTimeOffset expiryTime)
    {
        // Create SAS token builder for blob
        var sasBuilder = new DataLakeSasBuilder
        {
            Resource = "c",
            Protocol = SasProtocol.Https,
            StartsOn = DateTimeOffset.UtcNow,
            ExpiresOn = expiryTime,
            FileSystemName = containerName,
        };

        // Set permissions
        sasBuilder.SetPermissions(DataLakeSasPermissions.Read | DataLakeSasPermissions.List);

        // Create StorageSharedKeyCredential object
        var credential = new StorageSharedKeyCredential(storageAccountName, storageAccountKey);

        // Generate the SAS token
        var sasToken = sasBuilder.ToSasQueryParameters(credential).ToString();

        return sasToken;
    }

    public static PostgreSqlConnectionParams ParsePostgreSqlConnectionString(string connectionString)
    {
        var parameters = new Dictionary<string, string>();
        var result = new PostgreSqlConnectionParams();

        // Parse .NET-style connection string (semicolon-separated key=value pairs)
        var parts = connectionString.Split(';', StringSplitOptions.RemoveEmptyEntries);
        foreach (var part in parts)
        {
            var keyValue = part.Split('=', 2);
            if (keyValue.Length == 2)
            {
                var key = keyValue[0].Trim();
                var value = keyValue[1].Trim();
                parameters[key] = value;
            }
        }

        // Extract individual parameters
        if (parameters.TryGetValue("Host", out var host) || parameters.TryGetValue("Server", out host))
            result.Host = host;

        if (parameters.TryGetValue("Database", out var database))
            result.Database = database;

        if (parameters.TryGetValue("Username", out var username) || parameters.TryGetValue("User", out username) || parameters.TryGetValue("User ID", out username))
            result.User = username;

        if (parameters.TryGetValue("Password", out var password))
            result.Password = password;

        if (parameters.TryGetValue("Port", out var portString) && int.TryParse(portString, out var port))
            result.Port = port;

        return result;
    }

    public static string ConvertToLibpqConnectionString(string dotNetConnectionString)
    {
        var parameters = new Dictionary<string, string>();

        // Parse .NET-style connection string (semicolon-separated key=value pairs)
        var parts = dotNetConnectionString.Split(';', StringSplitOptions.RemoveEmptyEntries);
        foreach (var part in parts)
        {
            var keyValue = part.Split('=', 2);
            if (keyValue.Length == 2)
            {
                var key = keyValue[0].Trim();
                var value = keyValue[1].Trim();
                parameters[key] = value;
            }
        }

        // Convert to libpq-style parameters
        var libpqParts = new List<string>();

        if (parameters.TryGetValue("Host", out var host) || parameters.TryGetValue("Server", out host))
            libpqParts.Add($"host={host}");

        if (parameters.TryGetValue("Database", out var database))
            libpqParts.Add($"dbname={database}");

        if (parameters.TryGetValue("Username", out var username) || parameters.TryGetValue("User", out username) || parameters.TryGetValue("User ID", out username))
            libpqParts.Add($"user={username}");

        if (parameters.TryGetValue("Password", out var password))
            libpqParts.Add($"password={password}");

        if (parameters.TryGetValue("Port", out var port))
            libpqParts.Add($"port={port}");

        // Add SSL mode if specified
        if (parameters.TryGetValue("SSL Mode", out var sslMode) || parameters.TryGetValue("SslMode", out sslMode))
        {
            // Convert .NET SSL mode to libpq format
            var libpqSslMode = sslMode.ToLowerInvariant() switch
            {
                "require" => "require",
                "prefer" => "prefer",
                "allow" => "allow",
                "disable" => "disable",
                _ => "prefer"
            };
            libpqParts.Add($"sslmode={libpqSslMode}");
        }

        return string.Join(" ", libpqParts);
    }

    public static string EscapeConnectionString(string connectionString)
    {
        // Escape single quotes in connection string for DuckDB
        return connectionString.Replace("'", "''");
    }

    public static async Task<bool> TestDuckDbConnectionAsync(ILogger logger)
    {
        try
        {
            await using var connection = new DuckDBConnection("DataSource=:memory:;memory_limit=1536MB");
            await connection.OpenAsync();

            using var command = new DuckDBCommand("SELECT 1", connection);
            var result = await command.ExecuteScalarAsync();

            logger.LogInformation("DuckDB connection test successful");
            return Convert.ToInt32(result) == 1;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "DuckDB connection test failed");
            return false;
        }
    }
}