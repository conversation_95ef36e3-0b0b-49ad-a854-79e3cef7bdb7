using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices.JavaScript;
using System.Threading.Tasks;
using Auth0.Core.Exceptions;
using MassTransit;
using Microsoft.Extensions.Logging;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.TenantHub;
using Sleekflow.Ids;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;
using Sleekflow.Persistence;
using Sleekflow.TenantHub.Auth0;
using Sleekflow.TenantHub.Client;
using Sleekflow.TenantHub.Companies;
using Sleekflow.TenantHub.Invitations;
using Sleekflow.TenantHub.Models.Constants;
using Sleekflow.TenantHub.Models.Events;
using Sleekflow.TenantHub.Models.Invitations;
using Sleekflow.TenantHub.Models.Rbac;
using Sleekflow.TenantHub.Models.TravisBackend;
using Sleekflow.TenantHub.Models.Users;
using Sleekflow.TenantHub.Rbac;
using Sleekflow.TenantHub.Roles;

namespace Sleekflow.TenantHub.Users;

public interface IUserInvitationService
{
    Task<List<InviteUserByEmailResponseObject>> CreateAndSendInvitationEmailAsync(
        string sleekflowAdminUserId,
        string sleekflowCompanyId,
        List<InviteUserObject> inviteUsers,
        List<long> teamIds,
        string location,
        string tenantHubUserId);

    Task<(User User, CompleteLinkInvitationResponse Response)> CreateAndCompleteLinkInvitationAsync(
        string shareableId,
        InviteUserByLinkObject userByLink,
        string? location);

    Task<GenerateInviteLinkOutput> GenerateInviteLinkAsync(
        string sleekflowUserId,
        string sleekflowCompanyId,
        ShareableInvitationViewModel shareableInvitation,
        string? location);

    Task<CompleteEmailInvitationResponse> CompleteEmailInvitationAsync(
        string tenantHubUserId,
        string sleekflowUserId,
        string userName,
        string displayName,
        string firstName,
        string lastName,
        string phoneNumber,
        string password,
        string token,
        string position,
        string timeZoneInfoId,
        string location);
}

public class UserInvitationService : IUserInvitationService, IScopedService
{
    private readonly IIdService _idService;
    private readonly ITravisBackendClient _travisBackendClient;
    private readonly IUserService _userService;
    private readonly IRoleService _roleService;
    private readonly IRbacService _rbacService;
    private readonly ILogger<UserInvitationService> _logger;
    private readonly IAuth0UserManagementService _auth0UserManagementService;
    private readonly ICompanyService _companyService;
    private readonly IInvitationLinkService _invitationLinkService;
    private readonly IInvitationLinkRepository _invitationLinkRepository;
    private readonly IBus _bus;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;

    public UserInvitationService(
        IIdService idService,
        ITravisBackendClient travisBackendClient,
        IUserService userService,
        IRoleService roleService,
        IRbacService rbacService,
        ILogger<UserInvitationService> logger,
        IAuth0UserManagementService auth0UserManagementService,
        ICompanyService companyService,
        IInvitationLinkService invitationLinkService,
        IInvitationLinkRepository invitationLinkRepository,
        IBus bus,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer)
    {
        _idService = idService;
        _travisBackendClient = travisBackendClient;
        _userService = userService;
        _roleService = roleService;
        _rbacService = rbacService;
        _logger = logger;
        _auth0UserManagementService = auth0UserManagementService;
        _companyService = companyService;
        _invitationLinkService = invitationLinkService;
        _invitationLinkRepository = invitationLinkRepository;
        _bus = bus;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
    }

    public async Task<List<InviteUserByEmailResponseObject>> CreateAndSendInvitationEmailAsync(
        string sleekflowAdminUserId,
        string sleekflowCompanyId,
        List<InviteUserObject> inviteUsers,
        List<long> teamIds,
        string location,
        string tenantHubUserId)
    {
        try
        {
            if (string.IsNullOrEmpty(sleekflowAdminUserId))
            {
                throw new SfInternalErrorException("Sleekflow admin user id is required");
            }

            // Check usage limit
            var usage = await _travisBackendClient.GetCompanyUsageAsync(sleekflowCompanyId, location);
            if (usage is null)
            {
                throw new SfInternalErrorException("Failed to get company usage");
            }

            if (usage.Usage.MaximumAgents < usage.Usage.TotalAgents + inviteUsers.Count)
            {
                throw new SfExceedUsageLimitException("agents", usage.Usage.TotalAgents, usage.Usage.MaximumAgents);
            }

            // Check for duplicate emails before proceeding
            foreach (var inviteUser in inviteUsers)
            {
                var existingUser = await _userService.GetOrDefaultUserByEmailAsync(inviteUser.Email);
                if (existingUser != null)
                {
                    if (existingUser.UserWorkspaces?.FirstOrDefault()?.SleekflowUserId is not null
                        || existingUser.UserWorkspaces?.FirstOrDefault()?.SleekflowCompanyId != sleekflowCompanyId)
                    {
                        throw new SfDuplicateUserEmailException(inviteUser.Email);
                    }

                    if (existingUser.UserWorkspaces?.FirstOrDefault()?.SleekflowCompanyId == sleekflowCompanyId
                        && existingUser.UserWorkspaces?.FirstOrDefault()?.SleekflowUserId == null)
                    {
                        await _userService.DeleteAsync(existingUser.Id);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create and send invitation email");

            // Track failure in telemetry
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.TenantHubEmailInvitationCreateFailed,
                new Dictionary<string, string>
                {
                    { "admin_user_id", sleekflowAdminUserId },
                    { "company_id", sleekflowCompanyId },
                    { "error", ex.Message },
                    { "error_type", ex.GetType().Name },
                    { "invitation_count", inviteUsers.Count.ToString() }
                });
            throw;
        }

        var createdTenantHubUserList = new List<User>();
        var createdCoreUserList = new List<string>();

        try
        {
            var adminStaff = await _travisBackendClient.GetUserAsync(sleekflowAdminUserId, location);
            var allRoles = await _rbacService.GetAllRolesInCompanyAsync(sleekflowCompanyId);
            var isRbacEnabled = await _rbacService.IsRbacFeatureEnabledForCompanyAsync(sleekflowCompanyId);

            if (!isRbacEnabled)
            {
                var allDefaultRoles = await _roleService.GetAllDefaultRolesAsync();
                var userRole = allDefaultRoles
                    .Select(i => i.Name)
                    .Intersect(adminStaff?.Roles ?? new List<string>())
                    .ToList();
                if (!userRole.Contains("Admin") && !userRole.Contains("SuperAdmin"))
                {
                    throw new SfInternalErrorException($"Access denied. User {sleekflowAdminUserId} does not have Admin or SuperAdmin role.");
                }
            }

            // Create user in Tenant Hub first
            foreach (var inviteUser in inviteUsers)
            {
                try
                {
                    var updateRoleIds = allRoles
                        .Where(r => string.Equals( r.Name, inviteUser.UserRole, StringComparison.OrdinalIgnoreCase))
                        .Select(r => r.Id)
                        .ToList();

                    var updateTeamIds =
                        teamIds?.Select(x => x.ToString())?.ToList()
                        ?? new List<string>();

                    // We can generate username here first to omit the step of updating the username from the response.
                    var newTenantHubUserId = _idService.GetId(SysTypeNames.User);
                    var newSleekflowUserId = Guid.NewGuid().ToString();
                    var userName = $"invite.{newTenantHubUserId}";

                    // For single company.
                    var createdUser = await _userService.CreateAndGetAsync(
                        tenantHubUserId: newTenantHubUserId,
                        username: userName,
                        firstName: string.IsNullOrEmpty(inviteUser.Firstname) ? "-" : inviteUser.Firstname,
                        lastName: string.IsNullOrEmpty(inviteUser.Lastname) ? "-" : inviteUser.Lastname,
                        displayName: $"{inviteUser.Firstname ?? "-"} {inviteUser.Lastname}",
                        email: inviteUser.Email,
                        phoneNumber: string.Empty,
                        userWorkspaces: new List<UserWorkspace>()
                        {
                            new UserWorkspace(
                                sleekflowCompanyId,
                                inviteUser.SleekflowUserId,
                                string.Empty,
                                updateRoleIds,
                                updateTeamIds,
                                true,
                                new List<string>(),
                                new Dictionary<string, object?>()),
                        },
                        profilePictureUrl: string.Empty,
                        metadata: new Dictionary<string, object?>(),
                        createdBy: new AuditEntity.SleekflowStaff(sleekflowAdminUserId, new List<string>()),
                        updatedBy: new AuditEntity.SleekflowStaff(sleekflowAdminUserId, new List<string>()),
                        createdAt: DateTimeOffset.UtcNow,
                        updatedAt: DateTimeOffset.UtcNow);

                    var hasDefaultRole = await _rbacService.IsDefaultRoleAsync(inviteUser.UserRole, true);
                    inviteUser.UserName = createdUser.Username;
                    inviteUser.TenantHubUserId = createdUser.Id;
                    inviteUser.SleekflowUserId = newSleekflowUserId;
                    inviteUser.UserRbacRoles =
                        hasDefaultRole.IsDefault
                            ? null
                            : [inviteUser.UserRole];
                    inviteUser.UserRole =
                        hasDefaultRole.IsDefault
                            ? inviteUser.UserRole
                            : DefaultRoleNames.CustomRole;
                    createdTenantHubUserList.Add(createdUser);
                }
                catch (Exception ex)
                {
                    // Only clean up tenant hub users created so far
                    foreach (var removeUser in createdTenantHubUserList)
                    {
                        var user = await _userService.GetUserAsync(removeUser.Id);
                        if (user != null)
                        {
                            await _userService.DeleteAsync(removeUser.Id);
                        }
                    }

                    _logger.LogError(
                        ex,
                        "[{AndSendInvitationEmailAsyncName}] Failed to create tenant hub user. Email: {Email}, Error: {EMessage}. Cleaned up {Count} tenant hub users",
                        nameof(CreateAndSendInvitationEmailAsync),
                        inviteUser.Email,
                        ex.Message,
                        createdTenantHubUserList.Count);
                    throw;
                }
            }

            try
            {
                var response =
                    await _travisBackendClient.InviteUserByEmailAsync(
                        sleekflowAdminUserId,
                        sleekflowCompanyId,
                        inviteUsers,
                        teamIds ?? new List<long>(),
                        location,
                        tenantHubUserId);

                // Keep track of created DB users
                foreach (var responseItem in response!)
                {
                    createdCoreUserList.Add(responseItem.SleekflowUserId);
                }

                // Update user workspace includes role and staff id from response.
                foreach (var responseItem in response!)
                {
                    var userInput = inviteUsers
                        .Find(i => i.Email == responseItem.Email);

                    var user = createdTenantHubUserList
                        .Find(i => i.Email == responseItem.Email);
                    if (user is null)
                    {
                        throw new SfInternalErrorException(
                            $"[{nameof(CreateAndSendInvitationEmailAsync)}] User {userInput?.Email} not found in tenant hub after core user creation.");
                    }

                    var updateRbacRoleIds = allRoles
                            .Where(r => string.Equals(r.Name, responseItem.UserRbacRoles?.FirstOrDefault(), StringComparison.OrdinalIgnoreCase))
                            .Select(r => r.Id)
                            .ToList();
                    var userRoleIds =
                        (responseItem.UserRole is DefaultRoleNames.CustomRole)
                            ? updateRbacRoleIds
                            : allRoles
                                .Where(r => string.Equals(r.Name, responseItem.UserRole, StringComparison.OrdinalIgnoreCase))
                                .Select(r => r.Id).ToList();

                    var newTeamIds = teamIds != null && teamIds.Count != 0
                        ? teamIds.Select(x => x.ToString()).ToList()
                        : new List<string>();

                    var returnWorkspace = new UserWorkspace(
                        sleekflowCompanyId,
                        responseItem.SleekflowUserId,
                        responseItem.StaffId.ToString(),
                        userRoleIds,
                        newTeamIds,
                        true,
                        new List<string>(),
                        new Dictionary<string, object?>());

                    var userCompanyWorkspace = user.UserWorkspaces
                        .Find(i => i.SleekflowCompanyId == sleekflowCompanyId);

                    if (userCompanyWorkspace is null)
                    {
                        user!.UserWorkspaces.Add(returnWorkspace);
                    }
                    else
                    {
                        user!.UserWorkspaces.Remove(userCompanyWorkspace);
                        user!.UserWorkspaces.Add(returnWorkspace);
                    }

                    // await _userService.UpsertAsync(user);
                    var result= await _userService.UpdateAndGetAsync(user);
                    var roleIdToNameMap = allRoles
                        .Where(r => string.Equals(r.Name, responseItem.UserRole, StringComparison.OrdinalIgnoreCase))
                        .ToDictionary(r => r.Id, r => r.Name);

                    await _bus.Publish(
                        new OnUserRoleAssignedEvent(
                            sleekflowCompanyId,
                            responseItem.SleekflowUserId,
                            responseItem.StaffId.ToString(),
                            roleIdToNameMap,
                            DateTimeOffset.UtcNow));
                }

                // Track successful email invitation creation in telemetry
                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.TenantHubEmailInvitationCreated,
                    new Dictionary<string, string>
                    {
                        { "admin_user_id", sleekflowAdminUserId },
                        { "company_id", sleekflowCompanyId },
                        { "invitation_count", inviteUsers.Count.ToString() },
                        { "emails", string.Join(",", inviteUsers.Select(u => u.Email)) }
                    });

                // Now we have successful email invitations

                return response;
            }
            catch (Exception ex)
            {
                // Clean up both tenant hub users and DB users since we're in an inconsistent state
                foreach (var tenantHubUser in createdTenantHubUserList)
                {
                    var staffId = tenantHubUser.UserWorkspaces.FirstOrDefault()?.SleekflowStaffId;

                    var res = await _userService.DeleteAsync(tenantHubUser.Id);
                    if (!string.IsNullOrEmpty(staffId))
                    {
                        await _travisBackendClient.DeleteCompanyStaffAsync(sleekflowCompanyId, staffId, location);
                    }
                }

                _logger.LogError(
                    ex,
                    "[{AndSendInvitationEmailAsyncName}] Failed to create core users or update workspaces. Error: {EMessage}. Cleaned up {TenantHubCount} tenant hub users and {CoreCount} core users.",
                    nameof(CreateAndSendInvitationEmailAsync),
                    ex.Message,
                    createdTenantHubUserList.Count,
                    createdCoreUserList.Count);
                throw;
            }
        }
        catch (Exception e)
        {
            // Clean up both tenant hub users and DB users since we encountered an unknown error
            foreach (var tenantHubUser in createdTenantHubUserList)
            {
                await _userService.DeleteAsync(tenantHubUser.Id);
            }

            foreach (var coreUserId in createdCoreUserList)
            {
                await _travisBackendClient.DeleteDbUserAsync(coreUserId, true, location);
            }

            _logger.LogError(
                e,
                "[{AndSendInvitationEmailAsyncName}] Failed to invite users. Admin: {AdminId}, Company: {CompanyId}, Location: {Location}, Error: {EMessage}",
                nameof(CreateAndSendInvitationEmailAsync),
                sleekflowAdminUserId,
                sleekflowCompanyId,
                location,
                e.Message);

            // Track failure in telemetry
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.TenantHubEmailInvitationCreateFailed,
                new Dictionary<string, string>
                {
                    { "admin_user_id", sleekflowAdminUserId },
                    { "company_id", sleekflowCompanyId },
                    { "error", e.Message },
                    { "error_type", e.GetType().Name },
                    { "invitation_count", inviteUsers.Count.ToString() }
                });
            throw;
        }
    }

    public async Task<(User User, CompleteLinkInvitationResponse Response)> CreateAndCompleteLinkInvitationAsync(
        string shareableId,
        InviteUserByLinkObject userByLink,
        string? location)
    {
        User? createdTenantHubUser = null;
        string? sleekflowUserId = null;
        string? invitedUserId = null;
        InvitationLink? invitation = null;

        try
        {
            // 1. Check if the invitation ID exists and is valid
            var invitationLink = await _invitationLinkRepository.GetObjectsAsync(
                link => link.InvitationId == shareableId);

            if (invitationLink.Count == 0)
            {
                throw new SfNotFoundObjectException("invitation link", shareableId);
            }

            invitation = invitationLink[0];

            // 2. Validate required user properties
            if (string.IsNullOrWhiteSpace(userByLink.UserName))
            {
                throw new SfInvalidValueException(nameof(userByLink.UserName), "Username cannot be null or empty.");
            }

            if (string.IsNullOrWhiteSpace(userByLink.Firstname))
            {
                throw new SfInvalidValueException(nameof(userByLink.Firstname), "First name cannot be null or empty.");
            }

            if (string.IsNullOrWhiteSpace(userByLink.Lastname))
            {
                throw new SfInvalidValueException(nameof(userByLink.Lastname), "Last name cannot be null or empty.");
            }

            // 3. Check if the invitation link has expired
            if (invitation.ExpiryDate < DateTimeOffset.UtcNow)
            {
                throw new SfInternalErrorException($"Invitation link with ID {shareableId} has expired.");
            }

            sleekflowUserId = Guid.NewGuid().ToString();
            createdTenantHubUser = await _userService.CreateAndGetAsync(
                username: userByLink.UserName,
                firstName: userByLink.Firstname,
                lastName: userByLink.Lastname,
                displayName: $"{userByLink.Firstname} {userByLink.Lastname}",
                email: userByLink.Email,
                phoneNumber: userByLink.PhoneNumber,
                userWorkspaces: new List<UserWorkspace>(),
                profilePictureUrl: null,
                metadata: new Dictionary<string, object?>(),
                null);

            if (createdTenantHubUser is null)
            {
                throw new SfInternalErrorException($"Failed to create user with email {userByLink.Email}.");
            }

            userByLink.UserId = sleekflowUserId;
            userByLink.TenantHubUserId = createdTenantHubUser.Id;
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "[{CompleteLinkInvitationAsyncName}] Failed to create user by invite link. Email: {Email}, Error: {EMessage}",
                nameof(CreateAndCompleteLinkInvitationAsync),
                userByLink.Email,
                e.Message);

            // Track failure in telemetry
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.TenantHubLinkInvitationFailed,
                new Dictionary<string, string>
                {
                    { "user_id", createdTenantHubUser?.Id ?? "not created" },
                    { "email", userByLink.Email },
                    { "invitation_id", shareableId },
                    { "error", e.Message },
                    { "error_type", e.GetType().Name }
                });

            throw;
        }

        try
        {
            var result = await _travisBackendClient.CompleteLinkInvitationAsync(
                shareableId, userByLink, location);

            invitedUserId = result?.UserId;

            // Get company ID from the invitation link
            var companyId = invitation!.SleekflowCompanyId;
            var allRoles = await _rbacService.GetAllRolesInCompanyAsync(result?.CompanyId);
            var updateRoles = allRoles?.Where(
                    x => string.Equals(x.Name, result?.RoleType, StringComparison.OrdinalIgnoreCase))
                .ToDictionary(x => x.Id, x => x.Name);

            // 2. Use the roleIds from the InvitationLink rbac_roles
            var updateRoleIds = new List<string>();

            if (invitation.RbacRoles.Any())
            {
                updateRoleIds = invitation.RbacRoles.Select(r => r.Id).ToList();
            }
            else
            {
                // Fallback to get role IDs from the result if no roles in invitation
                updateRoleIds = allRoles?
                    .Where(x => string.Equals(x.Name, result?.RoleType, StringComparison.OrdinalIgnoreCase))
                    .Select(x => x.Id)
                    .ToList() ?? new List<string>();
            }

            // Use team IDs from invitation if available, otherwise use from result
            var teamIds = invitation.TeamIds?.Any() == true
                ? invitation.TeamIds
                : result.TeamIds;

            createdTenantHubUser!.UserWorkspaces = new List<UserWorkspace>
            {
                new UserWorkspace(
                    sleekflowCompanyId: companyId,
                    sleekflowUserId: sleekflowUserId!,
                    sleekflowStaffId: result.StaffId,
                    sleekflowRoleIds: updateRoleIds,
                    sleekflowTeamIds: teamIds,
                    isDefault: true,
                    additionalPermissions: new List<string>(),
                    metadata: new Dictionary<string, object?>())
            };

            var updatedUser = await _userService.UpdateAndGetAsync(createdTenantHubUser);

            await _bus.Publish(
                new OnUserRoleAssignedEvent(
                    result.CompanyId,
                    sleekflowUserId!,
                    result.StaffId,
                    updateRoles!,
                    DateTimeOffset.UtcNow));

            // Track successful link invitation completion in telemetry
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.TenantHubLinkInvitationCompleted,
                new Dictionary<string, string>
                {
                    { "user_id", createdTenantHubUser.Id },
                    { "sleekflow_user_id", sleekflowUserId! },
                    { "email", userByLink.Email },
                    { "invitation_id", shareableId }
                });

            return (updatedUser, result);
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "[{CompleteLinkInvitationAsyncName}] Failed to create user by invite link. Email: {Email}, Error: {EMessage}",
                nameof(CreateAndCompleteLinkInvitationAsync),
                userByLink.Email,
                e.Message);

            // Track failure in telemetry
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.TenantHubLinkInvitationFailed,
                new Dictionary<string, string>
                {
                    { "user_id", createdTenantHubUser?.Id ?? "not created" },
                    { "email", userByLink.Email },
                    { "invitation_id", shareableId },
                    { "error", e.Message },
                    { "error_type", e.GetType().Name }
                });

            if (createdTenantHubUser is not null)
            {
                // Clean up the created tenant hub user
                await _userService.DeleteAsync(createdTenantHubUser.Id);
            }

            if (invitedUserId is not null)
            {
                // Clean up the created DB user
                await _travisBackendClient.DeleteDbUserAsync(invitedUserId, true, location);
            }

            throw;
        }
    }

    public async Task<GenerateInviteLinkOutput> GenerateInviteLinkAsync(
        string sleekflowUserId,
        string sleekflowCompanyId,
        ShareableInvitationViewModel shareableInvitation,
        string? location)
    {
        var isDefaultRole = await _rbacService.IsDefaultRoleAsync(shareableInvitation.Role, true);
        if (!isDefaultRole.IsDefault)
        {
            shareableInvitation.RbacRole = shareableInvitation.Role;
            shareableInvitation.Role = DefaultRoleNames.CustomRole;
        }

        // Generate the invite link through Travis Backend
        var response = await _travisBackendClient.GenerateInviteLinkAsync(
            sleekflowUserId,
            shareableInvitation,
            location);

        // Store the invitation link in our database
        if (response != null)
        {
            try
            {
                // Get RbacRoles based on the role
                List<RbacRole> rbacRoles = new List<RbacRole>();
                if (!string.IsNullOrEmpty(shareableInvitation.RbacRole))
                {
                    var allRoles = await _rbacService.GetAllRolesInCompanyAsync(sleekflowCompanyId);
                    rbacRoles = allRoles
                        .Where(
                            r => string.Equals(
                                r.Name,
                                isDefaultRole.IsDefault
                                    ? shareableInvitation.Role
                                    : shareableInvitation.RbacRole,
                                StringComparison.OrdinalIgnoreCase))
                        .ToList();
                }

                // Convert teamIds from long to string
                var teamIdsStr = response.TeamIds?
                    .Select(id => id.ToString())
                    .ToList() ?? new List<string>();

                // Store invitation link details in our database
                await _invitationLinkService.UpsertByInvitationIdAsync(
                    sleekflowCompanyId,
                    response.InvitationId,
                    rbacRoles,
                    teamIdsStr,
                    response.ExpirationDate);
            }
            catch (SfInternalErrorException ex) when (ex.Message.Contains("already exists"))
            {
                // Log the exception but don't re-throw it, as we already have a valid TravisBackend response
                _logger.LogWarning(ex, "Could not store invitation link due to duplicate ID, but TravisBackend invitation was successful");
            }
            catch (Exception ex)
            {
                // Log other exceptions but don't fail the operation
                _logger.LogError(ex, "Error storing invitation link in database: {Message}", ex.Message);
                // No rethrow since we want to continue with valid TravisBackend response
            }
        }

        _applicationInsightsTelemetryTracer.TraceEvent(
            TraceEventNames.TenantHubLinkInvitationCreated,
            new Dictionary<string, string>
            {
                { "user_id", sleekflowUserId },
                { "company_id", sleekflowCompanyId },
                { "invitation_id", response?.InvitationId ?? string.Empty }
            });

        return response;
    }

    public async Task<CompleteEmailInvitationResponse> CompleteEmailInvitationAsync(
        string tenantHubUserId,
        string sleekflowUserId,
        string userName,
        string displayName,
        string firstName,
        string lastName,
        string phoneNumber,
        string password,
        string token,
        string position,
        string timeZoneInfoId,
        string location)
    {
        try
        {
            // Get user from database
            var user = await _userService.GetUserAsync(tenantHubUserId);
            if (user is null)
            {
                throw new SfInternalErrorException(
                    $"[{nameof(CompleteEmailInvitationAsync)}] User not found when trying to complete email invitation.");
            }

            // Update user properties
            user.Username = userName;
            user.DisplayName = displayName;
            user.FirstName = firstName;
            user.LastName = lastName;
            user.PhoneNumber = phoneNumber;

            // Complete the email invitation through the Travis backend
            var response = await _travisBackendClient.CompleteEmailInvitationAsync(
                user,
                sleekflowUserId,
                token,
                password,
                position,
                timeZoneInfoId,
                location);

            // Update the user in the database
            await _userService.UpdateAndGetAsync(user);

            // Track successful invitation completion in telemetry
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.TenantHubEmailInvitationCompleted,
                new Dictionary<string, string>
                {
                    { "user_id", tenantHubUserId },
                    { "sleekflow_user_id", sleekflowUserId },
                    { "email", user.Email }
                });

            // Ensure we don't return null
            if (response == null)
            {
                _logger.LogWarning(
                    "[{MethodName}] Received null response from Travis backend for tenantHubUserId: {TenantHubUserId}",
                    nameof(CompleteEmailInvitationAsync),
                    tenantHubUserId);

                // The trigger expects a CompleteEmailInvitationResponse object
                // Pass null for updatedUsers parameter as we're just creating a default response
                return new CompleteEmailInvitationResponse("Invitation completed successfully", null);
            }

            return response;
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "[{MethodName}] Error while completing email invitation: {ErrorMessage}",
                nameof(CompleteEmailInvitationAsync),
                e.Message);

            // Track failure in telemetry
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.TenantHubEmailInvitationFailed,
                new Dictionary<string, string>
                {
                    { "user_id", tenantHubUserId },
                    { "sleekflow_user_id", sleekflowUserId },
                    { "error", e.Message },
                    { "error_type", e.GetType().Name }
                });

            // Rethrow with more context
            throw new SfInternalErrorException(e, $"Error completing email invitation for user {tenantHubUserId}");
        }
    }
}
