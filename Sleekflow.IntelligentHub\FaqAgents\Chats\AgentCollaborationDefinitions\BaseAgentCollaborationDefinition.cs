using System.Net;
using System.Text;
using AngleSharp.Dom;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Microsoft.SemanticKernel.Agents.Chat;
using Microsoft.SemanticKernel.Agents.Orchestration.GroupChat;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.Google;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Newtonsoft.Json;
using OpenAI.Chat;
using PhoneNumbers;
using Sleekflow.IntelligentHub.Companies.CompanyConfigs;
using Sleekflow.IntelligentHub.FaqAgents.Chats.Enrichers;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Companies.CompanyConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.IntelligentHub.Utils;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;
using ChatMessageContent = Microsoft.SemanticKernel.ChatMessageContent;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions;

public abstract class BaseAgentCollaborationDefinition : IAgentCollaborationDefinition
{
    private readonly ILogger<BaseAgentCollaborationDefinition> _logger;
    private readonly Kernel _kernel;
    private readonly ISummaryPlugin _summaryPlugin;
    private readonly ILanguagePlugin _languagePlugin;
    private readonly IAgentDurationTracker _agentDurationTracker;
    private readonly ICompanyConfigService _companyConfigService;
    private readonly IChatHistoryEnricherFactory _enricherFactory;
    private readonly IFileContentExtractionPlugin _fileContentExtractionPlugin;

    protected BaseAgentCollaborationDefinition(
        ILogger<BaseAgentCollaborationDefinition> logger,
        Kernel kernel,
        ISummaryPlugin summaryPlugin,
        ILanguagePlugin languagePlugin,
        IAgentDurationTracker agentDurationTracker,
        ICompanyConfigService companyConfigService,
        IChatHistoryEnricherFactory enricherFactory,
        IFileContentExtractionPlugin fileContentExtractionPlugin)
    {
        _logger = logger;
        _kernel = kernel;
        _summaryPlugin = summaryPlugin;
        _languagePlugin = languagePlugin;
        _agentDurationTracker = agentDurationTracker;
        _companyConfigService = companyConfigService;
        _enricherFactory = enricherFactory;
        _fileContentExtractionPlugin = fileContentExtractionPlugin;
    }

    public abstract Task<List<Agent>> CreateAgents(
        Kernel kernel,
        List<SfChatEntry> chatEntries,
        string sleekflowCompanyId,
        AgentCollaborationConfig agentCollaborationConfig,
        BaseInternalAgentCreationConfiguration? internalAgentCreationConfiguration = null);

    public virtual GroupChatManager? GetGroupChatManager()
    {
        return null;
    }

    public abstract SelectionStrategy? CreateSelectionStrategy(Kernel kernel);

    public abstract RegexTerminationStrategy? CreateTerminationStrategy(List<Agent> agents);

    protected async Task<SanitizedChatResult> GetSanitizedChatResult(
        List<SfChatEntry> chatEntries,
        AgentCollaborationConfig agentCollaborationConfig,
        CompanyConfig companyConfig,
        int? topK = null)
    {
        SanitizedChatResult sanitizedChatResult;
        if (agentCollaborationConfig.EnableFileTranscription)
        {
            sanitizedChatResult = await GroupChatHistoryUtils.GetSanitizedChatEntriesWithFileContentAsync(
                chatEntries,
                _kernel,
                _fileContentExtractionPlugin,
                companyConfig.BackgroundInformation);
        }
        else
        {
            sanitizedChatResult = GroupChatHistoryUtils.GetSanitizedChatEntries(
                chatEntries);
        }

        if (topK != null)
        {
            sanitizedChatResult = new SanitizedChatResult(
                [.. sanitizedChatResult.EntryTexts.TakeLast(topK.Value)],
                [.. sanitizedChatResult.ChatEntries.TakeLast(topK.Value)]);
        }

        return sanitizedChatResult;
    }

    protected async Task<(
        List<string> EnricherSectionExplanations,
        string EnrichmentSectionsStr)> GetEnrichmentSectionsStr(
        AgentCollaborationConfig agentCollaborationConfig,
        ReplyGenerationContext replyGenerationContext)
    {
        var contactProperties = EnrichPhoneNumberAndCountry(
            replyGenerationContext.ContactProperties ?? new Dictionary<string, string>());

        // Create enrichers based on company config
        var enrichers = _enricherFactory.CreateEnrichers(agentCollaborationConfig.EnricherConfigs, _kernel);
        _logger.LogInformation("Created {Count} enrichers", enrichers.Count);

        var enrichmentResults = new List<string>();
        foreach (var enricher in enrichers)
        {
            enrichmentResults.Add(
                await RunEnricherSafelyAsync(enricher, replyGenerationContext, contactProperties, _kernel));
        }

        // Build additional sections for the chat message
        var enrichmentSections = new StringBuilder();
        for (int i = 0; i < enrichers.Count; i++)
        {
            var enricher = enrichers[i];
            var result = enrichmentResults[i];

            if (string.IsNullOrEmpty(result))
            {
                continue;
            }

            enrichmentSections.AppendLine($"===={enricher.GetContextSectionName()}====");
            enrichmentSections.AppendLine(result);
            enrichmentSections.Append($"===={enricher.GetContextSectionName()}====");

            if (i != enrichers.Count - 1)
            {
                enrichmentSections.AppendLine();
                enrichmentSections.AppendLine();
            }
        }

        return (
            [.. enrichers.Select(enricher => $"{enricher.GetContextSectionExplanation()}")],
            enrichmentSections.ToString());
    }

    public virtual async Task<(string ChatHistoryStr, string Context)> InitializeChatHistoryAsync(
        AgentGroupChat? agentGroupChat,
        string groupChatIdStr,
        List<SfChatEntry> chatEntries,
        ReplyGenerationContext replyGenerationContext,
        AgentCollaborationConfig agentCollaborationConfig,
        CompanyAgentConfig? agentConfig)
    {
        var companyConfig = await _companyConfigService.GetConfigAsync(replyGenerationContext.SleekflowCompanyId);

        var sanitizedChatResult = await GetSanitizedChatResult(
            chatEntries,
            agentCollaborationConfig,
            companyConfig);

        var summaryTask = _summaryPlugin.SummarizeConversationAsync(_kernel, sanitizedChatResult.EntryTexts);
        var detectedLanguageTask = GetDetectedLanguageAsync(sanitizedChatResult);

        await Task.WhenAll(summaryTask, detectedLanguageTask);

        var chatHistoryStr = await summaryTask;
        _logger.LogInformation("Chat history: {ChatHistoryStr}", chatHistoryStr);

        var detectAppropriateResponseLanguageResponse = await detectedLanguageTask;
        agentCollaborationConfig.DetectedResponseLanguage = detectAppropriateResponseLanguageResponse;

        var allContactProperties = replyGenerationContext.ContactProperties ?? new Dictionary<string, string>();
        _logger.LogInformation(
            "All Contact Properties: {AllContactProperties}",
            JsonConvert.SerializeObject(allContactProperties));

        var (enricherSectionExplanations, enrichmentSectionsStr) = await GetEnrichmentSectionsStr(
            agentCollaborationConfig,
            replyGenerationContext);

        // Prepare the explanation of each section for the agent
        var sectionExplanations = new List<string>
        {
            "BACKGROUND is a configuration specifying our the company or agent's background information.",
            "REQUESTED TONE is a configuration specifying the desired tone of the response.",
            "RESPONSE LANGUAGE is the language specifying the appropriate language for the final response.",
            "CURRENT TIME is the current UTC time when this conversation is happening. Reference this time when discussing time-sensitive topics, operating hours, or when the customer asks about current time-related information."
        };
        sectionExplanations.AddRange(
            enricherSectionExplanations);
        sectionExplanations.Add(
            $"CONVERSATION CONTEXT is the historical messages between our company and the customer. {(sanitizedChatResult.EntryTexts.Count > 10 ? "Please note that the conversation is very long, it is summarized." : string.Empty)}");
        sectionExplanations.Add(
            "ADDITIONAL INSTRUCTION is a configuration specifying the additional instruction that each agent should understand and adjust your behaviour.");

        sectionExplanations.Add("GUARDRAILS is a rules that the agent should follow. Observe these rules carefully and follow them strictly.");

        var (greetingBehaviourSection, greetingSectionExplanation) = GetGreetingBehaviour(agentConfig?.PromptInstruction?.GreetingMessage);

        // Add greeting section explanation if it exists
        if (greetingSectionExplanation != null)
        {
            sectionExplanations.Add(greetingSectionExplanation);
        }

        var detectedLanguageSection = string.IsNullOrEmpty(detectAppropriateResponseLanguageResponse)
            ? string.Empty
            : $"""
               ====RESPONSE LANGUAGE====
               {agentCollaborationConfig.DetectedResponseLanguage}
               ====RESPONSE LANGUAGE====
               """;

        var context =
            $"""
             ====BACKGROUND====
             {agentConfig?.PromptInstruction?.Objective ?? companyConfig.BackgroundInformation}
             ====BACKGROUND====

             {greetingBehaviourSection}

             ====REQUESTED TONE====
             {agentConfig?.Actions?.SendMessage?.Instructions ?? GetRequestedToneContext(agentCollaborationConfig.Tone)}
             ====REQUESTED TONE====

             {detectedLanguageSection}

             ====CURRENT TIME====
             {DateTimeOffset.UtcNow.ToString("yyyy-MM-dd HH:mm:ss")} UTC
             ====CURRENT TIME====

             {enrichmentSectionsStr}

             ====CONVERSATION CONTEXT====
             {chatHistoryStr}
             ====CONVERSATION CONTEXT====

             ====ADDITIONAL INSTRUCTION====
             {(string.IsNullOrWhiteSpace(agentCollaborationConfig.AdditionalInstructionCore)
                 ? "No additional instructions."
                 : agentCollaborationConfig.AdditionalInstructionCore)}
             ====ADDITIONAL INSTRUCTION====

             ====GUARDRAILS====
             {AgentUtils.TranslateGuardrailsToInstructions(agentConfig?.PromptInstruction?.Guardrails)}
             ====GUARDRAILS====

             {string.Join("\n", sectionExplanations)}
             """;

        // Modify the chat message to include enrichment sections
        agentGroupChat?.AddChatMessage(
            new ChatMessageContent(
                AuthorRole.User,
                context)
            {
                AuthorName = "Context"
            });

        return (chatHistoryStr, context);
    }

    protected async Task<string> RunEnricherSafelyAsync(
        IChatHistoryEnricher enricher,
        ReplyGenerationContext context,
        Dictionary<string, string>? contactProperties,
        Kernel kernel,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Running enricher {EnricherType}", enricher.GetType().Name);
            var startTime = DateTimeOffset.UtcNow;
            var result = await enricher.EnrichAsync(context, contactProperties, kernel, cancellationToken);
            var duration = DateTimeOffset.UtcNow - startTime;

            _logger.LogInformation(
                "Enricher {EnricherType} completed in {DurationMs}ms",
                enricher.GetType().Name,
                duration.TotalMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error running enricher {EnricherType}",
                enricher.GetType().Name);
            return $"Error retrieving data from {enricher.GetContextSectionName().ToLower()}";
        }
    }

    // TODO: 1. Testing the tone
    // TODO: 2. Update the ReviewerAgent to use the tone as the desired tone
    protected static string GetRequestedToneContext(string? tone = null)
    {
        return tone switch
        {
            TargetToneTypes.Professional =>
                "Respond with a professional tone, keeping things clear and structured while staying approachable. Use formal language but ensure it's polite and easy to follow. Avoid overly casual expressions or emojis, but still encourage the conversation to continue with a helpful attitude.",
            TargetToneTypes.Casual =>
                "Go for a friendly, casual tone, using easy-going and conversational language. Emojis and light exclamations are welcome to make the conversation feel more relaxed and engaging. Be sure to invite further questions or discussion in a warm, approachable way.",
            TargetToneTypes.Technical =>
                "Respond with a technical tone, focusing on delivering clear, accurate, and data-driven information. Use precise language to explain the facts while avoiding unnecessary elaboration. Maintain professionalism and authority, but also ensure the response is encouraging and open for follow-up or further clarification.",
            _ =>
                "Choose a tone that suits the conversation's context (e.g., warm, curious) with a vibe that's inviting and encourages engagement. Make it conversational by adapting the language to feel natural and open."
        };
    }

    private (string greetingBehaviourSection, string? sectionExplanation) GetGreetingBehaviour(string? greetingMessage)
    {
        if (string.IsNullOrEmpty(greetingMessage))
        {
            return (string.Empty, null);
        }

        var greetingBehaviourSection = $"""
                ====GREETING BEHAVIOUR====
                When the conversation is just starting (first interaction), follow these rules:
                1. ALWAYS start your response with exactly this greeting message: "{greetingMessage}"
                2. If the user's first message contains a question or request for information, provide a proper answer to their question AFTER the greeting message.
                3. If the user's first message is just a greeting (like "Hi", "Hello", etc.), respond with only the greeting message.
                4. Use the appropriate response language as specified in the RESPONSE LANGUAGE section, but the greeting message should remain exactly as defined.
                Example format when user has a question: "{greetingMessage}\n\n[Answer to their question]"
                ====GREETING BEHAVIOUR====
                """;

        var sectionExplanation = "GREETING BEHAVIOUR is a configuration specifying the exact greeting message to use when starting conversations and the rules for how to apply it.";

        return (greetingBehaviourSection, sectionExplanation);
    }

    public virtual Task<bool> InterceptAgentReplyAsync(
        ChatMessageContent response,
        string groupChatIdStr)
    {
        var responseAuthorName = response.AuthorName ?? string.Empty;
        var responseContent = response.Content ?? string.Empty;

        switch (response)
        {
            case OpenAIChatMessageContent { InnerContent: ChatCompletion chatCompletion }:
                var usageInputTokenCount = chatCompletion.Usage.InputTokenCount;
                var usageOutputTokenCount = chatCompletion.Usage.OutputTokenCount;

                // Track response metrics
                _agentDurationTracker.TrackResponse(
                    usageInputTokenCount,
                    usageOutputTokenCount,
                    responseAuthorName,
                    responseContent);
                break;

#pragma warning disable SKEXP0070
            case GeminiChatMessageContent:
#pragma warning restore SKEXP0070
            {
                var inputTokenCount = (int) (response.Metadata?["PromptTokenCount"] ?? 0);
                var outputTokenCount = (int) (response.Metadata?["CandidatesTokenCount"] ?? 0);

                // Track response metrics with default token counts
                _agentDurationTracker.TrackResponse(
                    inputTokenCount,
                    outputTokenCount,
                    responseAuthorName,
                    responseContent);
                break;
            }

            default:
                // Track response metrics with default token counts
                _agentDurationTracker.TrackResponse(
                    0, // No token count available
                    0, // No token count available
                    responseAuthorName,
                    responseContent);
                break;
        }

        return Task.FromResult(true);
    }

    public abstract string GetFinalReplyTag();

    public abstract string GetSourceTag();

    public virtual async Task<string> GetFinalReplyAsync(ChatHistory chatHistory)
    {
        var finalChatMessage = chatHistory
            .Last(x => x.Content != null && x.Content.Contains(GetFinalReplyTag()))
            .Content;

        if (finalChatMessage == null)
        {
            _logger.LogError("Final chat response missing or invalid");
        }

        var finalReplyToCustomer = TagUtils.ExtractContentBetweenTags(
            finalChatMessage!,
            GetFinalReplyTag()).Trim();

        // We encountered a scenario where the final reply to the customer was not decoded properly.
        // The emoji is displayed as a string instead of the actual emoji.
        // e.g. "👍" is displayed as "&#128077;". We need to decode the HTML entities.
        finalReplyToCustomer = WebUtility.HtmlDecode(finalReplyToCustomer);

        // Convert HTML-style formatting tags to WhatsApp markdown format
        finalReplyToCustomer = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(finalReplyToCustomer);

        return finalReplyToCustomer;
    }

    public virtual async Task<string> GetSourceAsync(ChatHistory chatHistory, string groupChatIdStr)
    {
        var allSourceMessages = chatHistory
            .Where(x => x.Content != null && x.Content.Contains(GetSourceTag()))
            .Select(m => TagUtils.ExtractContentBetweenTags(m.Content!, GetSourceTag()))
            .ToList();
        var sourceStr = string.Join("\n", allSourceMessages);

        return sourceStr;
    }

    public virtual async Task<string> GetDetectedLanguageAsync(SanitizedChatResult sanitizedChatResult)
    {
        var languageTask = await _languagePlugin.DetectAppropriateResponseLanguageAsync(
            _kernel,
            string.Join("--\n", sanitizedChatResult.EntryTexts));
        return languageTask.ResponseLanguageName;
    }

    /// <summary>
    /// Formats phone number fields in the contact properties to international format.
    /// </summary>
    protected Dictionary<string, string> EnrichPhoneNumberAndCountry(Dictionary<string, string> properties)
    {
        var phoneNumberUtil = PhoneNumberUtil.GetInstance();
        var phoneNumberOfflineGeocoder = PhoneNumberOfflineGeocoder.GetInstance();

        var result = new Dictionary<string, string>(properties);

        if (properties.TryGetValue("PhoneNumber", out var phoneNumberStr)
            && !string.IsNullOrWhiteSpace(phoneNumberStr))
        {
            try
            {
                // Try parsing with no region first (for numbers that may already include country code)
                var phoneNumber = phoneNumberUtil.Parse(
                    phoneNumberStr.StartsWith('+') ? phoneNumberStr : $"+{phoneNumberStr}",
                    null);

                // Format as international
                if (phoneNumberUtil.IsValidNumber(phoneNumber))
                {
                    result["PhoneNumber"] = phoneNumberUtil.Format(phoneNumber, PhoneNumberFormat.INTERNATIONAL);
                    result["Country"] =
                        phoneNumberOfflineGeocoder.GetDescriptionForNumber(phoneNumber, Locale.English);

                    _logger.LogInformation("Successfully formatted phone number to international format");
                }
            }
            catch (NumberParseException ex)
            {
                _logger.LogWarning(ex, "Failed to parse phone number: {PhoneNumber}", phoneNumberStr);
            }
        }

        return result;
    }
}