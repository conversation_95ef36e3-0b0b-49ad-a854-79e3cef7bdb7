using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents.FileDocuments;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.Chunks;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.KnowledgeBases;

[TriggerGroup(
    ControllerNames.KnowledgeBases,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class GetFileDocumentChunks
    : ITrigger<
        GetFileDocumentChunks.GetFileDocumentChunksInput,
        GetFileDocumentChunks.GetFileDocumentChunksOutput>
{
    private readonly ISleekflowAuthorizationContext _authorizationContext;
    private readonly IFileDocumentChunkService _fileDocumentChunkService;

    public GetFileDocumentChunks(
        ISleekflowAuthorizationContext authorizationContext,
        IFileDocumentChunkService fileDocumentChunkService)
    {
        _authorizationContext = authorizationContext;
        _fileDocumentChunkService = fileDocumentChunkService;
    }

    public class GetFileDocumentChunksInput
    {
        [Required]
        [JsonProperty("document_id")]
        public string DocumentId { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [Required]
        [Range(1, 1000)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonConstructor]
        public GetFileDocumentChunksInput(
            string documentId,
            string? continuationToken,
            int limit)
        {
            DocumentId = documentId;
            ContinuationToken = continuationToken;
            Limit = limit;
        }
    }

    public class GetFileDocumentChunksOutput
    {
        [JsonProperty("document_chunks")]
        public List<ChunkDto> DocumentChunks { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonConstructor]
        public GetFileDocumentChunksOutput(List<ChunkDto> documentChunks, string? nextContinuationToken)
        {
            DocumentChunks = documentChunks;
            NextContinuationToken = nextContinuationToken;
        }
    }

    public async Task<GetFileDocumentChunksOutput> F(GetFileDocumentChunksInput input)
    {
        var (chunks, nextContinuationToken) =
            await _fileDocumentChunkService.GetFileDocumentChunksAsync(
                _authorizationContext.SleekflowCompanyId!,
                input.DocumentId,
                input.ContinuationToken,
                input.Limit);

        return new GetFileDocumentChunksOutput(chunks.Select(c => new ChunkDto(c)).ToList(), nextContinuationToken);
    }
}