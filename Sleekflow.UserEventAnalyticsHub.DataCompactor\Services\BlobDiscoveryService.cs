using Azure.Storage.Files.DataLake;
using Azure.Storage.Files.DataLake.Models;
using Microsoft.Extensions.Logging;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Configs;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Models;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Utils;

namespace Sleekflow.UserEventAnalyticsHub.DataCompactor.Services;

public class BlobDiscoveryService : IBlobDiscoveryService
{
    private readonly ILogger<BlobDiscoveryService> _logger;
    private readonly ICompactorConfig _config;
    private readonly DataLakeServiceClient _dataLakeClient;

    public BlobDiscoveryService(ILogger<BlobDiscoveryService> logger, ICompactorConfig config)
    {
        _logger = logger;
        _config = config;

        var connectionString = $"DefaultEndpointsProtocol=https;AccountName={_config.StorageAccountName};AccountKey={_config.StorageAccountKey};EndpointSuffix=core.windows.net";
        _dataLakeClient = new DataLakeServiceClient(connectionString);
    }

    public async Task<List<BlobFileInfo>> DiscoverCompanyFilesAsync(string sleekflowCompanyId)
    {
        try
        {
            _logger.LogInformation("Discovering files for company: {CompanyId}", sleekflowCompanyId);

            var fileSystemClient = _dataLakeClient.GetFileSystemClient(_config.EventsContainerName);
            var files = new List<BlobFileInfo>();

            // Build path pattern for the company
            var pathPrefix = $"sleekflowCompanyId={sleekflowCompanyId}";

            await foreach (var pathItem in fileSystemClient.GetPathsAsync(path: pathPrefix, recursive: true))
            {
                if (pathItem.IsDirectory != true && BlobPathParser.IsParquetFile(pathItem.Name))
                {
                    var blobInfo = await CreateBlobFileInfoAsync(pathItem);
                    if (blobInfo != null)
                    {
                        files.Add(blobInfo);
                    }
                }
            }

            _logger.LogInformation("Discovered {FileCount} files for company: {CompanyId}", files.Count, sleekflowCompanyId);
            return files.OrderBy(f => f.Year).ThenBy(f => f.Month).ThenBy(f => f.Day).ThenBy(f => f.Hour).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error discovering files for company: {CompanyId}", sleekflowCompanyId);
            throw;
        }
    }

    public async Task<List<string>> GetAllCompanyIdsAsync()
    {
        try
        {
            _logger.LogInformation("Discovering all company IDs");

            var fileSystemClient = _dataLakeClient.GetFileSystemClient(_config.EventsContainerName);
            var companyIds = new HashSet<string>();

            await foreach (var pathItem in fileSystemClient.GetPathsAsync(recursive: true))
            {
                if (pathItem.IsDirectory != true && BlobPathParser.IsParquetFile(pathItem.Name))
                {
                    var companyId = BlobPathParser.ExtractCompanyId(pathItem.Name);
                    if (!string.IsNullOrEmpty(companyId))
                    {
                        companyIds.Add(companyId);
                    }
                }
            }

            var result = companyIds.OrderBy(id => id).ToList();
            _logger.LogInformation("Discovered {CompanyCount} companies", result.Count);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error discovering company IDs");
            throw;
        }
    }

    public async Task<BlobFileInfo> GetBlobMetadataAsync(string blobPath)
    {
        try
        {
            var fileSystemClient = _dataLakeClient.GetFileSystemClient(_config.EventsContainerName);
            var fileClient = fileSystemClient.GetFileClient(blobPath);

                        var properties = await fileClient.GetPropertiesAsync();
            return CreateBlobFileInfoFromProperties(blobPath, properties.Value.LastModified, properties.Value.ContentLength);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting blob metadata for: {BlobPath}", blobPath);
            throw;
        }
    }

    public async Task<bool> BlobExistsAsync(string blobPath)
    {
        try
        {
            var fileSystemClient = _dataLakeClient.GetFileSystemClient(_config.EventsContainerName);
            var fileClient = fileSystemClient.GetFileClient(blobPath);

            var response = await fileClient.ExistsAsync();
            return response.Value;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking blob existence for: {BlobPath}", blobPath);
            return false;
        }
    }

    public async Task<List<BlobFileInfo>> DiscoverAllFilesAsync()
    {
        try
        {
            _logger.LogInformation("Discovering all Parquet files");

            var fileSystemClient = _dataLakeClient.GetFileSystemClient(_config.EventsContainerName);
            var files = new List<BlobFileInfo>();

            await foreach (var pathItem in fileSystemClient.GetPathsAsync(recursive: true))
            {
                if (pathItem.IsDirectory != true && BlobPathParser.IsParquetFile(pathItem.Name))
                {
                    var blobInfo = await CreateBlobFileInfoAsync(pathItem);
                    if (blobInfo != null)
                    {
                        files.Add(blobInfo);
                    }
                }
            }

            _logger.LogInformation("Discovered {FileCount} total files", files.Count);
            return files.OrderBy(f => f.SleekflowCompanyId)
                       .ThenBy(f => f.Year)
                       .ThenBy(f => f.Month)
                       .ThenBy(f => f.Day)
                       .ThenBy(f => f.Hour)
                       .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error discovering all files");
            throw;
        }
    }

        private async Task<BlobFileInfo?> CreateBlobFileInfoAsync(PathItem pathItem)
    {
        try
        {
            if (!BlobPathParser.TryParseBlobPath(pathItem.Name, out var companyId, out var year, out var month, out var day, out var hour))
            {
                _logger.LogWarning("Failed to parse blob path: {BlobPath}", pathItem.Name);
                return null;
            }

            return new BlobFileInfo
            {
                BlobPath = pathItem.Name,
                LastModified = pathItem.LastModified.DateTime,
                SizeBytes = pathItem.ContentLength ?? 0,
                SleekflowCompanyId = companyId,
                Year = year,
                Month = month,
                Day = day,
                Hour = hour
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating BlobFileInfo for: {BlobPath}", pathItem.Name);
            return null;
        }
    }

    private BlobFileInfo? CreateBlobFileInfoFromProperties(string blobPath, DateTimeOffset lastModified, long contentLength)
    {
        try
        {
            if (!BlobPathParser.TryParseBlobPath(blobPath, out var companyId, out var year, out var month, out var day, out var hour))
            {
                _logger.LogWarning("Failed to parse blob path: {BlobPath}", blobPath);
                return null;
            }

            return new BlobFileInfo
            {
                BlobPath = blobPath,
                LastModified = lastModified.DateTime,
                SizeBytes = contentLength,
                SleekflowCompanyId = companyId,
                Year = year,
                Month = month,
                Day = day,
                Hour = hour
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating BlobFileInfo for: {BlobPath}", blobPath);
            return null;
        }
    }

    public async Task TestConnectivityAsync()
    {
        try
        {
            _logger.LogInformation("Testing Azure Data Lake connectivity...");

            var fileSystemClient = _dataLakeClient.GetFileSystemClient(_config.EventsContainerName);
            var exists = await fileSystemClient.ExistsAsync();

            if (!exists.Value)
            {
                throw new InvalidOperationException($"Container '{_config.EventsContainerName}' does not exist");
            }

            _logger.LogInformation("Azure Data Lake connectivity test passed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Azure Data Lake connectivity test failed");
            throw;
        }
    }
}