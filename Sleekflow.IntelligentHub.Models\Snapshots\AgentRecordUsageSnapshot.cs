﻿using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;

namespace Sleekflow.IntelligentHub.Models.Snapshots;

public class AgentRecordUsageSnapshot : IntelligentHubUsageSnapshot
{
    [JsonProperty("agent_id")]
    public string AgentId { get; set; }

    [JsonProperty("agent_versioned_id")]
    public string? AgentVersionedId { get; set; }

    [JsonConstructor]
    public AgentRecordUsageSnapshot(string agentId, string? agentVersionedId)
    {
        AgentId = agentId;
        AgentVersionedId = agentVersionedId;
    }
}