using Sleekflow.UserEventAnalyticsHub.DataCompactor.Models;

namespace Sleekflow.UserEventAnalyticsHub.DataCompactor.Services;

public interface IBlobDiscoveryService
{
    Task<List<BlobFileInfo>> DiscoverCompanyFilesAsync(string sleekflowCompanyId);
    Task<List<string>> GetAllCompanyIdsAsync();
    Task<BlobFileInfo> GetBlobMetadataAsync(string blobPath);
    Task<bool> BlobExistsAsync(string blobPath);
    Task<List<BlobFileInfo>> DiscoverAllFilesAsync();
    Task TestConnectivityAsync();
}