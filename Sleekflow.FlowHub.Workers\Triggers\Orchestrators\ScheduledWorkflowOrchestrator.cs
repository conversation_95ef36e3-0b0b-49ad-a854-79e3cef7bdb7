using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.Extensions.Logging;
using Sleekflow.FlowHub.Commons.Workflows;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Workers.Helpers;
using Sleekflow.FlowHub.Workers.Triggers.Activities;

namespace Sleekflow.FlowHub.Workers.Triggers.Orchestrators;

public class ScheduledWorkflowOrchestrator
{
    private readonly ILogger<ScheduledWorkflowOrchestrator> _logger;
    private readonly IWorkflowRecurringSettingsParser _workflowRecurringSettingsParser;
    private const int BatchSize = 1000;

    public ScheduledWorkflowOrchestrator(
        ILogger<ScheduledWorkflowOrchestrator> logger,
        IWorkflowRecurringSettingsParser workflowRecurringSettingsParser)
    {
        _logger = logger;
        _workflowRecurringSettingsParser = workflowRecurringSettingsParser;
    }

    [Function("ScheduledWorkflow_Orchestrator")]
    public async Task RunAsync(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var scheduleWorkflowInput = context.GetInput<TriggerScheduleWorkflowInput>();

        _logger.LogInformation(
            "Starting data processing for company {SleekflowCompanyId} with workflow {WorkflowVersionedId}",
            scheduleWorkflowInput!.SleekflowCompanyId,
            scheduleWorkflowInput!.WorkflowVersionedId);

        try
        {
            int currentBatchSize;

            GetContactsByBatchOutput? getContactsByBatchOutput;

            var getContactsByBatchFunctionInput = new GetContactsByBatch.GetContactsByBatchFunctionInput(
                scheduleWorkflowInput.Origin,
                scheduleWorkflowInput.SleekflowCompanyId,
                null,
                null,
                batchSize: BatchSize);

            // Collect all eligibility check tasks across all batches
            var allEligibilityCheckTasks = new List<Task>();

            do
            {
                getContactsByBatchOutput =
                    await context.CallActivityAsync<GetContactsByBatchOutput>(
                        "GetContactsByBatch",
                        getContactsByBatchFunctionInput);

                currentBatchSize = getContactsByBatchOutput.Contacts.Count;

                if (currentBatchSize > 0)
                {
                    // Split contacts into 10 chunks for parallel processing
                    var chunks = SplitContactDictionaryHelper.Chunk(getContactsByBatchOutput.Contacts, 10);

                    // Fire off tasks for each chunk without waiting
                    foreach (var chunk in chunks)
                    {
                        var eligibilityCheckInput =
                            new CheckWorkflowBatchContactsEnrolmentEligibility.CheckWorkflowBatchContactsEnrolmentEligibilityInput(
                                chunk,
                                scheduleWorkflowInput.SleekflowCompanyId,
                                scheduleWorkflowInput.WorkflowVersionedId,
                                scheduleWorkflowInput.WorkflowId);

                        // Fire and forget - just track the task
                        var task = context.CallActivityAsync(
                            "CheckWorkflowBatchContactsEnrolmentEligibility",
                            eligibilityCheckInput);
                        allEligibilityCheckTasks.Add(task);
                    }
                }

                getContactsByBatchFunctionInput = new GetContactsByBatch.GetContactsByBatchFunctionInput(
                    scheduleWorkflowInput.Origin,
                    scheduleWorkflowInput.SleekflowCompanyId,
                    getContactsByBatchOutput.NextBatch?.LastContactCreatedAt,
                    getContactsByBatchOutput.NextBatch?.LastContactId,
                    batchSize: BatchSize);
            }
            while (currentBatchSize > 0 && getContactsByBatchOutput.NextBatch != null);

            // Wait for all tasks to complete only at the very end
            if (allEligibilityCheckTasks.Count > 0)
            {
                await Task.WhenAll(allEligibilityCheckTasks);
            }
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Error executing ScheduledWorkflow {WorkflowVersionedId} for {SleekflowCompanyId}",
                scheduleWorkflowInput.WorkflowVersionedId,
                scheduleWorkflowInput.SleekflowCompanyId);

            // Optionally, decide if we still want to reschedule even if there was an error
            // For now, we will reschedule regardless of transient errors in the main logic.
        }
        finally // Ensure rescheduling happens even if exceptions occur in the main try block
        {
            // --- Recurrence Logic ---

            // Check if recurring settings are provided
            if (scheduleWorkflowInput.RecurringSettings == null)
            {
                _logger.LogInformation(
                    "No recurring settings found for Workflow {WorkflowVersionedId} / Company {SleekflowCompanyId}. Orchestration will complete.",
                    scheduleWorkflowInput.WorkflowVersionedId,
                    scheduleWorkflowInput.SleekflowCompanyId);
            }
            else
            {
                // Directly calculate the next run time using the new method
                var nextRunTime = _workflowRecurringSettingsParser.GetNextOccurrenceAfter(
                    scheduleWorkflowInput.ScheduledDatetime,
                    scheduleWorkflowInput.RecurringSettings,
                    context.CurrentUtcDateTime);

                _logger.LogInformation(
                    "Scheduling next run for Workflow {WorkflowVersionedId} for Company {SleekflowCompanyId} at {NextRunTime} UTC based on recurring settings.",
                    scheduleWorkflowInput.WorkflowVersionedId,
                    scheduleWorkflowInput.SleekflowCompanyId,
                    nextRunTime);

                // Wait until the next scheduled time
                await context.CreateTimer(nextRunTime.UtcDateTime, CancellationToken.None);

                // Continue as a new instance to keep history clean and implement the recurrence
                _logger.LogInformation(
                    "Timer fired for Workflow {WorkflowVersionedId} / Company {SleekflowCompanyId}. Continuing as new.",
                    scheduleWorkflowInput.WorkflowVersionedId,
                    scheduleWorkflowInput.SleekflowCompanyId);
                await context.CallActivityAsync(
                    "StartOrchestratorAndSaveDurablePayload",
                    new StartOrchestratorAndSaveDurablePayload.StartOrchestratorAndSaveDurablePayloadInput(
                        scheduleWorkflowInput.SleekflowCompanyId,
                        scheduleWorkflowInput.WorkflowVersionedId,
                        "ScheduledWorkflow_Orchestrator_V2",
                        scheduleWorkflowInput));
            }
        }
    }
}