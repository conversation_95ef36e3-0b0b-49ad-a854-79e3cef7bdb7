﻿using MassTransit;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnVersionedWorkflowDisabledEventConsumerDefinition
    : ConsumerDefinition<OnVersionedWorkflowDisabledEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnVersionedWorkflowDisabledEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(2);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnVersionedWorkflowDisabledEventConsumer : IConsumer<OnVersionedWorkflowDisabledEvent>
{
    private readonly IStateService _stateService;
    private readonly IWorkflowRuntimeService _workflowRuntimeService;
    private readonly IWorkflowService _workflowService;
    private readonly IBurstyWorkflowService _burstyWorkflowService;
    private readonly ILogger<OnVersionedWorkflowDisabledEventConsumer> _logger;

    public OnVersionedWorkflowDisabledEventConsumer(
        IStateService stateService,
        IWorkflowRuntimeService workflowRuntimeService,
        IWorkflowService workflowService,
        IBurstyWorkflowService burstyWorkflowService,
        ILogger<OnVersionedWorkflowDisabledEventConsumer> logger)
    {
        _stateService = stateService;
        _workflowRuntimeService = workflowRuntimeService;
        _workflowService = workflowService;
        _burstyWorkflowService = burstyWorkflowService;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<OnVersionedWorkflowDisabledEvent> context)
    {
        var @event = context.Message;

        var runningStates = await _stateService.GetRunningStatesByWorkflowVersionedIdAsync(
            @event.SleekflowCompanyId,
            @event.WorkflowVersionedId);

        foreach (var state in runningStates)
        {
            if (state.StateStatus == StateStatuses.Scheduled)
            {
                await _workflowRuntimeService.AbandonWorkflowAsync(
                    state.Identity.SleekflowCompanyId,
                    state.Id,
                    @event.DisableReasonCode);
            }
            else if (state.WorkflowContext.SnapshottedWorkflow.WorkflowType == WorkflowType.AIAgent)
            {
                // do not cancel agentflow
            }
            else
            {
                await _workflowRuntimeService.CancelWorkflowAsync(
                    state.Identity.SleekflowCompanyId,
                    state.Id,
                    @event.DisableReasonCode,
                    @event.DisabledBy);
            }
        }

        var proxyWorkflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
            @event.SleekflowCompanyId,
            @event.WorkflowVersionedId);

        if (proxyWorkflow is not null
            && (proxyWorkflow.WorkflowScheduleSettings.IsNewScheduledWorkflowSchema is true ||
                proxyWorkflow.WorkflowScheduleSettings.IsOldScheduledWorkflowSchemaFirstRecurringCompleted is true)
            && proxyWorkflow.WorkflowScheduleSettings.DurablePayload is not null)
        {
            try
            {
                await _burstyWorkflowService.TerminateScheduledWorkflowAsync(
                    @event.SleekflowCompanyId,
                    @event.WorkflowId);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Failed to terminate scheduled workflow {WorkflowVersionedId} for company {CompanyId}",
                    @event.WorkflowVersionedId,
                    @event.SleekflowCompanyId);
            }
        }
    }
}