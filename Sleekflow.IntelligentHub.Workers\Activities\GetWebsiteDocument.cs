using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Workers.Activities;

public class GetWebsiteDocument
{
    private readonly IKbDocumentService _kbDocumentService;

    public GetWebsiteDocument(IKbDocumentService kbDocumentService)
    {
        _kbDocumentService = kbDocumentService;
    }

    public class GetWebsiteDocumentInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("document_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string DocumentId { get; set; }

        [JsonConstructor]
        public GetWebsiteDocumentInput(
            string sleekflowCompanyId,
            string documentId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DocumentId = documentId;
        }
    }

    public class GetWebsiteDocumentOutput
    {
        [JsonProperty("website_document")]
        public WebsiteDocument WebsiteDocument { get; set; }

        [JsonConstructor]
        public GetWebsiteDocumentOutput(WebsiteDocument websiteDocument)
        {
            WebsiteDocument = websiteDocument;
        }
    }

    [Function(nameof(GetWebsiteDocument))]
    public async Task<GetWebsiteDocumentOutput> Run(
        [ActivityTrigger]
        GetWebsiteDocumentInput input)
    {
        var websiteDocument = (WebsiteDocument) await _kbDocumentService.GetDocumentAsync(
            input.SleekflowCompanyId,
            input.DocumentId);

        return new GetWebsiteDocumentOutput(websiteDocument);
    }
}