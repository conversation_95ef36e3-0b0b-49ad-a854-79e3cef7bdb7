using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.IntelligentHub.Configs;

public interface ILightRagConfig
{
    string Domain { get; }

    string APIKey { get; }

    List<string> SupportedCompanies { get; }
}

public class LightRagConfig : IConfig, ILightRagConfig
{
    public string Domain { get; }

    public string APIKey { get; }

    public List<string> SupportedCompanies { get; }

    public LightRagConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        Domain = Environment.GetEnvironmentVariable("LIGHT_RAG_DOMAIN", target)
                 ?? throw new SfMissingEnvironmentVariableException("LIGHT_RAG_DOMAIN");

        APIKey = Environment.GetEnvironmentVariable("LIGHT_RAG_API_KEY", target)
                 ?? throw new SfMissingEnvironmentVariableException("LIGHT_RAG_API_KEY");
        SupportedCompanies =
            Environment.GetEnvironmentVariable("LIGHT_RAG_SUPPORTED_COMPANIES", target)?.Split(',').ToList() ?? [];
    }
}