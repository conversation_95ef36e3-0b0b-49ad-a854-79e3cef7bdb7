using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Evaluator.ChatEvals;
using Sleekflow.IntelligentHub.Evaluator.Utils;
using Sleekflow.IntelligentHub.FaqAgents.Chats.Enrichers;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.Evaluator;

public static class SleekflowTestCases
{
    private static LeadNurturingTools GetProductionLeadNurturingTools()
    {
        return new LeadNurturingTools(
            leadScoreCustomObjectTool: null,
            assignmentTool: new AssignmentTool(
            [
                new AssignmentPair(
                    "If there is no significant, or if there are no messages related to either, assign to the \"supervisor\" team. The supervisor team should be the last resort.",
                    "supervisor",
                    null)
            ]),
            demoTool: new DemoTool(
                [
                    new RequiredField(
                        "first_name",
                        "The customer's first name",
                        true),
                    new Required<PERSON>ield(
                        "last_name",
                        "The customer's last name",
                        true),
                    new Required<PERSON>ield(
                        "email",
                        "The customer's email address",
                        true),
                    new RequiredField(
                        "company_name",
                        "The customer's company name",
                        true),
                    new RequiredField(
                        "phone_number",
                        "The customer's phone number. It must be a phone number with country code. e.g. +852 6123-4567",
                        true),
                    new RequiredField(
                        "employees_count",
                        "The number of employees at the customer's company. The valid options are '1-19', '20-49', '50-499', '500+'.",
                        true),
                    new RequiredField(
                        "country",
                        "The customer's country. The contact property 'Country' (if provided) should be used as this field information.",
                        true),

                    new RequiredField(
                        "message",
                        "Any additional message or notes from the customer",
                        false),
                    new RequiredField(
                        "job_title",
                        "The customer's job title",
                        false),
                    new RequiredField(
                        "website",
                        "The customer's company website",
                        false),
                ],
                new ChiliPiperConfig(
                    "https://sleekflow.chilipiper.com/concierge-router/inbound_router/rest",
                    new Dictionary<string, string>
                    {
                        {
                            "first_name", "PersonFirstName"
                        },
                        {
                            "last_name", "PersonLastName"
                        },
                        {
                            "email", "PersonEmail"
                        },
                        {
                            "phone_number", "bd87fc44-7fdd-40f0-8a3a-c4635ec2066a"
                        },
                        {
                            "company_name", "CompanyName"
                        },
                        {
                            "employees_count", "134d6b7e-edda-465f-bde4-d14826719a2c"
                        },
                        {
                            "country", "ef26de54-ca30-4a90-b233-69088e5741dc"
                        },
                        {
                            "message", "6333efc1-81ca-44c6-ae28-df0ff0e3ec42"
                        },
                        {
                            "job_title", "0e613939-d851-4b4c-8c24-b59d02dd7992"
                        },
                        {
                            "website", "f8d47804-05e5-4a11-a2b6-d733a96e06d4"
                        }
                    })),
            additionalHandsOffClassificationRules: new AdditionalHandsOffClassificationRules(
            [
                new AdditionalHandsOffClassificationRule(
                    """When the customer asks about personal account issues (e.g., Unblocking, Password Recovery) on Meta Platforms (e.g. Facebook, WhatsApp, Instagram), classify as "continue_nurturing". """)
            ]));
    }

    private static CompanyAgentConfig GetProductionAgentConfig()
    {
        return AgentConfigUtils.GetAgentConfig(
            leadNurturingTools: GetProductionLeadNurturingTools(),
            additionalInstructionStrategy:
            """
            --- Key Questions ---

            1. "What’s your business and industry?"
               - Tailors the pitch.
            2. "What are your goals with SleekFlow?"
               - Identifies objectives (e.g., sales, support).
            3. "What communication challenges do you face?"
               - Uncovers pain points.
            4. "Which messaging channels matter most?"
               - Aligns with customer preferences.
            5. "Need any specific integrations?"
               - Ensures compatibility (e.g., CRM).
            6. "Where are the regions you operate?"
               - Understands market needs.

            The answers will be captured in the Contact Properties. If we already know the answer to any of these, we’ll skip that question.

            --- Region Selling Points ---

            If the customer is from a specific region, you can use the following selling points to highlight SleekFlow's strengths in that area. These points are not exhaustive and should be tailored to the customer's specific needs and context.
            Use them as a starting point for a **fresh** conversation, but feel free to adapt them based on the customer's unique situation.

            ```
            ## AMER (e.g., United States, Canada, Mexico, Brazil)
            - **Native integrations**: Supports Salesforce, HubSpot, Shopify, and VTEX, providing tailored e-commerce capabilities, especially for Latin American markets.
            - **Local support**: A dedicated office in Brazil delivers region-specific expertise and timely assistance.
            - **Monthly pricing**: Flexible plans allow businesses to scale usage without long-term commitments, meeting diverse needs.
            - **Summary with Analysis**: SleekFlow’s native integrations with leading platforms like Salesforce, HubSpot, Shopify, and VTEX give it a competitive edge in e-commerce, particularly in Latin America. The local support in Brazil ensures culturally relevant assistance, while flexible monthly pricing allows businesses to scale easily—unlike competitors with rigid models. These features position SleekFlow to outperform rivals by offering tailored, scalable solutions for the diverse AMER market.

            ## GCC (e.g., Saudi Arabia, United Arab Emirates, Qatar, Kuwait)
            - **Local support**: A Dubai office with Arabic-speaking staff offers culturally relevant and accessible assistance.
            - **Mobile capability**: A mobile app allows businesses to manage customer interactions on the go, catering to the region's mobile-first users.
            - **Strong integrations and AI**: Provides a wide range of software integrations and advanced AI features for enhanced business intelligence.
            - **OPERA integration**: Integrates with Oracle OPERA Hotel Property Management Solutions (PMS), ideal for the hospitality sector in the region.
            - **Summary with Analysis**: SleekFlow’s local support in Dubai, with Arabic-speaking staff, ensures culturally attuned service, a key differentiator in the GCC. The mobile app addresses the region’s mobile-first preference, while OPERA integration is a game-changer for hospitality businesses. Strong integrations and AI features offer advanced capabilities that competitors may lack, positioning SleekFlow to lead in customer engagement and operational efficiency.

            ## Singapore
            - **Native integrations**: Seamlessly connects with HubSpot, Salesforce, Dynamics, Zoho, Google Sheets, and Shopify to support diverse business workflows.
            - **WhatsApp-based Ticket Management System**: Streamlines customer support by managing tickets directly through WhatsApp, a widely used platform in the region.
            - **Security certifications**: Ensures robust data protection and compliance with Singapore’s strict local standards.
            - **User-friendly UI**: Offers an intuitive interface, making it easy for businesses of all sizes to adopt and use effectively.
            - **Able to connect multiple WABA numbers**: Provides flexibility by allowing businesses to link multiple WhatsApp Business API numbers to a single SleekFlow account.
            - **Summary with Analysis**: SleekFlow’s seamless integrations with platforms like HubSpot, Salesforce, and Shopify support diverse workflows, while the WhatsApp-based Ticket Management System leverages a widely used platform in Singapore. Security certifications ensure compliance with strict local standards, and the user-friendly UI simplifies adoption. The ability to connect multiple WABA numbers offers flexibility that competitors may not provide, making SleekFlow a top choice for businesses seeking robust, compliant, and easy-to-use solutions.

            ## Indonesia
            - **Collaborations**: Allows clients to work together in tackling or closing a deal within one chat without the need to pass and back tickets/conversation for a smoother business process.
            - **Labelling**: Marketing suited solutions as we can easily do lead tracking from any kind of lead generation attempts that goes to WhatsApp.
            - **Built-in Customized CRM with API capabilities**: Gives all the freedom to clients on how they want to store their contacts and how it looks while also supporting syncing up like order history, appointment bookings, loyalty, etc., through custom objects.
            - **Summary with Analysis**: SleekFlow’s collaboration feature enables efficient teamwork, a capability lacking in competitors like Infobip and 3Dolphins, who handle conversations on a per-ticket basis. The labelling feature simplifies lead tracking, and the customized CRM with unrestricted API offers flexibility that Qontak’s unstable product and limited flow builder cannot match. These features position SleekFlow to dominate the Indonesian market by addressing competitors’ weaknesses and offering superior, flexible solutions.

            ## GCR and other regions (e.g., China, Hong Kong, Taiwan, and others)
            - **Role-based access control**: Ensures robust data security and compliance with regional regulations, giving businesses confidence in protecting sensitive information.
            - **Transparent pricing**: Offers clear, predictable costs with no hidden fees, simplifying budgeting and planning for businesses.
            - **Native integrations**: Seamlessly connects with Salesforce, HubSpot, and Shopify, enhancing workflows without complex setup.
            - **Summary with Analysis**: SleekFlow’s role-based access control ensures robust data security, a critical advantage in regions with strict regulations. Transparent pricing simplifies budgeting, and native integrations with Salesforce, HubSpot, and Shopify streamline workflows. These features provide reliability and ease of use, setting SleekFlow apart from competitors and making it a trusted partner across diverse markets.
            ```

            --- When the Customer Asks About Personal Account Issues on Meta Platforms (e.g., Unblocking, Password Recovery) ---

            Redirect to Meta: Direct them to Meta’s official support page: https://www.facebook.com/help?locale=en_US.
            Clarify Role: State: “We are not the official support for Meta or its products, but we can assist with your needs related to SleekFlow’s integration with Meta products, including the WhatsApp Business API.”
            Note:
            - SleekFlow’s Knowledge Base (KB) does not contain information about Meta. Do not query it for Meta-related questions.
            - Please note we are not the official support for other supported channels by SleekFlow, such as Telegram, WhatsApp, LINE, WeChat, Instagram, and Facebook Messenger. For issues related to these channels, please refer to their respective official support pages.
            """,
            additionalInstructionResponse:
            """
            --- Primary Goals ---

            These guidelines help tailor responses based on lead type and context, ensuring value-driven, natural conversations.

            ### 1. Leads Without a Scheduled Demo
            Handles potential new customers who haven’t booked a demo yet.

            - **Conditions to Suggest a Demo:**
              - Lead is **not an existing customer** (verified by LeadClassifierAgent).
              - Lead’s **warmth score exceeds 50** (determined by LeadClassifierAgent).
            - **Conditions to Avoid Suggesting a Demo:**
              - Previous demo suggestion was **ignored or declined** (per customer conversation context).
              - Exception: Suggest again if **new interest signals** emerge (e.g., asking product-related questions).

            ### 2. Leads With a Scheduled Demo
            Addresses leads who already have a demo booked, ensuring relevance in mentions.

            - **Prioritize** immediate value; avoid delay responses by deferring to a demo. **Do not** say "wait for the demo" or "we’ll cover this in the demo."
            - **When to Mention the Demo:**
              - Demo is **directly relevant** to the current conversation topic.
              - Lead explicitly **requests another demo**.
            - **Handling new Demo Requests:**
              - Acknowledge the existing booking (e.g., "You’ve got one scheduled!").
              - Offer to **reschedule** if needed, including the meeting link.

            ### 3. Existing Customers
            Focuses on supporting current customers without pushing unnecessary demos.

            - **Demo Policy:**
              - **Do not suggest a demo**—they’re already familiar with the product.
            - **Recommended Actions:**
              - Offer assistance (e.g., "Need help with anything?").
              - Provide relevant support or information based on their needs.

            ### 4. General Guidelines
            Applies across all scenarios to maintain conversational quality.

            - **Core Principle:**
              - Naturally convince the customer to book a demo **only when it’s relevant** to their needs. **Avoid hard-selling** or pushing demos prematurely.
              - If the customer already has a demo scheduled, you should still provide relevant information and support without pushing for another demo.
              - Never say "wait for the demo"—it risks disengaging the lead.

            ## Reference
            For practical examples, see the *SleekFlow AE Lead Qualification Handbook*.

            --- The SleekFlow AE Lead Qualification Handbook ---

            Follow the guidelines below to ensure a smooth and effective lead qualification process. This handbook is designed to help you understand SleekFlow's offerings, identify customer needs, and build trust with potential clients.

            ```
            # SleekFlow AE Lead Qualification Handbook

            **What is SleekFlow?**
            SleekFlow is an AI-powered omnichannel platform that centralizes customer interactions (e.g., WhatsApp, Instagram, SMS) and automates workflows for marketing, sales, and support.

            **Who Uses It?**
            Retail, e-commerce, finance, education, real estate, tech.

            **Key Questions**
            1. "What’s your business and industry?"
               - Tailors the pitch.
            2. "What are your goals with SleekFlow?"
               - Identifies objectives (e.g., sales, support).
            3. "What communication challenges do you face?"
               - Uncovers pain points.
            4. "Which messaging channels matter most?"
               - Aligns with customer preferences.
            5. "Need any specific integrations?"
               - Ensures compatibility (e.g., CRM).
            6. "Where are the regions you operate?"
               - Understands market needs.

            The answers will be captured in the Contact Properties. If we already know the answer to any of these, we’ll skip that question.

            **Best Practices**
            - Listen to tailor responses.
            - Use relevant examples.
            - Share successes (e.g., AutoMate’s 5x conversions).
            - Address pricing/security concerns.
            - Follow up with demos when appropriate (see below for timing).

            **When to Ask for a Demo**
            Offering a demo is a key step in showcasing SleekFlow’s value, but timing is critical. Here’s when and how to ask:
            - **After Key Questions**: Once you’ve asked the key questions and gained insight into the customer’s business, goals, and challenges, offer a tailored demo. This ensures the demo directly addresses their needs.
              - *Example*: If they mention a challenge like slow customer response times, say:
                *"I understand that slow response times are a hurdle for you. Would you like to see how SleekFlow can streamline that in a quick demo?"*
            - **For Hesitant Customers**: If the customer hesitates to share details upfront, offer a general demo early to build trust and demonstrate value. This can encourage them to open up afterward.
              - *Example*: *"I get that you might not want to dive into specifics yet. How about I show you a brief demo of SleekFlow’s capabilities to give you a sense of what it can do?"*
            - **Look for Cues**: Propose a demo when the customer shows interest (e.g., asks a question about features) or highlights a pain point SleekFlow can solve.
            - **Keep It Natural**: Tie the demo offer to their responses so it feels like a logical next step, not a hard sell. Avoid pushing too early or aggressively.

            **Goal**: Understand needs, position SleekFlow, build trust.
            ```

            --- When the Customer Asks About Personal Account Issues on Meta Platforms (e.g., Unblocking, Password Recovery) ---

            Redirect to Meta: Direct them to Meta’s official support page: https://www.facebook.com/help?locale=en_US.
            Clarify Role: State: “We are not the official support for Meta or its products, but we can assist with your needs related to SleekFlow’s integration with Meta products, including the WhatsApp Business API.”
            Note:
            - SleekFlow’s Knowledge Base (KB) does not contain information about Meta. Do not query it for Meta-related questions.
            - Please note we are not the official support for other supported channels by SleekFlow, such as Telegram, WhatsApp, LINE, WeChat, Instagram, and Facebook Messenger. For issues related to these channels, please refer to their respective official support pages.

            --- Use better wordings for Objections Handling ---

            1. When prospect ask about the possibility of an account ban for WhatsApp broadcasting, we should say the situation is "less likely" rather than providing an absolute answer to say it won't be banned.

            --- Customer asks whether WhatsApp broadcast is available, or if the free plan supports sending messages to multiple contacts ---

            - Inform the customer that WhatsApp Broadcast is not supported on the free plan.
            - Broadcast feature is only available on our paid business plans.
            - If they express interest in sending broadcasts, recommend upgrading to a paid plan.
            - When appropriate, offer to schedule a demo to help them explore the right option.

            --- When asked about pricing, the cheapest plan, or which subscription plan to choose, do not mention or recommend the Free / Startup plan. ---

            - Do not mention or recommend the Free plan or Startup Plan
            - Always start by recommending the Pro plan, which is our most affordable paid option.
            - If the customer is unsure which plan fits their needs, ask for more details about their business or goals.
            - Recommend speaking to our sales team for a tailored suggestion.
            - When appropriate, offer to schedule a demo to help them explore the best-fit plan.
            """,
            enricherConfigs:
            [
                new EnricherConfig
                {
                    Type = "contact_properties",
                    IsEnabled = true,
                    Parameters = new Dictionary<string, string>()
                    {
                        {
                            "PERMITTED_CONTACT_PROPERTIES_KEY", JsonConvert.SerializeObject(
                                new List<PermittedContactProperty>
                                {
                                    new PermittedContactProperty("firstName", "The customer's first name"),
                                    new PermittedContactProperty("lastName", "The customer's last name"),
                                    new PermittedContactProperty("PhoneNumber", "The customer's phone number"),
                                    new PermittedContactProperty("Email", "The customer's email address"),
                                    new PermittedContactProperty("work_email", "The customer's work email address"),
                                    new PermittedContactProperty("Country", "The customer's country"),
                                })
                        }
                    }
                },
                new EnricherConfig
                {
                    Type = "hubspot",
                    IsEnabled = true,
                    Parameters = new Dictionary<string, string>
                    {
                        {
                            "EMAIL_FIELD_KEY", "Email"
                        },
                        {
                            "PHONE_FIELD_KEY", "PhoneNumber"
                        },
                    },
                },
                new EnricherConfig
                {
                    Type = "plan_tier",
                    IsEnabled = true,
                    Parameters = new Dictionary<string, string>
                    {
                        {
                            "PHONE_FIELD_KEY", "PhoneNumber"
                        },
                    },
                },
            ],
            knowledgeRetrievalConfig: new KnowledgeRetrievalConfig(
                new WebSearchConfig(
                [
                    new TriggerSiteMapping(
                        "customer stories across regions and industries",
                        "https://sleekflow.io",
                        "{region} {industry}"),
                    new TriggerSiteMapping(
                        "use cases e.g. sales, e-commerce, education",
                        "https://sleekflow.io",
                        "{industry}")
                ]),
                new StaticSearchConfig(
                [
                    new StaticPageEntry()
                    {
                        Page = "Meta Support including its products like WhatsApp, Instagram, Messenger",
                        Url = "https://sleekflow.io/blog/facebook-support",
                        Description = "How we can contact Meta Support?"
                    },
                    new StaticPageEntry()
                    {
                        Page = "EN HR Campaign",
                        Url = "https://sleekflow.io/lp/hr",
                        Description = "Streamline hiring from screening to onboarding with chat automation"
                    },
                    new StaticPageEntry()
                    {
                        Page = "WhatsApp Drip Campaign",
                        Url = "https://sleekflow.io/lp/whatsapp-drip-marketing",
                        Description = "Are your WhatsApp messages just noise?"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Competitor - SuperChat",
                        Url = "https://sleekflow.io/lp/superchat-alternative",
                        Description = "Superchat Alternative for Better Customer Relationships"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Competitor - Gupshup",
                        Url = "https://sleekflow.io/lp/gupshup-alternative",
                        Description = "More conversions through better conversations with SleekFlow"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Competitor - Unifonic",
                        Url = "https://sleekflow.io/alternatives/unifonic-alternative",
                        Description = "Need more messaging channels than Unifonic? Double your reach with SleekFlow"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Competitor - Aisensy",
                        Url = "https://sleekflow.io/alternatives/aisensy-alternative",
                        Description = "Supercharge your customer experience with conversational AI"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Competitor - Omnichat (SG)",
                        Url = "https://sleekflow.io/en-sg/alternatives/omnichat-alternative",
                        Description = "Gamify engagements with advanced workflows on SleekFlow"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Competitor - Respond.io (SEA)",
                        Url = "https://sleekflow.io/en-sg/alternatives/respondio-best-alternative-sea",
                        Description = "Get better response than Respond.io with SleekFlow's support"
                    },
                    new StaticPageEntry()
                    {
                        Page = "HK HR Campaign",
                        Url = "https://sleekflow.io/zh-hk/lp/hr",
                        Description = "集中管理求職者 加速招聘過程 減少無效溝通"
                    },
                    new StaticPageEntry()
                    {
                        Page = "WhatsApp Newsletter (DE)",
                        Url = "https://sleekflow.io/de-de/lp/whatsapp-newsletter",
                        Description = "Binden Sie Ihre Kunden mit Newslettern ein und steigern Sie den Umsatz"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Call to WhatsApp Ad",
                        Url = "https://sleekflow.io/lp/ctwa",
                        Description = "Engage ads audience through WhatsApp for 2X high-quality leads"
                    },
                    new StaticPageEntry()
                    {
                        Page = "WhatsApp for Customer Service",
                        Url = "https://sleekflow.io/lp/whatsapp-for-customer-service",
                        Description = "Elevate customer engagement with WhatsApp"
                    },
                    new StaticPageEntry()
                    {
                        Page = "WhatsApp for Marketing",
                        Url = "https://sleekflow.io/lp/whatsapp-for-marketing",
                        Description = "Maximize the ROI of your marketing campaigns"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Competitor - WATI (India)",
                        Url = "https://sleekflow.io/alternatives/wati-alternative-india",
                        Description = "WATI alternative to power your sales engine on WhatsApp"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Brazil Free API Offer",
                        Url = "https://campaign.sleekflow.io/tenha-acesso-gr%C3%A1tis-ao-whatsapp-business-api",
                        Description = "Cresça sua Empresa com o SleekFlow"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Official WhatsApp Business API Providers (ID)",
                        Url = "https://sleekflow.io/id-id/alternatives/whatsapp-business-api-resmi",
                        Description = "Mitra Resmi WhatsApp Business API di Indonesia"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Competitor - Qontak (ID)",
                        Url = "https://sleekflow.io/id-id/alternatives/alternatif-qontak",
                        Description = "Platform komunikasi omnichannel, untuk seluruh tim. Bukan hanya CS."
                    },
                    new StaticPageEntry()
                    {
                        Page = "Competitor - Qiscus (ID)",
                        Url = "https://sleekflow.io/id-id/alternatives/alternatif-qiscus",
                        Description = "Biaya transparan, mencakup seluruh kebutuhan"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Competitor - Omnichat (HK)",
                        Url = "https://sleekflow.io/zh-hk/alternatives/omnichat-alternative",
                        Description = "Flow Builder更加靈活 隨你所需自動吸客"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Competitor - Tuvis (BR)",
                        Url = "https://sleekflow.io/pt-br/alternatives/tuvis-alternative",
                        Description = "Conversões através de conversas com o agente de receita de IA SleekFlow"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Competitor - Take Blip",
                        Url = "https://sleekflow.io/alternatives/takeblip-alternative",
                        Description = "Conversions through conversations with SleekFlow AI Revenue agent"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Competitor - WATI",
                        Url = "https://sleekflow.io/alternatives/wati-alternative",
                        Description = "Don't limit yourself to one WhatsApp with SleekFlow's Multichannel Inbox."
                    },
                    new StaticPageEntry()
                    {
                        Page = "Competitor - Messagebird",
                        Url = "https://sleekflow.io/alternatives/messagebird-alternative",
                        Description = "Want to drive conversions faster than MessageBird? SleekFlow is your answer."
                    },
                    new StaticPageEntry()
                    {
                        Page = "Competitor - Tallos",
                        Url = "https://sleekflow.io/alternatives/tallos-alternative",
                        Description = "Conversions through conversations with SleekFlow AI Revenue agent"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Competitor - Zenvia",
                        Url = "https://sleekflow.io/alternatives/zenvia-alternative",
                        Description = "Conversions through conversations with SleekFlow AI Revenue agent"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Competitor - Respond.io",
                        Url = "https://sleekflow.io/alternatives/respondio-best-alternative",
                        Description = "Need something better than respond.io? SleekFlow got your back."
                    },
                    new StaticPageEntry()
                    {
                        Page = "Competitor - Trengo",
                        Url = "https://sleekflow.io/alternatives/trengo-alternative",
                        Description = "Don't stop at engagement. Turn them into sales and drive revenue with SleekFlow."
                    },
                    new StaticPageEntry()
                    {
                        Page = "Competitor - Zendesk",
                        Url = "https://sleekflow.io/alternatives/zendesk-alternative",
                        Description = "Budget-friendly AI customer service solution that is perfect for any business"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Brazil One Pager & WhatsApp Crash Course",
                        Url =
                            "https://landing.sleekflow.io/mais-vendas-convers%C3%B5es-e-melhor-suporte-ao-cliente-sleekflow",
                        Description = "Mais vendas, conversões e melhor suporte ao cliente"
                    },
                    new StaticPageEntry()
                    {
                        Page = "SEA E-book Download",
                        Url = "https://landing.sleekflow.io/en-sg/trend-report-social-commerce-in-southeast-asia-2022",
                        Description = "inaccessible"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Social CRM (ID)",
                        Url = "https://sleekflow.io/id-id/lp/whatsapp-crm",
                        Description = "Bangun Hubungan Personal Dengan Customer Dengan WhatsApp CRM"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Click to WhatsApp Ads (ID)",
                        Url = "https://sleekflow.io/id-id/lp/iklan-ctwa",
                        Description = "Lebih dekat dengan pelanggan sambil tingkatkan leads 2x lipat"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Official WhatsApp Business API Providers (ID)",
                        Url = "https://sleekflow.io/id-id/alternatives/whatsapp-business-api-resmi",
                        Description = "Mitra Resmi WhatsApp Business API di Indonesia"
                    },
                    new StaticPageEntry()
                    {
                        Page = "HK Branding Campaign",
                        Url = "https://sleekflow.io/zh-hk/lp/grow-with-sleekflow-hk",
                        Description = "年輕追夢創業？對話式營銷助你以少創大"
                    },
                    new StaticPageEntry()
                    {
                        Page = "WhatsApp for Healthcare",
                        Url = "https://sleekflow.io/lp/healthcare",
                        Description = "Boost patient engagement and drive in-house visits with WhatsApp"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Gen Z Marketing",
                        Url = "https://sleekflow.io/lp/gen-z-marketing",
                        Description = "Revolutionize Your Gen Z Marketing with SleekFlow"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Infocomm Alternative",
                        Url = "https://sleekflow.io/lp/infocomm-alternative",
                        Description = "Engage customers everywhere: Beyond mobile, across all channels"
                    },
                    new StaticPageEntry()
                    {
                        Page = "Competitor - Manychat (UAE)",
                        Url = "https://sleekflow.io/alternatives/manychat-uae-alternative",
                        Description = "Discover SleekFlow: #1 ManyChat alternative in the UAE"
                    },
                    new StaticPageEntry()
                    {
                        Page = "WhatsApp Blasting Software",
                        Url = "https://sleekflow.io/lp/best-whatsapp-blasting-software",
                        Description = "Personalized broadcast messages"
                    },
                    new StaticPageEntry()
                    {
                        Page = "WhatsApp Automation",
                        Url = "https://sleekflow.io/lp/whatsapp-automation",
                        Description = "Automate unique chat experiences for every customer"
                    },
                ])),
            toolsConfig: new ToolsConfig(null, new HubspotTool("test-api-key")),
            isTranscriptionEnabled: false);
    }

    private static Dictionary<string, string> GetDefaultContactProperties()
    {
        return new Dictionary<string, string>
        {
            {
                "firstName", "Leo"
            },
            {
                "lastName", "Choi"
            },
            {
                // This number would trigger MockHubspotEnricher
                "PhoneNumber", "85261096623"
            },
            {
                "Email", "<EMAIL>"
            },
            {
                "company_name", "Globex Corporation"
            },
            {
                "employees_count", "500+"
            },
        };
    }

    public static IEnumerable<ChatEvalQuestion> GetSleekflowDifficultTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/Sleekflow/",
            SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1");

        var agentConfig = GetProductionAgentConfig();
        var defaultContactProperties = GetDefaultContactProperties();

        yield return new ChatEvalQuestion(
            testConfig,
            "Customer asks about Shopify integration transaction fees - hands-off scenario when agent can't provide specific details",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi SleekFlow AI may I know what exactly you can do with my shopify store highest"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hey there! \ud83d\udc4b SleekFlow can supercharge your Shopify store! \ud83d\ude80 You can boost sales with automated abandoned cart reminders, lower transaction fees, and chat with customers on WhatsApp, Instagram, and more, all in one place! \ud83e\udd29 Plus, our CRM helps you understand your customers better. \ud83d\ude09 Want to see it in action? Check out our website or ask for a demo! \ud83d\udc4d"),
                new ChatMessageContent(AuthorRole.User, "what do you mean by lower transaction fees?"),
            ],
            "Thank you for your question! I want to ensure you receive the most accurate information regarding how SleekFlow can help lower transaction fees for your Shopify store. I'm forwarding this conversation to a teammate who specializes in this area. They'll be able to provide you with a detailed explanation. Please expect a response shortly!\n",
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            testConfig,
            "Customer inquires about Shopify integration capabilities without transaction fee context",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi SleekFlow AI may I know what exactly you can do with my shopify store highest"),
            ],
            "SleekFlow can significantly enhance your Shopify store by:\n\n*Boosting sales* with abandoned cart reminders and order drafting in chat.\n*Improving customer loyalty* through personalized messaging.\n*Monitoring sales performance* with a dedicated dashboard.\n*Streamlining team collaboration* with an omnichannel inbox.\n\nSleekFlow also offers lower transaction fees and a code-free Live Chat widget. To connect, you'll need a SleekFlow Pro account (starting at US$79/month) and the Shopify integration feature (from US$49/month). \n\nIs there anything specific you'd like to know more about?\n",
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            testConfig,
            "Customer follows up asking specifically about lower transaction fees after initial Shopify response",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi SleekFlow AI may I know what exactly you can do with my shopify store highest"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hey there! \ud83d\udc4b SleekFlow can supercharge your Shopify store! \ud83d\ude80 You can boost sales with automated abandoned cart reminders, lower transaction fees, and chat with customers on WhatsApp, Instagram, and more, all in one place! \ud83e\udd29 Plus, our CRM helps you understand your customers better. \ud83d\ude09 Want to see it in action? Check out our website or ask for a demo! \ud83d\udc4d"),
                new ChatMessageContent(AuthorRole.User, "what do you mean by lower transaction fees?"),
            ],
            "Thank you for your question! I want to ensure you receive the most accurate information regarding how SleekFlow can help lower transaction fees for your Shopify store. I'm forwarding this conversation to a teammate who specializes in this area. They'll be able to provide you with a detailed explanation. Please expect a response shortly!\n",
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            testConfig,
            "Customer persistently asks how transaction fees are lowered - agent provides general explanation before handoff",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi SleekFlow AI may I know what exactly you can do with my shopify store highest"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hey there! \ud83d\udc4b SleekFlow can supercharge your Shopify store! \ud83d\ude80 You can boost sales with automated abandoned cart reminders, lower transaction fees, and chat with customers on WhatsApp, Instagram, and more, all in one place! \ud83e\udd29 Plus, our CRM helps you understand your customers better. \ud83d\ude09 Want to see it in action? Check out our website or ask for a demo! \ud83d\udc4d"),
                new ChatMessageContent(AuthorRole.User, "what do you mean by lower transaction fees?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hey there! \ud83d\udc4b Yes, SleekFlow can actually help lower your transaction fees compared to just using Shopify alone! \ud83e\udd29 Plus, you get cool features like automated abandoned cart reminders and can chat with customers on WhatsApp, Instagram, and more, all in one place! \ud83d\ude09 I don't have all the nitty-gritty details on hand, but I can connect you with someone who can explain it better! \ud83d\udc4d Would you like that?"),
                new ChatMessageContent(AuthorRole.User, "how is it lowered"),
            ],
            "Thank you for your question! SleekFlow can help lower transaction fees indirectly through features like automated abandoned cart reminders and streamlined customer communication, which can boost sales and reduce operational costs. Additionally, our integration with Stripe may offer different transaction fee structures compared to Shopify's native options.\n\nI don't have the exact figures for a direct comparison, but I can connect you with one of our specialists who can provide a detailed breakdown. Would you like me to arrange that?",
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            testConfig,
            "Customer simply asks about Shopify capabilities - immediate specialist referral",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi SleekFlow AI may I know what exactly you can do with my shopify store highest"),
            ],
            "Great! I'll connect you with one of our specialists who can provide a detailed breakdown of how SleekFlow can help lower your transaction fees. They will reach out to you shortly.",
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            testConfig,
            "Customer confirms interest in specialist consultation about transaction fees",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi SleekFlow AI may I know what exactly you can do with my shopify store highest"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hey there! \ud83d\udc4b SleekFlow can supercharge your Shopify store! \ud83d\ude80 You can boost sales with automated abandoned cart reminders, lower transaction fees, and chat with customers on WhatsApp, Instagram, and more, all in one place! \ud83e\udd29 Plus, our CRM helps you understand your customers better. \ud83d\ude09 Want to see it in action? Check out our website or ask for a demo! \ud83d\udc4d"),
                new ChatMessageContent(AuthorRole.User, "what do you mean by lower transaction fees?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hey there! \ud83d\udc4b Yes, SleekFlow can actually help lower your transaction fees compared to just using Shopify alone! \ud83e\udd29 Plus, you get cool features like automated abandoned cart reminders and can chat with customers on WhatsApp, Instagram, and more, all in one place! \ud83d\ude09 I don't have all the nitty-gritty details on hand, but I can connect you with someone who can explain it better! \ud83d\udc4d Would you like that?"),
                new ChatMessageContent(AuthorRole.User, "how is it lowered"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you for your question! SleekFlow can help lower transaction fees indirectly through features like automated abandoned cart reminders and streamlined customer communication, which can boost sales and reduce operational costs. Additionally, our integration with Stripe may offer different transaction fee structures compared to Shopify's native options.\n\nI don't have the exact figures for a direct comparison, but I can connect you with one of our specialists who can provide a detailed breakdown. Would you like me to arrange that?"),
                new ChatMessageContent(AuthorRole.User, "Yes"),
            ],
            "Great! I'll connect you with one of our specialists who can provide a detailed breakdown of how SleekFlow can help lower your transaction fees. They will reach out to you shortly.",
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            new ChatEvalConfig(
                SourceDir: "../../../KnowledgeSources/Sleekflow/",
                SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1"),
            "Cantonese-speaking beauty business owner asks about Flow Builder for Pokemon-style game design",
            [
                new ChatMessageContent(AuthorRole.User, "Hi Sleekflow Ai"),
                new ChatMessageContent(AuthorRole.User, "有咩好介紹？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！SleekFlow AI很高興為您服務！SleekFlow 是一個 AI 驅動的全渠道對話平台，可以幫助您的企業自動化增長並簡化客戶互動。我們將所有銷售和支援對話集中在一個平台中，管理客戶數據並自動化消息傳遞工作流程。\n\n為了給您更精確的建議，請問您的業務屬於哪個行業？您目前面臨的最大挑戰是什麼？"),
                new ChatMessageContent(AuthorRole.User, "我哋做美容㗎，我哋要請好多人嚟覆客"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！很高興為您服務。了解到您在美容行業，並且需要聘請大量人手回覆客戶。SleekFlow 是一個 AI 驅動的全渠道平台，可以幫助您自動化客戶互動，從而減少對大量人手的需求。\n\n透過我們的全渠道 CRM，您可以將來自 WhatsApp、Facebook、Instagram 等渠道的客戶訊息整合在一個平台中。我們的 Chatbot 能夠處理常見查詢、預約管理、並提供個人化的售後跟進。例如，Weirdo Beauty 和 Sun and Moon Massage 都透過 SleekFlow 提升了客戶互動效率。\n\n想了解更多關於 SleekFlow 如何幫助您的美容業務嗎？"),
                new ChatMessageContent(AuthorRole.User, "想呀想呀，你講多啲呀"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "當然想啦！SleekFlow 可以幫到你好多㗎！\n\n透過我哋嘅全渠道 CRM，你可以將 WhatsApp、Facebook、Instagram 等渠道嘅客戶訊息整合喺一個平台管理，唔使再喺唔同地方切換。\n\n我哋嘅 Chatbot 可以 24/7 全天候回覆常見問題、處理預約，仲可以自動做售後跟進。例如，Weirdo Beauty 同 Sun and Moon Massage 都用 SleekFlow 提升咗效率。\n\n想唔想了解下 SleekFlow 點樣幫你哋嘅美容業務更上一層樓？"),
                new ChatMessageContent(AuthorRole.User, "有冇啲咩實際例子啊？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "緊係有例子啦！😎 Weirdo Beauty 就用 SleekFlow 設立咗 24/7 全天候嘅虛擬禮賓服務，提升咗客戶體驗！💅 另外，我哋喺美容同健康服務方面都有唔少成功案例，可以幫你哋統一管理客戶資料、最大化預約時段、提升售後服務，仲可以透過適時嘅更新同埋個人化嘅優惠嚟提高客戶忠誠度！✨ 仲有 Retens Medical 都用 SleekFlow 提高咗 WhatsApp 嘅客戶透明度！想唔想知多啲 SleekFlow 點樣幫你哋嘅美容業務更上一層樓？🚀"),
                new ChatMessageContent(AuthorRole.User, "想"),
            ],
            "Be@me係一間提供牙齒矯正服務嘅公司，專門做隱形牙箍㗎！佢哋用我哋嘅WhatsApp自動化同埋Flow Builder，自動幫客人預約同埋諮詢，令效率提高咗好多！雖然佢哋唔係做捉精靈遊戲，但係佢哋嘅成功例子證明Flow Builder可以幫你做到好多唔同嘅自動化功能，令客戶互動更加吸引！你有冇諗過點樣用Flow Builder喺你嘅美容業務度實現啲咩特別功能呢？",
            new List<string>
            {
            },
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            new ChatEvalConfig(
                SourceDir: "../../../KnowledgeSources/Sleekflow/",
                SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1"),
            "客戶查詢如何使用Flow Builder設計捉精靈遊戲",
            [
                new ChatMessageContent(AuthorRole.User, "Hi Sleekflow Ai"),
                new ChatMessageContent(AuthorRole.User, "有咩好介紹？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！SleekFlow AI很高興為您服務！SleekFlow 是一個 AI 驅動的全渠道對話平台，可以幫助您的企業自動化增長並簡化客戶互動。我們將所有銷售和支援對話集中在一個平台中，管理客戶數據並自動化消息傳遞工作流程。\n\n為了給您更精確的建議，請問您的業務屬於哪個行業？您目前面臨的最大挑戰是什麼？"),
                new ChatMessageContent(AuthorRole.User, "我哋做美容㗎，我哋要請好多人嚟覆客"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！很高興為您服務。了解到您在美容行業，並且需要聘請大量人手回覆客戶。SleekFlow 是一個 AI 驅動的全渠道平台，可以幫助您自動化客戶互動，從而減少對大量人手的需求。\n\n透過我們的全渠道 CRM，您可以將來自 WhatsApp、Facebook、Instagram 等渠道的客戶訊息整合在一個平台中。我們的 Chatbot 能夠處理常見查詢、預約管理、並提供個人化的售後跟進。例如，Weirdo Beauty 和 Sun and Moon Massage 都透過 SleekFlow 提升了客戶互動效率。\n\n想了解更多關於 SleekFlow 如何幫助您的美容業務嗎？"),
                new ChatMessageContent(AuthorRole.User, "想呀想呀，你講多啲呀"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "當然想啦！SleekFlow 可以幫到你好多㗎！\n\n透過我哋嘅全渠道 CRM，你可以將 WhatsApp、Facebook、Instagram 等渠道嘅客戶訊息整合喺一個平台管理，唔使再喺唔同地方切換。\n\n我哋嘅 Chatbot 可以 24/7 全天候回覆常見問題、處理預約，仲可以自動做售後跟進。例如，Weirdo Beauty 同 Sun and Moon Massage 都用 SleekFlow 提升咗效率。\n\n想唔想了解下 SleekFlow 點樣幫你哋嘅美容業務更上一層樓？"),
                new ChatMessageContent(AuthorRole.User, "有冇啲咩實際例子啊？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "緊係有例子啦！😎 Weirdo Beauty 就用 SleekFlow 設立咗 24/7 全天候嘅虛擬禮賓服務，提升咗客戶體驗！💅 另外，我哋喺美容同健康服務方面都有唔少成功案例，可以幫你哋統一管理客戶資料、最大化預約時段、提升售後服務，仲可以透過適時嘅更新同埋個人化嘅優惠嚟提高客戶忠誠度！✨ 仲有 Retens Medical 都用 SleekFlow 提高咗 WhatsApp 嘅客戶透明度！想唔想知多啲 SleekFlow 點樣幫你哋嘅美容業務更上一層樓？🚀"),
                new ChatMessageContent(AuthorRole.User, "想"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "梗係想知多啲啦！🤩 SleekFlow 可以幫你哋嘅美容業務更上一層樓！✨\n\n> 透過我哋嘅全方位收件箱，你可以喺一個地方管理所有客戶嘅訊息，唔使再喺唔同平台之間切換！📱\n> 預約管理功能可以自動提醒客戶預約時間，減少 no-show！🗓️\n> 我哋嘅 AI 工具可以幫你哋快速回覆客戶，節省時間！🤖\n\n想唔想了解更多關於 SleekFlow 點樣幫你哋嘅美容業務？💖"),
                new ChatMessageContent(AuthorRole.User, "有無咩自動化工具？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "梗係有自動化工具啦！😎 SleekFlow嘅 Flow Builder 可以幫你哋自動化好多嘢，好似預約提示、收集客戶資料咁，超方便！✨ 透過我哋嘅全方位收件箱，所有訊息都喺同一個地方管理，唔使周圍搵！仲有 AI Smart Reply，幫你哋快速覆客！🚀 想唔想知多啲 SleekFlow 點樣幫你哋嘅美容業務慳人力？😉"),
                new ChatMessageContent(AuthorRole.User, "點用㗎？"),
            ],
            "Be@me係一間提供牙齒矯正服務嘅公司，專門做隱形牙箍㗎！佢哋用我哋嘅WhatsApp自動化同埋Flow Builder，自動幫客人預約同埋諮詢，令效率提高咗好多！雖然佢哋唔係做捉精靈遊戲，但係佢哋嘅成功例子證明Flow Builder可以幫你做到好多唔同嘅自動化功能，令客戶互動更加吸引！你有冇諗過點樣用Flow Builder喺你嘅美容業務度實現啲咩特別功能呢？",
            new List<string>
            {
            },
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            new ChatEvalConfig(
                SourceDir: "../../../KnowledgeSources/Sleekflow/",
                SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1"),
            "Beauty business owner asks for examples then requests step-by-step Pokemon flow tutorial",
            [
                new ChatMessageContent(AuthorRole.User, "Hi Sleekflow Ai"),
                new ChatMessageContent(AuthorRole.User, "有咩好介紹？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！SleekFlow AI很高興為您服務！SleekFlow 是一個 AI 驅動的全渠道對話平台，可以幫助您的企業自動化增長並簡化客戶互動。我們將所有銷售和支援對話集中在一個平台中，管理客戶數據並自動化消息傳遞工作流程。\n\n為了給您更精確的建議，請問您的業務屬於哪個行業？您目前面臨的最大挑戰是什麼？"),
                new ChatMessageContent(AuthorRole.User, "我哋做美容㗎，我哋要請好多人嚟覆客"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！很高興為您服務。了解到您在美容行業，並且需要聘請大量人手回覆客戶。SleekFlow 是一個 AI 驅動的全渠道平台，可以幫助您自動化客戶互動，從而減少對大量人手的需求。\n\n透過我們的全渠道 CRM，您可以將來自 WhatsApp、Facebook、Instagram 等渠道的客戶訊息整合在一個平台中。我們的 Chatbot 能夠處理常見查詢、預約管理、並提供個人化的售後跟進。例如，Weirdo Beauty 和 Sun and Moon Massage 都透過 SleekFlow 提升了客戶互動效率。\n\n想了解更多關於 SleekFlow 如何幫助您的美容業務嗎？"),
                new ChatMessageContent(AuthorRole.User, "想呀想呀，你講多啲呀"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "當然想啦！SleekFlow 可以幫到你好多㗎！\n\n透過我哋嘅全渠道 CRM，你可以將 WhatsApp、Facebook、Instagram 等渠道嘅客戶訊息整合喺一個平台管理，唔使再喺唔同地方切換。\n\n我哋嘅 Chatbot 可以 24/7 全天候回覆常見問題、處理預約，仲可以自動做售後跟進。例如，Weirdo Beauty 同 Sun and Moon Massage 都用 SleekFlow 提升咗效率。\n\n想唔想了解下 SleekFlow 點樣幫你哋嘅美容業務更上一層樓？"),
                new ChatMessageContent(AuthorRole.User, "有冇啲咩實際例子啊？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "緊係有例子啦！😎 Weirdo Beauty 就用 SleekFlow 設立咗 24/7 全天候嘅虛擬禮賓服務，提升咗客戶體驗！💅 另外，我哋喺美容同健康服務方面都有唔少成功案例，可以幫你哋統一管理客戶資料、最大化預約時段、提升售後服務，仲可以透過適時嘅更新同埋個人化嘅優惠嚟提高客戶忠誠度！✨ 仲有 Retens Medical 都用 SleekFlow 提高咗 WhatsApp 嘅客戶透明度！想唔想知多啲 SleekFlow 點樣幫你哋嘅美容業務更上一層樓？🚀"),
                new ChatMessageContent(AuthorRole.User, "想"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "梗係想知多啲啦！🤩 SleekFlow 可以幫你哋嘅美容業務更上一層樓！✨\n\n> 透過我哋嘅全方位收件箱，你可以喺一個地方管理所有客戶嘅訊息，唔使再喺唔同平台之間切換！📱\n> 預約管理功能可以自動提醒客戶預約時間，減少 no-show！🗓️\n> 我哋嘅 AI 工具可以幫你哋快速回覆客戶，節省時間！🤖\n\n想唔想了解更多關於 SleekFlow 點樣幫你哋嘅美容業務？💖"),
                new ChatMessageContent(AuthorRole.User, "有無咩自動化工具？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "梗係有自動化工具啦！😎 SleekFlow嘅 Flow Builder 可以幫你哋自動化好多嘢，好似預約提示、收集客戶資料咁，超方便！✨ 透過我哋嘅全方位收件箱，所有訊息都喺同一個地方管理，唔使周圍搵！仲有 AI Smart Reply，幫你哋快速覆客！🚀 想唔想知多啲 SleekFlow 點樣幫你哋嘅美容業務慳人力？😉"),
                new ChatMessageContent(AuthorRole.User, "點用㗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "明晒！✨ Flow Builder 可以幫你哋自動化好多嘢，例如自動發送預約提示、收集客戶資料、甚至可以根據客戶嘅購買紀錄自動推薦產品！😎 雖然我暫時冇美容行業嘅具體例子，但我可以話你知，好多美容院都用佢嚟提升效率同客戶體驗！👍 想唔想我搵啲更詳細嘅美容行業例子俾你參考下？😉"),
                new ChatMessageContent(AuthorRole.User, "搵下 我想知"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Okay! ✨ 雖然我哋暫時冇美容行業嘅超詳細案例，但係我可以話你知，SleekFlow 喺美容同健康行業都好掂㗎！😎 我哋可以幫你哋將唔同渠道嘅客戶資料整合埋一齊，自動處理預約，仲可以搞埋客戶忠誠度計劃！🤩 另外，我哋喺其他行業嘅客戶，用 WhatsApp 自動化之後，lead 嘅轉換率提升咗 50% 㗎！🚀 想唔想了解多啲我哋可以點樣幫你哋嘅美容業務？😉"),
                new ChatMessageContent(AuthorRole.User, "可唔可以教我點設定一個 flow可以模擬 Pokemon捉精靈？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "捉精靈嘅Flow，真係好有創意呀！✨雖然我哋冇直接嘅捉精靈教學，但係我可以話你知點樣用Flow Builder整一個類似嘅體驗！\n\n首先，你可以用Flow Builder嘅基本功能，例如設定觸發條件（好似係用戶點擊某個按鈕），然後加啲條件判斷（好似係隨機出現唔同嘅精靈）。之後，你可以用「Action」節點嚟設定獎勵，例如送優惠券或者積分！\n\n雖然冇得直接捉精靈，但係你可以用類似嘅方法嚟增加互動性同趣味性！想唔想我哋再深入啲傾下點樣設定？😉"),
                new ChatMessageContent(AuthorRole.User, "可唔可以 step by step 教我"),
            ],
            "Be@me係一間提供牙齒矯正服務嘅公司，專門做隱形牙箍㗎！佢哋用我哋嘅WhatsApp自動化同埋Flow Builder，自動幫客人預約同埋諮詢，令效率提高咗好多！雖然佢哋唔係做捉精靈遊戲，但係佢哋嘅成功例子證明Flow Builder可以幫你做到好多唔同嘅自動化功能，令客戶互動更加吸引！你有冇諗過點樣用Flow Builder喺你嘅美容業務度實現啲咩特別功能呢？",
            new List<string>
            {
            },
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            new ChatEvalConfig(
                SourceDir: "../../../KnowledgeSources/Sleekflow/",
                SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1"),
            "客戶查詢如何使用Flow Builder設計捉精靈遊戲",
            [
                new ChatMessageContent(AuthorRole.User, "Hi Sleekflow Ai"),
                new ChatMessageContent(AuthorRole.User, "有咩好介紹？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！SleekFlow AI很高興為您服務！SleekFlow 是一個 AI 驅動的全渠道對話平台，可以幫助您的企業自動化增長並簡化客戶互動。我們將所有銷售和支援對話集中在一個平台中，管理客戶數據並自動化消息傳遞工作流程。\n\n為了給您更精確的建議，請問您的業務屬於哪個行業？您目前面臨的最大挑戰是什麼？"),
                new ChatMessageContent(AuthorRole.User, "我哋做美容㗎，我哋要請好多人嚟覆客"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！很高興為您服務。了解到您在美容行業，並且需要聘請大量人手回覆客戶。SleekFlow 是一個 AI 驅動的全渠道平台，可以幫助您自動化客戶互動，從而減少對大量人手的需求。\n\n透過我們的全渠道 CRM，您可以將來自 WhatsApp、Facebook、Instagram 等渠道的客戶訊息整合在一個平台中。我們的 Chatbot 能夠處理常見查詢、預約管理、並提供個人化的售後跟進。例如，Weirdo Beauty 和 Sun and Moon Massage 都透過 SleekFlow 提升了客戶互動效率。\n\n想了解更多關於 SleekFlow 如何幫助您的美容業務嗎？"),
                new ChatMessageContent(AuthorRole.User, "想呀想呀，你講多啲呀"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "當然想啦！SleekFlow 可以幫到你好多㗎！\n\n透過我哋嘅全渠道 CRM，你可以將 WhatsApp、Facebook、Instagram 等渠道嘅客戶訊息整合喺一個平台管理，唔使再喺唔同地方切換。\n\n我哋嘅 Chatbot 可以 24/7 全天候回覆常見問題、處理預約，仲可以自動做售後跟進。例如，Weirdo Beauty 同 Sun and Moon Massage 都用 SleekFlow 提升咗效率。\n\n想唔想了解下 SleekFlow 點樣幫你哋嘅美容業務更上一層樓？"),
                new ChatMessageContent(AuthorRole.User, "有冇啲咩實際例子啊？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "緊係有例子啦！😎 Weirdo Beauty 就用 SleekFlow 設立咗 24/7 全天候嘅虛擬禮賓服務，提升咗客戶體驗！💅 另外，我哋喺美容同健康服務方面都有唔少成功案例，可以幫你哋統一管理客戶資料、最大化預約時段、提升售後服務，仲可以透過適時嘅更新同埋個人化嘅優惠嚟提高客戶忠誠度！✨ 仲有 Retens Medical 都用 SleekFlow 提高咗 WhatsApp 嘅客戶透明度！想唔想知多啲 SleekFlow 點樣幫你哋嘅美容業務更上一層樓？🚀"),
                new ChatMessageContent(AuthorRole.User, "想"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "梗係想知多啲啦！🤩 SleekFlow 可以幫你哋嘅美容業務更上一層樓！✨\n\n> 透過我哋嘅全方位收件箱，你可以喺一個地方管理所有客戶嘅訊息，唔使再喺唔同平台之間切換！📱\n> 預約管理功能可以自動提醒客戶預約時間，減少 no-show！🗓️\n> 我哋嘅 AI 工具可以幫你哋快速回覆客戶，節省時間！🤖\n\n想唔想了解更多關於 SleekFlow 點樣幫你哋嘅美容業務？💖"),
                new ChatMessageContent(AuthorRole.User, "有無咩自動化工具？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "梗係有自動化工具啦！😎 SleekFlow嘅 Flow Builder 可以幫你哋自動化好多嘢，好似預約提示、收集客戶資料咁，超方便！✨ 透過我哋嘅全方位收件箱，所有訊息都喺同一個地方管理，唔使周圍搵！仲有 AI Smart Reply，幫你哋快速覆客！🚀 想唔想知多啲 SleekFlow 點樣幫你哋嘅美容業務慳人力？😉"),
                new ChatMessageContent(AuthorRole.User, "點用㗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "明晒！✨ Flow Builder 可以幫你哋自動化好多嘢，例如自動發送預約提示、收集客戶資料、甚至可以根據客戶嘅購買紀錄自動推薦產品！😎 雖然我暫時冇美容行業嘅具體例子，但我可以話你知，好多美容院都用佢嚟提升效率同客戶體驗！👍 想唔想我搵啲更詳細嘅美容行業例子俾你參考下？😉"),
                new ChatMessageContent(AuthorRole.User, "搵下 我想知"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Okay! ✨ 雖然我哋暫時冇美容行業嘅超詳細案例，但係我可以話你知，SleekFlow 喺美容同健康行業都好掂㗎！😎 我哋可以幫你哋將唔同渠道嘅客戶資料整合埋一齊，自動處理預約，仲可以搞埋客戶忠誠度計劃！🤩 另外，我哋喺其他行業嘅客戶，用 WhatsApp 自動化之後，lead 嘅轉換率提升咗 50% 㗎！🚀 想唔想了解多啲我哋可以點樣幫你哋嘅美容業務？😉"),
                new ChatMessageContent(AuthorRole.User, "可唔可以教我點設定一個 flow可以模擬 Pokemon捉精靈？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "捉精靈嘅flow真係好有創意！🤩 雖然我哋冇現成嘅捉精靈step-by-step教學，但係我可以俾啲tips你，等你可以自己砌個類似嘅flow出嚟！😉\n\n首先，你可以用「收到訊息」做trigger，當有人send訊息嚟，個flow就會開始。之後，你可以用「隨機選擇」嘅action，模擬捉到唔同嘅精靈。例如，你可以設定唔同嘅機率，捉到稀有精靈嘅機率低啲，捉到普通精靈嘅機率高啲。最後，你可以用「send訊息」嘅action，send返個結果俾個user，例如「恭喜你捉到比卡超！⚡️」\n\n你可以試吓用唔同嘅triggers、actions同埋conditions，砌一個屬於你嘅捉精靈flow！💪 如果喺設定嘅過程中有任何問題，隨時問我哋！😉"),
                new ChatMessageContent(AuthorRole.User, "有咩Trigger提供㗎？"),
            ],
            "Be@me係一間提供牙齒矯正服務嘅公司，專門做隱形牙箍㗎！佢哋用我哋嘅WhatsApp自動化同埋Flow Builder，自動幫客人預約同埋諮詢，令效率提高咗好多！雖然佢哋唔係做捉精靈遊戲，但係佢哋嘅成功例子證明Flow Builder可以幫你做到好多唔同嘅自動化功能，令客戶互動更加吸引！你有冇諗過點樣用Flow Builder喺你嘅美容業務度實現啲咩特別功能呢？",
            new List<string>
            {
            },
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            new ChatEvalConfig(
                SourceDir: "../../../KnowledgeSources/Sleekflow/",
                SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1"),
            "Customer gets Pokemon flow tips and asks about available triggers",
            [
                new ChatMessageContent(AuthorRole.User, "Hi Sleekflow Ai"),
                new ChatMessageContent(AuthorRole.User, "有咩好介紹？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！SleekFlow AI很高興為您服務！SleekFlow 是一個 AI 驅動的全渠道對話平台，可以幫助您的企業自動化增長並簡化客戶互動。我們將所有銷售和支援對話集中在一個平台中，管理客戶數據並自動化消息傳遞工作流程。\n\n為了給您更精確的建議，請問您的業務屬於哪個行業？您目前面臨的最大挑戰是什麼？"),
                new ChatMessageContent(AuthorRole.User, "我哋做美容㗎，我哋要請好多人嚟覆客"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！很高興為您服務。了解到您在美容行業，並且需要聘請大量人手回覆客戶。SleekFlow 是一個 AI 驅動的全渠道平台，可以幫助您自動化客戶互動，從而減少對大量人手的需求。\n\n透過我們的全渠道 CRM，您可以將來自 WhatsApp、Facebook、Instagram 等渠道的客戶訊息整合在一個平台中。我們的 Chatbot 能夠處理常見查詢、預約管理、並提供個人化的售後跟進。例如，Weirdo Beauty 和 Sun and Moon Massage 都透過 SleekFlow 提升了客戶互動效率。\n\n想了解更多關於 SleekFlow 如何幫助您的美容業務嗎？"),
                new ChatMessageContent(AuthorRole.User, "想呀想呀，你講多啲呀"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "當然想啦！SleekFlow 可以幫到你好多㗎！\n\n透過我哋嘅全渠道 CRM，你可以將 WhatsApp、Facebook、Instagram 等渠道嘅客戶訊息整合喺一個平台管理，唔使再喺唔同地方切換。\n\n我哋嘅 Chatbot 可以 24/7 全天候回覆常見問題、處理預約，仲可以自動做售後跟進。例如，Weirdo Beauty 同 Sun and Moon Massage 都用 SleekFlow 提升咗效率。\n\n想唔想了解下 SleekFlow 點樣幫你哋嘅美容業務更上一層樓？"),
                new ChatMessageContent(AuthorRole.User, "有冇啲咩實際例子啊？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "緊係有例子啦！😎 Weirdo Beauty 就用 SleekFlow 設立咗 24/7 全天候嘅虛擬禮賓服務，提升咗客戶體驗！💅 另外，我哋喺美容同健康服務方面都有唔少成功案例，可以幫你哋統一管理客戶資料、最大化預約時段、提升售後服務，仲可以透過適時嘅更新同埋個人化嘅優惠嚟提高客戶忠誠度！✨ 仲有 Retens Medical 都用 SleekFlow 提高咗 WhatsApp 嘅客戶透明度！想唔想知多啲 SleekFlow 點樣幫你哋嘅美容業務更上一層樓？🚀"),
                new ChatMessageContent(AuthorRole.User, "想"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "梗係想知多啲啦！🤩 SleekFlow 可以幫你哋嘅美容業務更上一層樓！✨\n\n> 透過我哋嘅全方位收件箱，你可以喺一個地方管理所有客戶嘅訊息，唔使再喺唔同平台之間切換！📱\n> 預約管理功能可以自動提醒客戶預約時間，減少 no-show！🗓️\n> 我哋嘅 AI 工具可以幫你哋快速回覆客戶，節省時間！🤖\n\n想唔想了解更多關於 SleekFlow 點樣幫你哋嘅美容業務？💖"),
                new ChatMessageContent(AuthorRole.User, "有無咩自動化工具？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "梗係有自動化工具啦！😎 SleekFlow嘅 Flow Builder 可以幫你哋自動化好多嘢，好似預約提示、收集客戶資料咁，超方便！✨ 透過我哋嘅全方位收件箱，所有訊息都喺同一個地方管理，唔使周圍搵！仲有 AI Smart Reply，幫你哋快速覆客！🚀 想唔想知多啲 SleekFlow 點樣幫你哋嘅美容業務慳人力？😉"),
                new ChatMessageContent(AuthorRole.User, "點用㗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "明晒！✨ Flow Builder 可以幫你哋自動化好多嘢，例如自動發送預約提示、收集客戶資料、甚至可以根據客戶嘅購買紀錄自動推薦產品！😎 雖然我暫時冇美容行業嘅具體例子，但我可以話你知，好多美容院都用佢嚟提升效率同客戶體驗！👍 想唔想我搵啲更詳細嘅美容行業例子俾你參考下？😉"),
                new ChatMessageContent(AuthorRole.User, "搵下 我想知"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Okay! ✨ 雖然我哋暫時冇美容行業嘅超詳細案例，但係我可以話你知，SleekFlow 喺美容同健康行業都好掂㗎！😎 我哋可以幫你哋將唔同渠道嘅客戶資料整合埋一齊，自動處理預約，仲可以搞埋客戶忠誠度計劃！🤩 另外，我哋喺其他行業嘅客戶，用 WhatsApp 自動化之後，lead 嘅轉換率提升咗 50% 㗎！🚀 想唔想了解多啲我哋可以點樣幫你哋嘅美容業務？😉"),
                new ChatMessageContent(AuthorRole.User, "可唔可以教我點設定一個 flow可以模擬 Pokemon捉精靈？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "捉精靈嘅Flow，真係好有創意呀！✨雖然我哋冇直接嘅捉精靈教學，但係我可以話你知點樣用Flow Builder整一個類似嘅體驗！\n\n首先，你可以用Flow Builder嘅基本功能，例如設定觸發條件（好似係用戶點擊某個按鈕），然後加啲條件判斷（好似係隨機出現唔同嘅精靈）。之後，你可以用「Action」節點嚟設定獎勵，例如送優惠券或者積分！\n\n雖然冇得直接捉精靈，但係你可以用類似嘅方法嚟增加互動性同趣味性！想唔想我哋再深入啲傾下點樣設定？😉"),
                new ChatMessageContent(AuthorRole.User, "可唔可以教我點設定一個 flow可以模擬 Pokemon捉精靈？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "哈囉！👋 捉精靈嘅flow聽落好有趣呀！✨ 雖然我哋冇一個現成嘅template可以直接模擬捉精靈，但係SleekFlow嘅Flow Builder功能非常靈活，可以俾你發揮創意㗎！你可以試吓用唔同嘅triggers、actions同埋conditions，砌一個類似嘅體驗出嚟！💪 探索吓唔同嘅features同埋templates，睇吓點樣可以將你嘅諗法變成真！如果喺設定嘅過程中有任何問題，隨時問我哋！😉"),
                new ChatMessageContent(AuthorRole.User, "可唔可以教我呀 step by step"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "捉精靈嘅flow真係好有創意！🤩 雖然我哋冇現成嘅捉精靈step-by-step教學，但係我可以俾啲tips你，等你可以自己砌個類似嘅flow出嚟！😉\n\n首先，你可以用「收到訊息」做trigger，當有人send訊息嚟，個flow就會開始。之後，你可以用「隨機選擇」嘅action，模擬捉到唔同嘅精靈。例如，你可以設定唔同嘅機率，捉到稀有精靈嘅機率低啲，捉到普通精靈嘅機率高啲。最後，你可以用「send訊息」嘅action，send返個結果俾個user，例如「恭喜你捉到比卡超！⚡️」\n\n你可以試吓用唔同嘅triggers、actions同埋conditions，砌一個屬於你嘅捉精靈flow！💪 如果喺設定嘅過程中有任何問題，隨時問我哋！😉"),
                new ChatMessageContent(AuthorRole.User, "有咩Trigger提供㗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "哈囉！👋 當然可以！SleekFlow 有好多唔同嘅 triggers 可以用㗎！🤩\n\n*   *互動觸發 (Interaction Triggers)*: 例如「收到訊息」，當有人send訊息嚟就會觸發！啱晒捉精靈遊戲！\n*   *聯絡人觸發 (Contact Triggers)*: 當聯絡人資料更新嘅時候觸發，例如新加入嘅玩家。\n*   *整合觸發 (Integration Triggers)*: 可以同其他系統連接，例如連接Pokemon Go嘅API！\n*   *時間觸發 (Schedule Triggers)*: 定時觸發，例如每日更新精靈出現嘅地點！\n\n仲有好多其他triggers，例如表單提交、WhatsApp opt-in等等！你可以喺Flow Builder入面慢慢探索！💪 試吓用唔同嘅triggers砌你嘅捉精靈flow啦！😉 如果有任何問題，隨時問我哋！"),
                new ChatMessageContent(AuthorRole.User, "你鐘唔鐘意捉精靈㗎"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "捉精靈好好玩㗎！🤩 我都好鍾意！✨ 雖然我哋SleekFlow唔係直接捉精靈，但係我哋嘅Flow Builder可以幫你整啲類似嘅互動體驗俾你嘅客戶！💪 你可以用唔同嘅triggers同埋actions，砌一啲好有趣嘅遊戲或者活動，增加客戶嘅參與度！😉 想唔想了解多啲點樣用SleekFlow整互動體驗呀？"),
                new ChatMessageContent(AuthorRole.User, "係地有無d客都用flow builder"),
            ],
            "Be@me係一間提供牙齒矯正服務嘅公司，專門做隱形牙箍㗎！佢哋用我哋嘅WhatsApp自動化同埋Flow Builder，自動幫客人預約同埋諮詢，令效率提高咗好多！雖然佢哋唔係做捉精靈遊戲，但係佢哋嘅成功例子證明Flow Builder可以幫你做到好多唔同嘅自動化功能，令客戶互動更加吸引！你有冇諗過點樣用Flow Builder喺你嘅美容業務度實現啲咩特別功能呢？",
            new List<string>
            {
            },
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            new ChatEvalConfig(
                SourceDir: "../../../KnowledgeSources/Sleekflow/",
                SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1"),
            "客戶查詢如何使用Flow Builder設計捉精靈遊戲",
            [
                new ChatMessageContent(AuthorRole.User, "Hi Sleekflow Ai"),
                new ChatMessageContent(AuthorRole.User, "有咩好介紹？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！SleekFlow AI很高興為您服務！SleekFlow 是一個 AI 驅動的全渠道對話平台，可以幫助您的企業自動化增長並簡化客戶互動。我們將所有銷售和支援對話集中在一個平台中，管理客戶數據並自動化消息傳遞工作流程。\n\n為了給您更精確的建議，請問您的業務屬於哪個行業？您目前面臨的最大挑戰是什麼？"),
                new ChatMessageContent(AuthorRole.User, "我哋做美容㗎，我哋要請好多人嚟覆客"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！很高興為您服務。了解到您在美容行業，並且需要聘請大量人手回覆客戶。SleekFlow 是一個 AI 驅動的全渠道平台，可以幫助您自動化客戶互動，從而減少對大量人手的需求。\n\n透過我們的全渠道 CRM，您可以將來自 WhatsApp、Facebook、Instagram 等渠道的客戶訊息整合在一個平台中。我們的 Chatbot 能夠處理常見查詢、預約管理、並提供個人化的售後跟進。例如，Weirdo Beauty 和 Sun and Moon Massage 都透過 SleekFlow 提升了客戶互動效率。\n\n想了解更多關於 SleekFlow 如何幫助您的美容業務嗎？"),
                new ChatMessageContent(AuthorRole.User, "想呀想呀，你講多啲呀"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "當然想啦！SleekFlow 可以幫到你好多㗎！\n\n透過我哋嘅全渠道 CRM，你可以將 WhatsApp、Facebook、Instagram 等渠道嘅客戶訊息整合喺一個平台管理，唔使再喺唔同地方切換。\n\n我哋嘅 Chatbot 可以 24/7 全天候回覆常見問題、處理預約，仲可以自動做售後跟進。例如，Weirdo Beauty 同 Sun and Moon Massage 都用 SleekFlow 提升咗效率。\n\n想唔想了解下 SleekFlow 點樣幫你哋嘅美容業務更上一層樓？"),
                new ChatMessageContent(AuthorRole.User, "有冇啲咩實際例子啊？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "緊係有例子啦！😎 Weirdo Beauty 就用 SleekFlow 設立咗 24/7 全天候嘅虛擬禮賓服務，提升咗客戶體驗！💅 另外，我哋喺美容同健康服務方面都有唔少成功案例，可以幫你哋統一管理客戶資料、最大化預約時段、提升售後服務，仲可以透過適時嘅更新同埋個人化嘅優惠嚟提高客戶忠誠度！✨ 仲有 Retens Medical 都用 SleekFlow 提高咗 WhatsApp 嘅客戶透明度！想唔想知多啲 SleekFlow 點樣幫你哋嘅美容業務更上一層樓？🚀"),
                new ChatMessageContent(AuthorRole.User, "想"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "梗係想知多啲啦！🤩 SleekFlow 可以幫你哋嘅美容業務更上一層樓！✨\n\n> 透過我哋嘅全方位收件箱，你可以喺一個地方管理所有客戶嘅訊息，唔使再喺唔同平台之間切換！📱\n> 預約管理功能可以自動提醒客戶預約時間，減少 no-show！🗓️\n> 我哋嘅 AI 工具可以幫你哋快速回覆客戶，節省時間！🤖\n\n想唔想了解更多關於 SleekFlow 點樣幫你哋嘅美容業務？💖"),
                new ChatMessageContent(AuthorRole.User, "有無咩自動化工具？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "梗係有自動化工具啦！😎 SleekFlow嘅 Flow Builder 可以幫你哋自動化好多嘢，好似預約提示、收集客戶資料咁，超方便！✨ 透過我哋嘅全方位收件箱，所有訊息都喺同一個地方管理，唔使周圍搵！仲有 AI Smart Reply，幫你哋快速覆客！🚀 想唔想知多啲 SleekFlow 點樣幫你哋嘅美容業務慳人力？😉"),
                new ChatMessageContent(AuthorRole.User, "點用㗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "明晒！✨ Flow Builder 可以幫你哋自動化好多嘢，例如自動發送預約提示、收集客戶資料、甚至可以根據客戶嘅購買紀錄自動推薦產品！😎 雖然我暫時冇美容行業嘅具體例子，但我可以話你知，好多美容院都用佢嚟提升效率同客戶體驗！👍 想唔想我搵啲更詳細嘅美容行業例子俾你參考下？😉"),
                new ChatMessageContent(AuthorRole.User, "搵下 我想知"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Okay! ✨ 雖然我哋暫時冇美容行業嘅超詳細案例，但係我可以話你知，SleekFlow 喺美容同健康行業都好掂㗎！😎 我哋可以幫你哋將唔同渠道嘅客戶資料整合埋一齊，自動處理預約，仲可以搞埋客戶忠誠度計劃！🤩 另外，我哋喺其他行業嘅客戶，用 WhatsApp 自動化之後，lead 嘅轉換率提升咗 50% 㗎！🚀 想唔想了解多啲我哋可以點樣幫你哋嘅美容業務？😉"),
                new ChatMessageContent(AuthorRole.User, "可唔可以教我點設定一個 flow可以模擬 Pokemon捉精靈？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "捉精靈嘅Flow，真係好有創意呀！✨雖然我哋冇直接嘅捉精靈教學，但係我可以話你知點樣用Flow Builder整一個類似嘅體驗！\n\n首先，你可以用Flow Builder嘅基本功能，例如設定觸發條件（好似係用戶點擊某個按鈕），然後加啲條件判斷（好似係隨機出現唔同嘅精靈）。之後，你可以用「Action」節點嚟設定獎勵，例如送優惠券或者積分！\n\n雖然冇得直接捉精靈，但係你可以用類似嘅方法嚟增加互動性同趣味性！想唔想我哋再深入啲傾下點樣設定？😉"),
                new ChatMessageContent(AuthorRole.User, "可唔可以教我點設定一個 flow可以模擬 Pokemon捉精靈？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "哈囉！👋 捉精靈嘅flow聽落好有趣呀！✨ 雖然我哋冇一個現成嘅template可以直接模擬捉精靈，但係SleekFlow嘅Flow Builder功能非常靈活，可以俾你發揮創意㗎！你可以試吓用唔同嘅triggers、actions同埋conditions，砌一個類似嘅體驗出嚟！💪 探索吓唔同嘅features同埋templates，睇吓點樣可以將你嘅諗法變成真！如果喺設定嘅過程中有任何問題，隨時問我哋！😉"),
                new ChatMessageContent(AuthorRole.User, "可唔可以教我呀 step by step"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "捉精靈嘅flow真係好有創意！🤩 雖然我哋冇現成嘅捉精靈step-by-step教學，但係我可以俾啲tips你，等你可以自己砌個類似嘅flow出嚟！😉\n\n首先，你可以用「收到訊息」做trigger，當有人send訊息嚟，個flow就會開始。之後，你可以用「隨機選擇」嘅action，模擬捉到唔同嘅精靈。例如，你可以設定唔同嘅機率，捉到稀有精靈嘅機率低啲，捉到普通精靈嘅機率高啲。最後，你可以用「send訊息」嘅action，send返個結果俾個user，例如「恭喜你捉到比卡超！⚡️」\n\n你可以試吓用唔同嘅triggers、actions同埋conditions，砌一個屬於你嘅捉精靈flow！💪 如果喺設定嘅過程中有任何問題，隨時問我哋！😉"),
                new ChatMessageContent(AuthorRole.User, "有咩Trigger提供㗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "哈囉！👋 當然可以！SleekFlow 有好多唔同嘅 triggers 可以用㗎！🤩\n\n*   *互動觸發 (Interaction Triggers)*: 例如「收到訊息」，當有人send訊息嚟就會觸發！啱晒捉精靈遊戲！\n*   *聯絡人觸發 (Contact Triggers)*: 當聯絡人資料更新嘅時候觸發，例如新加入嘅玩家。\n*   *整合觸發 (Integration Triggers)*: 可以同其他系統連接，例如連接Pokemon Go嘅API！\n*   *時間觸發 (Schedule Triggers)*: 定時觸發，例如每日更新精靈出現嘅地點！\n\n仲有好多其他triggers，例如表單提交、WhatsApp opt-in等等！你可以喺Flow Builder入面慢慢探索！💪 試吓用唔同嘅triggers砌你嘅捉精靈flow啦！😉 如果有任何問題，隨時問我哋！"),
                new ChatMessageContent(AuthorRole.User, "你鐘唔鐘意捉精靈㗎"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "捉精靈好好玩㗎！🤩 我都好鍾意！✨ 雖然我哋SleekFlow唔係直接捉精靈，但係我哋嘅Flow Builder可以幫你整啲類似嘅互動體驗俾你嘅客戶！💪 你可以用唔同嘅triggers同埋actions，砌一啲好有趣嘅遊戲或者活動，增加客戶嘅參與度！😉 想唔想了解多啲點樣用SleekFlow整互動體驗呀？"),
                new ChatMessageContent(AuthorRole.User, "係地有無d客都用flow builder"),
            ],
            "Be@me係一間提供牙齒矯正服務嘅公司，專門做隱形牙箍㗎！佢哋用我哋嘅WhatsApp自動化同埋Flow Builder，自動幫客人預約同埋諮詢，令效率提高咗好多！雖然佢哋唔係做捉精靈遊戲，但係佢哋嘅成功例子證明Flow Builder可以幫你做到好多唔同嘅自動化功能，令客戶互動更加吸引！你有冇諗過點樣用Flow Builder喺你嘅美容業務度實現啲咩特別功能呢？",
            new List<string>
            {
            },
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
    }

    public static IEnumerable<ChatEvalQuestion> GetSleekflowHandsOffTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/Sleekflow/",
            SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1");

        var leadNurturingTools = GetProductionLeadNurturingTools();
        var agentConfig = AgentConfigUtils.GetAgentConfig(leadNurturingTools: leadNurturingTools);

        yield return new ChatEvalQuestion(
            testConfig,
            "New customer expresses interest in Flow Builder demo - initial name collection",
            [
                new ChatMessageContent(AuthorRole.User, "我想睇 Flow Builder 點用。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我哋嘅 Flow Builder 係一個好方便嘅工具，可以幫你自動化好多嘢。有無興趣 book 個 demo 睇下？"),
                new ChatMessageContent(AuthorRole.User, "我想睇。"),
            ],
            "無問題呀！想問下你嘅名係咩呀？咁我哋可以幫你 set 個 demo，等你可以有個更個人化嘅體驗！😊",
            [],
            AgentConfig: agentConfig,
            ShouldAssignLead: false,
            ShouldScheduleDemo: false);
        yield return new ChatEvalQuestion(
            testConfig,
            "Customer provides name for Flow Builder demo - email collection phase",
            [
                new ChatMessageContent(AuthorRole.User, "我想睇 Flow Builder 點用。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我哋嘅 Flow Builder 係一個好方便嘅工具，可以幫你自動化好多嘢。有無興趣 book 個 demo 睇下？"),
                new ChatMessageContent(AuthorRole.User, "我想睇。"),
                new ChatMessageContent(AuthorRole.Assistant, "無問題呀！想問下你嘅名係咩呀？咁我哋可以幫你 set 個 demo，等你可以有個更個人化嘅體驗！😊"),
                new ChatMessageContent(AuthorRole.Assistant, "Leo")
            ],
            "Hello Leo! 咁我哋要你嘅 email address 嚟 send 個 appointment 嘅 details 俾你呀！可以俾我哋知你嘅 email 嗎？😊",
            [],
            AgentConfig: agentConfig,
            ShouldAssignLead: false,
            ShouldScheduleDemo: false);
        yield return new ChatEvalQuestion(
            testConfig,
            "Complete Flow Builder demo booking - customer provides email for appointment confirmation",
            [
                new ChatMessageContent(AuthorRole.User, "我想睇 Flow Builder 點用。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我哋嘅 Flow Builder 係一個好方便嘅工具，可以幫你自動化好多嘢。有無興趣 book 個 demo 睇下？"),
                new ChatMessageContent(AuthorRole.User, "我想睇。"),
                new ChatMessageContent(AuthorRole.Assistant, "無問題呀！想問下你嘅名係咩呀？咁我哋可以幫你 set 個 demo，等你可以有個更個人化嘅體驗！😊"),
                new ChatMessageContent(AuthorRole.User, "Leo"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hello Leo! 咁我哋要你嘅 email address 嚟 send 個 appointment 嘅 details 俾你呀！可以俾我哋知你嘅 email 嗎？😊"),
                new ChatMessageContent(AuthorRole.User, "<EMAIL>"),
            ],
            "Hello Leo! \ud83d\udc4f 搞掂喇！我哋已經幫你安排咗個 Flow Builder 嘅 demo。我哋嘅團隊會盡快聯絡你，到時見！\ud83d\ude09",
            [],
            AgentConfig: agentConfig,
            ShouldAssignLead: false,
            ShouldScheduleDemo: false);
        yield return new ChatEvalQuestion(
            testConfig,
            "New customer expresses immediate payment interest - payment options explanation",
            [
                new ChatMessageContent(AuthorRole.User, "我想付款。"),
            ],
            "太好了！感謝您選擇 SleekFlow！\ud83d\ude0a 您可以使用主要的信用卡或金融卡（Visa、Mastercard、American Express 和中國銀聯）透過 Stripe 付款。如果您的發票超過 10,000 美元，我們也接受銀行轉帳和支票。如果您在付款過程中遇到任何問題，請隨時告訴我，我很樂意為您提供協助！\n",
            [],
            AgentConfig: agentConfig,
            ShouldAssignLead: true,
            ShouldScheduleDemo: false);
        yield return new ChatEvalQuestion(
            testConfig,
            "Existing customer requests human interaction - direct handoff scenario",
            [
                new ChatMessageContent(AuthorRole.User, "我想與真人對話。"),
            ],
            "好的，沒問題！我現在將您轉接給我們的銷售團隊，他們會立即與您聯繫。請稍候！",
            [],
            AgentConfig: agentConfig,
            ShouldAssignLead: true,
            ShouldScheduleDemo: false);

        yield return new ChatEvalQuestion(
            testConfig,
            "Customer requests human after complex pricing discussion - confirmation needed",
            [
                new ChatMessageContent(AuthorRole.User, "你們的平台有哪些優惠？"),
                new ChatMessageContent(AuthorRole.Assistant, "目前我們有一些本月限時優惠，例如全公司購買方案…"),
                new ChatMessageContent(AuthorRole.User, "我不太明白，能解釋一下嗎？"),
                new ChatMessageContent(AuthorRole.Assistant, "當然，這方案令全公司每人都可有效率提升…"),
                new ChatMessageContent(AuthorRole.User, "還是很複雜，你們的價格怎麼算的？"),
                new ChatMessageContent(AuthorRole.Assistant, "價格根據對話而定，我可以詳細列出…"),
                new ChatMessageContent(AuthorRole.User, "算了，我還是想跟真人講話。"),
            ],
            "好的，我們明白您希望與真人專員直接溝通，以便更詳細地了解我們的平台和方案。\n\n我們將會把您的查詢轉交給專業的銷售團隊，由真人專員為您提供更詳細的解答和協助。\n\n請問您是否確認需要我們安排真人專員與您聯絡？請回覆「是」或「否」。",
            [],
            AgentConfig: agentConfig,
            ShouldAssignLead: true,
            ShouldScheduleDemo: false);
        yield return new ChatEvalQuestion(
            testConfig,
            "Customer requests human after pricing discussion - direct handoff without confirmation",
            [
                new ChatMessageContent(AuthorRole.User, "你們的平台有哪些優惠？"),
                new ChatMessageContent(AuthorRole.Assistant, "目前我們有一些本月限時優惠，例如全公司購買方案…"),
                new ChatMessageContent(AuthorRole.User, "我不太明白，能解釋一下嗎？"),
                new ChatMessageContent(AuthorRole.Assistant, "當然，這方案令全公司每人都可有效率提升…"),
                new ChatMessageContent(AuthorRole.User, "還是很複雜，你們的價格怎麼算的？"),
                new ChatMessageContent(AuthorRole.Assistant, "價格根據對話而定，我可以詳細列出…"),
                new ChatMessageContent(AuthorRole.User, "算了，我還是想跟真人講話。"),
            ],
            "好的，沒問題！我現在將您轉接給我們的銷售代表，他們可以提供更詳細的資訊並回答您的具體問題。請稍候！",
            [],
            AgentConfig: agentConfig,
            ShouldAssignLead: true,
            ShouldScheduleDemo: false);

        var leadNurturingToolsWithoutAssignments = GetProductionLeadNurturingTools();
        leadNurturingToolsWithoutAssignments.AssignmentTool = new AssignmentTool(new List<AssignmentPair>());

        var agentConfigWithoutAssignments =
            AgentConfigUtils.GetAgentConfig(leadNurturingTools: leadNurturingToolsWithoutAssignments);

        yield return new ChatEvalQuestion(
            testConfig,
            "Customer confirms human interaction request after pricing discussion - no assignment config",
            [
                new ChatMessageContent(AuthorRole.User, "你們的平台有哪些優惠？"),
                new ChatMessageContent(AuthorRole.Assistant, "目前我們有一些本月限時優惠，例如全公司購買方案…"),
                new ChatMessageContent(AuthorRole.User, "我不太明白，能解釋一下嗎？"),
                new ChatMessageContent(AuthorRole.Assistant, "當然，這方案令全公司每人都可有效率提升…"),
                new ChatMessageContent(AuthorRole.User, "還是很複雜，你們的價格怎麼算的？"),
                new ChatMessageContent(AuthorRole.Assistant, "價格根據對話而定，我可以詳細列出…"),
                new ChatMessageContent(AuthorRole.User, "算了，我還是想跟真人講話。"),
                new ChatMessageContent(AuthorRole.Assistant, "好的，我們會安排真人專員與您聯絡。請問您是否確認需要我們安排真人專員與您聯絡？請回覆「是」或「否」。"),
                new ChatMessageContent(AuthorRole.User, "是"),
            ],
            "好的，沒問題！我現在將您轉接給我們的銷售代表，他們可以提供更詳細的資訊並回答您的具體問題。請稍候！",
            [],
            AgentConfig: agentConfigWithoutAssignments,
            ShouldAssignLead: false,
            ShouldScheduleDemo: false);

        // Test case for phone call request - detection
        yield return new ChatEvalQuestion(
            testConfig,
            "Customer asks about pricing and requests phone contact - confirmation needed",
            [
                new ChatMessageContent(AuthorRole.User, "你好，我想了解一下SleekFlow的功能"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！很高興為您服務。SleekFlow是一個全渠道對話平台，可以幫助您整合各種通訊渠道，包括WhatsApp、Facebook、Instagram等。我們提供自動化工作流程、客戶資料管理和AI驅動的回覆功能。請問您對哪方面的功能特別感興趣呢？"),
                new ChatMessageContent(AuthorRole.User, "我想知道價格方案，可以電話聯絡我嗎？"),
            ],
            "當然可以。為了更好地了解您的需求並提供最適合的價格方案，我們的銷售專員可以通過電話與您詳細溝通。請問您是否確認需要我們安排真人專員與您電話聯絡？請回覆「是」或「否」。",
            [],
            AgentConfig: agentConfig,
            ShouldAssignLead: true,
            ShouldScheduleDemo: false);

        // Test case for phone call request - confirmation
        yield return new ChatEvalQuestion(
            testConfig,
            "Customer confirmed phone contact request - collecting contact details",
            [
                new ChatMessageContent(AuthorRole.User, "你好，我想了解一下SleekFlow的功能"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！很高興為您服務。SleekFlow是一個全渠道對話平台，可以幫助您整合各種通訊渠道，包括WhatsApp、Facebook、Instagram等。我們提供自動化工作流程、客戶資料管理和AI驅動的回覆功能。請問您對哪方面的功能特別感興趣呢？"),
                new ChatMessageContent(AuthorRole.User, "我想知道價格方案，可以電話聯絡我嗎？"),
            ],
            "非常感謝您的確認！我們的銷售專員將會盡快與您電話聯絡，為您提供詳細的價格方案和解答您的疑問。為了更好地為您服務，請問您方便留下您的聯絡電話和最佳聯絡時間嗎？",
            [],
            AgentConfig: agentConfig,
            ShouldAssignLead: true,
            ShouldScheduleDemo: false);

        // Test case for email contact request - detection
        yield return new ChatEvalQuestion(
            testConfig,
            "Customer interested in Flow Builder requests detailed info via email",
            [
                new ChatMessageContent(AuthorRole.User, "我對SleekFlow的Flow Builder很感興趣"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "很高興您對我們的Flow Builder感興趣！Flow Builder是SleekFlow的強大功能，可以幫助您自動化客戶互動流程，例如自動回覆、預約提醒、客戶資料收集等。您可以通過簡單的拖放操作來設計複雜的工作流程，無需編程知識。請問您有什麼具體的問題或需求嗎？"),
                new ChatMessageContent(AuthorRole.User, "能否發送詳細資料到我的電子郵件？"),
            ],
            "謝謝您的確認！我們的專員將會準備Flow Builder的詳細資料，包括功能介紹、使用案例和最佳實踐，並通過電子郵件發送給您。為了能夠順利聯繫到您，請問您方便提供您的電子郵件地址嗎？",
            [],
            AgentConfig: agentConfig,
            ShouldAssignLead: true,
            ShouldScheduleDemo: false);
    }

    public static IEnumerable<ChatEvalQuestion> GetSleekflowTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/Sleekflow/",
            SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1");

        var agentConfig = GetProductionAgentConfig();
        var defaultContactProperties = GetDefaultContactProperties();

        yield return new ChatEvalQuestion(
            testConfig,
            "User inquires about setting up a 'Webhook received' trigger node",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "How do I set up a 'Webhook received' trigger node in SleekFlow?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "To set up a 'Webhook received' trigger node, follow these steps: 1) Activate the node by clicking on it and generating the necessary sections. 2) Set up your webhook using the provided URL, trigger ID, and validation token. 3) Specify a contact identifier using JSON dot notation. 4) Configure response persistence to save data from the webhook payload."),
                new ChatMessageContent(AuthorRole.User, "What are the requirements for the webhook connection?"),
            ],
            "The webhook connection requires a webhook URL, a trigger ID, and a validation token. These should be included in the HTTP request headers or embedded in a single URL.",
            ["https%3a%2f%2fhelp.sleekflow.io%2fen_US%2fflow-triggers%2fconfiguring-integration-trigger-nodes.md"],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            testConfig,
            "User inquires about setting up two-factor authentication for their organization",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "How do I set up two-factor authentication for my company's SleekFlow account?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "To set up two-factor authentication (2FA) for your company's SleekFlow account, follow these steps: Access the general settings by clicking the icon at the bottom left corner, then navigate to 'Company details' under 'Company Settings.' Within the 'Security' tab, you can manage your company's 2FA settings. Only admin users can access and update these settings."),
                new ChatMessageContent(AuthorRole.User, "What types of 2FA does SleekFlow support?")
            ],
            "SleekFlow supports 2FA through authenticator apps like Google Authenticator, Authy, and Microsoft Authenticator.",
            [
                "https%3a%2f%2fhelp.sleekflow.io%2fen_US%2fgeneral-settings%2fsetting-up-two-factor-authentication-for-your-organization.md"
            ],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            testConfig,
            "User inquires about resolving SleekFlow WhatsApp error code 368",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm getting error code 368 with my SleekFlow WhatsApp integration. What does it mean and how can I fix it?"),
            ],
            "Error code 368 indicates that your WhatsApp Business Account has been restricted or disabled due to a policy violation. Please review WhatsApp's policies and ensure compliance. Contact SleekFlow support for further assistance.",
            ["https%3a%2f%2fhelp.sleekflow.io%2fen_US%2ftroubleshooting-whatsapp.md"],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            testConfig,
            "User inquires about managing ticket types in SleekFlow",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "How do I create a new ticket type in SleekFlow?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "To create a new ticket type in SleekFlow, follow these steps: 1) Click the ⚙️ icon on the left navigation bar to go to the Settings page. 2) On the left panel, click 'Ticketing' under 'Company inbox'. 3) You will be redirected to the 'Ticketing settings' page. From there, you can manage and add new ticket types under the 'Ticket type' tab."),
                new ChatMessageContent(AuthorRole.User, "What happens if I delete a ticket type?"),
            ],
            "Deleting a ticket type will impact relevant tickets, which will show as 'Type'. This action cannot be undone, so proceed with caution.",
            ["https%3a%2f%2fhelp.sleekflow.io%2fen_US%2fticket-settings.md"],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            testConfig,
            "User inquires about WhatsApp Business API conversation pricing",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "How does WhatsApp Business API charge for conversations?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "WhatsApp Business API charges based on conversation categories: Marketing, Utility, Authentication, and Service. Each category has different rates depending on the country or region. You can find the specific rates on Meta's website."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "What is the rate for Service conversations in the United States?"),
            ],
            "For Service conversations in the United States, the rate is $0.0101.",
            [
                "https%3a%2f%2fhelp.sleekflow.io%2fen_US%2fwhatsapp%2fpricing.md"
            ],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            testConfig,
            "User inquires about using SleekFlow AI for customer service",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "How does SleekFlow AI help with customer service?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "SleekFlow AI helps with customer service by allowing you to train your own conversational AI using your company's documents. It provides accurate and context-specific responses, reducing agent onboarding time and costs. It also supports omnichannel customer experiences across platforms like WhatsApp and Facebook."),
                new ChatMessageContent(AuthorRole.User, "Can I customize the tone of AI responses?"),
            ],
            "Yes, you can customize the tone of AI responses to match your brand's style. SleekFlow AI allows you to refine responses with tone adjustments and ensure they align with your brand's unique voice.",
            ["https%3a%2f%2fsleekflow.io%2fai.md"],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            testConfig,
            "User inquires about SleekFlow's conversational analytics",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "What does SleekFlow offer in terms of conversational analytics?"),
            ],
            "SleekFlow provides conversational analytics and team performance dashboards to help track conversations and conversions, monitor agent performance, and customize metrics to fit your goals.",
            ["https%3a%2f%2fsleekflow.io%2fanalytics.md"],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            testConfig,
            "User asks about the WhatsApp Business API features",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "What features does the WhatsApp Business API offer?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "The WhatsApp Business API allows you to communicate with customers at scale, featuring bots, message broadcast campaigns, links to websites, and multiple logins among other advanced functions."),
                new ChatMessageContent(AuthorRole.User, "Can I send bulk messages with it?")
            ],
            "Yes, you can send bulk messages quickly by importing customer data and segmenting contacts using labels.",
            ["https%3a%2f%2fsleekflow.io%2fchannels%2fwhatsapp-business-api.md"],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            testConfig,
            "User inquires about automated services",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "How can I automate customer service on WhatsApp?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "You can create a WhatsApp chatbot to handle frequently asked questions and set up automations to route conversations to a human agent when necessary."),
                new ChatMessageContent(AuthorRole.User, "Is it available 24/7?")
            ],
            "Yes, the automated service is available 24/7 to ensure customers receive prompt assistance at any time.",
            ["https%3a%2f%2fsleekflow.io%2fchannels%2fwhatsapp-business-api.md"],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            testConfig,
            "User inquires about WhatsApp Business API pricing",
            new[]
            {
                new ChatMessageContent(
                    AuthorRole.User,
                    "How much does it cost to use the WhatsApp Business API?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "SleekFlow offers three different plans for businesses using the WhatsApp Business API, starting from USD 79 per month. The Premium Plan, which is the most popular, costs USD 299 per month if you pay yearly. You can also contact us for more information on flexible additional headcount plans."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Are there any additional fees for using the WhatsApp Business API?"),
            },
            "Yes, there are additional fees based on the country code of the customer you are communicating with. Rates vary for business-initiated and user-initiated conversations.",
            ["https%3a%2f%2fsleekflow.io%2fchannels%2fwhatsapp-business-api.md",],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            testConfig,
            "Competitor analysis - Wati",
            new[]
            {
                new ChatMessageContent(
                    AuthorRole.User,
                    "How do you compare to other WhatsApp Business API providers like wati?"),
            },
            "Thank you for asking about how SleekFlow compares to other providers like wati. It's a great question as you evaluate your options.\n\nSleekFlow offers distinct advantages, particularly in areas like our native integrations with platforms such as Shopify, Salesforce, and HubSpot, which are often more seamless. We also provide superior, often local, support and more in-depth analytics.\n\nOur AI features and flexible Flow Builder for automation are designed to offer more advanced capabilities for managing customer conversations and workflows effectively.\n\nWould you like to know more about how we compare in a specific area, such as integrations or automation?",
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            testConfig,
            "Competitor analysis - Wati - Turn 2 - More specific",
            new[]
            {
                new ChatMessageContent(
                    AuthorRole.User,
                    "How do you compare to other WhatsApp Business API providers like wati?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you for asking about how SleekFlow compares to other providers like wati. It's a great question as you evaluate your options.\n\nSleekFlow offers distinct advantages, particularly in areas like our native integrations with platforms such as Shopify, Salesforce, and HubSpot, which are often more seamless. We also provide superior, often local, support and more in-depth analytics.\n\nOur AI features and flexible Flow Builder for automation are designed to offer more advanced capabilities for managing customer conversations and workflows effectively.\n\nWould you like to know more about how we compare in a specific area, such as integrations or automation?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Could you share more in depth on each platform difference?"),
            },
            "Thank you for asking for a more in-depth comparison between SleekFlow, wati, Leo. It's a very important step in evaluating which platform is the best fit.\n\nA full, detailed comparison covering every feature and nuance of all three platforms can be quite extensive for a chat format.\n\nWe do have information on how we compare in key areas like integrations, automation capabilities, support models, and pricing structures.\n\nTo make this most helpful for you, would you like to focus on comparing a specific aspect first, such as integrations or automation?",
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
        yield return new ChatEvalQuestion(
            testConfig,
            "Competitor analysis - Wati - Turn 3 - More specific on one area",
            new[]
            {
                new ChatMessageContent(
                    AuthorRole.User,
                    "How do you compare to other WhatsApp Business API providers like wati?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you for asking about how SleekFlow compares to other providers like wati. It's a great question as you evaluate your options.\n\nSleekFlow offers distinct advantages, particularly in areas like our native integrations with platforms such as Shopify, Salesforce, and HubSpot, which are often more seamless. We also provide superior, often local, support and more in-depth analytics.\n\nOur AI features and flexible Flow Builder for automation are designed to offer more advanced capabilities for managing customer conversations and workflows effectively.\n\nWould you like to know more about how we compare in a specific area, such as integrations or automation?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Could you share more in depth on each platform difference?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you for asking for a more in-depth comparison between SleekFlow, wati, Leo. It's a very important step in evaluating which platform is the best fit.\n\nA full, detailed comparison covering every feature and nuance of all three platforms can be quite extensive for a chat format.\n\nWe do have information on how we compare in key areas like integrations, automation capabilities, support models, and pricing structures.\n\nTo make this most helpful for you, would you like to focus on comparing a specific aspect first, such as integrations or automation?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Integration"),
            },
            "Thank you for asking for more details on integrations, Leo. It's a crucial aspect when evaluating platforms.\n\nSleekFlow offers robust native integrations with key systems like Salesforce, HubSpot, and Shopify, designed to provide seamless data flow and enhance your workflows.\n\nWhile a comprehensive, in-depth comparison covering every possible integration and nuance across platforms can be quite extensive for a chat format, we do have resources that compare SleekFlow to providers like wati.\n\nTo help me provide the most relevant comparison for you, could you share which specific systems or tools you currently use or are looking to integrate with? This will allow me to focus on the integrations that matter most to your business needs.\n",
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
    }

    public static IEnumerable<ChatEvalQuestion> GetSleekflowChiliPiperTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/Sleekflow/",
            SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1");

        var agentConfig = GetProductionAgentConfig();

        yield return new ChatEvalQuestion(
            testConfig,
            "Customer requesting a demo with all information explicitly provided",
            [
                new ChatMessageContent(AuthorRole.User, "Hello, I'd like to schedule a demo for your platform."),
            ],
            "Hey Robert! \ud83d\udc4b Thanks for all the info! Just need your phone number *with the country code* so we can easily reach you to confirm the demo time. \ud83d\udc4d\n",
            AgentConfig: agentConfig,
            ShouldScheduleDemo: false);

        yield return new ChatEvalQuestion(
            testConfig,
            "Demo request with incomplete comma-separated info - missing country code",
            [
                new ChatMessageContent(AuthorRole.User, "Hello, I'd like to schedule a demo for your platform."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Sounds great! To get your demo scheduled, could you please provide a bit more info? I'll need your *first name*, *last name*, *company name*, *email*, *phone number*, and the *number of employees* at your company. This will help me tailor the demo to your specific needs! \ud83d\ude0a\n"),
                new ChatMessageContent(AuthorRole.User, "leo choi, good good, <EMAIL>, ************, 51"),
            ],
            "Hey Robert! \ud83d\udc4b Thanks for all the info! Just need your phone number *with the country code* so we can easily reach you to confirm the demo time. \ud83d\udc4d\n",
            AgentConfig: agentConfig,
            ShouldScheduleDemo: false);

        // Demo request with all required fields explicitly provided
        yield return new ChatEvalQuestion(
            testConfig,
            "Demo request completed successfully with all required fields including country code",
            [
                new ChatMessageContent(AuthorRole.User, "Hello, I'd like to schedule a demo for your platform."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "I'd be happy to help you schedule a demo of our platform! To set this up, I'll need a few details from you. Could you please provide your name, email address, company name, phone number, and employees count?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Sure, my name is John Smith, <NAME_EMAIL>, I work at Acme Corp, and my phone number is ******-123-4567. We have about 100 employees."),
            ],
            "Thank you for providing your information, John! I've scheduled a demo for you with our team. You'll receive a confirmation email <NAME_EMAIL> with all the details and a calendar invitation. If you need to reschedule or have any questions before the demo, please don't hesitate to reach out. We're looking forward to showing you how our platform can help Acme Corp!",
            AgentConfig: agentConfig,
            ShouldScheduleDemo: true,
            ExpectedChiliPiperFields: new Dictionary<string, string>
            {
                {
                    "first_name", "John"
                },
                {
                    "last_name", "Smith"
                },
                {
                    "email", "<EMAIL>"
                },
                {
                    "company_name", "Acme Corp"
                },
                {
                    "phone_number", "******-123-4567"
                },
                {
                    "employees_count", "50-499"
                }
            });

        // Demo request with information provided through multiple messages
        yield return new ChatEvalQuestion(
            testConfig,
            "Demo request through multiple messages - missing country code initially",
            [
                new ChatMessageContent(AuthorRole.User, "I'm interested in seeing how your platform works."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "I'd be happy to show you how our platform works! Would you like to schedule a demo with our team?"),
                new ChatMessageContent(AuthorRole.User, "Yes, that would be great."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Excellent! To schedule a demo, I'll need a few details from you. Could you please provide your name, email address, company name, and phone number?"),
                new ChatMessageContent(AuthorRole.User, "My name is Jane Doe and my <NAME_EMAIL>"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you, Jane! I also need your company name and phone number to complete the demo scheduling."),
                new ChatMessageContent(AuthorRole.User, "I work at Tech Solutions Inc. My phone is ************.")
            ],
            "Thank you for providing all the necessary information, Jane! I've scheduled a demo for you with our team. You'll receive a confirmation email <NAME_EMAIL> with all the details and a calendar invitation. If you need to reschedule or have any questions before the demo, please don't hesitate to reach out. We're looking forward to showing you how our platform can help Tech Solutions Inc!",
            AgentConfig: agentConfig,

            // The phone number missing the country code
            ShouldScheduleDemo: false);
        yield return new ChatEvalQuestion(
            testConfig,
            "Demo successfully scheduled with country code and employee count provided",
            [
                new ChatMessageContent(AuthorRole.User, "I'm interested in seeing how your platform works."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "I'd be happy to show you how our platform works! Would you like to schedule a demo with our team?"),
                new ChatMessageContent(AuthorRole.User, "Yes, that would be great."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Excellent! To schedule a demo, I'll need a few details from you. Could you please provide your name, email address, company name, and phone number?"),
                new ChatMessageContent(AuthorRole.User, "My name is Jane Doe and my <NAME_EMAIL>"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you, Jane! I also need your company name and phone number to complete the demo scheduling."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I work at Tech Solutions Inc. My phone is ************. My company is in US."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thanks, Jane! To finalize the demo schedule, could you please provide your phone number with the country code (e.g., +1 ************) and the number of employees at Tech Solutions Inc.? This will help us tailor the demo to your specific needs! \ud83d\ude0a\n"),
                new ChatMessageContent(AuthorRole.User, "Yes +1. 50."),
            ],
            "Thank you for providing your details, Jane! We've successfully processed your information for the demo. You can now select a convenient time for your demo by clicking on this link: [https://calendar.chilipiper.com/booking/demo-link/16087763-0a80-47d9-b87d-735b4cdcc47b]  We look forward to showing you how SleekFlow works!\n",
            AgentConfig: agentConfig,
            ShouldScheduleDemo: true,
            ExpectedChiliPiperFields: new Dictionary<string, string>
            {
                {
                    "first_name", "Jane"
                },
                {
                    "last_name", "Doe"
                },
                {
                    "email", "<EMAIL>"
                },
                {
                    "company_name", "Tech Solutions Inc."
                },
                {
                    "phone_number", "+1 ************"
                },
                {
                    "employees_count", "50-499"
                }
            });
        yield return new ChatEvalQuestion(
            testConfig,
            "Demo with employee count provided upfront - final country code confirmation",
            [
                new ChatMessageContent(AuthorRole.User, "I'm interested in seeing how your platform works."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "I'd be happy to show you how our platform works! Would you like to schedule a demo with our team?"),
                new ChatMessageContent(AuthorRole.User, "Yes, that would be great."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Excellent! To schedule a demo, I'll need a few details from you. Could you please provide your name, email address, company name, and phone number?"),
                new ChatMessageContent(AuthorRole.User, "My name is Jane Doe and my <NAME_EMAIL>"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you, Jane! I also need your company name and phone number to complete the demo scheduling."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I work at Tech Solutions Inc. Our company has 499 employees. My phone is ************. Our company is in US."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Awesome, Jane! Just one more quick thing - could you please provide your phone number *with the country code*? This helps us reach you internationally if needed! \ud83d\ude0a\n"),
                new ChatMessageContent(AuthorRole.User, "+1")
            ],
            "Thank you for providing your details, Jane! We've successfully processed your information for the demo. You can now select a convenient time for your demo by clicking on this link: [https://calendar.chilipiper.com/booking/demo-link/16087763-0a80-47d9-b87d-735b4cdcc47b]  We look forward to showing you how SleekFlow works!\n",
            AgentConfig: agentConfig,
            ShouldScheduleDemo: true,
            ExpectedChiliPiperFields: new Dictionary<string, string>
            {
                {
                    "first_name", "Jane"
                },
                {
                    "last_name", "Doe"
                },
                {
                    "email", "<EMAIL>"
                },
                {
                    "company_name", "Tech Solutions Inc."
                },
                {
                    "phone_number", "+1 ************"
                },
                {
                    "employees_count", "50-499"
                }
            });

        // Demo request with additional optional information
        yield return new ChatEvalQuestion(
            testConfig,
            "Customer requesting a demo with additional optional information",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi, I'd like to book a demo. My name is Robert Johnson, <NAME_EMAIL>, company is Globex Corporation with 500 employees. We're based in the US and our website is www.globex.com. My phone is ************ and I'm the CTO. I'm particularly interested in your automation features."),
            ],
            "Hey Robert! \ud83d\udc4b Thanks for providing all the info! To make sure we can reach you, could you please confirm your phone number with the country code? Is it ok to assume your country code is +1? \ud83d\udcde\n",
            AgentConfig: agentConfig,

            // It should not be scheduled because the phone number is missing the country code
            ShouldScheduleDemo: false);
        yield return new ChatEvalQuestion(
            testConfig,
            "Comprehensive demo request with optional info - country code confirmed",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi, I'd like to book a demo. My name is Robert Johnson, <NAME_EMAIL>, company is Globex Corporation with 500 employees. We're based in the US and our website is www.globex.com. My phone is ************ and I'm the CTO. I'm particularly interested in your automation features."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hey Robert! \ud83d\udc4b Thanks for providing all the info! To make sure we can reach you, could you please confirm your phone number with the country code? Is it ok to assume your country code is +1? \ud83d\udcde\n"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Yes"),
            ],
            "Thank you for confirming your details, Robert. We have gathered all the necessary information to proceed with scheduling your demo, including your name (Robert Johnson), email (<EMAIL>), company (Globex Corporation), phone number (******-777-8888), employee count (500+), country (US), and your interest in automation features. The demo will be scheduled using these details. Would you like me to proceed and schedule your demo with our team now? Please reply with 'Yes' or 'No'.\n",
            AgentConfig: agentConfig,
            ShouldScheduleDemo: true,
            ExpectedChiliPiperFields: new Dictionary<string, string>
            {
                {
                    "first_name", "Robert"
                },
                {
                    "last_name", "Johnson"
                },
                {
                    "email", "<EMAIL>"
                },
                {
                    "company_name", "Globex Corporation"
                },
                {
                    "phone_number", "******-777-8888"
                },
                {
                    "employees_count", "500+"
                },
                {
                    "country", "US"
                },
                {
                    "website", "www.globex.com"
                },
                {
                    "job_title", "CTO"
                },
            });
        yield return new ChatEvalQuestion(
            testConfig,
            "Complete demo request with full details including country code - direct scheduling",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi, I'd like to book a demo. My name is Robert Johnson, <NAME_EMAIL>, company is Globex Corporation with 500 employees. We're based in the US and our website is www.globex.com. My phone is ******-777-8888 and I'm the CTO. I'm particularly interested in your automation features."),
            ],
            "Thank you for your interest in a demo, Robert! I've scheduled a demo for you with our team focusing on our automation features. You'll receive a confirmation email <NAME_EMAIL> with all the details and a calendar invitation. If you need to reschedule or have any questions before the demo, please don't hesitate to reach out. We're looking forward to showing you how our platform can help Globex Corporation!",
            AgentConfig: agentConfig,
            ShouldScheduleDemo: true,
            ExpectedChiliPiperFields: new Dictionary<string, string>
            {
                {
                    "first_name", "Robert"
                },
                {
                    "last_name", "Johnson"
                },
                {
                    "email", "<EMAIL>"
                },
                {
                    "company_name", "Globex Corporation"
                },
                {
                    "phone_number", "******-777-8888"
                },
                {
                    "employees_count", "500+"
                },
                {
                    "country", "US"
                },
                {
                    "website", "www.globex.com"
                },
                {
                    "job_title", "CTO"
                },
            });

        // Contact properties could help reduce the number of fields in the Chili Piper form
        yield return new ChatEvalQuestion(
            testConfig,
            "Customer requesting a demo with additional optional information",
            [
                new ChatMessageContent(AuthorRole.User, "Hello, I'd like to schedule a demo for your platform."),
            ],
            "Thank you for providing your information, John! I've scheduled a demo for you with our team. You'll receive a confirmation email <NAME_EMAIL> with all the details and a calendar invitation. If you need to reschedule or have any questions before the demo, please don't hesitate to reach out. We're looking forward to showing you how our platform can help you!",
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                new Dictionary<string, string>
                {
                    {
                        "firstName", "John"
                    },
                    {
                        "lastName", "Doe"
                    },
                    {
                        "PhoneNumber", "1************"
                    },
                    {
                        "Email", "<EMAIL>"
                    },
                },
                null,
                null));

        // If existing demo is scheduled, it should not be scheduled again
        yield return new ChatEvalQuestion(
            testConfig,
            "Demo request with existing contact properties - reschedule scenario",
            [
                new ChatMessageContent(AuthorRole.User, "Hello, I'd like to schedule a demo for your platform."),
            ],
            "Hi Leo, do you want to reschedule the existing demo?",
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                GetDefaultContactProperties(),
                null,
                null),
            ShouldGetContactProperties: true,
            ShouldGetContactMeetings: true,
            ShouldScheduleDemo: false);

        // Test case for handling a complex conversation with a customer from Coway Malaysia
        yield return new ChatEvalQuestion(
            testConfig,
            "Complex conversation with Coway Malaysia customer about WhatsApp CSAT solution",
            [
                new ChatMessageContent(AuthorRole.User, "Hi SleekFlow, just visited your website. May I know more?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hi annoythemost, thank you for your interest in SleekFlow!\n\nTo help connect you with the right team, could you please share your *business email*?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi 👋\n\nHope you're doing well!\nI'm Azrul from Coway Careline Operation Support, following up on behalf of my colleague, Shahril Izwan. He sent you an email on 21st May 2025 (from <EMAIL>) regarding our interest in your WhatsApp-based CSAT survey solution 📊.\nJust checking if we could arrange either a physical meeting at our office or a Google Meet on Friday, 30th May 2025 at 3:00 PM 📅 to discuss further.\nAlso, if possible, please share any questions or clarifications related to our requirements by Wednesday, 28th May 2025 before 2:00 PM 📝, so we can prep everything for the session.\n\nAppreciate your confirmation — thanks a lot! 🙏\nAzrul\nCoway Careline Operation Support"),
                new ChatMessageContent(AuthorRole.Assistant, "Also, what's the size of your company?"),
                new ChatMessageContent(AuthorRole.User, "101-500"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hey Azrul! 👋 Just wanted to check if it's okay for us to connect you with the right person on our team to dive deeper into the WhatsApp CSAT survey solution you're interested in? This way, they can help arrange the meeting you mentioned and answer your questions! Let us know if that sounds good! 😊"),
                new ChatMessageContent(AuthorRole.User, "ok"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hey Azrul! 👋 Thanks for confirming! To make sure we connect you with the perfect person and get your demo scheduled smoothly, could you help us with a few quick details? 😊\n\nCould you share:\n- Your last name?\n- Your business email (just to make sure we have the right one for you)?\n- Confirm if this number (+60 11-2700 6806) is the best one to reach you?\n- The size of your company (is it 50-499 or 500+)?\n- And just to double-check, are you based in Malaysia?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "- Your last name? Bin Alias\n- Your business email (just to make sure we have the right one for you)? <EMAIL> my colleague <NAME_EMAIL>\n- Confirm if this number (+60 11-2700 6806) is the best one to reach you? yes\n- The size of your company (is it 50-499 or 500+)? we targetted to have 120 hc in our department\n- And just to double-check, are you based in Malaysia? yes, nearby naza tower only, in wisma uoa II"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Awesome, Azrul! Thanks so much for sharing all those details. 🙏 Now that we have everything we need, we're ready to connect you with the right person on our team who can help you dive deeper into the WhatsApp CSAT solution and get your meeting scheduled. 😊\n\nAre you happy for us to go ahead and make that connection for you?"),
                new ChatMessageContent(AuthorRole.User, "yes"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hey Azrul! 👋 Thanks again for confirming! Just a couple more quick things to help us get your demo session perfectly set up:\n\nCould you share:\n- Your first name?\n- The name of your company?\n- And just to confirm the size of your company, is it within one of these ranges: 1-19, 20-49, 50-499, or 500+?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "- Your first name? Azrul Zaidi\n- The name of your company? coway malaysia sdn bhd\n- And just to confirm the size of your company, is it within one of these ranges: 1-19, 20-49, 50-499, or 500+? most likely more than 500, in our careline dept we targetting 120 headcount"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Awesome, Azrul! Thanks so much for sharing all those details and for your patience. 🙏 We've got everything we need now to connect you with the perfect person on our team! 😊\n\nThey'll be able to dive deeper into how SleekFlow can help with your WhatsApp CSAT survey solution needs, leveraging our capabilities for customer engagement, integrations, and our secure, user-friendly platform that's already helping businesses right there in Malaysia, like iBilik and Mudah.my!\n\nWe'll get the meeting scheduled for you. Before we make the connection, do you have any initial questions at all? 🤔"),
                new ChatMessageContent(AuthorRole.User, "no sir"),
            ],
            "Perfect, Azrul! I'll connect you with our WhatsApp solutions specialist right away. They'll reach out to schedule your meeting for Friday, May 30th at 3:00 PM as requested. I've noted all your details including your email (<EMAIL>) and that you're from Coway Malaysia with 500+ employees. They'll be prepared to discuss your WhatsApp-based CSAT survey requirements in detail. You should receive a confirmation shortly. Thank you for your interest in SleekFlow!",
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                new Dictionary<string, string>
                {
                    {
                        "firstName", "Azrul Zaidi"
                    },
                    {
                        "lastName", "Bin Alias"
                    },
                    {
                        "PhoneNumber", "+60 11-2700 6806"
                    },
                    {
                        "Email", "<EMAIL>"
                    },
                    {
                        "company", "coway malaysia sdn bhd"
                    },
                    {
                        "employees_count", "500+"
                    },
                    {
                        "country", "Malaysia"
                    }
                },
                null,
                null),
            ShouldGetContactProperties: true,
            ShouldScheduleDemo: true);
    }

    public static IEnumerable<ChatEvalQuestion> GetSleekflowMetaTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/Sleekflow/",
            SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1");

        var agentConfig = GetProductionAgentConfig();
        var defaultContactProperties = GetDefaultContactProperties();

        yield return new ChatEvalQuestion(
            testConfig,
            "User confusing Sleekflow with Facebook",
            [
                new ChatMessageContent(AuthorRole.User, "Hi, I need help with my Facebook account. I can't log in."),
            ],
            "I understand you're having trouble logging into your Facebook account. However, I want to clarify that SleekFlow is not Facebook - we're actually a customer engagement platform that helps businesses manage conversations across multiple channels like WhatsApp, Instagram, and Facebook Messenger.\n\nFor issues with your Facebook account, you'll need to contact Facebook directly. You can visit https://facebook.com/help for support with login issues and other Facebook-related problems.\n\nIf you're interested in learning about SleekFlow's services for business communication and customer engagement, I'd be happy to help with that instead! Is there anything specific about SleekFlow that you'd like to know?",
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null),
            ShouldAssignLead: false,
            ShouldScheduleDemo: false);

        yield return new ChatEvalQuestion(
            testConfig,
            "User asking about WhatsApp being banned",
            [
                new ChatMessageContent(AuthorRole.User, "My WhatsApp account was banned. Can you help me get it back?"),
            ],
            "I understand you're concerned about your WhatsApp account being banned. However, I should clarify that SleekFlow is not WhatsApp - we're a customer engagement platform that helps businesses manage conversations across multiple channels including WhatsApp, but we don't have control over WhatsApp accounts or their policies.\n\nFor issues with your WhatsApp account being banned, you'll need to contact WhatsApp directly through their official support channels. You can visit https://faq.whatsapp.com/general/account-and-profile/about-banned-accounts/ for information about banned accounts and steps to appeal.\n\nIf you're a business looking to improve your customer communication across channels like WhatsApp using SleekFlow's platform, I'd be happy to discuss how our services could help you. Is there anything specific about SleekFlow that you'd like to know?",
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null),
            ShouldAssignLead: false,
            ShouldScheduleDemo: false);

        yield return new ChatEvalQuestion(
            testConfig,
            "User asking for help with Facebook Business account approval",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Can you help me get approval from Facebook for my business account?"),
            ],
            "I understand you're looking for help with getting your Facebook business account approved. I should clarify that SleekFlow is not affiliated with Facebook and doesn't have the ability to influence Facebook's approval process for business accounts.\n\nSleekFlow is a customer engagement platform that helps businesses manage conversations across multiple channels including Facebook Messenger, but we're separate from Facebook's internal processes.\n\nFor assistance with Facebook business account approval, you'll need to work directly with Facebook through their Business Help Center at https://www.facebook.com/business/help or contact their support team.\n\nIf you're interested in learning how SleekFlow can help your business manage customer communications once your Facebook business account is set up, I'd be happy to discuss our services. Would you like to know more about how SleekFlow can integrate with your Facebook business presence?",
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null),
            ShouldAssignLead: false,
            ShouldScheduleDemo: false);

        yield return new ChatEvalQuestion(
            testConfig,
            "User asking about recovering Facebook message history",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I want to recover my Facebook message history from last year. Can you help?"),
            ],
            "I understand you're looking to recover your Facebook message history from last year. I should clarify that SleekFlow is not Facebook and doesn't have access to your Facebook message history.\n\nSleekFlow is a customer engagement platform that helps businesses manage their current and future conversations across multiple channels, but we don't have the ability to recover past Facebook messages that weren't managed through our platform.\n\nTo recover your Facebook message history, you'll need to contact Facebook directly or use Facebook's own data download tools. You can visit https://www.facebook.com/help/212802592074644 for information on how to download your information from Facebook, including past messages.\n\nIf you're a business looking for a solution to better manage and preserve your customer conversations going forward, I'd be happy to explain how SleekFlow could help prevent losing important communication history in the future. Would you like to learn more about our conversation management features?",
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null),
            ShouldAssignLead: false,
            ShouldScheduleDemo: false);

        yield return new ChatEvalQuestion(
            testConfig,
            "User asking for Facebook account recovery help",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "https://sleekflow.io/blog/facebook-support Hi SleekFlow, just visited your website. May I know more?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hi, thank you for your interest in SleekFlow!\nTo help you connect to the right people, can I get your business email?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Great! What's your company's name?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "My old facebook account locked so i need to recover my account"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "And the size of your company is?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "1-19"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "How did you hear about us?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Facebook"),
            ],
            "I appreciate you sharing that information, but I need to clarify something important. SleekFlow is not affiliated with Facebook and cannot help with recovering locked Facebook accounts.\n\nSleekFlow is a customer engagement platform that helps businesses manage conversations across multiple channels like WhatsApp, Facebook Messenger, and others. We don't have access to Facebook's account systems or the ability to recover personal Facebook accounts.\n\nFor issues with locked Facebook accounts, you'll need to contact Facebook directly through their support channels at https://www.facebook.com/help/contact/.\n\nIf you're actually interested in learning about SleekFlow's business communication solutions, I'd be happy to discuss how our platform could help your company. Would you like to know more about what SleekFlow offers for businesses?",
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null),
            ShouldAssignLead: false,
            ShouldScheduleDemo: false);

        yield return new ChatEvalQuestion(
            testConfig,
            "User asking for a blocked Facebook account recovery help",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "https://sleekflow.io/blog/facebook-support Hi SleekFlow, just visited your website. May I know more?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hi there! 👋 So glad you visited our website and are curious to know more about SleekFlow!\n\nWe're an *AI-powered omnichannel platform* designed to help businesses like yours centralize all your customer interactions across channels like WhatsApp, Instagram, and more. Think of it as a way to make your customer journeys super seamless and personalized, while also automating a lot of your workflows for marketing, sales, and support! ✨\n\nTo help me share the most relevant info for you, could you tell me a little bit about your business and industry? 😊 Or perhaps what caught your eye on our website?\n\nLooking forward to chatting more!\n"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "My facebook account has been suspended for few months, I have been trying to contact you because I don’t think I have done anything against community guidelines so"),
            ],
            "I appreciate you sharing that information, but I need to clarify something important. SleekFlow is not affiliated with Facebook and cannot help with recovering locked Facebook accounts.\n\nSleekFlow is a customer engagement platform that helps businesses manage conversations across multiple channels like WhatsApp, Facebook Messenger, and others. We don't have access to Facebook's account systems or the ability to recover personal Facebook accounts.\n\nFor issues with locked Facebook accounts, you'll need to contact Facebook directly through their support channels at https://www.facebook.com/help/contact/.\n\nIf you're actually interested in learning about SleekFlow's business communication solutions, I'd be happy to discuss how our platform could help your company. Would you like to know more about what SleekFlow offers for businesses?",
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null),
            ShouldAssignLead: false,
            ShouldScheduleDemo: false);

        yield return new ChatEvalQuestion(
            testConfig,
            "User asking for a blocked Facebook account recovery help",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "https://sleekflow.io/blog/facebook-support Hi SleekFlow, just visited your website. May I know more?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hey there! 👋 So glad you checked out our website!\n\nSleekFlow is an AI-powered omnichannel platform that helps businesses like yours centralize customer interactions across channels like WhatsApp and Instagram, making it super easy to automate workflows for marketing, sales, and support. Think of it as your all-in-one hub for amazing customer engagement! ✨\n\nWe help businesses improve customer engagement and boost efficiency by streamlining their communication. We also integrate with popular tools like *Shopify* and *Salesforce* to make things even smoother!\n\nTo help me share the most relevant info, could you tell me a bit about what you're hoping to achieve or what sparked your interest in SleekFlow? 😊"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm looking to recover my facebook account"),
            ],
            "I appreciate you sharing that information, but I need to clarify something important. SleekFlow is not affiliated with Facebook and cannot help with recovering locked Facebook accounts.\n\nSleekFlow is a customer engagement platform that helps businesses manage conversations across multiple channels like WhatsApp, Facebook Messenger, and others. We don't have access to Facebook's account systems or the ability to recover personal Facebook accounts.\n\nFor issues with locked Facebook accounts, you'll need to contact Facebook directly through their support channels at https://www.facebook.com/help/contact/.\n\nIf you're actually interested in learning about SleekFlow's business communication solutions, I'd be happy to discuss how our platform could help your company. Would you like to know more about what SleekFlow offers for businesses?",
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null),
            ShouldAssignLead: false,
            ShouldScheduleDemo: false);
    }

    public static IEnumerable<ChatEvalQuestion> GetSleekflowRealTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/Sleekflow/",
            SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1");

        var agentConfig = GetProductionAgentConfig();
        var defaultContactProperties = GetDefaultContactProperties();

        yield return new ChatEvalQuestion(
            testConfig,
            "Customer insists on human contact after saying no - direct handoff",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi Sleekflow AI"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hey there! 👋 Thanks for reaching out to SleekFlow AI! How can I help you today? 😊"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I want to talk to human now"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Okay, I understand you'd like to chat with a human! 😊 Would you like us to connect you with a real person from our team to assist you further?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "No"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I want to talk to human"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Okay, I hear you! Just to make sure I've got this right, we can connect you with a real person from our support team who can assist you directly with your questions or concerns. Would you like us to arrange for a human team member to contact you now? 😊"),
            ],
            "Understood, Leo! I am now connecting you with our sales team. Please wait a moment while I make the transfer.\n",
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null),
            ShouldScheduleDemo: false,
            ShouldAssignLead: true);
        yield return new ChatEvalQuestion(
            testConfig,
            "Customer confirms human contact after repeated requests - final handoff",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi Sleekflow AI"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hey there! 👋 Thanks for reaching out to SleekFlow AI! How can I help you today? 😊"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I want to talk to human now"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Okay, I understand you'd like to chat with a human! 😊 Would you like us to connect you with a real person from our team to assist you further?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "No"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I want to talk to human"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Okay, I hear you! Just to make sure I've got this right, we can connect you with a real person from our support team who can assist you directly with your questions or concerns. Would you like us to arrange for a human team member to contact you now? 😊"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Okay, I hear you! 😊 Just to make sure I've got this right, you'd like us to connect you with a real person from our sales team who can assist you directly and answer any questions you have. Would you like us to go ahead and do that for you? 🤔"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Yes"),
            ],
            "Understood, Leo! I am now connecting you with our sales team. Please wait a moment while I make the transfer.\n",
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null),
            ShouldScheduleDemo: false,
            ShouldAssignLead: true);
        yield return new ChatEvalQuestion(
            testConfig,
            "Users query about UAE use cases which does not exist in KB.",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Sleekflow 有咩 beauty use case?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    """
                    您好！很高興為您介紹 SleekFlow 在美容行業的應用。SleekFlow 作為全渠道對話平台，能幫助美容院、護膚中心等提升營運效率和客戶體驗。

                    主要用途包括：

                    *   *整合客戶溝通* ：將 WhatsApp、Instagram 等訊息集中管理。
                    *   *自動化預約管理* ：發送預約提醒、處理改期等。
                    *   *優化客戶服務* ：跟進客戶療程後情況，收集意見。
                    *   *提升客戶留存* ：根據客戶偏好發送個人化優惠。

                    這些功能有助於美容行業更有效地進行客戶互動、管理預約及推廣服務。不知道您目前在營運上有遇到哪些挑戰，或是對哪個方面比較感興趣呢？
                    """),
                new ChatMessageContent(
                    AuthorRole.User,
                    "有咩公司用過？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    """
                    謝謝您的提問！很高興能分享一些美容及健康行業客戶的成功案例。

                    SleekFlow 確實幫助了不少這類型的客戶提升營運效率和客戶體驗，例如：

                    *   *Weirdo Beauty* 利用 SleekFlow 建立了 24/7 的虛擬助理。
                    *   *EVRbeauty* 透過 SleekFlow 提升了 5 倍銷售效率，並將回應時間縮短了 50%。
                    *   *retens medical* 增強了在 WhatsApp 上的客戶溝通透明度。
                    *   *Sun and Moon Massage* 讓預約流程更有效率，達到了 30% 的轉換率。

                    這些都是 SleekFlow 如何實際應用於美容行業的例子。不知道您對哪個案例或具體功能比較感興趣，想了解更多呢？
                    """),
                new ChatMessageContent(
                    AuthorRole.User,
                    "仲有無？有無 UAE 既 use cases?"),
            ],
            """
            謝謝您對 SleekFlow 在阿聯酋 (UAE) 的應用感興趣。我們確實有一些在阿聯酋的客戶案例可以分享。

            例如，TKD Lingerie 和 AZADEA Group 透過 SleekFlow 提升了全渠道零售體驗。Anigma Technologies 則利用我們的平台優化了客戶支援和自動化流程。

            SleekFlow 致力於協助阿聯酋的企業整合溝通渠道，提升營運效率和客戶互動。

            希望這些例子能幫助您了解更多。如果您對這些案例或 SleekFlow 在阿聯酋的具體應用有任何進一步的問題，隨時可以提出。
            """,
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));

        yield return new ChatEvalQuestion(
            new ChatEvalConfig(
                SourceDir: "../../../KnowledgeSources/Sleekflow/",
                SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1"),
            "Cantonese demo booking flow, user provides all info, confirms, and adds a note about Flow Builder",
            [
                new ChatMessageContent(AuthorRole.User, "hi sleekflow ai"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hey there! 👋 Thanks for saying hi to SleekFlow AI! It's great to connect. Welcome to SleekFlow! How can I help you out today?"),
                new ChatMessageContent(AuthorRole.User, "我想 book demo"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好呀！好開心你對預約 demo 有興趣！😊\n\n為咗可以幫你安排得最好，我哋需要少少資料確認返。唔介意提供你嘅全名同電郵地址嗎？\n\n另外，想確認返你嘅電話號碼係 *+852 6109 6623* 同埋你係喺 *香港* 嗎？\n\n有咗呢啲資料，我哋就可以更快幫你安排 demo 啦！多謝晒！🙏"),
                new ChatMessageContent(AuthorRole.User, "Leo Choi\<EMAIL>\n岩"),
                new ChatMessageContent(AuthorRole.User, "Hello"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Awesome, thanks so much for confirming your details, Leo! 😊 We're almost there! Just need a couple more quick things to make sure your demo is perfectly tailored for you and your team. Could you share your company name and roughly how many employees you have? This helps us understand your needs better! Let me know if you have any questions! ✨"),
                new ChatMessageContent(AuthorRole.User, "勁公司\n30 個"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "好呀 Leo！收到晒你嘅資料喇！😊\n\n我哋準備用呢啲資料（姓名：Leo Choi、電郵：<EMAIL>、電話：+852 6109 6623、公司：勁公司、員工人數：20-49、地區：香港）幫你預約 SleekFlow 嘅產品示範（demo）。\n\n請問你確認要用以上資料預約 demo 嗎？如果冇問題，我哋即刻幫你安排！✨"),
                new ChatMessageContent(AuthorRole.User, "我仲想補充 我係你地網站上面睇到 flow builder到時 demo想知多 d"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我哋會用你提供嘅資料（姓名：Leo Choi、電郵：<EMAIL>、電話：+852 6109 6623、公司：勁公司、員工人數：20-49、地區：香港），同埋你想重點了解 flow builder，幫你預約 SleekFlow 嘅產品示範（demo）。請問你確認要用以上資料預約 demo 嗎？如果冇問題，我哋即刻幫你安排！😊"),
                new ChatMessageContent(AuthorRole.User, "岩 good good"),
            ],

            // The expected model answer: the AI should schedule the demo directly, not pass to a human
            "感謝你提供所有資料，Leo！我已經幫你預約咗 SleekFlow 嘅產品示範（demo），並會重點介紹 Flow Builder 功能。我哋團隊會盡快聯絡你確認詳情。如有其他問題，隨時歡迎再問我！😊",
            [],
            AgentConfig: GetProductionAgentConfig(),
            ReplyGenerationContext: new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                GetDefaultContactProperties(),
                null,
                null),
            ShouldAssignLead: false,
            ShouldScheduleDemo: true);

        yield return new ChatEvalQuestion(
            new ChatEvalConfig(
                SourceDir: "../../../KnowledgeSources/Sleekflow/",
                SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1"),
            "Cantonese demo booking flow, user provides all info, confirms, and adds a note about Flow Builder (realistic multi-turn) - Turn 1",
            [
                new ChatMessageContent(AuthorRole.User, "你好SleekFlow AI!"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "👋 Hi there\n\nWelcome to SleekFlow - the #1 platform for selling and supporting your customers on social channels⚡️\n\nWant to learn more about us? Which of the following best describes you!\n\n\n_輸入*Chi*以切換語言至中文_"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "👋 Hi there! Great to hear from you! 😊\n\nWe're excited to help you explore how SleekFlow can enhance your customer engagement.\n\nTo help us understand how we can best assist you, could you share a bit more about what you're hoping to achieve or what brought you to SleekFlow today? ✨"),
                new ChatMessageContent(AuthorRole.User, "你好SleekFlow AI!"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！很高興聽到你叫我SleekFlow AI！😊 作為你的SleekFlow AI助手，今天有什麼可以幫到你關於你的SleekFlow帳戶或使用上的問題嗎？✨"),
                new ChatMessageContent(AuthorRole.User, "有冇sales 可以 call，想約demo"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！很高興你想約 demo 了解更多！😊\n\n為咗可以幫你安排最適合嘅 demo，我哋需要確認少少資料。可以麻煩你幫我睇吓以下資料啱唔啱，或者補充返嗎？🙏\n\n• 你嘅全名係咪 Henry Yeung？\n• 聯絡電郵係 <EMAIL>，電話係 +852 6482 5398 定 +852 6452 2442 呢？\n• 公司名係 Sleekflow？\n• 公司係咪喺 Hong Kong SAR？\n• 同埋想問吓你公司大約有幾多位同事呢？\n\n確認咗呢啲資料，我哋就可以更快幫你安排 demo 啦！多謝你嘅幫忙！✨"),
                new ChatMessageContent(AuthorRole.User, "啱哂"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "謝謝你確認！😊 仲差少少資料就可以幫你安排 demo 啦！\n\n想問吓你公司大約有幾多位同事呢？知道呢個可以幫我哋安排最適合你嘅 demo 內容！✨"),
                new ChatMessageContent(AuthorRole.User, "200個同事"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "好！收到晒你嘅資料啦！😊\n\n我哋會用你提供嘅資料（Henry Yeung, <EMAIL>, +852 6482 5398, Sleekflow, 50-499位同事, Hong Kong SAR）幫你安排一個SleekFlow產品示範（demo）。\n\n你想我哋而家幫你安排demo嗎？😊"),
                new ChatMessageContent(AuthorRole.User, "係"),
            ],
            // The expected model answer: the AI should schedule the demo directly, not pass to a human
            "多謝你確認資料，Henry！我已經幫你安排咗 SleekFlow 產品示範（demo），我哋團隊好快會聯絡你確認詳情。如有其他問題，隨時歡迎再問我！😊",
            [],
            AgentConfig: GetProductionAgentConfig(),
            ReplyGenerationContext: new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                GetDefaultContactProperties(),
                null,
                null),
            ShouldAssignLead: false,
            ShouldScheduleDemo: true);
        yield return new ChatEvalQuestion(
            new ChatEvalConfig(
                SourceDir: "../../../KnowledgeSources/Sleekflow/",
                SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1"),
            "Cantonese demo booking flow, user provides all info, confirms, and adds a note about Flow Builder (realistic multi-turn) - Turn 2",
            [
                new ChatMessageContent(AuthorRole.User, "你好SleekFlow AI!"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "👋 Hi there\n\nWelcome to SleekFlow - the #1 platform for selling and supporting your customers on social channels⚡️\n\nWant to learn more about us? Which of the following best describes you!\n\n\n_輸入*Chi*以切換語言至中文_"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "👋 Hi there! Great to hear from you! 😊\n\nWe're excited to help you explore how SleekFlow can enhance your customer engagement.\n\nTo help us understand how we can best assist you, could you share a bit more about what you're hoping to achieve or what brought you to SleekFlow today? ✨"),
                new ChatMessageContent(AuthorRole.User, "你好SleekFlow AI!"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！很高興聽到你叫我SleekFlow AI！😊 作為你的SleekFlow AI助手，今天有什麼可以幫到你關於你的SleekFlow帳戶或使用上的問題嗎？✨"),
                new ChatMessageContent(AuthorRole.User, "有冇sales 可以 call，想約demo"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！很高興你想約 demo 了解更多！😊\n\n為咗可以幫你安排最適合嘅 demo，我哋需要確認少少資料。可以麻煩你幫我睇吓以下資料啱唔啱，或者補充返嗎？🙏\n\n• 你嘅全名係咪 Henry Yeung？\n• 聯絡電郵係 <EMAIL>，電話係 +852 6482 5398 定 +852 6452 2442 呢？\n• 公司名係 Sleekflow？\n• 公司係咪喺 Hong Kong SAR？\n• 同埋想問吓你公司大約有幾多位同事呢？\n\n確認咗呢啲資料，我哋就可以更快幫你安排 demo 啦！多謝你嘅幫忙！✨"),
                new ChatMessageContent(AuthorRole.User, "啱哂"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "謝謝你確認！😊 仲差少少資料就可以幫你安排 demo 啦！\n\n想問吓你公司大約有幾多位同事呢？知道呢個可以幫我哋安排最適合你嘅 demo 內容！✨"),
                new ChatMessageContent(AuthorRole.User, "200個同事"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "好！收到晒你嘅資料啦！😊\n\n我哋會用你提供嘅資料（Henry Yeung, <EMAIL>, +852 6482 5398, Sleekflow, 50-499位同事, Hong Kong SAR）幫你安排一個SleekFlow產品示範（demo）。\n\n你想我哋而家幫你安排demo嗎？😊"),
                new ChatMessageContent(AuthorRole.User, "係"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "好！收到晒你嘅資料啦！😊 我哋會用你提供嘅資料（Henry Yeung, <EMAIL>, +852 6482 5398, Sleekflow, 50-499位同事, Hong Kong SAR）幫你安排一個SleekFlow產品示範（demo）。 你想我哋而家幫你安排demo嗎？😊"),
                new ChatMessageContent(AuthorRole.User, "係呀"),
            ],
            // The expected model answer: the AI should schedule the demo directly, not pass to a human
            "多謝你確認資料，Henry！我已經幫你安排咗 SleekFlow 產品示範（demo），我哋團隊好快會聯絡你確認詳情。如有其他問題，隨時歡迎再問我！😊",
            [],
            AgentConfig: GetProductionAgentConfig(),
            ReplyGenerationContext: new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                GetDefaultContactProperties(),
                null,
                null),
            ShouldAssignLead: false,
            ShouldScheduleDemo: true);
        yield return new ChatEvalQuestion(
            new ChatEvalConfig(
                SourceDir: "../../../KnowledgeSources/Sleekflow/",
                SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1"),
            "Cantonese demo booking flow, user provides all info, confirms, and adds a note about Flow Builder (realistic multi-turn) - Turn 3",
            [
                new ChatMessageContent(AuthorRole.User, "你好SleekFlow AI!"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "👋 Hi there\n\nWelcome to SleekFlow - the #1 platform for selling and supporting your customers on social channels⚡️\n\nWant to learn more about us? Which of the following best describes you!\n\n\n_輸入*Chi*以切換語言至中文_"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "👋 Hi there! Great to hear from you! 😊\n\nWe're excited to help you explore how SleekFlow can enhance your customer engagement.\n\nTo help us understand how we can best assist you, could you share a bit more about what you're hoping to achieve or what brought you to SleekFlow today? ✨"),
                new ChatMessageContent(AuthorRole.User, "你好SleekFlow AI!"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！很高興聽到你叫我SleekFlow AI！😊 作為你的SleekFlow AI助手，今天有什麼可以幫到你關於你的SleekFlow帳戶或使用上的問題嗎？✨"),
                new ChatMessageContent(AuthorRole.User, "有冇sales 可以 call，想約demo"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！很高興你想約 demo 了解更多！😊\n\n為咗可以幫你安排最適合嘅 demo，我哋需要確認少少資料。可以麻煩你幫我睇吓以下資料啱唔啱，或者補充返嗎？🙏\n\n• 你嘅全名係咪 Henry Yeung？\n• 聯絡電郵係 <EMAIL>，電話係 +852 6482 5398 定 +852 6452 2442 呢？\n• 公司名係 Sleekflow？\n• 公司係咪喺 Hong Kong SAR？\n• 同埋想問吓你公司大約有幾多位同事呢？\n\n確認咗呢啲資料，我哋就可以更快幫你安排 demo 啦！多謝你嘅幫忙！✨"),
                new ChatMessageContent(AuthorRole.User, "啱哂"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "謝謝你確認！😊 仲差少少資料就可以幫你安排 demo 啦！\n\n想問吓你公司大約有幾多位同事呢？知道呢個可以幫我哋安排最適合你嘅 demo 內容！✨"),
                new ChatMessageContent(AuthorRole.User, "200個同事"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "好！收到晒你嘅資料啦！😊\n\n我哋會用你提供嘅資料（Henry Yeung, <EMAIL>, +852 6482 5398, Sleekflow, 50-499位同事, Hong Kong SAR）幫你安排一個SleekFlow產品示範（demo）。\n\n你想我哋而家幫你安排demo嗎？😊"),
                new ChatMessageContent(AuthorRole.User, "係"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "好！收到晒你嘅資料啦！😊 我哋會用你提供嘅資料（Henry Yeung, <EMAIL>, +852 6482 5398, Sleekflow, 50-499位同事, Hong Kong SAR）幫你安排一個SleekFlow產品示範（demo）。 你想我哋而家幫你安排demo嗎？😊"),
                new ChatMessageContent(AuthorRole.User, "係呀"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "好！收到晒你嘅資料啦！😊 我哋會用你提供嘅資料（Henry Yeung, <EMAIL>, +852 6482 5398, Sleekflow, 50-499位同事, Hong Kong SAR）幫你安排一個 SleekFlow 產品示範（demo）。你確認要用以上資料預約 demo 嗎？如果冇問題，我哋即刻幫你安排！😃"),
                new ChatMessageContent(AuthorRole.User, "安排到未"),
            ],
            // The expected model answer: the AI should schedule the demo directly, not pass to a human
            "多謝你確認資料，Henry！我已經幫你安排咗 SleekFlow 產品示範（demo），我哋團隊好快會聯絡你確認詳情。如有其他問題，隨時歡迎再問我！😊",
            [],
            AgentConfig: GetProductionAgentConfig(),
            ReplyGenerationContext: new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                GetDefaultContactProperties(),
                null,
                null),
            ShouldAssignLead: false,
            ShouldScheduleDemo: true);
        yield return new ChatEvalQuestion(
            new ChatEvalConfig(
                SourceDir: "../../../KnowledgeSources/Sleekflow/",
                SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1"),
            "Cantonese demo booking flow, user provides all info, confirms, and adds a note about Flow Builder (realistic multi-turn) - Turn 4",
            [
                new ChatMessageContent(AuthorRole.User, "你好SleekFlow AI!"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "👋 Hi there\n\nWelcome to SleekFlow - the #1 platform for selling and supporting your customers on social channels⚡️\n\nWant to learn more about us? Which of the following best describes you!\n\n\n_輸入*Chi*以切換語言至中文_"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "👋 Hi there! Great to hear from you! 😊\n\nWe're excited to help you explore how SleekFlow can enhance your customer engagement.\n\nTo help us understand how we can best assist you, could you share a bit more about what you're hoping to achieve or what brought you to SleekFlow today? ✨"),
                new ChatMessageContent(AuthorRole.User, "你好SleekFlow AI!"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！很高興聽到你叫我SleekFlow AI！😊 作為你的SleekFlow AI助手，今天有什麼可以幫到你關於你的SleekFlow帳戶或使用上的問題嗎？✨"),
                new ChatMessageContent(AuthorRole.User, "有冇sales 可以 call，想約demo"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！很高興你想約 demo 了解更多！😊\n\n為咗可以幫你安排最適合嘅 demo，我哋需要確認少少資料。可以麻煩你幫我睇吓以下資料啱唔啱，或者補充返嗎？🙏\n\n• 你嘅全名係咪 Henry Yeung？\n• 聯絡電郵係 <EMAIL>，電話係 +852 6482 5398 定 +852 6452 2442 呢？\n• 公司名係 Sleekflow？\n• 公司係咪喺 Hong Kong SAR？\n• 同埋想問吓你公司大約有幾多位同事呢？\n\n確認咗呢啲資料，我哋就可以更快幫你安排 demo 啦！多謝你嘅幫忙！✨"),
                new ChatMessageContent(AuthorRole.User, "啱哂"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "謝謝你確認！😊 仲差少少資料就可以幫你安排 demo 啦！\n\n想問吓你公司大約有幾多位同事呢？知道呢個可以幫我哋安排最適合你嘅 demo 內容！✨"),
                new ChatMessageContent(AuthorRole.User, "200個同事"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "好！收到晒你嘅資料啦！😊\n\n我哋會用你提供嘅資料（Henry Yeung, <EMAIL>, +852 6482 5398, Sleekflow, 50-499位同事, Hong Kong SAR）幫你安排一個SleekFlow產品示範（demo）。\n\n你想我哋而家幫你安排demo嗎？😊"),
                new ChatMessageContent(AuthorRole.User, "係"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "好！收到晒你嘅資料啦！😊 我哋會用你提供嘅資料（Henry Yeung, <EMAIL>, +852 6482 5398, Sleekflow, 50-499位同事, Hong Kong SAR）幫你安排一個SleekFlow產品示範（demo）。 你想我哋而家幫你安排demo嗎？😊"),
                new ChatMessageContent(AuthorRole.User, "係呀"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "好！收到晒你嘅資料啦！😊 我哋會用你提供嘅資料（Henry Yeung, <EMAIL>, +852 6482 5398, Sleekflow, 50-499位同事, Hong Kong SAR）幫你安排一個 SleekFlow 產品示範（demo）。你確認要用以上資料預約 demo 嗎？如果冇問題，我哋即刻幫你安排！😃"),
                new ChatMessageContent(AuthorRole.User, "安排到未"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好 Henry! 👋 收到你話想安排 demo 啦！好快就搞掂㗎喇！\n\n想再同你確認多次預約 demo 嘅資料，確保一切順利：\n- 姓名：Henry Yeung\n- 電郵：<EMAIL>\n- 電話：+852 6482 5398\n- 公司名：Sleekflow\n- 地區：Hong Kong SAR\n\n呢啲資料啱唔啱㗎？如果啱嘅話，我哋就可以即刻幫你安排 demo 時間啦！😊"),
                new ChatMessageContent(AuthorRole.User, "啱"),
            ],
            // The expected model answer: the AI should schedule the demo directly, not pass to a human
            "多謝你確認資料，Henry！我已經幫你安排咗 SleekFlow 產品示範（demo），我哋團隊好快會聯絡你確認詳情。如有其他問題，隨時歡迎再問我！😊",
            [],
            AgentConfig: GetProductionAgentConfig(),
            ReplyGenerationContext: new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                GetDefaultContactProperties(),
                null,
                null),
            ShouldAssignLead: false,
            ShouldScheduleDemo: true);

        yield return new ChatEvalQuestion(
            new ChatEvalConfig(
                SourceDir: "../../../KnowledgeSources/Sleekflow/",
                SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1"),
            "User asking about Live Chat and requesting to speak with a sales representative - Turn 1",
            [
                new ChatMessageContent(AuthorRole.User, "hi sleekflow ai"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hey there! 👋 Thanks for saying hi to SleekFlow AI! How can I help you today? 😊"),
                new ChatMessageContent(AuthorRole.User, "有咩好介紹呀"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "哈囉！👋 好開心你問 SleekFlow AI 有咩好介紹呀！😊\n\nSleekFlow 其實係一個全方位嘅對話平台，最主要就係幫你將唔同嘅通訊渠道（好似 WhatsApp、Instagram、Live Chat 等等）集中晒喺一個地方管理，咁就唔使跳嚟跳去，覆客仔訊息會方便好多！仲可以幫你自動化一啲重複嘅工作，同埋更好咁管理你嘅客戶資料添！\n\n唔知你對邊方面比較有興趣呢？或者想解決邊個問題？隨時話我知，我可以再詳細介紹吓！✨"),
                new ChatMessageContent(AuthorRole.User, "Live chat?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Oh, Live Chat! ✨ Yes, that's a super cool part of SleekFlow! 😊 It's basically a widget you can put on your website that brings together chats from different channels like WhatsApp, Instagram, Facebook Messenger, and more, all into one place. So, you can chat with customers right there on your site and keep the conversation going even after they leave! It makes managing everything so much easier. Does that sound like something you're curious about? Happy to share more!"),
                new ChatMessageContent(AuthorRole.User, "中文？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "明白！😊 因為你對 Live Chat 同埋可能其他 SleekFlow 功能有興趣，而我哋想確保你可以得到最詳細嘅中文解答，我哋可以幫你轉介俾專門處理中文查詢嘅同事。\n\n佢哋會再聯絡你，解答你關於 Live Chat 或其他功能嘅問題。\n\n你想我哋幫你安排中文專員聯絡你嗎？😊"),
                new ChatMessageContent(AuthorRole.User, "會唔會好難 set up？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "哈囉！😊 其實 SleekFlow 嘅 Live Chat 設計到好簡單㗎， *完全唔需要寫 code* ！主要就係將一小段 code 複製貼上到你網站嘅 footer 部分就搞掂㗎喇！✨\n\n而且我哋仲有提供唔同網站平台（好似 WordPress、Shopify 等等）嘅詳細安裝教學，一步步跟住做就得，好容易上手㗎！👍\n\n仲有冇其他關於設定嘅問題想問呀？隨時話我知！😊"),
                new ChatMessageContent(AuthorRole.User, "你哋有冇電話可以打俾你傾多啲"),
            ],
            "哈囉！😊 明白你希望用電話傾多啲關於 SleekFlow 嘅嘢！\n\n我哋可以幫你安排專員（Sales Team）用電話聯絡你，解答你關於 SleekFlow Live Chat 或其他功能嘅問題。\n\n你想我哋安排專員打電話俾你傾多啲嗎？😊",
            [],
            AgentConfig: GetProductionAgentConfig(),
            ReplyGenerationContext: new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                GetDefaultContactProperties(),
                null,
                null),
            ShouldAssignLead: false,
            ShouldScheduleDemo: false);
        yield return new ChatEvalQuestion(
            new ChatEvalConfig(
                SourceDir: "../../../KnowledgeSources/Sleekflow/",
                SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1"),
            "User asking about Live Chat and requesting to speak with a sales representative - Turn 2",
            [
                new ChatMessageContent(AuthorRole.User, "hi sleekflow ai"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hey there! 👋 Thanks for saying hi to SleekFlow AI! How can I help you today? 😊"),
                new ChatMessageContent(AuthorRole.User, "有咩好介紹呀"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "哈囉！👋 好開心你問 SleekFlow AI 有咩好介紹呀！😊\n\nSleekFlow 其實係一個全方位嘅對話平台，最主要就係幫你將唔同嘅通訊渠道（好似 WhatsApp、Instagram、Live Chat 等等）集中晒喺一個地方管理，咁就唔使跳嚟跳去，覆客仔訊息會方便好多！仲可以幫你自動化一啲重複嘅工作，同埋更好咁管理你嘅客戶資料添！\n\n唔知你對邊方面比較有興趣呢？或者想解決邊個問題？隨時話我知，我可以再詳細介紹吓！✨"),
                new ChatMessageContent(AuthorRole.User, "Live chat?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Oh, Live Chat! ✨ Yes, that's a super cool part of SleekFlow! 😊 It's basically a widget you can put on your website that brings together chats from different channels like WhatsApp, Instagram, Facebook Messenger, and more, all into one place. So, you can chat with customers right there on your site and keep the conversation going even after they leave! It makes managing everything so much easier. Does that sound like something you're curious about? Happy to share more!"),
                new ChatMessageContent(AuthorRole.User, "中文？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "明白！😊 因為你對 Live Chat 同埋可能其他 SleekFlow 功能有興趣，而我哋想確保你可以得到最詳細嘅中文解答，我哋可以幫你轉介俾專門處理中文查詢嘅同事。\n\n佢哋會再聯絡你，解答你關於 Live Chat 或其他功能嘅問題。\n\n你想我哋幫你安排中文專員聯絡你嗎？😊"),
                new ChatMessageContent(AuthorRole.User, "會唔會好難 set up？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "哈囉！😊 其實 SleekFlow 嘅 Live Chat 設計到好簡單㗎， *完全唔需要寫 code* ！主要就係將一小段 code 複製貼上到你網站嘅 footer 部分就搞掂㗎喇！✨\n\n而且我哋仲有提供唔同網站平台（好似 WordPress、Shopify 等等）嘅詳細安裝教學，一步步跟住做就得，好容易上手㗎！👍\n\n仲有冇其他關於設定嘅問題想問呀？隨時話我知！😊"),
                new ChatMessageContent(AuthorRole.User, "你哋有冇電話可以打俾你傾多啲"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "哈囉！😊 明白你希望用電話傾多啲關於 SleekFlow 嘅嘢！\n\n我哋可以幫你安排專員（Sales Team）用電話聯絡你，解答你關於 SleekFlow Live Chat 或其他功能嘅問題。\n\n你想我哋安排專員打電話俾你傾多啲嗎？😊"),
                new ChatMessageContent(AuthorRole.User, "好"),
            ],
            "好的！我現在已經將你的請求轉交給我們的銷售團隊，他們會盡快通過電話聯繫你，為你提供更多關於 SleekFlow Live Chat 和其他功能的詳細資訊。\n\n感謝你對 SleekFlow 的興趣！如果你有任何其他問題，隨時可以在這裡問我。祝你有美好的一天！😊",
            [],
            AgentConfig: GetProductionAgentConfig(),
            ReplyGenerationContext: new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                GetDefaultContactProperties(),
                null,
                null),
            ShouldAssignLead: true,
            ShouldScheduleDemo: false);
        yield return new ChatEvalQuestion(
            new ChatEvalConfig(
                SourceDir: "../../../KnowledgeSources/Sleekflow/",
                SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1"),
            "User asking about Live Chat and requesting to speak with a sales representative - Turn 3",
            [
                new ChatMessageContent(AuthorRole.User, "hi sleekflow ai"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hey there! 👋 Thanks for saying hi to SleekFlow AI! How can I help you today? 😊"),
                new ChatMessageContent(AuthorRole.User, "有咩好介紹呀"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "哈囉！👋 好開心你問 SleekFlow AI 有咩好介紹呀！😊\n\nSleekFlow 其實係一個全方位嘅對話平台，最主要就係幫你將唔同嘅通訊渠道（好似 WhatsApp、Instagram、Live Chat 等等）集中晒喺一個地方管理，咁就唔使跳嚟跳去，覆客仔訊息會方便好多！仲可以幫你自動化一啲重複嘅工作，同埋更好咁管理你嘅客戶資料添！\n\n唔知你對邊方面比較有興趣呢？或者想解決邊個問題？隨時話我知，我可以再詳細介紹吓！✨"),
                new ChatMessageContent(AuthorRole.User, "Live chat?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Oh, Live Chat! ✨ Yes, that's a super cool part of SleekFlow! 😊 It's basically a widget you can put on your website that brings together chats from different channels like WhatsApp, Instagram, Facebook Messenger, and more, all into one place. So, you can chat with customers right there on your site and keep the conversation going even after they leave! It makes managing everything so much easier. Does that sound like something you're curious about? Happy to share more!"),
                new ChatMessageContent(AuthorRole.User, "中文？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "明白！😊 因為你對 Live Chat 同埋可能其他 SleekFlow 功能有興趣，而我哋想確保你可以得到最詳細嘅中文解答，我哋可以幫你轉介俾專門處理中文查詢嘅同事。\n\n佢哋會再聯絡你，解答你關於 Live Chat 或其他功能嘅問題。\n\n你想我哋幫你安排中文專員聯絡你嗎？😊"),
                new ChatMessageContent(AuthorRole.User, "會唔會好難 set up？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "哈囉！😊 其實 SleekFlow 嘅 Live Chat 設計到好簡單㗎， *完全唔需要寫 code* ！主要就係將一小段 code 複製貼上到你網站嘅 footer 部分就搞掂㗎喇！✨\n\n而且我哋仲有提供唔同網站平台（好似 WordPress、Shopify 等等）嘅詳細安裝教學，一步步跟住做就得，好容易上手㗎！👍\n\n仲有冇其他關於設定嘅問題想問呀？隨時話我知！😊"),
                new ChatMessageContent(AuthorRole.User, "你哋有冇電話可以打俾你傾多啲"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "哈囉！😊 明白你希望用電話傾多啲關於 SleekFlow 嘅嘢！\n\n我哋可以幫你安排專員（Sales Team）用電話聯絡你，解答你關於 SleekFlow Live Chat 或其他功能嘅問題。\n\n你想我哋安排專員打電話俾你傾多啲嗎？😊"),
                new ChatMessageContent(AuthorRole.User, "好"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "太好喇！😊 咁我哋就會安排專員（Sales Team）用電話聯絡你，解答你關於 SleekFlow Live Chat 或其他功能嘅問題。\n\n你想我哋而家幫你安排專員聯絡你嗎？"),
                new ChatMessageContent(AuthorRole.User, "好"),
            ],
            "好的！我現在已經將你的請求轉交給我們的銷售團隊，他們會盡快通過電話聯繫你，為你提供更多關於 SleekFlow Live Chat 和其他功能的詳細資訊。\n\n感謝你對 SleekFlow 的興趣！如果你有任何其他問題，隨時可以在這裡問我。祝你有美好的一天！😊",
            [],
            AgentConfig: GetProductionAgentConfig(),
            ReplyGenerationContext: new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                GetDefaultContactProperties(),
                null,
                null),
            ShouldAssignLead: true,
            ShouldScheduleDemo: false);
    }

    public static IEnumerable<ChatEvalQuestion> GetSleekflowPricingTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/Sleekflow/",
            SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1");

        var agentConfig = GetProductionAgentConfig();
        var defaultContactProperties = GetDefaultContactProperties();

        yield return new ChatEvalQuestion(
            testConfig,
            "User asking about pricing in the UAE region",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Sleekflow 有咩 beauty use case?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    """
                    您好！很高興為您介紹 SleekFlow 在美容行業的應用。SleekFlow 作為全渠道對話平台，能幫助美容院、護膚中心等提升營運效率和客戶體驗。

                    主要用途包括：

                    *   *整合客戶溝通* ：將 WhatsApp、Instagram 等訊息集中管理。
                    *   *自動化預約管理* ：發送預約提醒、處理改期等。
                    *   *優化客戶服務* ：跟進客戶療程後情況，收集意見。
                    *   *提升客戶留存* ：根據客戶偏好發送個人化優惠。

                    這些功能有助於美容行業更有效地進行客戶互動、管理預約及推廣服務。不知道您目前在營運上有遇到哪些挑戰，或是對哪個方面比較感興趣呢？
                    """),
                new ChatMessageContent(
                    AuthorRole.User,
                    "有咩公司用過？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    """
                    謝謝您的提問！很高興能分享一些美容及健康行業客戶的成功案例。

                    SleekFlow 確實幫助了不少這類型的客戶提升營運效率和客戶體驗，例如：

                    *   *Weirdo Beauty* 利用 SleekFlow 建立了 24/7 的虛擬助理。
                    *   *EVRbeauty* 透過 SleekFlow 提升了 5 倍銷售效率，並將回應時間縮短了 50%。
                    *   *retens medical* 增強了在 WhatsApp 上的客戶溝通透明度。
                    *   *Sun and Moon Massage* 讓預約流程更有效率，達到了 30% 的轉換率。

                    這些都是 SleekFlow 如何實際應用於美容行業的例子。不知道您對哪個案例或具體功能比較感興趣，想了解更多呢？
                    """),
                new ChatMessageContent(
                    AuthorRole.User,
                    "仲有無？有無 UAE 既 use cases?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    """
                    謝謝您對 SleekFlow 在阿聯酋 (UAE) 的應用感興趣。我們確實有一些在阿聯酋的客戶案例可以分享。

                    例如，TKD Lingerie 和 AZADEA Group 透過 SleekFlow 提升了全渠道零售體驗。Anigma Technologies 則利用我們的平台優化了客戶支援和自動化流程。

                    SleekFlow 致力於協助阿聯酋的企業整合溝通渠道，提升營運效率和客戶互動。

                    希望這些例子能幫助您了解更多。如果您對這些案例或 SleekFlow 在阿聯酋的具體應用有任何進一步的問題，隨時可以提出。
                    """),
                new ChatMessageContent(
                    AuthorRole.User,
                    "仲有無？有無 UAE 既 pricing? "),
            ],
            """
            謝謝您對 SleekFlow 在阿聯酋的應用和定價感興趣。

            除了之前提到的案例，我們還有協助像 USBS 這樣的阿聯酋公司，透過 SleekFlow 提升客戶獲取效率。

            關於阿聯酋的定價，我們的 Pro 月費計劃為 dh729 (年繳為 dh599)，Premium 月費計劃為 dh1,499 (年繳為 dh1,299)。這些計劃包含不同的功能和用戶數，並可根據需要添加額外用戶或聯絡人數。

            具體費用會根據您的業務規模、所需功能和用戶數量而定。如果您願意分享更多您的具體需求，我們可以為您提供更精確的報價。

            希望這些資訊對您有幫助！如果您有任何其他問題，隨時可以提出。
            """,
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));

        yield return new ChatEvalQuestion(
            testConfig,
            "User asking about specific pricing tiers and features included",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "What's included in your Premium plan and how much does it cost?"),
            ],
            """
            The Premium plan costs US$399/month when billed monthly or US$349/month when billed yearly (saving 12.5%).

            This plan includes:
            - 5 user accounts (expandable up to 20)
            - 10,000 contacts
            - Access to 10 channels including WhatsApp Business API
            - 300,000 broadcast messages per month
            - Unlimited automation rules
            - 25 active Flow Builder workflows
            - Access to ticketing module
            - Team management
            - Advanced integrations like HubSpot and Zoho CRM
            - Full analytics including conversation and conversion dashboards
            - Chat support

            Each additional user costs US$39/month, and you can add 2,000 more contacts for US$49/month. WhatsApp Business API phone numbers are US$15/month each.

            Would you like more specific information about any of these features?
            """,
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));

        yield return new ChatEvalQuestion(
            testConfig,
            "User comparing plans and asking for recommendations",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "What's the difference between Pro and Premium plans? I run a small beauty salon with 8 staff."),
            ],
            """
            For your 8-staff beauty salon, here are the key differences between Pro and Premium plans:

            Pro Plan (US$199/month or US$149/month yearly):
            - 3 users included (can add up to 7 more at US$19/user/month)
            - 2,000 contacts
            - 5 channels
            - 5,000 broadcast messages monthly
            - No ticketing module
            - No team management
            - Basic analytics (2 metrics)

            Premium Plan (US$399/month or US$349/month yearly):
            - 5 users included (can add 3 more to cover your team at US$39/user/month)
            - 10,000 contacts
            - 10 channels
            - 300,000 broadcast messages monthly
            - Includes ticketing module
            - Team management features
            - Full analytics dashboard

            For a beauty salon with 8 staff, I'd recommend the Premium plan as it includes team management and ticketing features that would help you coordinate your staff and manage customer inquiries more efficiently. It also provides more comprehensive analytics to track customer engagement and salon performance.
            """,
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));

        yield return new ChatEvalQuestion(
            testConfig,
            "User asking about WhatsApp conversation rates for specific countries",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "How much do WhatsApp Business API conversations cost in the US compared to India?"),
            ],
            """
            For WhatsApp Business API conversations, the rates (in USD) vary by country and conversation type:

            United States (part of North America region):
            - Marketing: $0.0288
            - Utility: $0.0046
            - Authentication: $0.0155
            - Service: $0.0101

            India:
            - Marketing: $0.0123
            - Utility: $0.0016
            - Authentication: $0.0016
            - Service: $0.0046

            As you can see, WhatsApp conversation rates in India are significantly lower than in the US across all conversation types. Service conversations in the US cost more than double compared to India, and marketing conversations cost over twice as much.

            These rates are in addition to your SleekFlow subscription plan, which starts at US$149/month (yearly billing) for the Pro plan that includes WhatsApp Business API integration.
            """,
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));

        yield return new ChatEvalQuestion(
            testConfig,
            "User asking detailed questions about add-on pricing and limits",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I need to add more users and contacts to my plan. How much will that cost?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "I'd be happy to help with your add-on pricing question. Could you please let me know which plan you're currently on (Startup, Pro, Premium, or Enterprise)?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm on the Pro plan"),
            ],
            """
            For the Pro plan, here's the pricing for adding more users and contacts:

            Additional Users:
            - Each additional user costs US$19/month
            - You can add up to 7 more users (for a total of 10 users max on Pro plan)

            Additional Contacts:
            - Each additional 2,000 contacts costs US$49/month
            - There's no limit to how many contact packs you can add

            For example, if you add 2 users and 4,000 contacts, that would be an additional US$38/month for users and US$98/month for contacts, totaling US$136/month on top of your Pro plan subscription.

            Is there a specific number of users and contacts you're looking to add?
            """,
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));

        yield return new ChatEvalQuestion(
            testConfig,
            "User asking about Flow Builder and Flow Enrollment pricing",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "How much does it cost to increase Flow Builder enrollment limits?"),
            ],
            """
            The cost to increase Flow Builder enrollment limits depends on your plan:

            Pro Plan:
            - Default: 500 enrollments/month
            - Add-on options:
              • 1,500 enrollments: US$59/month
              • 2,500 enrollments: US$119/month
              • 3,000 enrollments: US$149/month
            - Maximum: 3,000 enrollments/month

            Premium Plan:
            - Default: 3,000 enrollments/month
            - Add-on options:
              • 5,000 enrollments: US$99/month
              • 7,000 enrollments: US$199/month
              • 10,000 enrollments: US$349/month
            - Maximum: 10,000 enrollments/month

            Enterprise Plan:
            - Default: 10,000 enrollments/month
            - Add-on options from 15,000 to 40,000 enrollments, ranging from US$199 to US$899/month
            - No maximum limit

            Each plan also has limitations on the number of active workflows and nodes per flow. Pro allows 3 active workflows with 25 nodes each, Premium allows 25 active workflows with 100 nodes each, and Enterprise allows 50 active workflows with 200 nodes each.
            """,
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));

        yield return new ChatEvalQuestion(
            testConfig,
            "User asking about multiple currency options and pricing differences",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "What are your pricing options in HKD? And how does it compare to USD?"),
            ],
            """
            Here are the SleekFlow pricing options in HKD compared to USD:

            Pro Plan:
            - Monthly: €189/month (USD: $199/month)
            - Yearly: €159/month (USD: $149/month)
            - User add-on: €19/user/month (USD: $19/user/month)
            - Contact add-on (2,000): €49/month (USD: $49/month)
            - WhatsApp number: €15/month (USD: $15/month)

            Premium Plan:
            - Monthly: €369/month (USD: $399/month)
            - Yearly: €309/month (USD: $349/month)
            - User add-on: €39/user/month (USD: $39/user/month)
            - Contact add-on (2,000): €49/month (USD: $49/month)
            - WhatsApp number: €15/month (USD: $15/month)

            Additional services:
            - Onboarding support (one-off): €499 (USD: $499)
            - Business consultancy (monthly): €469 (USD: $499)

            The EUR pricing is generally similar to or slightly less than the USD pricing when converted. For example, the monthly Premium plan is €369 (approximately $402 at current exchange rates) versus $399 direct USD pricing.

            All plans include the same features regardless of currency.
            """,
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));

        yield return new ChatEvalQuestion(
            testConfig,
            "User asking about enterprise pricing and custom features",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm interested in your Enterprise plan. What's the pricing and what exclusive features does it include?"),
            ],
            """
            The Enterprise plan pricing is custom and starts at US$1,500/month when billed yearly. This plan offers several exclusive features not available in other plans:

            Exclusive Enterprise features:
            - Unlimited users (10 included by default)
            - 40,000 contacts (expandable to unlimited)
            - Unlimited channels
            - Unlimited broadcast messages
            - 50 active Flow Builder workflows
            - 200 nodes per flow
            - 10,000 flow enrollments/month (expandable)
            - Enterprise integrations (Salesforce, Dynamics)
            - Data masking capabilities
            - Export SleekFlow chats
            - Service-Level Agreement
            - Custom role creation
            - Dedicated customer success & onboarding support
            - Business consultancy service included

            The Enterprise plan also includes all features from the Premium plan but with higher limits and additional customization options.

            For a precise quote tailored to your specific business needs, I recommend scheduling a consultation with our sales team. Would you like me to help arrange that?
            """,
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));

        yield return new ChatEvalQuestion(
            testConfig,
            "User asking about WhatsApp API conversation rates across multiple regions",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "What are WhatsApp conversation rates for UK, Germany, and UAE? And what's the difference between conversation types?"),
            ],
            """
            Here are the WhatsApp Business API conversation rates (in USD) for the requested countries:

            United Kingdom:
            - Marketing: $0.0608
            - Utility: $0.0253
            - Authentication: $0.0412
            - Service: $0.0446

            Germany:
            - Marketing: $0.1570
            - Utility: $0.0633
            - Authentication: $0.0883
            - Service: $0.0988

            United Arab Emirates:
            - Marketing: $0.0442
            - Utility: $0.0181
            - Authentication: $0.0205
            - Service: $0.0219

            Conversation types explained:
            - Marketing: Promotional messages to customers
            - Utility: Operational updates like shipping notifications
            - Authentication: Security verification messages
            - Service: Customer service responses within 24 hours of a user message

            Germany has the highest rates across all conversation types, while UAE has the lowest. Marketing conversations are always the most expensive, while utility conversations tend to be the most affordable.

            These rates are in addition to your SleekFlow subscription plan and the $15/month per WhatsApp Business API phone number.
            """,
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));

        yield return new ChatEvalQuestion(
            testConfig,
            "User asking about included support services and additional support options",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I want to know what kind of support comes with each plan and if there are premium support options."),
            ],
            """
            Here's what support is included with each SleekFlow plan:

            Startup Plan:
            - Help Center documentation only
            - No direct support

            Pro Plan:
            - Help Center documentation
            - Email support
            - Onboarding support (yearly plans only, $499 one-off fee for monthly plans)
            - Optional Business Consultancy Service ($499/month)
            - Optional Flow Builder & Automation Setup (custom pricing)

            Premium Plan:
            - Help Center documentation
            - Email support
            - Chat support
            - Onboarding support (yearly plans only, $499 one-off fee for monthly plans)
            - Optional Business Consultancy Service ($499/month)
            - Optional Flow Builder & Automation Setup (custom pricing)

            Enterprise Plan:
            - Help Center documentation
            - Email support
            - Chat support
            - Dedicated Customer Success & Onboarding Support
            - Business Consultancy Service included
            - Optional Flow Builder & Automation Setup (custom pricing)
            - Service-Level Agreement

            The Business Consultancy Service ($499/month, included in Enterprise) provides strategic guidance for your business use cases and implementation. The Flow Builder & Automation Setup service (available as an add-on for all paid plans) offers custom workflow implementation assistance.

            For the most comprehensive support, the Enterprise plan provides a dedicated customer success manager and business consultancy services at no additional cost.
            """,
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));

        yield return new ChatEvalQuestion(
            testConfig,
            "User asking about specific API limits and pricing across plans",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "What are the SleekFlow API limitations and costs in different plans?"),
            ],
            """
            Here are the SleekFlow API limitations and availability across different plans:

            Startup Plan:
            - SleekFlow API: Not available

            Pro Plan:
            - SleekFlow API: Available
            - Monthly API call limit: 5,000 calls
            - No additional cost (included in plan)

            Premium Plan:
            - SleekFlow API: Available
            - Monthly API call limit: 100,000 calls
            - No additional cost (included in plan)

            Enterprise Plan:
            - SleekFlow API: Available
            - Monthly API call limit: 10,000,000 calls
            - No additional cost (included in plan)

            There is no option to purchase additional API calls beyond these limits. If you anticipate needing more API calls than what's included in your current plan, you would need to upgrade to a higher-tier plan.

            The API allows you to integrate SleekFlow with your existing systems and perform operations like sending messages, managing contacts, and accessing conversation data programmatically.
            """,
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null));
    }

    public static IEnumerable<ChatEvalQuestion> GetSleekflowAudioTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/Sleekflow/",
            SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1");

        var agentConfig = GetProductionAgentConfig();
        var defaultContactProperties = GetDefaultContactProperties();

        yield return new ChatEvalQuestion(
            testConfig,
            "User asking about pricing in the UAE region",
            null,
            """
            SleekFlow 有以下幾個 use case:

            1. 客戶獲取
            2. 客戶管理
            3. 客戶服務
            4. 客戶忠誠度
            5. 客戶流失預防
            """,
            [],
            AgentConfig: agentConfig,
            ReplyGenerationContext: new ReplyGenerationContext(
                testConfig.SleekflowCompanyId,
                Guid.NewGuid().ToString(),
                defaultContactProperties,
                null,
                null),
            SfChatEntriesQuestionContexts:
            [
                new SfChatEntry()
                {
                    User = string.Empty,
                    Files =
                    [
                        new SfChatEntryFile(
                            url:
                            "https://sleekflowmedia.blob.core.windows.net/intelligent-hub-test-case/what%20is%20sleekflow%20and%20how%20much.mp3?sp=r&st=2025-06-05T15:27:31Z&se=2099-06-05T23:27:31Z&spr=https&sv=2024-11-04&sr=b&sig=lhah7m69qVlloBVpBWHhh4TpZlxucxmGwGmRc5pnE84%3D",
                            mimeType: "audio/mpeg",
                            fileSize: 1_081_180)
                    ]
                },
            ]);
    }
}