FROM krakend/builder:2.4.6 AS builder

WORKDIR /app

COPY . .

RUN go build -buildmode=plugin -o krakend-authentication-plugin.so .

FROM devopsfaith/krakend:2.4.6

COPY --from=builder ["/app/krakend-authentication-plugin.so", "/etc/krakend/plugins/krakend-authentication-plugin.so"]

EXPOSE 8080

COPY post_proxy.lua /etc/krakend/post_proxy.lua

# Copy the krakend.json file to the container and config in docker-compose.yml to toggle between regional and global krakend config
COPY krakend.json /etc/krakend/krakend.json