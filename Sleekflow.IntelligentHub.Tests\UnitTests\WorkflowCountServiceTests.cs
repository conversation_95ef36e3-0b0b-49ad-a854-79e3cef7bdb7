using MassTransit;
using Microsoft.Extensions.Logging;
using Moq;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Services;
using Sleekflow.Models.Constants;
using Sleekflow.Models.FlowHub;

namespace Sleekflow.IntelligentHub.Tests.UnitTests;

[TestFixture]
public class WorkflowCountServiceTests
{
  private Mock<IRequestClient<GetActiveWorkflowCountsByAgentConfigIdsRequest>> _mockRequestClient;
  private Mock<ILogger<WorkflowCountService>> _mockLogger;
  private WorkflowCountService _workflowCountService;

  [SetUp]
  public void SetUp()
  {
    _mockRequestClient = new Mock<IRequestClient<GetActiveWorkflowCountsByAgentConfigIdsRequest>>();
    _mockLogger = new Mock<ILogger<WorkflowCountService>>();
    _workflowCountService = new WorkflowCountService(requestClient: _mockRequestClient.Object, logger: _mockLogger.Object);
  }

  [Test]
  public async Task GetActiveWorkflowCountsByAgentConfigIdsAsync_WithValidAgentConfigIds_ReturnsCorrectCounts()
  {
    // Arrange
    var sleekflowCompanyId = "test-company-id";
    var agentConfigIds = new List<string>
    {
      "agent1", "agent2", "agent3"
    };

    var expectedWorkflowCounts = new Dictionary<string, int>
    {
      {
        "agent1", 2
      },
      {
        "agent2", 1
      },
      {
        "agent3", 0
      }
    };

    var mockResponse = new Mock<Response<GetActiveWorkflowCountsByAgentConfigIdsReply>>();
    mockResponse.Setup(expression: x => x.Message)
      .Returns(value: new GetActiveWorkflowCountsByAgentConfigIdsReply(workflowCounts: expectedWorkflowCounts));

    _mockRequestClient
      .Setup(expression: x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.Is<GetActiveWorkflowCountsByAgentConfigIdsRequest>(req =>
          req.SleekflowCompanyId == sleekflowCompanyId &&
          req.AgentConfigIds.SequenceEqual(agentConfigIds)),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()))
      .ReturnsAsync(value: mockResponse.Object);

    // Act
    var result = await _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(
      sleekflowCompanyId: sleekflowCompanyId,
      agentConfigIds: agentConfigIds);

    // Assert
    Assert.That(actual: result, expression: Is.Not.Null);
    Assert.That(actual: result.Count, expression: Is.EqualTo(expected: 3));
    Assert.That(actual: result[key: "agent1"], expression: Is.EqualTo(expected: 2));
    Assert.That(actual: result[key: "agent2"], expression: Is.EqualTo(expected: 1));
    Assert.That(actual: result[key: "agent3"], expression: Is.EqualTo(expected: 0));

    // Verify that the request client was called with correct parameters
    _mockRequestClient.Verify(
      expression: x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.Is<GetActiveWorkflowCountsByAgentConfigIdsRequest>(req =>
          req.SleekflowCompanyId == sleekflowCompanyId &&
          req.AgentConfigIds.SequenceEqual(agentConfigIds)),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()),
      times: Times.Once);
  }

  [Test]
  public async Task GetActiveWorkflowCountsByAgentConfigIdsAsync_WithEmptyAgentConfigList_ReturnsEmptyDictionary()
  {
    // Arrange
    var sleekflowCompanyId = "test-company-id";
    var agentConfigIds = new List<string>();

    // Act
    var result = await _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(
      sleekflowCompanyId: sleekflowCompanyId,
      agentConfigIds: agentConfigIds);

    // Assert
    Assert.That(actual: result, expression: Is.Not.Null);
    Assert.That(actual: result.Count, expression: Is.EqualTo(expected: 0));

    // Verify that the request client was not called for empty list
    _mockRequestClient.Verify(
      expression: x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()),
      times: Times.Never);
  }

  [Test]
  public async Task GetActiveWorkflowCountsByAgentConfigIdsAsync_WithMassTransitException_ReturnsZerosAndLogsError()
  {
    // Arrange
    var sleekflowCompanyId = "test-company-id";
    var agentConfigIds = new List<string>
    {
      "agent1", "agent2"
    };

    _mockRequestClient
      .Setup(expression: x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()))
      .ThrowsAsync(exception: new RequestTimeoutException(requestId: "FlowHub request timeout"));

    // Act
    var result = await _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(
      sleekflowCompanyId: sleekflowCompanyId,
      agentConfigIds: agentConfigIds);

    // Assert
    Assert.That(actual: result, expression: Is.Not.Null);
    Assert.That(actual: result.Count, expression: Is.EqualTo(expected: 2));
    Assert.That(actual: result[key: "agent1"], expression: Is.EqualTo(expected: 0));
    Assert.That(actual: result[key: "agent2"], expression: Is.EqualTo(expected: 0));

    // Verify error was logged
    _mockLogger.Verify(
      expression: x => x.Log(
        LogLevel.Error,
        It.IsAny<EventId>(),
        It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Failed to retrieve workflow counts for company")),
        It.IsAny<Exception>(),
        It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
      times: Times.Once);
  }

  [Test]
  public async Task GetActiveWorkflowCountsByAgentConfigIdsAsync_WithPartialFailureInFlowHub_ReturnsPartialResults()
  {
    // Arrange
    var sleekflowCompanyId = "test-company-id";
    var agentConfigIds = new List<string>
    {
      "agent1", "agent2"
    };

    // FlowHub returns partial results (one agent config has count, other is 0 due to error)
    var expectedWorkflowCounts = new Dictionary<string, int>
    {
      {
        "agent1", 1
      },
      {
        "agent2", 0
      } // FlowHub consumer handled the error and returned 0
    };

    var mockResponse = new Mock<Response<GetActiveWorkflowCountsByAgentConfigIdsReply>>();
    mockResponse.Setup(expression: x => x.Message)
      .Returns(value: new GetActiveWorkflowCountsByAgentConfigIdsReply(workflowCounts: expectedWorkflowCounts));

    _mockRequestClient
      .Setup(expression: x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()))
      .ReturnsAsync(value: mockResponse.Object);

    // Act
    var result = await _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(
      sleekflowCompanyId: sleekflowCompanyId,
      agentConfigIds: agentConfigIds);

    // Assert
    Assert.That(actual: result, expression: Is.Not.Null);
    Assert.That(actual: result.Count, expression: Is.EqualTo(expected: 2));
    Assert.That(actual: result[key: "agent1"], expression: Is.EqualTo(expected: 1));
    Assert.That(actual: result[key: "agent2"], expression: Is.EqualTo(expected: 0));
  }

  [Test]
  public async Task GetActiveWorkflowCountsByAgentConfigIdsAsync_VerifiesCorrectRequestParameters()
  {
    // Arrange
    var sleekflowCompanyId = "test-company-id";
    var agentConfigId = "test-agent-config";
    var agentConfigIds = new List<string>
    {
      agentConfigId
    };

    var expectedWorkflowCounts = new Dictionary<string, int>
    {
      {
        agentConfigId, 0
      }
    };

    var mockResponse = new Mock<Response<GetActiveWorkflowCountsByAgentConfigIdsReply>>();
    mockResponse.Setup(expression: x => x.Message)
      .Returns(value: new GetActiveWorkflowCountsByAgentConfigIdsReply(workflowCounts: expectedWorkflowCounts));

    _mockRequestClient
      .Setup(expression: x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()))
      .ReturnsAsync(value: mockResponse.Object);

    // Act
    await _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(
      sleekflowCompanyId: sleekflowCompanyId,
      agentConfigIds: agentConfigIds);

    // Assert - Verify the request parameters
    _mockRequestClient.Verify(
      expression: x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.Is<GetActiveWorkflowCountsByAgentConfigIdsRequest>(req =>
          req.SleekflowCompanyId == sleekflowCompanyId &&
          req.AgentConfigIds.Count == 1 &&
          req.AgentConfigIds.Contains(agentConfigId)),
        It.IsAny<CancellationToken>(),
        It.Is<RequestTimeout>(timeout => timeout.Value == TimeSpan.FromSeconds(30))),
      times: Times.Once);
  }

  [Test]
  public async Task
    GetActiveWorkflowCountsByAgentConfigIdsAsync_WithLargeNumberOfAgentConfigs_ProcessesAllConcurrently()
  {
    // Arrange
    var sleekflowCompanyId = "test-company-id";
    var agentConfigIds = Enumerable.Range(start: 1, count: 10).Select(selector: i => $"agent{i}").ToList();

    var expectedWorkflowCounts = agentConfigIds.ToDictionary(keySelector: id => id, elementSelector: _ => 1);

    var mockResponse = new Mock<Response<GetActiveWorkflowCountsByAgentConfigIdsReply>>();
    mockResponse.Setup(expression: x => x.Message)
      .Returns(value: new GetActiveWorkflowCountsByAgentConfigIdsReply(workflowCounts: expectedWorkflowCounts));

    _mockRequestClient
      .Setup(expression: x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()))
      .ReturnsAsync(value: mockResponse.Object);

    // Act
    var result = await _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(
      sleekflowCompanyId: sleekflowCompanyId,
      agentConfigIds: agentConfigIds);

    // Assert
    Assert.That(actual: result, expression: Is.Not.Null);
    Assert.That(actual: result.Count, expression: Is.EqualTo(expected: 10));

    // Verify all agent configs have counts
    foreach (var agentConfigId in agentConfigIds)
    {
      Assert.That(actual: result.ContainsKey(key: agentConfigId), expression: Is.True);
      Assert.That(actual: result[key: agentConfigId], expression: Is.EqualTo(expected: 1));
    }

    // Verify request client was called once (not once per agent config like HTTP version)
    _mockRequestClient.Verify(
      expression: x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()),
      times: Times.Once);
  }

  [Test]
  public void CompanyAgentConfigDto_DefaultConstructor_SetsActiveWorkflowCountToZero()
  {
    // Arrange & Act
    var config = new CompanyAgentConfig(
      id: "test-id",
      name: "Test Config",
      sleekflowCompanyId: "test-company-id",
      isChatHistoryEnabledAsContext: true,
      isContactPropertiesEnabledAsContext: true,
      numberOfPreviousMessagesInChatHistoryAvailableAsContext: 10,
      channelType: null,
      channelId: null,
      description: null,
      promptInstruction: null,
      type: CompanyAgentTypes.Sales,
      collaborationMode: AgentCollaborationModes.Default,
      leadNurturingTools: null,
      createdAt: DateTimeOffset.UtcNow,
      updatedAt: DateTimeOffset.UtcNow,
      createdBy: null,
      updatedBy: null,
      eTag: null,
      enricherConfigs: null,
      knowledgeRetrievalConfig: null,
      toolsConfig: null,
      actions: null);

    var dto = new CompanyAgentConfigDto(config: config);

    // Assert
    Assert.That(actual: dto.ActiveWorkflowCount, expression: Is.EqualTo(expected: 0));
  }
}