// See https://aka.ms/new-console-template for more information

using Sleekflow.KrakenD.Generator;
using Sleekflow.KrakenD.Generator.Constants;
using Sleekflow.KrakenD.Generator.InternalObjects;

var healthz = new List<Healthz>
{
    new Healthz("SALESFORCE_INTEGRATOR_HOST", "salesforce-integrator"),
    new Healthz("HUBSPOT_INTEGRATOR_HOST", "hubspot-integrator"),
    new Healthz("DYNAMICS365_INTEGRATOR_HOST", "dynamics365-integrator"),
    new Healthz("GOOGLE_SHEETS_INTEGRATOR_HOST", "google-sheets-integrator"),
    new Healthz("ZOHO_INTEGRATOR_HOST", "zoho-integrator"),
    new Healthz("TIKTOK_ADS_INTEGRATOR_HOST", "tiktok-ads-integrator"),
    new Healthz("CRM_HUB_HOST", "crm-hub"),
    new Healthz("EMAIL_HUB_HOST", "email-hub"),
    new Healthz("MESSAGING_HUB_HOST", "messaging-hub"),
    new Healthz("COMMERCE_HUB_HOST", "commerce-hub"),
    new Healthz("WEBHOOK_HUB_HOST", "webhook-hub"),
    new Healthz("SHARE_HUB_HOST", "share-hub"),
    new Healthz("PUBLIC_API_GATEWAY_HOST", "public-api-gateway"),
    new Healthz("FLOW_HUB_HOST", "flow-hub"),
    new Healthz("FLOW_HUB_EXECUTOR_HOST", "flow-hub-executor"),
    new Healthz("FLOW_HUB_INTEGRATOR_HOST", "flow-hub-integrator"),
    new Healthz("TENANT_HUB_HOST", "tenant-hub"),
    new Healthz("INTELLIGENT_HUB_HOST", "intelligent-hub"),
    new Healthz("USER_EVENT_HUB_HOST", "user-event-hub"),
    new Healthz("SUPPORT_HUB_HOST", "support-hub"),
    new Healthz("TICKETING_HUB_HOST", "ticketing-hub"),
    new Healthz("INTERNAL_INTEGRATION_HUB_HOST", "internal-integration-hub"),
};

var crmHubIntegrators = new List<CrmHubIntegrator>
{
    new CrmHubIntegrator("salesforce-integrator", "SALESFORCE_INTEGRATOR_HOST", "SALESFORCE_INTEGRATOR_KEY"),
    new CrmHubIntegrator("hubspot-integrator", "HUBSPOT_INTEGRATOR_HOST", "HUBSPOT_INTEGRATOR_KEY"),
    new CrmHubIntegrator("dynamics365-integrator", "DYNAMICS365_INTEGRATOR_HOST", "DYNAMICS365_INTEGRATOR_KEY"),
    new CrmHubIntegrator("google-sheets-integrator", "GOOGLE_SHEETS_INTEGRATOR_HOST", "GOOGLE_SHEETS_INTEGRATOR_KEY"),
    new CrmHubIntegrator("zoho-integrator", "ZOHO_INTEGRATOR_HOST", "ZOHO_INTEGRATOR_KEY"),
    new CrmHubIntegrator("tiktok-ads-integrator", "TIKTOK_ADS_INTEGRATOR_HOST", "TIKTOK_ADS_INTEGRATOR_KEY"),
};

var managedEndpoints = new List<ManagedEndpoint>
{
    new ManagedEndpoint("crm-hub", "Objects", "CRM_HUB_HOST", "CRM_HUB_KEY"),
    new ManagedEndpoint("crm-hub", "Providers", "CRM_HUB_HOST", "CRM_HUB_KEY"),
    new ManagedEndpoint("crm-hub", "UnifyRules", "CRM_HUB_HOST", "CRM_HUB_KEY"),
    new ManagedEndpoint("crm-hub", "Webhooks", "CRM_HUB_HOST", "CRM_HUB_KEY"),
    new ManagedEndpoint("crm-hub", "Schemas", "CRM_HUB_HOST", "CRM_HUB_KEY"),
    new ManagedEndpoint("crm-hub", "SchemafulObjects", "CRM_HUB_HOST", "CRM_HUB_KEY"),
    new ManagedEndpoint("crm-hub", "CrmHubConfigs", "CRM_HUB_HOST", "CRM_HUB_KEY"),
    new ManagedEndpoint("crm-hub", "InflowActions", "CRM_HUB_HOST", "CRM_HUB_KEY"),
    new ManagedEndpoint("crm-hub", "Blobs", "CRM_HUB_HOST", "CRM_HUB_KEY"),
    new ManagedEndpoint("email-hub", "Emails", "EMAIL_HUB_HOST", "EMAIL_HUB_KEY"),
    new ManagedEndpoint("email-hub", "Subscriptions", "EMAIL_HUB_HOST", "EMAIL_HUB_KEY"),
    new ManagedEndpoint("email-hub", "Blobs", "EMAIL_HUB_HOST", "EMAIL_HUB_KEY"),
    new ManagedEndpoint("email-hub", "Providers", "EMAIL_HUB_HOST", "EMAIL_HUB_KEY"),
    new ManagedEndpoint("messaging-hub", "Messages", "MESSAGING_HUB_HOST", "MESSAGING_HUB_KEY"),
    new ManagedEndpoint("messaging-hub", "Wabas", "MESSAGING_HUB_HOST", "MESSAGING_HUB_KEY"),
    new ManagedEndpoint("messaging-hub", "Channels", "MESSAGING_HUB_HOST", "MESSAGING_HUB_KEY"),
    new ManagedEndpoint("messaging-hub", "Templates", "MESSAGING_HUB_HOST", "MESSAGING_HUB_KEY"),
    new ManagedEndpoint("messaging-hub", "Balances", "MESSAGING_HUB_HOST", "MESSAGING_HUB_KEY"),
    new ManagedEndpoint("messaging-hub", "TransactionLogs", "MESSAGING_HUB_HOST", "MESSAGING_HUB_KEY"),
    new ManagedEndpoint("messaging-hub", "Migrations", "MESSAGING_HUB_HOST", "MESSAGING_HUB_KEY"),
    new ManagedEndpoint("messaging-hub", "Managements", "MESSAGING_HUB_HOST", "MESSAGING_HUB_KEY"),
    new ManagedEndpoint("messaging-hub", "Medias", "MESSAGING_HUB_HOST", "MESSAGING_HUB_KEY"),
    new ManagedEndpoint("messaging-hub", "ProductCatalogs", "MESSAGING_HUB_HOST", "MESSAGING_HUB_KEY"),
    new ManagedEndpoint("messaging-hub", "AuditLogs", "MESSAGING_HUB_HOST", "MESSAGING_HUB_KEY"),
    new ManagedEndpoint("messaging-hub", "ConversationalAutomations", "MESSAGING_HUB_HOST", "MESSAGING_HUB_KEY"),
    new ManagedEndpoint("messaging-hub", "WhatsappFlows", "MESSAGING_HUB_HOST", "MESSAGING_HUB_KEY"),
    new ManagedEndpoint("messaging-hub", "MetaConversionApis", "MESSAGING_HUB_HOST", "MESSAGING_HUB_KEY"),
    new ManagedEndpoint("commerce-hub", "Blobs", "COMMERCE_HUB_HOST", "COMMERCE_HUB_KEY"),
    new ManagedEndpoint("commerce-hub", "Carts", "COMMERCE_HUB_HOST", "COMMERCE_HUB_KEY"),
    new ManagedEndpoint("commerce-hub", "Categories", "COMMERCE_HUB_HOST", "COMMERCE_HUB_KEY"),
    new ManagedEndpoint("commerce-hub", "Currencies", "COMMERCE_HUB_HOST", "COMMERCE_HUB_KEY"),
    new ManagedEndpoint("commerce-hub", "CustomCatalogs", "COMMERCE_HUB_HOST", "COMMERCE_HUB_KEY"),
    new ManagedEndpoint("commerce-hub", "Languages", "COMMERCE_HUB_HOST", "COMMERCE_HUB_KEY"),
    new ManagedEndpoint("commerce-hub", "Orders", "COMMERCE_HUB_HOST", "COMMERCE_HUB_KEY"),
    new ManagedEndpoint("commerce-hub", "PaymentProviderConfigs", "COMMERCE_HUB_HOST", "COMMERCE_HUB_KEY"),
    new ManagedEndpoint("commerce-hub", "Payments", "COMMERCE_HUB_HOST", "COMMERCE_HUB_KEY"),
    new ManagedEndpoint("commerce-hub", "Products", "COMMERCE_HUB_HOST", "COMMERCE_HUB_KEY"),
    new ManagedEndpoint("commerce-hub", "ProductVariants", "COMMERCE_HUB_HOST", "COMMERCE_HUB_KEY"),
    new ManagedEndpoint("commerce-hub", "Stores", "COMMERCE_HUB_HOST", "COMMERCE_HUB_KEY"),
    new ManagedEndpoint("commerce-hub", "Webhooks", "COMMERCE_HUB_HOST", "COMMERCE_HUB_KEY"),
    new ManagedEndpoint("commerce-hub", "CustomCatalogConfigs", "COMMERCE_HUB_HOST", "COMMERCE_HUB_KEY"),
    new ManagedEndpoint("commerce-hub", "Vtex", "COMMERCE_HUB_HOST", "COMMERCE_HUB_KEY"),
    new ManagedEndpoint("commerce-hub", "InflowActions", "COMMERCE_HUB_HOST", "COMMERCE_HUB_KEY"),
    new ManagedEndpoint("share-hub", "Analytics", "SHARE_HUB_HOST", "SHARE_HUB_KEY"),
    new ManagedEndpoint("share-hub", "CustomDomains", "SHARE_HUB_HOST", "SHARE_HUB_KEY"),
    new ManagedEndpoint("share-hub", "Links", "SHARE_HUB_HOST", "SHARE_HUB_KEY"),
    new ManagedEndpoint("share-hub", "QrCodes", "SHARE_HUB_HOST", "SHARE_HUB_KEY"),
    new ManagedEndpoint("audit-hub", "AuditLogs", "AUDIT_HUB_HOST", "AUDIT_HUB_KEY"),
    new ManagedEndpoint("audit-hub", "SystemAuditLogs", "AUDIT_HUB_HOST", "AUDIT_HUB_KEY"),
    new ManagedEndpoint("flow-hub", "Blobs", "FLOW_HUB_HOST", "FLOW_HUB_KEY"),
    new ManagedEndpoint("flow-hub", "Events", "FLOW_HUB_HOST", "FLOW_HUB_KEY"),
    new ManagedEndpoint("flow-hub", "Executions", "FLOW_HUB_HOST", "FLOW_HUB_KEY"),
    new ManagedEndpoint("flow-hub", "FlowHubConfigs", "FLOW_HUB_HOST", "FLOW_HUB_KEY"),
    new ManagedEndpoint("flow-hub", "States", "FLOW_HUB_HOST", "FLOW_HUB_KEY"),
    new ManagedEndpoint("flow-hub", "Workflows", "FLOW_HUB_HOST", "FLOW_HUB_KEY"),
    new ManagedEndpoint("flow-hub", "NeedConfigs", "FLOW_HUB_HOST", "FLOW_HUB_KEY"),
    new ManagedEndpoint("flow-hub", "AiWorkflows", "FLOW_HUB_HOST", "FLOW_HUB_KEY"),
    new ManagedEndpoint("intelligent-hub", "TextEnrichments", "INTELLIGENT_HUB_HOST", "INTELLIGENT_HUB_KEY"),
    new ManagedEndpoint("intelligent-hub", "IntelligentHubConfigs", "INTELLIGENT_HUB_HOST", "INTELLIGENT_HUB_KEY"),
    new ManagedEndpoint("intelligent-hub", "Blobs", "INTELLIGENT_HUB_HOST", "INTELLIGENT_HUB_KEY"),
    new ManagedEndpoint("intelligent-hub", "Documents", "INTELLIGENT_HUB_HOST", "INTELLIGENT_HUB_KEY"),
    new ManagedEndpoint("intelligent-hub", "KnowledgeBases", "INTELLIGENT_HUB_HOST", "INTELLIGENT_HUB_KEY"),
    new ManagedEndpoint("intelligent-hub", "RecommendedReplies", "INTELLIGENT_HUB_HOST", "INTELLIGENT_HUB_KEY"),
    new ManagedEndpoint("intelligent-hub", "TopicAnalytics", "INTELLIGENT_HUB_HOST", "INTELLIGENT_HUB_KEY"),
    new ManagedEndpoint("webhook-hub", "FacebookWebhooks", "WEBHOOK_HUB_HOST", "WEBHOOK_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "Companies", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "Users", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "EnabledFeatures", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "Roles", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "Migrations", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "Features", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "IpWhitelists", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "management/Companies", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "management/Users", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "management/EnabledFeatures", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "management/Roles", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "management/Features", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "management/IpWhitelists", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "management/Rbac", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "management/ImportUser", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("user-event-hub", "Messages", "USER_EVENT_HUB_HOST", "USER_EVENT_HUB_KEY"),
    new ManagedEndpoint("user-event-hub", "Sessions", "USER_EVENT_HUB_HOST", "USER_EVENT_HUB_KEY"),
    new ManagedEndpoint("ticketing-hub", "Blobs", "TICKETING_HUB_HOST", "TICKETING_HUB_KEY"),
    new ManagedEndpoint("ticketing-hub", "TicketCompanyConfigs", "TICKETING_HUB_HOST", "TICKETING_HUB_KEY"),
    new ManagedEndpoint("ticketing-hub", "TicketPriorities", "TICKETING_HUB_HOST", "TICKETING_HUB_KEY"),
    new ManagedEndpoint("ticketing-hub", "Tickets", "TICKETING_HUB_HOST", "TICKETING_HUB_KEY"),
    new ManagedEndpoint("ticketing-hub", "TicketTypes", "TICKETING_HUB_HOST", "TICKETING_HUB_KEY"),
    new ManagedEndpoint("ticketing-hub", "TicketActivities", "TICKETING_HUB_HOST", "TICKETING_HUB_KEY"),
    new ManagedEndpoint("ticketing-hub", "TicketStatuses", "TICKETING_HUB_HOST", "TICKETING_HUB_KEY"),
    new ManagedEndpoint("ticketing-hub", "TicketComments", "TICKETING_HUB_HOST", "TICKETING_HUB_KEY"),
    new ManagedEndpoint("ticketing-hub", "Managements", "TICKETING_HUB_HOST", "TICKETING_HUB_KEY"),
    new ManagedEndpoint("user-event-hub", "Notifications", "USER_EVENT_HUB_HOST", "USER_EVENT_HUB_KEY"),
    new ManagedEndpoint("internal-integration-hub", "NetSuite/Internal", "INTERNAL_INTEGRATION_HUB_HOST", "INTERNAL_INTEGRATION_HUB_KEY"),
};

var auth0ManagedEndpoints = new List<Auth0ManagedEndpoint>()
{
    new Auth0ManagedEndpoint("messaging-hub", "authorized/Balances", "MESSAGING_HUB_HOST", "MESSAGING_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "UserWorkspaces", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "Workspaces", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/Companies", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/Users", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/Plans", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/Roles", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/Features", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/PlanDefinitions", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/Subscriptions", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/UserWorkspaces", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/IpWhitelists", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/ExperimentalFeatures", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/Rbac", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/Blobs", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/EnabledFeatures", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("user-event-hub", "authorized/Notifications", "USER_EVENT_HUB_HOST", "USER_EVENT_HUB_KEY"),
    new Auth0ManagedEndpoint("audit-hub", "authorized/SystemAuditLogs", "AUDIT_HUB_HOST", "AUDIT_HUB_KEY"),
    new Auth0ManagedEndpoint("intelligent-hub", "authorized/CompanyConfigs", "INTELLIGENT_HUB_HOST", "INTELLIGENT_HUB_KEY"),
    new Auth0ManagedEndpoint("intelligent-hub", "authorized/CompanyAgentConfigs", "INTELLIGENT_HUB_HOST", "INTELLIGENT_HUB_KEY"),
    new Auth0ManagedEndpoint("intelligent-hub", "authorized/KnowledgeBases", "INTELLIGENT_HUB_HOST", "INTELLIGENT_HUB_KEY"),
    new Auth0ManagedEndpoint("user-event-hub", "authorized/SqlJobs", "USER_EVENT_HUB_HOST", "USER_EVENT_HUB_KEY"),
    new Auth0ManagedEndpoint("user-event-hub", "authorized/UserEvents", "USER_EVENT_HUB_HOST", "USER_EVENT_HUB_KEY"),
    new Auth0ManagedEndpoint("user-event-hub", "authorized/UserEventTypes", "USER_EVENT_HUB_HOST", "USER_EVENT_HUB_KEY"),
    new Auth0ManagedEndpoint("intelligent-hub", "authorized/Playgrounds", "INTELLIGENT_HUB_HOST", "INTELLIGENT_HUB_KEY"),
    new Auth0ManagedEndpoint("flow-hub", "authorized/AiWorkflows", "FLOW_HUB_HOST", "FLOW_HUB_KEY"),
    new Auth0ManagedEndpoint("intelligent-hub", "authorized/Conversations", "INTELLIGENT_HUB_HOST", "INTELLIGENT_HUB_KEY"),
};

var publicApiGatewayEndpoints = new List<PublicApiGatewayEndpoint>()
{
    new PublicApiGatewayEndpoint(
        "public-api-gateway",
        "messaging-hub/management",
        "SleekflowCompanies",
        "PUBLIC_API_GATEWAY_HOST",
        "PUBLIC_API_GATEWAY_KEY",
        inputAuthenticationType: AuthenticationTypes.ManagementKey),
    new PublicApiGatewayEndpoint(
        "public-api-gateway",
        "messaging-hub/management",
        "SleekflowCompanyUsers",
        "PUBLIC_API_GATEWAY_HOST",
        "PUBLIC_API_GATEWAY_KEY",
        inputAuthenticationType: AuthenticationTypes.ManagementKey),
    new PublicApiGatewayEndpoint(
        "public-api-gateway",
        "messaging-hub/management",
        "Subscriptions",
        "PUBLIC_API_GATEWAY_HOST",
        "PUBLIC_API_GATEWAY_KEY",
        inputAuthenticationType: AuthenticationTypes.ManagementKey),
    new PublicApiGatewayEndpoint(
        "public-api-gateway",
        "portal",
        "Authentications",
        "PUBLIC_API_GATEWAY_HOST"),
    new PublicApiGatewayEndpoint(
        "public-api-gateway",
        "messaging-hub/portal",
        "Wabas",
        "PUBLIC_API_GATEWAY_HOST",
        inputAuthenticationType: AuthenticationTypes.JwtToken),
    new PublicApiGatewayEndpoint(
        "public-api-gateway",
        "messaging-hub/portal",
        "Accounts",
        "PUBLIC_API_GATEWAY_HOST",
        inputAuthenticationType: AuthenticationTypes.JwtToken),
    new PublicApiGatewayEndpoint(
        "public-api-gateway",
        "messaging-hub/portal",
        "Channels",
        "PUBLIC_API_GATEWAY_HOST",
        inputAuthenticationType: AuthenticationTypes.JwtToken),
    new PublicApiGatewayEndpoint(
        "public-api-gateway",
        "messaging-hub/whatsapp/cloudapi",
        "Medias",
        "PUBLIC_API_GATEWAY_HOST",
        inputAuthenticationType: AuthenticationTypes.ApiKey),
    new PublicApiGatewayEndpoint(
        "public-api-gateway",
        "messaging-hub/whatsapp/cloudapi",
        "Messages",
        "PUBLIC_API_GATEWAY_HOST",
        inputAuthenticationType: AuthenticationTypes.ApiKey),
    new PublicApiGatewayEndpoint(
        "public-api-gateway",
        "messaging-hub/whatsapp/cloudapi",
        "Templates",
        "PUBLIC_API_GATEWAY_HOST",
        inputAuthenticationType: AuthenticationTypes.ApiKey),
    new PublicApiGatewayEndpoint(
        "public-api-gateway",
        "messaging-hub/whatsapp/cloudapi",
        "Webhooks",
        "PUBLIC_API_GATEWAY_HOST",
        inputAuthenticationType: AuthenticationTypes.ApiKey),
    new PublicApiGatewayEndpoint(
        "public-api-gateway",
        "messaging-hub/whatsapp/cloudapi",
        "Channels",
        "PUBLIC_API_GATEWAY_HOST",
        inputAuthenticationType: AuthenticationTypes.ApiKey),
    new PublicApiGatewayEndpoint(
        "public-api-gateway",
        "messaging-hub/whatsapp/cloudapi",
        "Transactions",
        "PUBLIC_API_GATEWAY_HOST",
        inputAuthenticationType: AuthenticationTypes.ApiKey),
};

var endpoints = new List<Endpoint>
{
    new Endpoint
    {
        EndpointEndpoint = "/v1/healthz",
        Method = Method.Get,
        Backend = healthz
            .Select(
                h => new Backend
                {
                    UrlPattern = "/healthz/liveness",
                    Method = Method.Get,
                    Encoding = BackendEncoding.String,
                    Host = new object[]
                    {
                        "{{ env \"" + h.HostEnvName + "\" }}"
                    },
                    Group = h.Group
                })
            .ToArray(),
        ExtraConfig = new EndpointExtraConfig
        {
            QosRatelimitRouter = new RateLimiting
            {
                MaxRate = 100, ClientMaxRate = 5, Strategy = QosRatelimitServiceStrategy.Ip, Key = "X-Azure-ClientIP"
            }
        }
    },
    new InternalEndpoint("commerce-hub", "COMMERCE_HUB_HOST", "COMMERCE_HUB_KEY"),
    new InternalEndpoint("flow-hub", "FLOW_HUB_HOST", "FLOW_HUB_KEY"),
    new InternalEndpoint("flow-hub-executor", "FLOW_HUB_EXECUTOR_HOST", "FLOW_HUB_EXECUTOR_KEY"),
    new InternalEndpoint("intelligent-hub", "INTELLIGENT_HUB_HOST", "INTELLIGENT_HUB_KEY"),
    new InternalEndpoint("webhook-hub", "WEBHOOK_HUB_HOST", "WEBHOOK_HUB_KEY"),
    new InternalEndpoint("crm-hub", "CRM_HUB_HOST", "CRM_HUB_KEY"),
    new Endpoint()
    {
        EndpointEndpoint = "/v1/flow-hub/Public/e/{webhookTriggerId}/{validationToken}",
        Method = Method.Post,
        OutputEncoding = OutputEncoding.NoOp,
        Backend = new[]
        {
            new Backend()
            {
                UrlPattern = "/Public/e",
                Method = Method.Post,
                Encoding = BackendEncoding.NoOp,
                Host = new[]
                {
                    (object) "{{ env \"FLOW_HUB_HOST\" }}"
                }
            }
        },
        ExtraConfig = new EndpointExtraConfig()
        {
            ModifierLuaProxy = new ModifierLuaEndpointClass()
            {
                Pre = "local r = request.load(); r:headers('X-Sleekflow-Workflow-Webhook-Trigger-Id', r:params('WebhookTriggerId')); r:headers('X-Sleekflow-Workflow-Validation-Token', r:params('ValidationToken')); r:headers('Content-Type', 'application/json')",
                Live = false,
                AllowOpenLibs = false
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/flow-hub/Public/e",
        Method = Method.Post,
        OutputEncoding = OutputEncoding.NoOp,
        InputHeaders = new[]
        {
            "Content-Type",
            "X-Sleekflow-Workflow-Webhook-Trigger-Id",
            "X-Sleekflow-Workflow-Validation-Token"
        },
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/Public/e",
                Method = Method.Post,
                Encoding = BackendEncoding.NoOp,
                Host = new[]
                {
                    (object)"{{ env \"FLOW_HUB_HOST\" }}"
                }
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/hubspot-integrator/HubspotCallback",
        Method = Method.Post,
        InputHeaders = new[]
        {
            "X-HubSpot-Signature"
        },
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/Public/HubspotCallback",
                Method = Method.Post,
                Encoding = BackendEncoding.Json,
                Host = new[]
                {
                    (object)" {{ env \"HUBSPOT_INTEGRATOR_HOST\" }} "
                }
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/salesforce-integrator/ApexCallback",
        Method = Method.Post,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/Public/ApexCallback",
                Method = Method.Post,
                Encoding = BackendEncoding.Json,
                Host = new[]
                {
                    (object)"{{ env \"SALESFORCE_INTEGRATOR_HOST\" }}"
                },
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/messaging-hub/WhatsappCloudApiWebhook",
        Method = Method.Post,
        OutputEncoding = OutputEncoding.NoOp,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/WhatsappCloudApiWebhook",
                Method = Method.Post,
                Encoding = BackendEncoding.NoOp,
                Host = new[]
                {
                    (object)"{{ env \"MESSAGING_HUB_HOST\" }}"
                },
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/messaging-hub/WhatsappCloudApiWebhook",
        Method = Method.Get,
        InputQueryStrings = new[]
        {
            "hub.mode",
            "hub.challenge",
            "hub.verify_token",
        },
        OutputEncoding = OutputEncoding.NoOp,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/WhatsappCloudApiWebhook",
                Method = Method.Get,
                Encoding = BackendEncoding.NoOp,
                Host = new[]
                {
                    (object)"{{ env \"MESSAGING_HUB_HOST\" }}"
                },
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/messaging-hub/MetaConversionApiSignalEvent/SendConversionApiSignalEvent",
        Method = Method.Post,
        OutputEncoding = OutputEncoding.NoOp,
        InputHeaders = new[]
        {
            "Content-Type",
            "X-Sleekflow-Facebook-Waba-Id"
        },
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/MetaConversionApiSignalEvent/SendConversionApiSignalEvent",
                Method = Method.Post,
                Encoding = BackendEncoding.NoOp,
                Host = new[]
                {
                    (object)"{{ env \"MESSAGING_HUB_HOST\" }}"
                }
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/messaging-hub/StripeWebhook/{type}",
        Method = Method.Post,
        OutputEncoding = OutputEncoding.NoOp,
        InputHeaders = new[]
        {
            "Stripe-Signature"
        },
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/StripeWebhook/{type}",
                Method = Method.Post,
                Encoding = BackendEncoding.NoOp,
                Host = new[]
                {
                    (object)"{{ env \"MESSAGING_HUB_HOST\" }}"
                },
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/l/{str}",
        Method = Method.Get,
        OutputEncoding = OutputEncoding.NoOp,
        InputHeaders = new[]
        {
            "*"
        },
        InputQueryStrings = new[]
        {
            "*"
        },
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/Public/l/{str}",
                Method = Method.Get,
                Encoding = BackendEncoding.NoOp,
                Host = new[]
                {
                    (object)"{{ env \"SHARE_HUB_HOST\" }}"
                },
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/q/{str}",
        Method = Method.Get,
        OutputEncoding = OutputEncoding.NoOp,
        InputHeaders = new[]
        {
            "*"
        },
        InputQueryStrings = new[]
        {
            "*"
        },
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/Public/q/{str}",
                Method = Method.Get,
                Encoding = BackendEncoding.NoOp,
                Host = new[]
                {
                    (object)"{{ env \"SHARE_HUB_HOST\" }}"
                },
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/commerce-hub/StripeWebhook/{type}",
        Method = Method.Post,
        OutputEncoding = OutputEncoding.NoOp,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/StripeWebhook/{type}",
                Method = Method.Post,
                Encoding = BackendEncoding.NoOp,
                Host = new[]
                {
                    (object)"{{ env \"COMMERCE_HUB_HOST\" }}"
                },
            }
        },
        InputHeaders = new[]
        {
            "Stripe-Signature"
        },
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/commerce-hub/StripeConnectOnboardingLinkRefresh",
        Method = Method.Post,
        OutputEncoding = OutputEncoding.NoOp,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/StripeConnectOnboardingLinkRefresh",
                Method = Method.Post,
                Encoding = BackendEncoding.NoOp,
                Host = new[]
                {
                    (object)"{{ env \"COMMERCE_HUB_HOST\" }}"
                },
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/commerce-hub/CustomCatalogs/GetCsvTemplate",
        Method = Method.Get,
        OutputEncoding = OutputEncoding.NoOp,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/CustomCatalogs/GetCsvTemplate",
                Method = Method.Get,
                Encoding = BackendEncoding.NoOp,
                Host = new[]
                {
                    (object)"{{ env \"COMMERCE_HUB_HOST\" }}"
                },
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/commerce-hub/CustomCatalogs/GetCsvTemplateSample",
        Method = Method.Get,
        OutputEncoding = OutputEncoding.NoOp,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/CustomCatalogs/GetCsvTemplateSample",
                Method = Method.Get,
                Encoding = BackendEncoding.NoOp,
                Host = new[]
                {
                    (object)"{{ env \"COMMERCE_HUB_HOST\" }}"
                },
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/commerce-hub/VtexWebhook/order",
        Method = Method.Post,
        InputHeaders = new[]
        {
            "Content-Type",
            "Traceparent",
            "X-Sleeklow-VtexAuthenticationId",
            "X-Sleeklow-CompanyId",
            "X-Sleekflow-Distributed-Invocation-Context"
        },
        OutputEncoding = OutputEncoding.NoOp,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/VtexWebhook/order",
                Method = Method.Post,
                Encoding = BackendEncoding.NoOp,
                Host = new[]
                {
                    (object)" {{ env \"COMMERCE_HUB_HOST\" }} "
                }
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/email-hub/gmail/auth/callback",
        Method = Method.Get,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/GmailAuthentication/GmailAuthCallback",
                Method = Method.Get,
                Encoding = BackendEncoding.Json,
                Host = new[]
                {
                    (object)" {{ env \"EMAIL_HUB_HOST\" }} "
                }
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/email-hub/outlook/auth/callback",
        Method = Method.Get,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/OutlookAuthentication/OutlookAuthCallback",
                Method = Method.Get,
                Encoding = BackendEncoding.Json,
                Host = new[]
                {
                    (object)" {{ env \"EMAIL_HUB_HOST\" }} "
                }
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/email-hub/gmail/email/receive",
        Method = Method.Post,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/GmailWebhook/NotifyOnGmailReceive",
                Method = Method.Post,
                Encoding = BackendEncoding.Json,
                Host = new[]
                {
                    (object)" {{ env \"EMAIL_HUB_HOST\" }} "
                }
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/email-hub/disposable/email/receive",
        Method = Method.Post,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/DisposableWebhook/NotifyOnDisposableReceive",
                Method = Method.Post,
                Encoding = BackendEncoding.Json,
                Host = new[]
                {
                    (object)" {{ env \"EMAIL_HUB_HOST\" }} "
                }
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/email-hub/outlook/subscription/callback",
        Method = Method.Post,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/OutlookSubscription/OutlookSubscriptionCallBack",
                Method = Method.Post,
                Encoding = BackendEncoding.Json,
                Host = new[]
                {
                    (object)" {{ env \"EMAIL_HUB_HOST\" }} "
                }
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/user-event-hub/ReliableMessage/negotiate",
        Method = Method.Post,
        OutputEncoding = OutputEncoding.NoOp,
        InputQueryStrings = new[]
        {
            "data"
        },
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/ReliableMessage/negotiate",
                Method = Method.Post,
                Encoding = BackendEncoding.NoOp,
                Host = new[]
                {
                    (object)" {{ env \"USER_EVENT_HUB_HOST\" }} "
                },
                ExtraConfig = new BackendExtraConfiguration()
                {
                    PluginHttpClient = new HttpClientPluginsSeeHttpsWwwKrakendIoDocsExtendingInjectingPlugins()
                    {
                        Name = "krakend-authentication-plugin",
                        KrakendAuthenticationPlugin = new HttpClientPluginsKrakendAuthenticationPluginConfig()
                        {
                            GetUserAuthDetailsUrl =
                                "{{ env \"TENANT_HUB_HOST\" }}{{ env \"TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS\" }}"
                        }
                    },
                }
            }
        },
        ExtraConfig = new EndpointExtraConfig
        {
            AuthValidator = Auth0ManagedEndpoint.ConstructAuthValidator(),
            ModifierLuaProxy = Auth0ManagedEndpoint.ConstructModifierLuaProxy(),
            QosRatelimitRouter = new RateLimiting
            {
                Capacity = 1000,
                ClientCapacity = 2,
                ClientMaxRate = 1,
                Every = "10s",
                Key = "Authorization",
                MaxRate = 0.0,
                Strategy = QosRatelimitServiceStrategy.Header
            }
        },
        InputHeaders = new string[]
        {
            "Content-Type",
            "X-Sleekflow-Roles",
            "X-Sleekflow-Email",
            "X-Sleekflow-User-Id",
            "X-Sleekflow-TenantHub-User-Id",
            "X-Sleekflow-Login-As-User",
            "X-Sleekflow-Connection-Strategy"
        },
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/user-event-hub/SignalRWebhook",
        Method = Method.Post,
        OutputEncoding = OutputEncoding.NoOp,
        InputHeaders = new[]
        {
            "*"
        },
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/SignalRWebhook",
                Method = Method.Post,
                Encoding = BackendEncoding.NoOp,
                Host = new[]
                {
                    (object)" {{ env \"USER_EVENT_HUB_HOST\" }} "
                }
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/tenant-hub/Geolocations/{method}",
        Method = Method.Post,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "Geolocations/{method}",
                Method = Method.Post,
                Host = new[]
                {
                    (object)"{{ env \"TENANT_HUB_HOST\" }}"
                },
            }
        },
        ExtraConfig = new EndpointExtraConfig
        {
            ModifierLuaProxy = new ModifierLuaEndpointClass
            {
                Sources = new object[]
                {
                    "lua/post_proxy.lua"
                },
                Post = "post_proxy()",
                Live = false,
                AllowOpenLibs = false
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/tenant-hub/Webhooks/Auth0/{method}",
        Method = Method.Post,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "Webhooks/Auth0/{method}",
                Method = Method.Post,
                Host = new[]
                {
                    (object)"{{ env \"TENANT_HUB_HOST\" }}"
                },
            }
        },
        ExtraConfig = new EndpointExtraConfig
        {
            ModifierLuaProxy = new ModifierLuaEndpointClass
            {
                Sources = new object[]
                {
                    "lua/post_proxy.lua"
                },
                Post = "post_proxy()",
                Live = false,
                AllowOpenLibs = false
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/tenant-hub/invite/{method}",
        Method = Method.Post,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "invite/{method}",
                Method = Method.Post,
                Host = new[]
                {
                    (object)"{{ env \"TENANT_HUB_HOST\" }}"
                },
            }
        },
        ExtraConfig = new EndpointExtraConfig
        {
            ModifierLuaProxy = new ModifierLuaEndpointClass
            {
                Sources = new object[]
                {
                    "lua/post_proxy.lua"
                },
                Post = "post_proxy()",
                Live = false,
                AllowOpenLibs = false
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/tenant-hub/Register/Companies/{method}",
        Method = Method.Post,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "Register/Companies/{method}",
                Method = Method.Post,
                Host = new[]
                {
                    (object)"{{ env \"TENANT_HUB_HOST\" }}"
                },
            }
        },
        ExtraConfig = new EndpointExtraConfig
        {
            AuthValidator = new JwtValidator
            {
                Alg = Algorithm.Rs256,
                Audience = new[]
                {
                    "AUTH_0_AUDIENCE"
                }.Select(a => "{{ env \"" + a + "\" }}").ToArray(),
                JwkUrl = "{{ env \"" + "AUTH_0_JWK_URL" + "\" }}",
                Cache = true,
                PropagateClaims = new[]
                {
                    new[]
                    {
                        "https://app.sleekflow.io/email",
                        "X-Sleekflow-Email"
                    },
                    new[]
                    {
                        "https://app.sleekflow.io/user_id",
                        "X-Sleekflow-User-Id"
                    },
                    new[]
                    {
                        "https://app.sleekflow.io/tenanthub_user_id",
                        "X-Sleekflow-TenantHub-User-Id"
                    },
                    new[]
                    {
                        "https://app.sleekflow.io/connection_strategy",
                        "X-Sleekflow-Connection-Strategy"
                    }
                },
            },
            ModifierLuaProxy = new ModifierLuaEndpointClass
            {
                Sources = new object[]
                {
                    "lua/post_proxy.lua",
                },
                Post = "post_proxy()",
                Live = false,
                AllowOpenLibs = false
            }
        },
        InputHeaders = new string[]
        {
            "Content-Type",
            "X-Sleekflow-User-Id",
            "X-Sleekflow-TenantHub-User-Id",
            "X-Sleekflow-Email",
            "X-Sleekflow-Connection-Strategy"
        },
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/webhook-hub/FacebookWebhook/Webhooks",
        Method = Method.Post,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/FacebookWebhook/Webhooks",
                Method = Method.Post,
                Encoding = BackendEncoding.Json,
                Host = new[]
                {
                    (object)"{{ env \"WEBHOOK_HUB_HOST\" }}"
                },
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/flow-hub-integrator/Zapier/Triggers/{triggerName}",
        Method = Method.Post,
        InputHeaders = new[]
        {
            "Content-Type",
            "X-Sleekflow-Zapier-Api-Key",
            "X-Sleekflow-Location"
        },
        OutputEncoding = OutputEncoding.NoOp,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/Public/Zapier/Triggers/{triggerName}",
                Method = Method.Post,
                Encoding = BackendEncoding.NoOp,
                Host = new[]
                {
                    (object)" {{ env \"FLOW_HUB_INTEGRATOR_HOST\" }} "
                }
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/flow-hub-integrator/Zapier/Triggers/{triggerName}",
        Method = Method.Delete,
        InputHeaders = new[]
        {
            "X-Sleekflow-Zapier-Api-Key",
            "X-Sleekflow-Location"
        },
        InputQueryStrings = new[]
        {
            "zap_id"
        },
        OutputEncoding = OutputEncoding.NoOp,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/Public/Zapier/Triggers/{triggerName}",
                Method = Method.Delete,
                Encoding = BackendEncoding.NoOp,
                Host = new[]
                {
                    (object)" {{ env \"FLOW_HUB_INTEGRATOR_HOST\" }} "
                }
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/flow-hub-integrator/Zapier/Triggers/{triggerName}",
        Method = Method.Get,
        InputHeaders = new[]
        {
            "X-Sleekflow-Zapier-Api-Key",
            "X-Sleekflow-Location"
        },
        InputQueryStrings = new[]
        {
            "schema_unique_name"
        },
        OutputEncoding = OutputEncoding.NoOp,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/Public/Zapier/Triggers/{triggerName}",
                Method = Method.Get,
                Encoding = BackendEncoding.NoOp,
                Host = new[]
                {
                    (object)" {{ env \"FLOW_HUB_INTEGRATOR_HOST\" }} "
                }
            }
        }
    },
    new Endpoint()
    {
        EndpointEndpoint = "/v1/internal-integration-hub/NetSuite/External/{triggerName}",
        Method = Method.Post,
        Backend = new[]
        {
            new Backend()
            {
                UrlPattern = "/NetSuite/External/{triggerName}",
                Method = Method.Post,
                Host = new[]
                {
                    (object) "{{ env \"INTERNAL_INTEGRATION_HUB_HOST\" }}"
                }
            }
        },
        ExtraConfig = new EndpointExtraConfig
        {
            ModifierLuaEndpoint = new ModifierLuaEndpointClass
            {
                Sources = new object[]
                {
                    "lua/pre_endpoint.lua"
                },
                Pre = "pre_endpoint('{{ env \"INTERNAL_INTEGRATION_HUB_NETSUITE_KEY\" }}')",
                Live = false,
                AllowOpenLibs = false
            },
            ModifierLuaProxy = new ModifierLuaEndpointClass
            {
                Sources = new object[]
                {
                    "lua/post_proxy.lua"
                },
                Post = "post_proxy()",
                Live = false,
                AllowOpenLibs = false
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/tiktok-ads-integrator/Webhooks",
        Method = Method.Post,
        InputHeaders = new[]
        {
            "Content-Type",
            "x-open-signature-type",
            "x-open-signature",
        },
        OutputEncoding = OutputEncoding.NoOp,
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/Public/Webhooks",
                Method = Method.Post,
                Encoding = BackendEncoding.NoOp,
                Host = new object[]
                {
                    "{{ env \"TIKTOK_ADS_INTEGRATOR_HOST\" }}"
                },
            }
        }
    }
};

foreach (var crmHubIntegrator in crmHubIntegrators)
{
    endpoints.AddRange(
        new List<Endpoint>
        {
            new Endpoint
            {
                EndpointEndpoint = "/v1/" + crmHubIntegrator.Group + "/AuthenticateCallback",
                Method = Method.Get,
                InputQueryStrings = new[]
                {
                    "code",
                    "state"
                },
                OutputEncoding = OutputEncoding.NoOp,
                Backend = new[]
                {
                    new Backend
                    {
                        UrlPattern = "/Public/AuthenticateCallback",
                        Method = Method.Get,
                        Encoding = BackendEncoding.NoOp,
                        Host = new object[]
                        {
                            "{{ env \"" + crmHubIntegrator.HostEnvName + "\" }}"
                        },
                    }
                }
            },
            new Endpoint
            {
                EndpointEndpoint = "/v1/" + crmHubIntegrator.Group + "/internals/{method}",
                Method = Method.Post,
                Backend = new[]
                {
                    new Backend
                    {
                        UrlPattern = "/Internals/{method}",
                        Method = Method.Post,
                        Host = new object[]
                        {
                            "{{ env \"" + crmHubIntegrator.HostEnvName + "\" }}"
                        },
                    }
                },
                ExtraConfig = new EndpointExtraConfig
                {
                    ModifierLuaEndpoint = new ModifierLuaEndpointClass
                    {
                        Sources = new object[]
                        {
                            "lua/pre_endpoint.lua"
                        },
                        Pre = "pre_endpoint('{{ env \"" + crmHubIntegrator.InternalsKeyEnvName + "\" }}')",
                        Live = false,
                        AllowOpenLibs = false
                    },
                    ModifierLuaProxy = new ModifierLuaEndpointClass
                    {
                        Sources = new object[]
                        {
                            "lua/post_proxy.lua"
                        },
                        Post = "post_proxy()",
                        Live = false,
                        AllowOpenLibs = false
                    }
                }
            }
        });
}

// Attaching the endpoints to the main list
endpoints.AddRange(managedEndpoints);
endpoints.AddRange(auth0ManagedEndpoints);
endpoints.AddRange(publicApiGatewayEndpoints);

Console.WriteLine("Exporting regional Krakend.json");

// Exporting the krakend.json
var krakendExporter = new KrakendExporter("krakend.json", endpoints);
krakendExporter.Export();

// Validating the krakend.json
var krakendValidator = new KrakendValidator(healthz, endpoints);
krakendValidator.Validate();

var internalHealthz = new List<Healthz>
{
    new Healthz("USER_EVENT_HUB_HOST", "user-event-hub"),
};

var internalEndpoints = new List<Endpoint>
{
    new Endpoint
    {
        EndpointEndpoint = "/v1/healthz",
        Method = Method.Get,
        Backend = internalHealthz
            .Select(
                h => new Backend
                {
                    UrlPattern = "/healthz/liveness",
                    Method = Method.Get,
                    Encoding = BackendEncoding.String,
                    Host = new object[]
                    {
                        "{{ env \"" + h.HostEnvName + "\" }}"
                    },
                    Group = h.Group
                })
            .ToArray(),
        ExtraConfig = new EndpointExtraConfig
        {
            QosRatelimitRouter = new RateLimiting
            {
                MaxRate = 100, ClientMaxRate = 5, Strategy = QosRatelimitServiceStrategy.Ip, Key = "X-Azure-ClientIP"
            }
        }
    },
    new Endpoint
    {
        EndpointEndpoint = "/v1/user-event-hub/SignalRWebhook",
        Method = Method.Post,
        OutputEncoding = OutputEncoding.NoOp,
        InputHeaders = new[]
        {
            "*"
        },
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/SignalRWebhook",
                Method = Method.Post,
                Encoding = BackendEncoding.NoOp,
                Host = new[]
                {
                    (object)" {{ env \"USER_EVENT_HUB_HOST\" }} "
                }
            }
        }
    }
};

Console.WriteLine("Exporting regional internal Krakend.json");

// Exporting the krakend.json
var internalKrakendExporter = new KrakendExporter("krakend.internal.json", internalEndpoints, false);
internalKrakendExporter.Export();

var globalHealthz = new List<Healthz>
{
    new Healthz("TENANT_HUB_HOST", "tenant-hub"),
};

var globalEndpoints = new List<Endpoint>
{
    new Endpoint
    {
        EndpointEndpoint = "/v1/healthz",
        Method = Method.Get,
        Backend = globalHealthz
            .Select(
                h => new Backend
                {
                    UrlPattern = "/healthz/liveness",
                    Method = Method.Get,
                    Encoding = BackendEncoding.String,
                    Host = new object[]
                    {
                        "{{ env \"" + h.HostEnvName + "\" }}"
                    },
                    Group = h.Group
                })
            .ToArray(),
        ExtraConfig = new EndpointExtraConfig
        {
            QosRatelimitRouter = new RateLimiting
            {
                MaxRate = 100, ClientMaxRate = 5, Strategy = QosRatelimitServiceStrategy.Ip, Key = "X-Azure-ClientIP"
            }
        }
    },
};

var globalManagedEndpoints = new List<ManagedEndpoint>
{
    new ManagedEndpoint("tenant-hub", "Companies", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "Users", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "EnabledFeatures", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "Roles", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "Migrations", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "Features", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "IpWhitelists", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "management/Companies", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "management/Users", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "management/EnabledFeatures", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "management/Roles", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "management/Features", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "management/IpWhitelists", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "management/Rbac", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new ManagedEndpoint("tenant-hub", "management/ImportUser", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
};

var globalAuth0ManagedEndpoints = new List<Auth0ManagedEndpoint>()
{
    new Auth0ManagedEndpoint("tenant-hub", "UserWorkspaces", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "Workspaces", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/Companies", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/Users", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/Plans", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/Roles", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/Features", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/PlanDefinitions", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/Subscriptions", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/UserWorkspaces", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/IpWhitelists", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/ExperimentalFeatures", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/Rbac", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/Blobs", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
    new Auth0ManagedEndpoint("tenant-hub", "authorized/EnabledFeatures", "TENANT_HUB_HOST", "TENANT_HUB_KEY"),
};


globalEndpoints.AddRange(globalManagedEndpoints);
globalEndpoints.AddRange(globalAuth0ManagedEndpoints);

globalEndpoints.AddRange(
    new List<Endpoint>
    {
        new Endpoint
        {
            EndpointEndpoint = "/v1/tenant-hub/Geolocations/{method}",
            Method = Method.Post,
            Backend = new[]
            {
                new Backend
                {
                    UrlPattern = "Geolocations/{method}",
                    Method = Method.Post,
                    Host = new[]
                    {
                        (object)"{{ env \"TENANT_HUB_HOST\" }}"
                    },
                }
            },
            ExtraConfig = new EndpointExtraConfig
            {
                ModifierLuaProxy = new ModifierLuaEndpointClass
                {
                    Sources = new object[]
                    {
                        "lua/post_proxy.lua"
                    },
                    Post = "post_proxy()",
                    Live = false,
                    AllowOpenLibs = false
                }
            }
        },
        new Endpoint
        {
            EndpointEndpoint = "/v1/tenant-hub/Webhooks/Auth0/{method}",
            Method = Method.Post,
            Backend = new[]
            {
                new Backend
                {
                    UrlPattern = "Webhooks/Auth0/{method}",
                    Method = Method.Post,
                    Host = new[]
                    {
                        (object)"{{ env \"TENANT_HUB_HOST\" }}"
                    },
                }
            },
            ExtraConfig = new EndpointExtraConfig
            {
                ModifierLuaProxy = new ModifierLuaEndpointClass
                {
                    Sources = new object[]
                    {
                        "lua/post_proxy.lua"
                    },
                    Post = "post_proxy()",
                    Live = false,
                    AllowOpenLibs = false
                }
            }
        },
        new Endpoint
        {
            EndpointEndpoint = "/v1/tenant-hub/invite/{method}",
            Method = Method.Post,
            Backend = new[]
            {
                new Backend
                {
                    UrlPattern = "invite/{method}",
                    Method = Method.Post,
                    Host = new[]
                    {
                        (object)"{{ env \"TENANT_HUB_HOST\" }}"
                    },
                }
            },
            ExtraConfig = new EndpointExtraConfig
            {
                ModifierLuaProxy = new ModifierLuaEndpointClass
                {
                    Sources = new object[]
                    {
                        "lua/post_proxy.lua"
                    },
                    Post = "post_proxy()",
                    Live = false,
                    AllowOpenLibs = false
                }
            }
        },
        new Endpoint
        {
            EndpointEndpoint = "/v1/tenant-hub/Register/Companies/{method}",
            Method = Method.Post,
            Backend = new[]
            {
                new Backend
                {
                    UrlPattern = "Register/Companies/{method}",
                    Method = Method.Post,
                    Host = new[]
                    {
                        (object)"{{ env \"TENANT_HUB_HOST\" }}"
                    },
                }
            },
            ExtraConfig = new EndpointExtraConfig
            {
                AuthValidator = new JwtValidator
                {
                    Alg = Algorithm.Rs256,
                    Audience = new[]
                    {
                        "AUTH_0_AUDIENCE"
                    }.Select(a => "{{ env \"" + a + "\" }}").ToArray(),
                    JwkUrl = "{{ env \"" + "AUTH_0_JWK_URL" + "\" }}",
                    Cache = true,
                    PropagateClaims = new[]
                    {
                        new[]
                        {
                            "https://app.sleekflow.io/email",
                            "X-Sleekflow-Email"
                        },
                        new[]
                        {
                            "https://app.sleekflow.io/user_id",
                            "X-Sleekflow-User-Id"
                        },
                        new[]
                        {
                            "https://app.sleekflow.io/tenanthub_user_id",
                            "X-Sleekflow-TenantHub-User-Id"
                        },
                        new[]
                        {
                            "https://app.sleekflow.io/connection_strategy",
                            "X-Sleekflow-Connection-Strategy"
                        }
                    },
                },
                ModifierLuaProxy = new ModifierLuaEndpointClass
                {
                    Sources = new object[]
                    {
                        "lua/post_proxy.lua",
                    },
                    Post = "post_proxy()",
                    Live = false,
                    AllowOpenLibs = false
                }
            },
            InputHeaders = new string[]
            {
                "Content-Type",
                "X-Sleekflow-User-Id",
                "X-Sleekflow-TenantHub-User-Id",
                "X-Sleekflow-Email",
                "X-Sleekflow-Connection-Strategy"
            },
        },
    });

Console.WriteLine("Exporting global Krakend.json");

// Exporting the krakend.json
var globalKrakendExporter = new KrakendExporter("krakend.global.json", globalEndpoints);
globalKrakendExporter.Export();

// Validating the krakend.json
var globalKrakendValidator = new KrakendValidator(
    healthz,
    globalEndpoints,
    new List<string>
    {
        ServiceNames.TenantHub
    });
globalKrakendValidator.Validate();