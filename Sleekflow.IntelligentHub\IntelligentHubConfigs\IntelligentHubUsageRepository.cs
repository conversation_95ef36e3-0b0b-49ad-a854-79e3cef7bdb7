﻿using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.Salesforce;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Snapshots;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.IntelligentHubConfigs;

public interface IIntelligentHubUsageRepository : IRepository<IntelligentHubUsage>
{
    Task<int> CountFeatureUsageAsync(
        string featureName,
        string sleekflowCompanyId,
        IntelligentHubUsageFilter? intelligentHubUsageFilter);

    Task<int> CountAgentRecommendReplyFeatureUsageAsync(
        string sleekflowCompanyId,
        List<string> collaborationMode,
        IntelligentHubUsageFilter? intelligentHubUsageFilter);

    Task<(List<IntelligentHubUsage> IntelligentHubUsages, string? NextContinuationToken)>
        GetAgentRecordUsagesAsync(
            string sleekflowCompanyId,
            string agentId,
            List<string> snapshotTypes,
            string? continuationToken,
            int limit);
}

public class IntelligentHubUsageRepository
    : BaseRepository<IntelligentHubUsage>, IIntelligentHubUsageRepository, IScopedService
{
    public IntelligentHubUsageRepository(
        ILogger<IntelligentHubUsageRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public async Task<int> CountFeatureUsageAsync(
        string featureName,
        string sleekflowCompanyId,
        IntelligentHubUsageFilter? intelligentHubUsageFilter)
    {
        var excludedFeatureName = new List<string>
        {
            PriceableFeatures.AgentRecommendReply,
            PriceableFeatures.Summary,
            PriceableFeatures.Scoring,
            PriceableFeatures.HandoffTeam
        };

        var queryResults = await GetObjectsAsync<Dictionary<string, int>>(
            new QueryDefinition(
                    $"""
                     SELECT COUNT(1) count
                     FROM c
                     WHERE c.sleekflow_company_id = @sleekflowCompanyId {(
                         featureName == PriceableFeatures.AiFeaturesTotalUsage
                             ? "AND NOT ARRAY_CONTAINS(@excludedFeatureName, c.feature_name) "
                             : "AND c.feature_name = @featureName")} {(
                             intelligentHubUsageFilter is { FromDateTime: not null }
                                 ? "AND c.created_at >= @fromDateTime "
                                 : string.Empty)} {(
                                 intelligentHubUsageFilter is { ToDateTime: not null }
                                     ? "AND c.created_at <= @toDateTime "
                                     : string.Empty)}
                     """)
                .WithParameter("@excludedFeatureName", excludedFeatureName)
                .WithParameter("@featureName", featureName)
                .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
                .WithParameter("@fromDateTime", intelligentHubUsageFilter?.FromDateTime)
                .WithParameter("@toDateTime", intelligentHubUsageFilter?.ToDateTime));

        return queryResults[0]["count"];
    }

    public async Task<int> CountAgentRecommendReplyFeatureUsageAsync(
        string sleekflowCompanyId,
        List<string> collaborationMode,
        IntelligentHubUsageFilter? intelligentHubUsageFilter)
    {
        var queryResults = await GetObjectsAsync<Dictionary<string, int>>(
            new QueryDefinition(
                    $"""
                     SELECT COUNT(1) count
                     FROM c
                     WHERE c.sleekflow_company_id = @sleekflowCompanyId
                         AND c.feature_name = @featureName
                         AND ARRAY_CONTAINS(@collaborationMode, c.intelligent_hub_usage_snapshot.collaboration_mode) {(
                             intelligentHubUsageFilter is { FromDateTime: not null }
                                 ? "AND c.created_at >= @fromDateTime "
                                 : string.Empty)} {(
                                 intelligentHubUsageFilter is { ToDateTime: not null }
                                     ? "AND c.created_at <= @toDateTime "
                                     : string.Empty)}
                     """)
                .WithParameter("@featureName", PriceableFeatures.AgentRecommendReply)
                .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
                .WithParameter("@collaborationMode", collaborationMode)
                .WithParameter("@fromDateTime", intelligentHubUsageFilter?.FromDateTime)
                .WithParameter("@toDateTime", intelligentHubUsageFilter?.ToDateTime));

        return queryResults[0]["count"];
    }

    public async Task<(List<IntelligentHubUsage> IntelligentHubUsages, string? NextContinuationToken)>
        GetAgentRecordUsagesAsync(
            string sleekflowCompanyId,
            string agentId,
            List<string> snapshotTypes,
            string? continuationToken,
            int limit)
    {
        if (snapshotTypes.Count == 0)
        {
            throw new ArgumentException("At least one snapshot type must be provided", nameof(snapshotTypes));
        }

        var typeCondition = string.Join(
            " OR ",
            snapshotTypes.Select((_, i) => $"c.intelligent_hub_usage_snapshot['$type'] = @snapshot_type_{i}"));

        var queryDefinition = snapshotTypes
            .Select(
                (type, i) => new
                {
                    Name = $"@snapshot_type_{i}", Value = type
                })
            .Aggregate(
                new QueryDefinition(
                        $"""
                         SELECT *
                         FROM c
                         WHERE c.sleekflow_company_id = @sleekflow_company_id
                             AND c.intelligent_hub_usage_snapshot.agent_id = @agent_id
                             AND ({typeCondition})
                         ORDER BY c.created_at DESC
                         """)
                    .WithParameter("@sleekflow_company_id", sleekflowCompanyId)
                    .WithParameter("@agent_id", agentId),
                (query, param) => query.WithParameter(param.Name, param.Value));

        return await GetContinuationTokenizedObjectsAsync(queryDefinition, continuationToken, limit);
    }
}