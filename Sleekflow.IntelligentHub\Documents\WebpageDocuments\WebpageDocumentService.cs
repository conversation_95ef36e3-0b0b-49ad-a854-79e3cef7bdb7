using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.IntelligentHub.WebCrawlingSessions;

namespace Sleekflow.IntelligentHub.Documents.WebpageDocuments;

public interface IWebpageDocumentService
{
    Task CreateWebpageDocumentAsync(
        string sleekflowCompanyId,
        Dictionary<string, List<string>> sessionIdToUrls,
        List<string> agentIds,
        string staffId);
}

public class WebpageDocumentService : IWebpageDocumentService, IScopedService
{
    private readonly ILogger<WebpageDocumentService> _logger;
    private readonly IWebCrawlingSessionService _webCrawlingSessionService;
    private readonly IWebpageDocumentRepository _webpageDocumentRepository;
    private readonly IIdService _idService;
    private readonly ICompanyAgentConfigService _companyAgentConfigService;
    private readonly IKnowledgeBaseIngestionService _knowledgeBaseIngestionService;
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;
    private readonly IIntelligentHubCharacterCountService _intelligentHubCharacterCountService;

    public WebpageDocumentService(
        ILogger<WebpageDocumentService> logger,
        IWebCrawlingSessionService webCrawlingSessionService,
        IWebpageDocumentRepository webpageDocumentRepository,
        IIdService idService,
        ICompanyAgentConfigService companyAgentConfigService,
        IKnowledgeBaseIngestionService knowledgeBaseIngestionService,
        IIntelligentHubConfigService intelligentHubConfigService,
        IIntelligentHubUsageService intelligentHubUsageService,
        IIntelligentHubCharacterCountService intelligentHubCharacterCountService)
    {
        _logger = logger;
        _webCrawlingSessionService = webCrawlingSessionService;
        _webpageDocumentRepository = webpageDocumentRepository;
        _idService = idService;
        _companyAgentConfigService = companyAgentConfigService;
        _knowledgeBaseIngestionService = knowledgeBaseIngestionService;
        _intelligentHubConfigService = intelligentHubConfigService;
        _intelligentHubUsageService = intelligentHubUsageService;
        _intelligentHubCharacterCountService = intelligentHubCharacterCountService;
    }

    public async Task CreateWebpageDocumentAsync(
        string sleekflowCompanyId,
        Dictionary<string, List<string>> sessionIdToUrls,
        List<string> agentIds,
        string staffId)
    {
        _logger.LogInformation(
            "[WebpageDocument] Creating webpage document for company {CompanyId} with {UrlCount} URLs and {AgentCount} agents",
            sleekflowCompanyId,
            sessionIdToUrls.Sum(s => s.Value.Count),
            agentIds.Count);

        // Validate agent IDs and create agent assignments
        var agentAssignments = await CreateAgentAssignmentsAsync(sleekflowCompanyId, agentIds);

        var currentTime = DateTimeOffset.UtcNow;

        // Validate that there are URLs to process across all sessions
        var totalUrlCount = sessionIdToUrls.Values.Sum(urls => urls.Count);
        if (totalUrlCount == 0)
        {
            throw new Exception("No URLs selected across all sessions.");
        }

        List<WebpageDocument> webpageDocuments = [];
        var intelligentHubConfig =
            await _intelligentHubConfigService.GetIntelligentHubConfigAsync(sleekflowCompanyId);

        if (intelligentHubConfig == null)
        {
            throw new Exception("Intelligent Hub config not found");
        }

        // Check character count limits for each agent
        var characterCountLimit = _intelligentHubUsageService.GetFeatureTotalUsageLimit(
            intelligentHubConfig,
            PriceableFeatures.AiAgentsKnowledgeBaseTotalCharacterCount);

        // Pre-fetch character counts for all agents to optimize limit checking
        var agentCharacterCounts = new Dictionary<string, int>();
        var regularAgentIds = agentIds.Where(id => id != CompanyAgentTypes.SmartReply).ToList();

        if (regularAgentIds.Any())
        {
            var characterCountTasks = regularAgentIds
                .Select(
                    async agentId => new
                    {
                        AgentId = agentId,
                        CharacterCount = await _intelligentHubCharacterCountService.GetCharacterCountForAgent(
                            sleekflowCompanyId,
                            agentId)
                    });

            var characterCountResults = await Task.WhenAll(characterCountTasks);
            foreach (var result in characterCountResults)
            {
                agentCharacterCounts[result.AgentId] = result.CharacterCount;
            }
        }

        // Process each session separately
        foreach (var sessionIdToUrl in sessionIdToUrls)
        {
            var webCrawlingSessionId = sessionIdToUrl.Key;
            var selectedUrls = sessionIdToUrl.Value;

            // Get the web crawling session for this specific session ID
            var webCrawlingSession =
                await _webCrawlingSessionService.GetWebCrawlingSessionAsync(sleekflowCompanyId, webCrawlingSessionId);

            foreach (var crawlingResult in webCrawlingSession.CrawlingResults
                         .Where(crawlingResult => selectedUrls.Contains(crawlingResult.Url)))
            {
                // Check character count limits before creating the document
                foreach (var agentId in regularAgentIds)
                {
                    var currentCharacterCount = agentCharacterCounts[agentId];
                    if (currentCharacterCount + crawlingResult.CharacterCount > characterCountLimit)
                    {
                        throw new SfUserFriendlyException(
                            $"Agent {agentId} would exceed the character count limit of {characterCountLimit}. Current count: {currentCharacterCount}, Adding: {crawlingResult.CharacterCount}");
                    }
                }

                // Create the WebpageDocument
                var webpageDocument = new WebpageDocument(
                    _idService.GetId(SysTypeNames.WebpageDocument),
                    sleekflowCompanyId,
                    string.Empty,
                    crawlingResult.Url,
                    crawlingResult.WebpageTitle,
                    crawlingResult.CharacterCount,
                    new Dictionary<string, object?>(),
                    ProcessFileDocumentStatuses.NotConverted,
                    0.0,
                    agentAssignments,
                    currentTime,
                    currentTime,
                    staffId,
                    new DebugTimestamps());

                await _webpageDocumentRepository.CreateAsync(
                    webpageDocument,
                    sleekflowCompanyId);

                // Update running totals for all agents
                foreach (var agentId in regularAgentIds)
                {
                    agentCharacterCounts[agentId] += crawlingResult.CharacterCount;
                }

                webpageDocuments.Add(webpageDocument);
            }
        }

        if (agentIds.Any())
        {
            await _knowledgeBaseIngestionService.StartKnowledgeBaseBatchIngestion(
                sleekflowCompanyId,
                webpageDocuments.Select(document => document.Id).ToArray());
        }

        _logger.LogInformation(
            "Successfully created webpage document for company {CompanyId}",
            sleekflowCompanyId);
    }

    private async Task<List<AgentAssignment>> CreateAgentAssignmentsAsync(
        string sleekflowCompanyId,
        List<string> agentIds)
    {
        if (!agentIds.Any())
        {
            return new List<AgentAssignment>();
        }

        // Validate agent IDs exist (excluding Smart Reply which is special)
        await ValidateAgentIdsAsync(sleekflowCompanyId, agentIds);

        // Separate SmartReply from other agent IDs
        var smartReplyIds = agentIds.Where(id => id == CompanyAgentTypes.SmartReply).ToList();
        var otherAgentIds = agentIds.Where(id => id != CompanyAgentTypes.SmartReply).ToList();

        var agentAssignments = new List<AgentAssignment>();

        // Handle regular agent IDs
        if (otherAgentIds.Any())
        {
            // Fetch agent configs concurrently for regular agents
            var agentConfigTasks = otherAgentIds
                .Select(id => _companyAgentConfigService.GetOrDefaultAsync(id, sleekflowCompanyId));
            var agentConfigs = await Task.WhenAll(agentConfigTasks);

            // Map agent configs to agent assignments, initializing RAG status to Pending
            var regularAgentAssignments = agentConfigs
                .Where(config => config != null) // Filter out null configs (invalid IDs)
                .Select(config => new AgentAssignment(config!.Id, RagStatus.Pending, 0.0))
                .ToList();

            agentAssignments.AddRange(regularAgentAssignments);
        }

        // Handle SmartReply special case
        if (smartReplyIds.Any())
        {
            // Add SmartReply agent assignment directly without querying config service
            agentAssignments.Add(
                new AgentAssignment(
                    CompanyAgentTypes.SmartReply,
                    RagStatus.Pending,
                    0.0));
        }

        return agentAssignments;
    }

    private async Task ValidateAgentIdsAsync(string sleekflowCompanyId, List<string> agentIds)
    {
        // Filter out SmartReply which is a special case
        var regularAgentIds = agentIds.Where(id => id != CompanyAgentTypes.SmartReply).ToList();

        if (!regularAgentIds.Any())
        {
            return; // No regular agent IDs to validate
        }

        // Fetch agent configs concurrently to validate they exist
        var agentConfigTasks = regularAgentIds
            .Select(id => _companyAgentConfigService.GetOrDefaultAsync(id, sleekflowCompanyId));
        var agentConfigs = await Task.WhenAll(agentConfigTasks);

        // Check for invalid agent IDs
        var invalidAgentIds = new List<string>();
        for (int i = 0; i < regularAgentIds.Count; i++)
        {
            if (agentConfigs[i] == null)
            {
                invalidAgentIds.Add(regularAgentIds[i]);
            }
        }

        if (invalidAgentIds.Any())
        {
            throw new SfNotFoundObjectException(
                $"The following agent IDs do not exist: {string.Join(", ", invalidAgentIds)}");
        }
    }
}