namespace Sleekflow.Infras.Constants;

public static class ServiceNames
{
    public const string AuditHub = "AuditHub";
    public const string ApiGateway = "ApiGateway";
    public const string CommerceHub = "CommerceHub";
    public const string CrmHub = "CrmHub";
    public const string SalesforceIntegrator = "SalesforceIntegrator";
    public const string SfmcJourneyBuilderCustomActivity = "SfmcJourneyBuilderCustomActivity";
    public const string HubspotIntegrator = "HubspotIntegrator";
    public const string Dynamics365Integrator = "Dynamics365Integrator";
    public const string GoogleSheetsIntegrator = "GoogleSheetsIntegrator";
    public const string ZohoIntegrator = "ZohoIntegrator";
    public const string TikTokAdsIntegrator = "TikTokAdsIntegrator";
    public const string EmailHub = "EmailHub";
    public const string FlowHub = "FlowHub";
    public const string FlowHubExecutor = "FlowHubExecutor";
    public const string FlowHubIntegrator = "FlowHubIntegrator";
    public const string FlowHubOnWorkflowExecutionEndedPostProcessRequestedEventConsumer = "FlowHubOnWorkflowExecutionEndedPostProcessRequestedEventConsumer";
    public const string MessagingHub = "MessagingHub";
    public const string IntelligentHub = "IntelligentHub";
    public const string PublicApiGateway = "PublicApiGateway";
    public const string ShareHub = "ShareHub";
    public const string TenantHub = "TenantHub";
    public const string WebhookHub = "WebhookHub";
    public const string WebhookBridge = "WebhookBridge";
    public const string UserEventHub = "UserEventHub";
    public const string UserEventAnalyticsHub = "UserEventAnalyticsHub";
    public const string SupportHub = "SupportHub";
    public const string TicketingHub = "TicketingHub";
    public const string Scheduler = "Scheduler";
    public const string InternalGateway = "InternalGateway";
    public const string InternalIntegrationHub = "InternalIntegrationHub";
    public const string OpenPolicyAgent = "OpenPolicyAgent";
    public const string OpenPolicyAdministrationLayer = "OpenPolicyAdministrationLayer";
    public const string IntelligentHubLightRag = "IntelligentHubLightRag";
    public const string IntelligentHubPremium = "IntelligentHubPremium";

    public static string GetShortName(string serviceName)
    {
        return serviceName switch
        {
            AuditHub => "ah",
            ApiGateway => "apigw",
            CommerceHub => "commh",
            CrmHub => "crm-hub",
            SalesforceIntegrator => "sf-in",
            SfmcJourneyBuilderCustomActivity => "sfmc-jb-ca",
            HubspotIntegrator => "hs-in",
            Dynamics365Integrator => "d365-in",
            GoogleSheetsIntegrator => "gs-in",
            ZohoIntegrator => "zh-in",
            TikTokAdsIntegrator => "tta-in",
            EmailHub => "eh",
            FlowHub => "fh",
            FlowHubExecutor => "fhe",
            FlowHubIntegrator => "fh-in",
            MessagingHub => "mh",
            IntelligentHub => "ih",
            PublicApiGateway => "pagw",
            ShareHub => "sh",
            TenantHub => "th",
            WebhookHub => "wh",
            WebhookBridge => "whb",
            UserEventHub => "ueh",
            UserEventAnalyticsHub => "ueah",
            SupportHub => "suph",
            TicketingHub => "tih",
            Scheduler => "sch",
            InternalGateway => "igw",
            InternalIntegrationHub => "iih",
            OpenPolicyAgent => "opa",
            OpenPolicyAdministrationLayer => "opal",
            IntelligentHubLightRag => "ihlr",
            _ => throw new Exception("ServiceNames")
        };
    }

    public static string GetSleekflowPrefixedShortName(string serviceName)
    {
        return $"sleekflow-{GetShortName(serviceName)}";
    }

    public static string GetWorkerName(string serviceName)
    {
        return $"{serviceName}Worker";
    }
}