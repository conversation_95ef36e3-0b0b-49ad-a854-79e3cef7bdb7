using Newtonsoft.Json;

namespace Sleekflow.Models.WorkflowSteps;

public class SetAgentFirstWorkflowPublishedTimestampResponse
{
    [JsonProperty("success")]
    public bool Success { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonConstructor]
    public SetAgentFirstWorkflowPublishedTimestampResponse(bool success, string message)
    {
        Success = success;
        Message = message;
    }
}