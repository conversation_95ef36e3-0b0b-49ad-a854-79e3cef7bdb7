using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Blobs;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.KnowledgeBaseEntries;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.Models.Documents.Statistics;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments;

public interface IFileDocumentService
{
    Task<FileDocument> CreateOrGetDocumentAsync(
        string sleekflowCompanyId,
        string blobId,
        string blobType,
        string contentType,
        DocumentStatistics documentStatistics,
        Dictionary<string, object?> metadata,
        string languageIsoCode,
        string fileDocumentProcessStatus,
        string fileName,
        string uploadedBy,
        List<string> assignToAgentIds);

    Task<FileDocument> CreateAndGetDocumentAsync(
        FileDocument fileDocument);

    Task<FileDocument?> GetDocumentByBlobIdAsync(
        string sleekflowCompanyId,
        string blobId);

    Task DeleteFileDocumentAsync(string documentId, string sleekflowCompanyId, string blobId);

    Task<FileDocument> PatchFileDocumentProcessStatusByBlobIdAsync(
        string sleekflowCompanyId,
        string blobId,
        string newStatus);

    Task<int> GetUsedPagesAsync(string sleekflowCompanyId);
}

public class FileDocumentService : IFileDocumentService, IScopedService
{
    private readonly IFileDocumentRepository _fileDocumentRepository;
    private readonly ICompanyAgentConfigService _companyAgentConfigService;
    private readonly IKnowledgeBaseEntryService _knowledgeBaseEntryService;
    private readonly IIdService _idService;
    private readonly IBlobService _blobService;
    private readonly IBlobUploadHistoryService _blobUploadHistoryService;
    private readonly ILogger<FileDocumentService> _logger;

    public FileDocumentService(
        IFileDocumentRepository fileDocumentRepository,
        ICompanyAgentConfigService companyAgentConfigService,
        IKnowledgeBaseEntryService knowledgeBaseEntryService,
        IIdService idService,
        IBlobService blobService,
        IBlobUploadHistoryService blobUploadHistoryService,
        ILogger<FileDocumentService> logger)
    {
        _fileDocumentRepository = fileDocumentRepository;
        _companyAgentConfigService = companyAgentConfigService;
        _knowledgeBaseEntryService = knowledgeBaseEntryService;
        _idService = idService;
        _blobService = blobService;
        _blobUploadHistoryService = blobUploadHistoryService;
        _logger = logger;
    }

    private async Task ValidateAgentIds(string sleekflowCompanyId, List<string> agentIds)
    {
        // Filter out SmartReply which is a special case
        var regularAgentIds = agentIds.Where(id => id != CompanyAgentTypes.SmartReply).ToList();

        if (!regularAgentIds.Any())
        {
            return; // No regular agent IDs to validate
        }

        // Fetch agent configs concurrently to validate they exist
        var agentConfigTasks =
            regularAgentIds.Select(id => _companyAgentConfigService.GetOrDefaultAsync(id, sleekflowCompanyId));
        var agentConfigs = await Task.WhenAll(agentConfigTasks);

        // Check for invalid agent IDs
        var invalidAgentIds = new List<string>();
        for (int i = 0; i < regularAgentIds.Count; i++)
        {
            if (agentConfigs[i] == null)
            {
                invalidAgentIds.Add(regularAgentIds[i]);
            }
        }

        if (invalidAgentIds.Any())
        {
            throw new SfNotFoundObjectException(
                $"The following agent IDs do not exist: {string.Join(", ", invalidAgentIds)}");
        }
    }

    public async Task<FileDocument> CreateOrGetDocumentAsync(
        string sleekflowCompanyId,
        string blobId,
        string blobType,
        string contentType,
        DocumentStatistics documentStatistics,
        Dictionary<string, object?> metadata,
        string languageIsoCode,
        string fileDocumentProcessStatus,
        string fileName,
        string uploadedBy,
        List<string> assignToAgentIds)
    {
        var documents = await _fileDocumentRepository.GetObjectsAsync(c =>
            c.SleekflowCompanyId == sleekflowCompanyId
            && c.BlobId == blobId);
        if (documents.Count != 0)
        {
            return documents[0];
        }

        // Validate that agent IDs exist
        await ValidateAgentIds(sleekflowCompanyId, assignToAgentIds);

        // Separate SmartReply from other agent IDs
        var smartReplyIds = assignToAgentIds.Where(id => id == CompanyAgentTypes.SmartReply).ToList();
        var otherAgentIds = assignToAgentIds.Where(id => id != CompanyAgentTypes.SmartReply).ToList();

        // Create agent assignments list
        var agentAssignments = new List<AgentAssignment>();

        // Handle regular agent IDs
        if (otherAgentIds.Any())
        {
            // Fetch agent configs concurrently for regular agents
            var agentConfigTasks =
                otherAgentIds.Select(id => _companyAgentConfigService.GetOrDefaultAsync(id, sleekflowCompanyId));
            var agentConfigs = await Task.WhenAll(agentConfigTasks);

            // Map agent configs to agent assignments, initializing RAG status to Pending
            var regularAgentAssignments = agentConfigs
                .Where(config => config != null) // Filter out null configs (invalid IDs)
                .Select(config => new AgentAssignment(config!.Id, RagStatus.Pending, 0.0))
                .ToList();

            agentAssignments.AddRange(regularAgentAssignments);
        }

        // Handle SmartReply special case
        if (smartReplyIds.Any())
        {
            // Add SmartReply agent assignment directly without querying config service
            agentAssignments.Add(
                new AgentAssignment(
                    CompanyAgentTypes.SmartReply,
                    RagStatus.Pending,
                    0.0));
        }

        var createdDocument = await _fileDocumentRepository.CreateAndGetAsync(
            new FileDocument(
                _idService.GetId(SysTypeNames.FileDocument),
                sleekflowCompanyId,
                blobId,
                blobType,
                contentType,
                documentStatistics,
                metadata,
                languageIsoCode,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                fileDocumentProcessStatus,
                0.0,
                fileName,
                uploadedBy,
                agentAssignments,
                new DebugTimestamps()), // Pass the created agent assignments list
            sleekflowCompanyId);

        return createdDocument;
    }

    public async Task<FileDocument> CreateAndGetDocumentAsync(FileDocument fileDocument)
    {
        var createdDocument =
            await _fileDocumentRepository.CreateAndGetAsync(fileDocument, fileDocument.SleekflowCompanyId);
        return createdDocument;
    }

    public async Task<FileDocument?> GetDocumentByBlobIdAsync(string sleekflowCompanyId, string blobId)
    {
        var document = await _fileDocumentRepository.GetFileDocumentsByBlobIdAsync(sleekflowCompanyId, blobId);
        return document.Any() ? document[0] : null;
    }

    public async Task DeleteFileDocumentAsync(string documentId, string sleekflowCompanyId, string blobId)
    {
        await _knowledgeBaseEntryService.DeleteKnowledgeBaseEntriesByDocumentIdAsync(
            sleekflowCompanyId,
            documentId);
        await _blobUploadHistoryService.DeleteBlobUploadHistory(sleekflowCompanyId, blobId);
        await _fileDocumentRepository.DeleteAsync(documentId, sleekflowCompanyId);
        try
        {
            await _blobService.DeleteBlobs(
                sleekflowCompanyId,
                new List<string>
                {
                    DocumentHelper.GetSourceName(blobId)
                },
                "File");
        }
        catch (Exception e)
        {
            _logger.LogError("Failed to delete blob: {exception}", e);
        }
    }

    public async Task<FileDocument> PatchFileDocumentProcessStatusByBlobIdAsync(
        string sleekflowCompanyId,
        string blobId,
        string newStatus)
    {
        return await _fileDocumentRepository.PatchFileDocumentProcessStatusByBlobIdAsync(
            sleekflowCompanyId,
            blobId,
            newStatus);
    }

    public async Task<int> GetUsedPagesAsync(string sleekflowCompanyId)
    {
        return await _fileDocumentRepository.GetUsedPagesAsync(sleekflowCompanyId);
    }
}