using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.Models.Prompts;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;

/// <summary>
/// Configuration for knowledge retrieval from various sources
/// </summary>
public class KnowledgeRetrievalConfig
{
    /// <summary>
    /// Configuration for Web Search integration.
    /// </summary>
    [JsonProperty("web_search")]
    public WebSearchConfig? WebSearch { get; set; }

    /// <summary>
    /// Configuration for Static Search integration.
    /// </summary>
    [JsonProperty("static_search")]
    public StaticSearchConfig? StaticSearch { get; set; }

    [JsonConstructor]
    public KnowledgeRetrievalConfig(WebSearchConfig? webSearch = null, StaticSearchConfig? staticSearch = null)
    {
        WebSearch = webSearch;
        StaticSearch = staticSearch;
    }
}

/// <summary>
/// Configuration for search trigger to site prefix mapping
/// </summary>
public class TriggerSiteMapping
{
    /// <summary>
    /// Trigger phrase that will activate the search (e.g. "use_cases", "pricing").
    /// </summary>
    [JsonProperty("trigger")]
    public string Trigger { get; set; }

    /// <summary>
    /// Site prefix to limit search results (e.g. "site:example.com/use-cases").
    /// </summary>
    [JsonProperty("site")]
    public string? Site { get; set; }

    [JsonProperty("pattern")]
    public string Pattern { get; set; }

    [JsonConstructor]
    public TriggerSiteMapping(string trigger, string? site, string pattern)
    {
        Trigger = trigger;
        Site = site;
        Pattern = pattern;
    }
}

/// <summary>
/// Configuration for Web Search integration.
/// </summary>
public class WebSearchConfig
{
    /// <summary>
    /// List of trigger to site prefix mappings.
    /// </summary>
    [JsonProperty("trigger_site_mappings")]
    public List<TriggerSiteMapping> TriggerSiteMappings { get; set; } = new ();

    [JsonConstructor]
    public WebSearchConfig(List<TriggerSiteMapping>? triggerSiteMappings = null)
    {
        TriggerSiteMappings = triggerSiteMappings ?? new List<TriggerSiteMapping>();
    }
}

/// <summary>
/// Represents the configuration for a company's AI agent, defining its behavior, capabilities, and integration settings.
/// </summary>
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IIntelligentHubDbResolver))]
[ContainerId(ContainerNames.CompanyAgentConfig)]
public class CompanyAgentConfig : AuditEntity, IHasETag
{
    public const string PropertyNameName = "name";
    public const string PropertyNamePromptInstruction = "prompt_instruction";

    public const string PropertyNameIsChatHistoryEnabledAsContext
        = "is_chat_history_enabled_as_context";

    public const string PropertyNameIsContactPropertiesEnabledAsContext
        = "is_contact_properties_enabled_as_context";

    public const string PropertyNameNumberOfPreviousMessagesInChatHistoryAvailableAsContext
        = "number_of_previous_messages_in_chat_history_availabe_as_context";

    public const string PropertyNameCollaborationMode = "collaboration_mode";

    public const string PropertyNameEffectiveCollaborationMode = "effective_collaboration_mode";

    public const string PropertyNameLeadNurturingTools = "lead_nurturing_tools";

    public const string PropertyNameToolsConfig = "tools_config";

    public const string PropertyNameChannelType = "channel_type";

    public const string PropertyNameChannelId = "channel_id";

    public const string PropertyNameEnricherConfigs = "enricher_configs";

    public const string PropertyNameType = "type";

    public const string PropertyNameKnowledgeRetrievalConfig = "knowledge_retrieval_config";

    public const string PropertyNameDescription = "description";

    public const string PropertyNameActions = "actions";

    public const string PropertyNameActiveWorkflowCount = "active_workflow_count";

    public const string PropertyNameIsTranscriptionEnabled = "is_transcription_enabled";

    public const string PropertyNameFirstWorkflowPublishedAt = "first_workflow_published_at";

    public const string PropertyNameEditMode = "edit_mode";

    public const string PropertyNameAgentVersionedId = "agent_versioned_id";

    /// <summary>
    /// The display name of the agent configuration.
    /// </summary>
    [JsonProperty(PropertyNameName)]
    public string Name { get; set; }

    /// <summary>
    /// Indicates whether the agent should use chat history as context when generating responses.
    /// When enabled, previous messages in the conversation will be considered.
    /// </summary>
    [JsonProperty(PropertyNameIsChatHistoryEnabledAsContext)]
    public bool IsChatHistoryEnabledAsContext { get; set; }

    /// <summary>
    /// Indicates whether the agent should use contact properties from CRM as context when generating responses.
    /// When enabled, user profile information will be accessible to the agent.
    /// </summary>
    [JsonProperty(PropertyNameIsContactPropertiesEnabledAsContext)]
    public bool IsContactPropertiesEnabledAsContext { get; set; }

    /// <summary>
    /// Specifies the maximum number of previous messages from the chat history that should be
    /// included as context for the agent. This limits the context window size.
    /// </summary>
    [JsonProperty(PropertyNameNumberOfPreviousMessagesInChatHistoryAvailableAsContext)]
    public int NumberOfPreviousMessagesInChatHistoryAvailableAsContext { get; set; }

    /// <summary>
    /// The type of messaging channel this agent configuration is associated with.
    /// Examples include "whatsapp", "web", etc.
    /// </summary>
    [JsonProperty(PropertyNameChannelType)]
    public string? ChannelType { get; set; }

    /// <summary>
    /// The specific identifier for the channel this agent configuration is associated with.
    /// Used to link the agent to a specific messaging channel instance.
    /// </summary>
    [JsonProperty(PropertyNameChannelId)]
    public string? ChannelId { get; set; }

    /// <summary>
    /// A brief description of the agent's intended purpose, provided by the user.
    /// </summary>
    [JsonProperty(PropertyNameDescription)]
    public string? Description { get; set; }

    /// <summary>
    /// Configuration for the agent's prompt instructions, including tone, disclosure level,
    /// response level, and additional instructions for different stages of processing.
    /// </summary>
    [JsonProperty(PropertyNamePromptInstruction)]
    public PromptInstruction? PromptInstruction { get; set; }

    /// <summary>
    /// The collaboration mode for the agent, which determines how the agent processes and responds to messages.
    /// Values include "Long", "Short", "Medium", "LeadNurturing", and "ReAct".
    /// </summary>
    [JsonProperty(PropertyNameCollaborationMode)]
    [RegularExpression(
        $"{AgentCollaborationModes.Long}|{AgentCollaborationModes.Short}|{AgentCollaborationModes.Medium}|{AgentCollaborationModes.LeadNurturing}|{AgentCollaborationModes.ManagerLeadNurturing}|{AgentCollaborationModes.ReAct}")]
    public string CollaborationMode { get; set; }

    /// <summary>
    /// The effective collaboration mode for the agent, which determines how the agent processes and responds to messages.
    /// Values include "Long", "Short", "Medium", "LeadNurturing", and "ReAct".
    /// </summary>
    [JsonProperty(PropertyNameEffectiveCollaborationMode)]
    [RegularExpression(
        $"{AgentCollaborationModes.Long}|{AgentCollaborationModes.Short}|{AgentCollaborationModes.Medium}|{AgentCollaborationModes.LeadNurturing}|{AgentCollaborationModes.ReAct}")]
    public string EffectiveCollaborationMode => GetEffectiveCollaborationMode();

    /// <summary>
    /// The functional type of the agent. Currently limited to "Sales", "Support", and "Custom" agents.
    /// </summary>
    [JsonProperty(PropertyNameType)]
    [RegularExpression($"{CompanyAgentTypes.Sales}|{CompanyAgentTypes.Support}|{CompanyAgentTypes.Custom}")]
    public string Type { get; set; }

    /// <summary>
    /// Configuration for Lead Nurturing tools used by the agent when in LeadNurturing mode.
    /// Includes settings for lead scoring, assignment, demo scheduling, etc.
    /// </summary>
    [JsonProperty(PropertyNameLeadNurturingTools)]
    public LeadNurturingTools? LeadNurturingTools { get; set; }

    /// <summary>
    /// Configuration for ReAct tools used by the agent when in ReAct mode.
    /// Includes settings for reactive actions like assignment, custom object handling, etc.
    /// </summary>
    [JsonProperty(PropertyNameToolsConfig)]
    public ToolsConfig? ToolsConfig { get; set; }

    /// <summary>
    /// List of enricher configurations that enhance the agent's context with additional data.
    /// Enrichers can provide contact properties, company information, etc.
    /// </summary>
    [JsonProperty(PropertyNameEnricherConfigs)]
    public List<EnricherConfig> EnricherConfigs { get; set; } = new ();

    /// <summary>
    /// Configuration for knowledge retrieval capabilities, including web search and static search settings.
    /// This allows the agent to search for and use external information when generating responses.
    /// </summary>
    [JsonProperty(PropertyNameKnowledgeRetrievalConfig)]
    public KnowledgeRetrievalConfig? KnowledgeRetrievalConfig { get; set; }

    /// <summary>
    /// Configuration for agent actions like sending messages, calculating lead scores, etc.
    /// </summary>
    [JsonProperty(PropertyNameActions)]
    public CompanyAgentConfigActions? Actions { get; set; }

    /// <summary>
    /// Indicates whether the agent should use transcription when generating responses.
    /// </summary>
    [JsonProperty(PropertyNameIsTranscriptionEnabled, DefaultValueHandling = DefaultValueHandling.Populate)]
    [System.ComponentModel.DefaultValue(false)]
    public bool IsTranscriptionEnabled { get; set; } = false;

    /// <summary>
    /// The edit mode for the agent configuration, determining the complexity of the UI.
    /// Values include "basic" and "advanced".
    /// </summary>
    [JsonProperty(PropertyNameEditMode, DefaultValueHandling = DefaultValueHandling.Populate)]
    [System.ComponentModel.DefaultValue(AgentEditModes.Basic)]
    [RegularExpression($"{AgentEditModes.Basic}|{AgentEditModes.Advanced}")]
    public string EditMode { get; set; } = AgentEditModes.Default;

    /// <summary>
    /// The first time the agent is associated with a published workflow.
    /// </summary>
    [JsonProperty(PropertyNameFirstWorkflowPublishedAt)]
    public DateTimeOffset? FirstWorkflowPublishedAt { get; set; }

    [JsonProperty(PropertyNameAgentVersionedId)]
    public string? AgentVersionedId { get; set; }

    /// <summary>
    /// Entity tag used for optimistic concurrency control when updating the configuration.
    /// </summary>
    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="CompanyAgentConfig"/> class with the specified parameters.
    /// </summary>
    /// <param name="id">Unique identifier for this configuration.</param>
    /// <param name="name">Display name of the agent configuration.</param>
    /// <param name="sleekflowCompanyId">The company ID this agent belongs to.</param>
    /// <param name="isChatHistoryEnabledAsContext">Whether chat history should be used as context.</param>
    /// <param name="isContactPropertiesEnabledAsContext">Whether contact properties should be used as context.</param>
    /// <param name="numberOfPreviousMessagesInChatHistoryAvailableAsContext">Maximum number of previous messages to include in context.</param>
    /// <param name="channelType">The type of messaging channel.</param>
    /// <param name="channelId">The specific channel identifier.</param>
    /// <param name="description">A brief description of the agent's intended purpose, provided by the user.</param>
    /// <param name="promptInstruction">Configuration for the agent's prompt instructions.</param>
    /// <param name="type">The functional type of the agent.</param>
    /// <param name="collaborationMode">The collaboration mode for the agent.</param>
    /// <param name="leadNurturingTools">Configuration for Lead Nurturing tools.</param>
    /// <param name="createdAt">When this configuration was created.</param>
    /// <param name="updatedAt">When this configuration was last updated.</param>
    /// <param name="createdBy">Staff member who created this configuration.</param>
    /// <param name="updatedBy">Staff member who last updated this configuration.</param>
    /// <param name="eTag">Entity tag for optimistic concurrency control.</param>
    /// <param name="enricherConfigs">List of enricher configurations.</param>
    /// <param name="knowledgeRetrievalConfig">Configuration for knowledge retrieval capabilities.</param>
    /// <param name="toolsConfig">Configuration for ReAct tools.</param>
    /// <param name="actions">Configuration for agent actions.</param>
    /// <param name="isTranscriptionEnabled">Configuration for transcription.</param>
    /// <param name="editMode">The edit mode for the agent configuration on the UI.</param>
    /// <param name="agentVersionedId">Agent Version </param>
    [JsonConstructor]
    public CompanyAgentConfig(
        string id,
        string name,
        string sleekflowCompanyId,
        bool isChatHistoryEnabledAsContext,
        bool isContactPropertiesEnabledAsContext,
        int numberOfPreviousMessagesInChatHistoryAvailableAsContext,
        string? channelType,
        string? channelId,
        string? description,
        PromptInstruction? promptInstruction,
        string type,
        string collaborationMode,
        LeadNurturingTools? leadNurturingTools,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        SleekflowStaff? createdBy = null,
        SleekflowStaff? updatedBy = null,
        string? eTag = null,
        List<EnricherConfig>? enricherConfigs = null,
        KnowledgeRetrievalConfig? knowledgeRetrievalConfig = null,
        ToolsConfig? toolsConfig = null,
        CompanyAgentConfigActions? actions = null,
        bool isTranscriptionEnabled = false,
        DateTimeOffset? firstWorkflowPublishedAt = null,
        string editMode = AgentEditModes.Default,
        string? agentVersionedId = null)
        : base(id, SysTypeNames.CompanyAgentConfig, createdAt, updatedAt, sleekflowCompanyId, createdBy, updatedBy)
    {
        Name = name;
        IsChatHistoryEnabledAsContext = isChatHistoryEnabledAsContext;
        IsContactPropertiesEnabledAsContext = isContactPropertiesEnabledAsContext;
        NumberOfPreviousMessagesInChatHistoryAvailableAsContext =
            numberOfPreviousMessagesInChatHistoryAvailableAsContext;
        ChannelType = channelType;
        ChannelId = channelId;
        Description = description;
        PromptInstruction = promptInstruction;
        Type = type;
        ETag = eTag;
        CollaborationMode = collaborationMode;
        LeadNurturingTools = leadNurturingTools;
        ToolsConfig = toolsConfig;
        EnricherConfigs = enricherConfigs ?? new List<EnricherConfig>();
        KnowledgeRetrievalConfig = knowledgeRetrievalConfig;
        Actions = actions;
        IsTranscriptionEnabled = isTranscriptionEnabled;
        FirstWorkflowPublishedAt = firstWorkflowPublishedAt;
        EditMode = editMode;
        AgentVersionedId = agentVersionedId;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="CompanyAgentConfig"/> class from a DTO.
    /// </summary>
    /// <param name="agentConfigDto">The DTO containing the configuration data.</param>
    public CompanyAgentConfig(CompanyAgentConfigDto agentConfigDto)
        : base(
            agentConfigDto.Id,
            agentConfigDto.Name,
            agentConfigDto.CreatedAt,
            agentConfigDto.UpdatedAt,
            agentConfigDto.SleekflowCompanyId,
            agentConfigDto.CreatedBy,
            agentConfigDto.UpdatedBy)
    {
        Name = agentConfigDto.Name;
        IsChatHistoryEnabledAsContext = agentConfigDto.IsChatHistoryEnabledAsContext;
        IsContactPropertiesEnabledAsContext = agentConfigDto.IsContactPropertiesEnabledAsContext;
        NumberOfPreviousMessagesInChatHistoryAvailableAsContext =
            agentConfigDto.NumberOfPreviousMessagesInChatHistoryAvailableAsContext;
        ChannelType = agentConfigDto.ChannelType;
        ChannelId = agentConfigDto.ChannelId;
        PromptInstruction = new PromptInstruction(
            agentConfigDto.PromptInstruction!.Objective,
            agentConfigDto.PromptInstruction.Tone,
            agentConfigDto.PromptInstruction.DiscloseLevel,
            agentConfigDto.PromptInstruction.ResponseLevel,
            agentConfigDto.PromptInstruction.RestrictivenessLevel,
            agentConfigDto.PromptInstruction.GreetingMessage,
            agentConfigDto.PromptInstruction.AdditionalInstructionCore,
            agentConfigDto.PromptInstruction.AdditionalInstructionStrategy,
            agentConfigDto.PromptInstruction.AdditionalInstructionResponse,
            agentConfigDto.PromptInstruction.AdditionalInstructionKnowledgeRetrieval,
            agentConfigDto.PromptInstruction.Guardrails);
        CollaborationMode = agentConfigDto.CollaborationMode;
        Type = agentConfigDto.Type;
        LeadNurturingTools = agentConfigDto.LeadNurturingTools;
        ToolsConfig = agentConfigDto.ToolsConfig;
        ETag = agentConfigDto.ETag;
        EnricherConfigs = agentConfigDto.EnricherConfigs ?? new List<EnricherConfig>();
        KnowledgeRetrievalConfig = agentConfigDto.KnowledgeRetrievalConfig;
        Actions = agentConfigDto.Actions != null ? new CompanyAgentConfigActions(agentConfigDto.Actions) : null;
        IsTranscriptionEnabled = agentConfigDto.IsTranscriptionEnabled;
        EditMode = agentConfigDto.EditMode;
    }

    /// <summary>
    /// Gets the effective collaboration mode based on the Actions configuration.
    /// If Actions.SendMessage.ResponseType is "intelligent", returns "Long".
    /// If Actions.SendMessage.ResponseType is "fast", returns "Short".
    /// Otherwise, returns the configured CollaborationMode value.
    /// </summary>
    /// <returns>The effective collaboration mode for the agent.</returns>
    public string GetEffectiveCollaborationMode()
    {
        if (CollaborationMode is AgentCollaborationModes.LeadNurturing or AgentCollaborationModes.ReAct)
        {
            return CollaborationMode;
        }

        return Actions?.SendMessage?.ResponseType?.ToLowerInvariant() switch
        {
            AgentActionSendMessageResponseTypes.Intelligent => AgentCollaborationModes.Long,
            AgentActionSendMessageResponseTypes.Fast => AgentCollaborationModes.Short,
            _ => CollaborationMode
        };
    }
}