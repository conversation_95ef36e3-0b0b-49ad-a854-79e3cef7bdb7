using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;

namespace Sleekflow.IntelligentHub.Documents.WebpageDocuments;

public interface IWebpageDocumentChunkService
{
    Task<WebpageDocumentChunk> CreateWebpageDocumentChunkAsync(
        string sleekflowCompanyId,
        string documentId,
        string pageUrl,
        string content,
        string contentEn,
        List<Category> categories,
        Dictionary<string, object?> metadata);

    Task<List<string>> GetWebpageDocumentChunkIdsAsync(
        string sleekflowCompanyId,
        string documentId);

    Task<WebpageDocumentChunk> GetWebpageDocumentChunkAsync(
        string sleekflowCompanyId,
        string webpageDocumentChunkId);
}

public class WebpageDocumentChunkService : IWebpageDocumentChunkService, IScopedService
{
    private readonly IWebpageDocumentChunkRepository _webpageDocumentChunkRepository;
    private readonly IIdService _idService;

    public WebpageDocumentChunkService(
        IWebpageDocumentChunkRepository webpageDocumentChunkRepository,
        IIdService idService)
    {
        _webpageDocumentChunkRepository = webpageDocumentChunkRepository;
        _idService = idService;
    }

    public async Task<WebpageDocumentChunk> CreateWebpageDocumentChunkAsync(
        string sleekflowCompanyId,
        string documentId,
        string pageUrl,
        string content,
        string contentEn,
        List<Category> categories,
        Dictionary<string, object?> metadata)
    {
        var createdWebpageDocumentChunk = await _webpageDocumentChunkRepository.CreateAndGetAsync(
            new WebpageDocumentChunk(
                _idService.GetId(SysTypeNames.WebpageDocumentChunk),
                sleekflowCompanyId,
                documentId,
                pageUrl,
                content,
                contentEn,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                categories,
                metadata),
            sleekflowCompanyId);

        return createdWebpageDocumentChunk;
    }

    public async Task<List<string>> GetWebpageDocumentChunkIdsAsync(
        string sleekflowCompanyId,
        string documentId)
    {
        return await _webpageDocumentChunkRepository.GetWebpageDocumentChunkIdsAsync(
            sleekflowCompanyId,
            documentId);
    }

    public async Task<WebpageDocumentChunk> GetWebpageDocumentChunkAsync(
        string sleekflowCompanyId,
        string webpageDocumentChunkId)
    {
        var webpageDocumentChunk =
            await _webpageDocumentChunkRepository.GetAsync(webpageDocumentChunkId, sleekflowCompanyId);
        if (webpageDocumentChunk == null)
        {
            throw new SfNotFoundObjectException(webpageDocumentChunkId, sleekflowCompanyId);
        }

        return webpageDocumentChunk;
    }
}