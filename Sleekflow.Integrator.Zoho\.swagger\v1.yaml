openapi: 3.0.1
info:
  title: Sleekflow 1.0
  version: '1.0'
servers:
  - url: https://localhost:7105
    description: Local
paths:
  /Integrations/CreateObjectV2:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateObjectV2Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateObjectV2OutputOutput'
  /Integrations/CreateUserMappingConfig:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserMappingConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateUserMappingConfigOutputOutput'
  /Integrations/DeleteConnection:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteConnectionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteConnectionOutputOutput'
  /Integrations/GetConnections:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetConnectionsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetConnectionsOutputOutput'
  /Integrations/GetCustomObjectTypes:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCustomObjectTypesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCustomObjectTypesOutputOutput'
  /Integrations/GetSubscriptions:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSubscriptionsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSubscriptionsOutputOutput'
  /Integrations/GetTypeFieldsV2:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetTypeFieldsV2Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTypeFieldsV2OutputOutput'
  /Integrations/GetUserMappingConfig:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetUserMappingConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetUserMappingConfigOutputOutput'
  /Integrations/InitProviderV2:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitProviderV2Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InitProviderV2OutputOutput'
  /Integrations/InitTypeSyncV2:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitTypeSyncV2Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InitTypeSyncV2OutputOutput'
  /Integrations/PreviewObjectsV2:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PreviewObjectsV2Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreviewObjectsV2OutputOutput'
  /Integrations/ReInitConnection:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReInitConnectionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReInitConnectionOutputOutput'
  /Integrations/RenameConnection:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RenameConnectionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RenameConnectionOutputOutput'
  /Integrations/SearchObjects:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchObjectsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchObjectsOutputOutput'
  /Integrations/UpdateObjectV2:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateObjectV2Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateObjectV2OutputOutput'
  /Integrations/UpdateUserMappingConfig:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserMappingConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateUserMappingConfigOutputOutput'
  /Internals/LoopThroughAndEnrollObjectsToFlowHubBatch:
    post:
      tags:
        - Internals
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoopThroughAndEnrollObjectsToFlowHubBatchInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoopThroughAndEnrollObjectsToFlowHubBatchOutputOutput'
  /Internals/SubscriptionsCheckBatch:
    post:
      tags:
        - Internals
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubscriptionsCheckBatchInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionsCheckBatchOutputOutput'
  /Public/AuthenticateCallback:
    get:
      tags:
        - Public
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: code
          in: query
          schema:
            type: string
        - name: state
          in: query
          schema:
            type: string
        - name: location
          in: query
          schema:
            type: string
        - name: accounts-server
          in: query
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
components:
  schemas:
    CreateObjectV2Input:
      required:
        - connection_id
        - dict
        - entity_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        dict:
          type: object
          additionalProperties:
            nullable: true
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    CreateObjectV2Output:
      type: object
      properties:
        is_async_operation:
          type: boolean
      additionalProperties: false
    CreateObjectV2OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateObjectV2Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserMappingConfigInput:
      required:
        - connection_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        user_mappings:
          type: array
          items:
            $ref: '#/components/schemas/UserMapping'
          nullable: true
      additionalProperties: false
    CreateUserMappingConfigOutput:
      type: object
      properties:
        user_mapping_config:
          $ref: '#/components/schemas/ProviderUserMappingConfigDto'
      additionalProperties: false
    CreateUserMappingConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateUserMappingConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CustomObjectType:
      type: object
      properties:
        api_name:
          type: string
          nullable: true
        display_name:
          type: string
          nullable: true
      additionalProperties: false
    DeleteConnectionInput:
      required:
        - connection_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
      additionalProperties: false
    DeleteConnectionOutput:
      type: object
      additionalProperties: false
    DeleteConnectionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteConnectionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DurablePayload:
      type: object
      properties:
        id:
          type: string
          nullable: true
        statusQueryGetUri:
          type: string
          nullable: true
        sendEventPostUri:
          type: string
          nullable: true
        terminatePostUri:
          type: string
          nullable: true
        rewindPostUri:
          type: string
          nullable: true
        purgeHistoryDeleteUri:
          type: string
          nullable: true
        restartPostUri:
          type: string
          nullable: true
      additionalProperties: false
    GetConnectionsInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetConnectionsOutput:
      required:
        - connections
      type: object
      properties:
        connections:
          type: array
          items:
            $ref: '#/components/schemas/ZohoConnectionDto'
      additionalProperties: false
    GetConnectionsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetConnectionsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetCustomObjectTypesInput:
      required:
        - connection_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetCustomObjectTypesOutput:
      required:
        - custom_object_types
      type: object
      properties:
        custom_object_types:
          type: array
          items:
            $ref: '#/components/schemas/CustomObjectType'
      additionalProperties: false
    GetCustomObjectTypesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetCustomObjectTypesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetSubscriptionsInput:
      required:
        - connection_id
        - entity_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    GetSubscriptionsOutput:
      required:
        - subscriptions
      type: object
      properties:
        subscriptions:
          type: array
          items:
            $ref: '#/components/schemas/ZohoSubscriptionDto'
      additionalProperties: false
    GetSubscriptionsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetSubscriptionsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetTypeFieldsOutputFieldDto:
      type: object
      properties:
        calculated:
          type: boolean
        compound_field_name:
          type: string
          nullable: true
        createable:
          type: boolean
        custom:
          type: boolean
        encrypted:
          type: boolean
        label:
          type: string
          nullable: true
        length:
          type: integer
          format: int32
        name:
          type: string
          nullable: true
        picklist_values:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputPicklistValue'
          nullable: true
        soap_type:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        unique:
          type: boolean
        updateable:
          type: boolean
        mandatory:
          type: boolean
          nullable: true
      additionalProperties: false
    GetTypeFieldsOutputPicklistValue:
      type: object
      properties:
        label:
          type: string
          nullable: true
        value:
          type: string
          nullable: true
      additionalProperties: false
    GetTypeFieldsV2Input:
      required:
        - connection_id
        - entity_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    GetTypeFieldsV2Output:
      type: object
      properties:
        updatable_fields:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputFieldDto'
          nullable: true
        creatable_fields:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputFieldDto'
          nullable: true
        viewable_fields:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputFieldDto'
          nullable: true
      additionalProperties: false
    GetTypeFieldsV2OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetTypeFieldsV2Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetUserMappingConfigInput:
      required:
        - connection_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetUserMappingConfigOutput:
      type: object
      properties:
        user_mapping_config:
          $ref: '#/components/schemas/ProviderUserMappingConfigDto'
      additionalProperties: false
    GetUserMappingConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetUserMappingConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    InitProviderV2Input:
      required:
        - failure_url
        - sleekflow_company_id
        - success_url
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        success_url:
          minLength: 1
          type: string
        failure_url:
          minLength: 1
          type: string
        additional_details:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    InitProviderV2Output:
      type: object
      properties:
        zoho_authentication_url:
          type: string
          nullable: true
      additionalProperties: false
    InitProviderV2OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/InitProviderV2Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    InitTypeSyncV2Input:
      required:
        - connection_id
        - entity_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        sync_interval:
          type: integer
          format: int32
          nullable: true
        is_flows_based:
          type: boolean
          nullable: true
      additionalProperties: false
    InitTypeSyncV2Output:
      type: object
      additionalProperties: false
    InitTypeSyncV2OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/InitTypeSyncV2Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    LoopThroughAndEnrollObjectsToFlowHubBatchInput:
      required:
        - connection_id
        - entity_type_name
        - flow_hub_workflow_id
        - flow_hub_workflow_versioned_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        next_page_size:
          type: integer
          format: int32
          nullable: true
        next_page:
          type: integer
          format: int32
          nullable: true
        flow_hub_workflow_id:
          minLength: 1
          type: string
        flow_hub_workflow_versioned_id:
          minLength: 1
          type: string
      additionalProperties: false
    LoopThroughAndEnrollObjectsToFlowHubBatchOutput:
      type: object
      properties:
        count:
          type: integer
          format: int64
        next_page_size:
          type: integer
          format: int32
          nullable: true
        next_page:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    LoopThroughAndEnrollObjectsToFlowHubBatchOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/LoopThroughAndEnrollObjectsToFlowHubBatchOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    PreviewObjectsV2Input:
      required:
        - connection_id
        - entity_type_name
        - filter_groups
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilterGroup'
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
      additionalProperties: false
    PreviewObjectsV2Output:
      required:
        - objects
      type: object
      properties:
        objects:
          type: array
          items:
            type: object
            additionalProperties: { }
      additionalProperties: false
    PreviewObjectsV2OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/PreviewObjectsV2Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ProviderConnectionDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        organization_id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        environment:
          type: string
          nullable: true
        is_active:
          type: boolean
        is_api_request_limit_exceeded:
          type: boolean
          nullable: true
        connected_at:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    ProviderUserMappingConfigDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        connection_id:
          type: string
          nullable: true
        user_mappings:
          type: array
          items:
            $ref: '#/components/schemas/UserMapping'
          nullable: true
      additionalProperties: false
    ReInitConnectionInput:
      required:
        - connection_id
        - failure_url
        - sleekflow_company_id
        - success_url
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        success_url:
          minLength: 1
          type: string
        failure_url:
          minLength: 1
          type: string
        additional_details:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    ReInitConnectionOutput:
      type: object
      properties:
        provider_name:
          type: string
          nullable: true
        is_re_authentication_required:
          type: boolean
        zoho_authentication_url:
          type: string
          nullable: true
      additionalProperties: false
    ReInitConnectionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ReInitConnectionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    RenameConnectionInput:
      required:
        - connection_id
        - name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        name:
          minLength: 1
          type: string
      additionalProperties: false
    RenameConnectionOutput:
      type: object
      properties:
        connection:
          $ref: '#/components/schemas/ProviderConnectionDto'
      additionalProperties: false
    RenameConnectionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RenameConnectionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SearchObjectCondition:
      required:
        - field_name
        - operator
      type: object
      properties:
        field_name:
          minLength: 1
          type: string
        operator:
          minLength: 1
          type: string
        value:
          nullable: true
      additionalProperties: false
    SearchObjectsInput:
      required:
        - conditions
        - connection_id
        - entity_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/SearchObjectCondition'
      additionalProperties: false
    SearchObjectsOutput:
      required:
        - objects
      type: object
      properties:
        objects:
          type: array
          items:
            type: object
            additionalProperties: { }
      additionalProperties: false
    SearchObjectsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SearchObjectsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SubscriptionsCheckBatchInput:
      required:
        - last_object_modification_time
        - sleekflow_company_id
        - subscription
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        subscription:
          $ref: '#/components/schemas/ZohoSubscription'
        last_object_modification_time:
          type: string
          format: date-time
        next_page:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    SubscriptionsCheckBatchOutput:
      type: object
      properties:
        count:
          type: integer
          format: int64
        next_last_object_modification_time:
          type: string
          format: date-time
        next_page:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    SubscriptionsCheckBatchOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SubscriptionsCheckBatchOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SyncConfigFieldFilter:
      type: object
      properties:
        name:
          type: string
          nullable: true
      additionalProperties: false
    SyncConfigFilter:
      type: object
      properties:
        field_name:
          type: string
          nullable: true
        value:
          type: string
          nullable: true
        operator:
          type: string
          nullable: true
      additionalProperties: false
    SyncConfigFilterGroup:
      type: object
      properties:
        filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilter'
          nullable: true
      additionalProperties: false
    UpdateObjectV2Input:
      required:
        - connection_id
        - dict
        - entity_type_name
        - object_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        object_id:
          minLength: 1
          type: string
        dict:
          type: object
          additionalProperties:
            nullable: true
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    UpdateObjectV2Output:
      type: object
      properties:
        is_async_operation:
          type: boolean
      additionalProperties: false
    UpdateObjectV2OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateObjectV2Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateUserMappingConfigInput:
      required:
        - sleekflow_company_id
        - user_mapping_config_id
      type: object
      properties:
        user_mapping_config_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        user_mappings:
          type: array
          items:
            $ref: '#/components/schemas/UserMapping'
          nullable: true
      additionalProperties: false
    UpdateUserMappingConfigOutput:
      type: object
      properties:
        user_mapping_config:
          $ref: '#/components/schemas/ProviderUserMappingConfigDto'
      additionalProperties: false
    UpdateUserMappingConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateUserMappingConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UserMapping:
      type: object
      properties:
        provider_user_id:
          type: string
          nullable: true
        sleekflow_user_id:
          type: string
          nullable: true
      additionalProperties: false
    ZohoConnectionDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        organization_id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        environment:
          type: string
          nullable: true
        is_active:
          type: boolean
        is_api_request_limit_exceeded:
          type: boolean
      additionalProperties: false
    ZohoSubscription:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        entity_type_name:
          type: string
          nullable: true
        interval:
          type: integer
          format: int32
        last_execution_start_time:
          type: string
          format: date-time
        last_object_modification_time:
          type: string
          format: date-time
          nullable: true
        durable_payload:
          $ref: '#/components/schemas/DurablePayload'
        is_flows_based:
          type: boolean
        connection_id:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    ZohoSubscriptionDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        entity_type_name:
          type: string
          nullable: true
        interval:
          type: integer
          format: int32
        is_flows_based:
          type: boolean
          nullable: true
        connection_id:
          type: string
          nullable: true
      additionalProperties: false