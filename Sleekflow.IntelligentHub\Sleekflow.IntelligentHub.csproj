<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <PropertyGroup Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <DefineConstants>SWAGGERGEN</DefineConstants>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AngleSharp" Version="1.3.0" />
        <PackageReference Include="Azure.AI.DocumentIntelligence" Version="1.0.0" />
        <PackageReference Include="Azure.AI.FormRecognizer" Version="4.1.0" />
        <PackageReference Include="Azure.AI.Translation.Text" Version="1.0.0" />
        <PackageReference Include="CsvHelper" Version="33.0.1" />
        <PackageReference Include="DocumentFormat.OpenXml" Version="3.3.0" />
        <PackageReference Include="HtmlAgilityPack" Version="1.12.1" />
        <PackageReference Include="libphonenumber-csharp" Version="9.0.4" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.13" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />
        <PackageReference Include="Microsoft.Azure.Cosmos" Version="3.48.0" />
        <PackageReference Include="Microsoft.Playwright" Version="1.52.0" />
        <PackageReference Include="Microsoft.SemanticKernel" Version="1.54.0" />
        <PackageReference Include="Microsoft.SemanticKernel.Agents.Core" Version="1.54.0" />
        <PackageReference Include="Microsoft.SemanticKernel.Agents.Orchestration" Version="1.54.0-preview" />
        <PackageReference Include="Microsoft.SemanticKernel.Agents.Runtime.InProcess" Version="1.54.0-preview" />
        <PackageReference Include="Microsoft.SemanticKernel.Connectors.AzureOpenAI" Version="1.54.0" />
        <PackageReference Include="Microsoft.SemanticKernel.Connectors.CosmosNoSql" Version="1.54.0-preview" />
        <PackageReference Include="Microsoft.SemanticKernel.Connectors.Google" Version="1.54.0-alpha" />
        <PackageReference Include="Microsoft.SemanticKernel.Plugins.Core" Version="1.54.0-preview" />
        <PackageReference Include="Microsoft.SemanticKernel.Plugins.Web" Version="1.54.0-alpha" />
        <PackageReference Include="OpenAI" Version="2.2.0-beta.4" />
        <PackageReference Include="PdfPig" Version="0.1.9" />
        <PackageReference Include="PDFsharp" Version="6.1.1" />
        <PackageReference Include="PDFtoImage" Version="5.1.0-preview6" />
        <PackageReference Include="Tiktoken" Version="2.2.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Sleekflow.FlowHub.Models\Sleekflow.FlowHub.Models.csproj" />
        <ProjectReference Include="..\Sleekflow.IntelligentHub.Models\Sleekflow.IntelligentHub.Models.csproj" />
        <ProjectReference Include="..\Sleekflow.Models\Sleekflow.Models.csproj" />
        <ProjectReference Include="..\Sleekflow.Mvc\Sleekflow.Mvc.csproj" />
    </ItemGroup>

    <ItemGroup>
        <InternalsVisibleTo Include="Sleekflow.IntelligentHub.Evaluator" />
        <InternalsVisibleTo Include="Sleekflow.IntelligentHub.Tests" />
    </ItemGroup>

    <ItemGroup>
        <Content Include="Plugins\Assets\*.csv">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
    </ItemGroup>

    <Target Name="SwaggerGen" AfterTargets="Build" Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <MakeDir Directories=".swagger" />
        <Exec Command="dotnet tool restore" />
        <Exec Command="dotnet swagger tofile --output ./.swagger/v1.yaml --yaml $(OutputPath)$(AssemblyName).dll v1" WorkingDirectory="$(ProjectDir)" />
    </Target>

    <PropertyGroup>
        <NoWarn>$(NoWarn);CA2007;IDE1006;SKEXP0001;SKEXP0110;OPENAI001</NoWarn>
    </PropertyGroup>

</Project>