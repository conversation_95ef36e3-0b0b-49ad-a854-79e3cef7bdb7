﻿using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Events.OnCloudApiBalanceAutoTopUpEvents;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceAutoTopUpCharge;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.WabaBalanceAutoTopup;

public interface IWabaBalanceAutoTopUpService
{
    bool ShouldPerformAutoTopUp(
        string facebookBusinessId,
        string facebookWabaId,
        WabaBalance wabaBalance,
        WabaBalanceAutoTopUpProfile wabaBalanceAutoTopUpProfile);

    Task PublishAutoTopUpEvent(
        string facebookBusinessId,
        string facebookWabaId,
        string businessBalanceId,
        WabaBalanceAutoTopUpProfile wabaBalanceAutoTopUpProfile,
        CancellationToken cancellationToken = default);
}


public class WabaBalanceAutoTopUpService : IWabaBalanceAutoTopUpService, ISingletonService
{
    private readonly ILogger<WabaBalanceAutoTopUpService> _logger;
    private readonly IBus _bus;
    private readonly IBalanceAutoTopUpChargeService _balanceAutoTopUpChargeService;

    public WabaBalanceAutoTopUpService(ILogger<WabaBalanceAutoTopUpService> logger, IBus bus, IBalanceAutoTopUpChargeService balanceAutoTopUpChargeService)
    {
        _logger = logger;
        _bus = bus;
        _balanceAutoTopUpChargeService = balanceAutoTopUpChargeService;
    }

    public bool ShouldPerformAutoTopUp(
        string facebookBusinessId,
        string facebookWabaId,
        WabaBalance wabaBalance,
        WabaBalanceAutoTopUpProfile wabaBalanceAutoTopUpProfile)
    {
        if (wabaBalanceAutoTopUpProfile.IsAutoTopUpEnabled is false)
        {
            return false;
        }

        if (wabaBalanceAutoTopUpProfile.MinimumBalance.CurrencyIsoCode != wabaBalance.Balance.CurrencyIsoCode)
        {
            _logger.LogError(
                "facebookBusinessId: {FacebookBusinessId} " +
                "facebookWabaId: {FacebookWabaId}" +
                " wabaBalanceAutoTopUpProfileId: {WabaBalanceAutoTopUpProfileId} minimum balance currency iso code: {MinimumBalanceCurrencyIsoCode} " +
                " does not match the waba balance currency iso code:{BalanceCurrencyIsoCode}",
                facebookBusinessId,
                facebookWabaId,
                wabaBalanceAutoTopUpProfile.Id,
                wabaBalanceAutoTopUpProfile.MinimumBalance.CurrencyIsoCode,
                wabaBalance.Balance.CurrencyIsoCode);

            return false;
        }

        if (wabaBalanceAutoTopUpProfile.MinimumBalance.Amount <= wabaBalance.Balance.Amount)
        {
            return false;
        }

        return true;
    }

    public async Task PublishAutoTopUpEvent(
        string facebookBusinessId,
        string facebookWabaId,
        string businessBalanceId,
        WabaBalanceAutoTopUpProfile wabaBalanceAutoTopUpProfile,
        CancellationToken cancellationToken = default)
    {
        var onCloudApiWabaBalanceAutoTopUpEvent = new OnCloudApiWabaBalanceAutoTopUpEvent(
            businessBalanceId,
            facebookWabaId,
            facebookBusinessId,
            wabaBalanceAutoTopUpProfile.CustomerId!,
            wabaBalanceAutoTopUpProfile.AutoTopUpPlan);

        await _bus.Publish(onCloudApiWabaBalanceAutoTopUpEvent, cancellationToken);
    }
}