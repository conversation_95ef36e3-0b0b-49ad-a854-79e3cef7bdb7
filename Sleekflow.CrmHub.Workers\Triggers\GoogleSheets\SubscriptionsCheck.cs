﻿using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.CrmHub.Workers.Subscriptions;

namespace Sleekflow.CrmHub.Workers.Triggers.GoogleSheets;

public class SubscriptionsCheck
{
    private readonly ILogger<SubscriptionsCheck> _logger;
    private readonly IGoogleSheetsSubscriptionRepository _googleSheetsSubscriptionRepository;

    public SubscriptionsCheck(
        ILogger<SubscriptionsCheck> logger,
        IGoogleSheetsSubscriptionRepository googleSheetsSubscriptionRepository)
    {
        _logger = logger;
        _googleSheetsSubscriptionRepository = googleSheetsSubscriptionRepository;
    }

    public class SubscriptionsCheckInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("subscription")]
        [Required]
        public GoogleSheetsSubscription Subscription { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckInput(
            string sleekflowCompanyId,
            GoogleSheetsSubscription subscription)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Subscription = subscription;
        }
    }

    [Function("GoogleSheets_SubscriptionsCheck")]
    public async Task RunAsync(
        [TimerTrigger("0 */1 * * * *")]
        TimerInfo timerInfo,
        [Microsoft.Azure.Functions.Worker.DurableClient]
        DurableTaskClient starter)
    {
        var queryDefinition =
            new QueryDefinition(
                    "SELECT * "
                    + "FROM %%CONTAINER_NAME%% c "
                    + "WHERE (DateTimeDiff('second', c.last_execution_start_time, @now) > c.interval AND c.durable_payload = null) "
                    + "OR (DateTimeDiff('second', c.last_execution_start_time, @now) > 300 AND c.durable_payload != null)")
                .WithParameter("@now", DateTimeOffset.UtcNow);
        var objects =
            await _googleSheetsSubscriptionRepository.GetObjectsAsync(queryDefinition);

        foreach (var subscription in objects)
        {
            var instanceId = await starter.ScheduleNewOrchestrationInstanceAsync(
                "GoogleSheets_SubscriptionsCheck_Orchestrator",
                input: new SubscriptionsCheckInput(
                    subscription.SleekflowCompanyId,
                    subscription));

            var httpManagementPayload = starter.CreateHttpManagementPayload(instanceId);
            if (httpManagementPayload == null)
            {
                _logger.LogInformation(
                    "Unable to get GoogleSheets_SubscriptionsCheck_Orchestrator httpManagementPayload");
            }

            await _googleSheetsSubscriptionRepository.PatchAsync(
                subscription.Id,
                subscription.SleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Replace(
                        $"/{GoogleSheetsSubscription.PropertyNameDurablePayload}",
                        httpManagementPayload)
                });
        }
    }
}