using System.Net;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Microsoft.SemanticKernel.Agents.Chat;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Companies.CompanyConfigs;
using Sleekflow.IntelligentHub.FaqAgents.Chats.Enrichers;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.IntelligentHub.Plugins.Knowledges;
using Sleekflow.IntelligentHub.Utils;
using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.Shorts;

public interface IShortAgentCollaborationDefinition : IAgentCollaborationDefinition
{
}

public class ShortAgentCollaborationDefinition
    : BaseAgentCollaborationDefinition, IShortAgentCollaborationDefinition, IScopedService
{
    private readonly ILogger _logger;
    private readonly IShortAgentDefinitions _shortAgentDefinitions;
    private readonly ISourceOnlyKnowledgePlugin _sourceOnlyKnowledgePlugin;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;

    private string? _sources;

    public ShortAgentCollaborationDefinition(
        ILogger<ShortAgentCollaborationDefinition> logger,
        Kernel kernel,
        ISummaryPlugin summaryPlugin,
        ILanguagePlugin languagePlugin,
        IAgentDurationTracker agentDurationTracker,
        ICompanyConfigService companyConfigService,
        IShortAgentDefinitions shortAgentDefinitions,
        ISourceOnlyKnowledgePlugin sourceOnlyKnowledgePlugin,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        IChatHistoryEnricherFactory enricherFactory,
        IFileContentExtractionPlugin fileContentExtractionPlugin)
        : base(
            logger,
            kernel,
            summaryPlugin,
            languagePlugin,
            agentDurationTracker,
            companyConfigService,
            enricherFactory,
            fileContentExtractionPlugin)
    {
        _logger = logger;
        _shortAgentDefinitions = shortAgentDefinitions;
        _sourceOnlyKnowledgePlugin = sourceOnlyKnowledgePlugin;
        _promptExecutionSettingsService = promptExecutionSettingsService;
    }

    public override async Task<List<Agent>> CreateAgents(
        Kernel kernel,
        List<SfChatEntry> chatEntries,
        string sleekflowCompanyId,
        AgentCollaborationConfig agentCollaborationConfig,
        BaseInternalAgentCreationConfiguration? internalAgentCreationConfiguration = null)
    {
        var sanitizedChatResult = GroupChatHistoryUtils.GetSanitizedChatEntries(chatEntries);

        _sources = await QueryKnowledgeSourcesAsync(kernel, sanitizedChatResult.EntryTexts);

        var salesAgent = _shortAgentDefinitions.GetSalesAgent(
            kernel,
            _sources,
            ShortAgentDefinitions.SalesAgentName,
            agentCollaborationConfig.DetectedResponseLanguage,
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH_2_5, true));

        return
        [
            salesAgent
        ];
    }

    private async Task<string> QueryKnowledgeSourcesAsync(Kernel kernel, List<string> entryTexts)
    {
        var fullConversationQueryTask = _sourceOnlyKnowledgePlugin.QueryKnowledgeSourcesAsync(
            kernel,
            string.Join("--\n", entryTexts),
            10);

        if (entryTexts.Count <= 1)
        {
            return await fullConversationQueryTask;
        }

        // conversation has 2 or more messages, we do a separate query on the last message to improve relevance
        var lastMessageQueryTask = _sourceOnlyKnowledgePlugin.QueryKnowledgeSourcesAsync(
            kernel,
            entryTexts.Last(),
            10);

        var sources = await Task.WhenAll(fullConversationQueryTask, lastMessageQueryTask);
        return string.Join("\n", sources);
    }

    public override async Task<string> GetFinalReplyAsync(ChatHistory chatHistory)
    {
        var finalReplyKey = GetFinalReplyTag();

        // Find the message containing the final reply
        var finalChatMessage = chatHistory
            .LastOrDefault(
                x =>
                    x.Content != null
                    && JsonUtils.TryParseJson<Dictionary<string, object>>(x.Content, out var json)
                    && json != null
                    && json.ContainsKey(finalReplyKey))
            ?.Content;

        if (finalChatMessage == null)
        {
            _logger.LogError("Final chat response missing or invalid");
            throw new Exception("Final reply not found in chat messages.");
        }

        if (!JsonUtils.TryParseJson<Dictionary<string, object>>(finalChatMessage, out var finalJson))
        {
            throw new Exception("Could not parse final reply JSON. " + finalChatMessage);
        }

        var finalReplyToCustomer = finalJson?[finalReplyKey].ToString()?.Trim() ?? string.Empty;

        // We encountered a scenario where the final reply to the customer was not decoded properly.
        // The emoji is displayed as a string instead of the actual emoji.
        // e.g. "👍" is displayed as "&#128077;". We need to decode the HTML entities.
        finalReplyToCustomer = WebUtility.HtmlDecode(finalReplyToCustomer);

        // Convert HTML-style formatting tags to WhatsApp markdown format
        finalReplyToCustomer = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(finalReplyToCustomer);

        return finalReplyToCustomer;
    }

    public override Task<string> GetSourceAsync(ChatHistory chatHistory, string groupChatIdStr)
    {
        return Task.FromResult(_sources ?? string.Empty);
    }

    public override SelectionStrategy? CreateSelectionStrategy(Kernel kernel)
    {
        return new SequentialSelectionStrategy();
    }

    public override RegexTerminationStrategy? CreateTerminationStrategy(List<Agent> agents)
    {
        return new RegexTerminationStrategy("reply_to_customer")
        {
            MaximumIterations = 15,
            AutomaticReset = true,
            Agents = agents.Where(a => a.Name == ShortAgentDefinitions.SalesAgentName).ToList()
        };
    }

    public override string GetFinalReplyTag() => "reply_to_customer";

    public override string GetSourceTag() => "CONFIRMED_KNOWLEDGE";
}