# API Versioning and Documentation

This document is part of [Sleekflow Project Practices](ProjectPractices.md).

## Swagger Generation

Swagger is utilized to track API interface changes:

```shell
dotnet build /p:SwaggerGen=TRUE
```

The generated files are located in `.swagger` directories and committed to Git for version tracking.

## API Versioning

The project follows standard ASP.NET API versioning practices as documented at [aspnet-api-versioning/wiki](https://github.com/dotnet/aspnet-api-versioning/wiki).