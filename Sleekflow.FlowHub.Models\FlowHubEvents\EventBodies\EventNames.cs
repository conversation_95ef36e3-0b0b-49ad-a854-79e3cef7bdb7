﻿namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

public static class EventNames
{
    public const string OnContactConversationStatusChanged = "OnContactConversationStatusChanged";
    public const string OnContactCreated = "OnContactCreated";
    public const string OnContactLabelRelationshipsChanged = "OnContactLabelRelationshipsChanged";
    public const string OnContactListRelationshipsChanged = "OnContactListRelationshipsChanged";
    public const string OnContactUpdated = "OnContactUpdated";
    public const string OnMessageReceived = "OnMessageReceived";
    public const string OnMessageSent = "OnMessageSent";
    public const string OnWebhook = "OnWebhook";
    public const string OnFbIgPostCommentReceived = "OnFbIgPostCommentReceived";
    public const string OnInstagramMediaCommentReceived = "OnInstagramMediaCommentReceived";
    public const string OnSalesforceObjectUpdated = "OnSalesforceObjectUpdated";
    public const string OnSalesforceObjectCreated = "OnSalesforceObjectCreated";
    public const string OnSalesforceObjectEnrolled = "OnSalesforceObjectEnrolled";
    public const string OnClickToWhatsAppAdsMessageReceived = "OnClickToWhatsAppAdsMessageReceived";
    public const string OnSchemafulObjectCreated = "OnSchemafulObjectCreated";
    public const string OnSchemafulObjecUpdated = "OnSchemafulObjecUpdated";
    public const string OnContactEnrolled = "OnContactEnrolled";
    public const string OnContactManuallyEnrolled = "OnContactManuallyEnrolled";
    public const string OnContactRecurrentlyEnrolled = "OnContactRecurrentlyEnrolled";
    public const string OnSchemafulObjectEnrolled = "OnSchemafulObjectEnrolled";
    public const string OnWhatsappFlowSubmissionMessageReceived = "OnWhatsappFlowSubmissionMessageReceived";
    public const string OnMessageStatusUpdated = "OnMessageStatusUpdated";
    public const string OnTicketCreated = "OnTicketCreated";
    public const string OnTicketUpdated = "OnTicketUpdated";
    public const string OnGoogleSheetsRowCreated = "OnGoogleSheetsRowCreated";
    public const string OnGoogleSheetsRowUpdated = "OnGoogleSheetsRowUpdated";
    public const string OnHubspotObjectEnrolled = "OnHubspotObjectEnrolled";
    public const string OnHubspotObjectUpdated = "OnHubspotObjectUpdated";
    public const string OnHubspotObjectCreated = "OnHubspotObjectCreated";
    public const string OnZohoObjectUpdated = "OnZohoObjectUpdated";
    public const string OnZohoObjectCreated = "OnZohoObjectCreated";
    public const string OnZohoObjectEnrolled = "OnZohoObjectEnrolled";
    public const string OnMetaDetectedOutcomeReceived = "OnMetaDetectedOutcomeReceived";
    public const string OnScheduledWorkflowEnrolled = "OnScheduledWorkflowEnrolled";
    public const string OnVtexOrderCreated = "OnVtexOrderCreated";
    public const string OnVtexOrderStatusChanged = "OnVtexOrderStatusChanged";
    public const string OnScheduledDateTimeArrived = "OnScheduledDateTimeArrived";
    public const string OnVtexOrderEnrolled = "OnVtexOrderEnrolled";
    public const string OnDateAndTimeArrived = "OnDateAndTimeArrived";
    public const string OnTikTokAdsLeadReceived = "OnTikTokAdsLeadReceived";
    public const string OnShopifyOrderCreated = "OnShopifyOrderCreated";
    public const string OnShopifyOrderUpdated = "OnShopifyOrderUpdated";
    public const string OnShopifyCustomerCreated = "OnShopifyCustomerCreated";
    public const string OnShopifyCustomerUpdated = "OnShopifyCustomerUpdated";
    public const string OnShopifyOrderEnrolled = "OnShopifyOrderEnrolled";
    public const string OnShopifyAbandonedCartCreated = "OnShopifyAbandonedCartCreated";
    public const string OnShopifyAbandonedCartUpdated = "OnShopifyAbandonedCartUpdated";
}