using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Connectors.Google;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Sleekflow.DependencyInjection;

namespace Sleekflow.IntelligentHub.Kernels;

public interface IPromptExecutionSettingsService
{
    PromptExecutionSettings GetPromptExecutionSettings(string serviceId, bool isJson = false, float temperature = 0f);
}

public class PromptExecutionSettingsService
    : IPromptExecutionSettingsService, ISingletonService
{
    public PromptExecutionSettings GetPromptExecutionSettings(
        string serviceId,
        bool isJson = false,
        float temperature = 0f)
    {
        PromptExecutionSettings promptExecutionSettings;
        if (SemanticKernelExtensions.AzureOpenAiChatCompletionServices.Contains(serviceId))
        {
            if (serviceId is SemanticKernelExtensions.S_O3_MINI or SemanticKernelExtensions.S_O4_MINI)
            {
                promptExecutionSettings = new OpenAIPromptExecutionSettings
                {
                    MaxTokens = 4096, Temperature = temperature,
                };
                promptExecutionSettings.ServiceId = serviceId;
            }
            else
            {
                promptExecutionSettings = new OpenAIPromptExecutionSettings
                {
                    Temperature = temperature,
                    MaxTokens = 8192,
                    ServiceId = serviceId,
                    FunctionChoiceBehavior = FunctionChoiceBehavior.None(),
                };
            }

            if (isJson)
            {
#pragma warning disable SKEXP0010
                ((OpenAIPromptExecutionSettings) promptExecutionSettings).ResponseFormat = "json_object";
#pragma warning restore SKEXP0010
            }
        }
        else if (SemanticKernelExtensions.GoogleGeminiChatCompletionServices.Contains(serviceId))
        {
#pragma warning disable SKEXP0070
            promptExecutionSettings = new GeminiPromptExecutionSettings();
            promptExecutionSettings.ServiceId = serviceId;
            ((GeminiPromptExecutionSettings) promptExecutionSettings).MaxTokens = 8192;
            ((GeminiPromptExecutionSettings) promptExecutionSettings).Temperature = temperature;

            var nonThinkingModels = new[]
            {
                SemanticKernelExtensions.S_FLASH_2_5,
            };

            if (nonThinkingModels.Contains(serviceId))
            {
                ((GeminiPromptExecutionSettings) promptExecutionSettings).ThinkingConfig = new GeminiThinkingConfig
                {
                    ThinkingBudget = 0,
                };
            }

            if (isJson)
            {
                ((GeminiPromptExecutionSettings) promptExecutionSettings).ResponseMimeType = "application/json";
            }
#pragma warning restore SKEXP0070
        }
        else
        {
            promptExecutionSettings = new PromptExecutionSettings
            {
                ServiceId = serviceId
            };
        }

        return promptExecutionSettings;
    }
}