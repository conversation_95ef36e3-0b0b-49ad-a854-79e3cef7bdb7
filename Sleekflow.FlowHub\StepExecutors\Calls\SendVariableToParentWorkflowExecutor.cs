using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.Utils;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.Events;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ISendVariableToParentWorkflowExecutor : IStepExecutor;

public class SendVariableToParentWorkflowExecutor(
    IWorkflowStepLocator workflowStepLocator,
    IWorkflowRuntimeService workflowRuntimeService,
    IServiceProvider serviceProvider,
    IStateService stateService,
    IServiceBusManager serviceBusManager,
    ILogger<SendVariableToParentWorkflowExecutor> logger)
    : GeneralStepExecutor<CallStep<SendVariableToParentWorkflowStepArgs>>(
            workflowStepLocator,
            workflowRuntimeService,
            serviceProvider),
        ISendVariableToParentWorkflowExecutor,
        IScopedService
{
    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        try
        {
            var runningStates = await stateService.GetRunningStatesAsync(
                state.Identity.ObjectId,
                state.Identity.ObjectType,
                state.Identity.SleekflowCompanyId,
                state.TriggerEventBody);

            var (parentState, targetStepInParent) = StateUtils.GetParentState(
                runningStates,
                state.WorkflowContext.SnapshottedWorkflow.WorkflowId,
                logger);

            var fieldsToInclude = new HashSet<string>
            {
                "category", "score", "confidence_score", "exit_condition"
            };

            var sysVarDict = await state.SysVarDict.GetInnerDictionary().ToDictionaryAsync();
            var mergedVariablesForParent = DictionaryTransformUtils.MergeAndFilterValues(
                sysVarDict.Values,
                fieldsToInclude);

            var callStep = ToConcreteStep(step);

            var exitCondition = callStep.Args.ExitConditionExpr;
            if (exitCondition != null)
            {
                mergedVariablesForParent["exit_condition"] = exitCondition;
            }

            await serviceBusManager
                .PublishAsync(
                    new OnAgentCompleteStepActivationEvent(
                        targetStepInParent.Id,
                        parentState.Id,
                        stackEntries,
                        JsonConvert.SerializeObject(mergedVariablesForParent)));

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }

        // catch when GetParentState failed
        catch (SfWorkflowParentStateNotFoundException ex)
        {
            // log but not count as enrollemnt fail
            logger.LogError(ex, ex.Message);
            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception ex)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                ex);
        }
    }
}