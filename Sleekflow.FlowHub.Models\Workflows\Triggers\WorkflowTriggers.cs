using Newtonsoft.Json;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Models.Workflows.Triggers;

public class WorkflowTriggers
{
    [ValidateObject]
    [JsonProperty("conversation_status_changed")]
    public WorkflowTrigger? ConversationStatusChanged { get; set; }

    [ValidateObject]
    [JsonProperty("contact_created")]
    public WorkflowTrigger? ContactCreated { get; set; }

    [ValidateObject]
    [JsonProperty("contact_label_relationships_changed")]
    public WorkflowTrigger? ContactLabelRelationshipsChanged { get; set; }

    [ValidateObject]
    [JsonProperty("contact_list_relationships_changed")]
    public WorkflowTrigger? ContactListRelationshipsChanged { get; set; }

    [ValidateObject]
    [JsonProperty("contact_updated")]
    public WorkflowTrigger? ContactUpdated { get; set; }

    [ValidateObject]
    [JsonProperty("message_received")]
    public WorkflowTrigger? MessageReceived { get; set; }

    [ValidateObject]
    [JsonProperty("message_sent")]
    public WorkflowTrigger? MessageSent { get; set; }

    [ValidateObject]
    [JsonProperty("webhook")]
    public WorkflowTrigger? Webhook { get; set; }

    [ValidateObject]
    [JsonProperty("facebook_post_comment_received")]
    public WorkflowTrigger? FbIgPostCommentReceived { get; set; }

    [ValidateObject]
    [JsonProperty("instagram_media_comment_received")]
    public WorkflowTrigger? InstagramMediaCommentReceived { get; set; }

    [ValidateObject]
    [JsonProperty("salesforce_account_updated")]
    public WorkflowTrigger? SalesforceAccountUpdated { get; set; }

    [ValidateObject]
    [JsonProperty("salesforce_account_created")]
    public WorkflowTrigger? SalesforceAccountCreated { get; set; }

    [ValidateObject]
    [JsonProperty("salesforce_contact_updated")]
    public WorkflowTrigger? SalesforceContactUpdated { get; set; }

    [ValidateObject]
    [JsonProperty("salesforce_contact_created")]
    public WorkflowTrigger? SalesforceContactCreated { get; set; }

    [ValidateObject]
    [JsonProperty("salesforce_lead_updated")]
    public WorkflowTrigger? SalesforceLeadUpdated { get; set; }

    [ValidateObject]
    [JsonProperty("salesforce_lead_created")]
    public WorkflowTrigger? SalesforceLeadCreated { get; set; }

    [ValidateObject]
    [JsonProperty("salesforce_opportunity_updated")]
    public WorkflowTrigger? SalesforceOpportunityUpdated { get; set; }

    [ValidateObject]
    [JsonProperty("salesforce_opportunity_created")]
    public WorkflowTrigger? SalesforceOpportunityCreated { get; set; }

    [ValidateObject]
    [JsonProperty("salesforce_campaign_updated")]
    public WorkflowTrigger? SalesforceCampaignUpdated { get; set; }

    [ValidateObject]
    [JsonProperty("salesforce_campaign_created")]
    public WorkflowTrigger? SalesforceCampaignCreated { get; set; }

    [ValidateObject]
    [JsonProperty("salesforce_custom_object_created")]
    public WorkflowTrigger? SalesforceCustomObjectCreated { get; set; }

    [ValidateObject]
    [JsonProperty("salesforce_custom_object_updated")]
    public WorkflowTrigger? SalesforceCustomObjectUpdated { get; set; }

    [ValidateObject]
    [JsonProperty("click_to_whatsapp_ads_message_received")]
    public WorkflowTrigger? ClickToWhatsAppAdsMessageReceived { get; set; }

    [ValidateObject]
    [JsonProperty("schemaful_object_created")]
    public WorkflowTrigger? SchemafulObjectCreated { get; set; }

    [ValidateObject]
    [JsonProperty("schemaful_object_updated")]
    public WorkflowTrigger? SchemafulObjectUpdated { get; set; }

    [ValidateObject]
    [JsonProperty("salesforce_account_enrolled")]
    public WorkflowTrigger? SalesforceAccountEnrolled { get; set; }

    [ValidateObject]
    [JsonProperty("salesforce_contact_enrolled")]
    public WorkflowTrigger? SalesforceContactEnrolled { get; set; }

    [ValidateObject]
    [JsonProperty("salesforce_lead_enrolled")]
    public WorkflowTrigger? SalesforceLeadEnrolled { get; set; }

    [ValidateObject]
    [JsonProperty("salesforce_opportunity_enrolled")]
    public WorkflowTrigger? SalesforceOpportunityEnrolled { get; set; }

    [ValidateObject]
    [JsonProperty("salesforce_campaign_enrolled")]
    public WorkflowTrigger? SalesforceCampaignEnrolled { get; set; }

    [ValidateObject]
    [JsonProperty("salesforce_custom_object_enrolled")]
    public WorkflowTrigger? SalesforceCustomObjectEnrolled { get; set; }

    [ValidateObject]
    [JsonProperty("scheduled_workflow_contact_enrolled")]
    public WorkflowTrigger? ScheduledWorkflowContactEnrolled { get; set; }

    [ValidateObject]
    [JsonProperty("contact_enrolled")]
    public WorkflowTrigger? ContactEnrolled { get; set; }

    [ValidateObject]
    [JsonProperty("contact_manually_enrolled")]
    public WorkflowTrigger? ContactManuallyEnrolled { get; set; }

    [ValidateObject]
    [JsonProperty("schemaful_object_enrolled")]
    public WorkflowTrigger? SchemafulObjectEnrolled { get; set; }

    [ValidateObject]
    [JsonProperty("whatsapp_flow_submission_message_received")]
    public WorkflowTrigger? WhatsappFlowSubmissionMessageReceived { get; set; }

    [ValidateObject]
    [JsonProperty("message_status_updated")]
    public WorkflowTrigger? MessageStatusUpdated { get; set; }

    [ValidateObject]
    [JsonProperty("ticket_updated")]
    public WorkflowTrigger? TicketUpdated { get; set; }

    [ValidateObject]
    [JsonProperty("google_sheets_row_created")]
    public WorkflowTrigger? GoogleSheetsRowCreated { get; set; }

    [ValidateObject]
    [JsonProperty("google_sheets_row_updated")]
    public WorkflowTrigger? GoogleSheetsRowUpdated { get; set; }

    [ValidateObject]
    [JsonProperty("hubspot_object_enrolled")]
    public WorkflowTrigger? HubspotObjectEnrolled { get; set; }

    [ValidateObject]
    [JsonProperty("hubspot_object_updated")]
    public WorkflowTrigger? HubspotObjectUpdated { get; set; }

    [ValidateObject]
    [JsonProperty("hubspot_object_created")]
    public WorkflowTrigger? HubspotObjectCreated { get; set; }

    [ValidateObject]
    [JsonProperty("zoho_object_updated")]
    public WorkflowTrigger? ZohoObjectUpdated { get; set; }

    [ValidateObject]
    [JsonProperty("zoho_object_created")]
    public WorkflowTrigger? ZohoObjectCreated { get; set; }

    [ValidateObject]
    [JsonProperty("zoho_object_enrolled")]
    public WorkflowTrigger? ZohoObjectEnrolled { get; set; }

    [ValidateObject]
    [JsonProperty("tiktok_ads_lead_received")]
    public WorkflowTrigger? TikTokAdsLeadReceived { get; set; }

    [ValidateObject]
    [JsonProperty("salesforce_object_created")]
    public WorkflowTrigger? SalesforceObjectCreated { get; set; }

    [ValidateObject]
    [JsonProperty("salesforce_object_updated")]
    public WorkflowTrigger? SalesforceObjectUpdated { get; set; }

    [ValidateObject]
    [JsonProperty("meta_detected_outcome_received")]
    public WorkflowTrigger? MetaDetectedOutcomeReceived { get; set; }

    [ValidateObject]
    [JsonProperty("vtex_order_created")]
    public WorkflowTrigger? VtexOrderCreated { get; set; }

    [ValidateObject]
    [JsonProperty("vtex_order_status_changed")]
    public WorkflowTrigger? VtexOrderStatusChanged { get; set; }

    [ValidateObject]
    [JsonProperty("vtex_order_enrolled")]
    public WorkflowTrigger? VtexOrderEnrolled { get; set; }

    [JsonProperty("scheduled_date_and_time_enrolled")]
    public WorkflowTrigger? ScheduledDateAndTimeEnrolled { get; set; }

    [JsonProperty("contact_property_date_and_time_enrolled")]
    public WorkflowTrigger? ContactPropertyDateAndTimeEnrolled { get; set; }

    [JsonProperty("schemaful_object_property_date_and_time_enrolled")]
    public WorkflowTrigger? SchemafulObjectPropertyDateAndTimeEnrolled { get; set; }

    [ValidateObject]
    [JsonProperty("shopify_order_created")]
    public WorkflowTrigger? ShopifyOrderCreated { get; set; }

    [ValidateObject]
    [JsonProperty("shopify_order_updated")]
    public WorkflowTrigger? ShopifyOrderUpdated { get; set; }

    [ValidateObject]
    [JsonProperty("shopify_customer_created")]
    public WorkflowTrigger? ShopifyCustomerCreated { get; set; }

    [ValidateObject]
    [JsonProperty("shopify_customer_updated")]
    public WorkflowTrigger? ShopifyCustomerUpdated { get; set; }

    [ValidateObject]
    [JsonProperty("shopify_order_enrolled")]
    public WorkflowTrigger? ShopifyOrderEnrolled { get; set; }

    [ValidateObject]
    [JsonProperty("shopify_abandoned_cart_created")]
    public WorkflowTrigger? ShopifyAbandonedCartCreated { get; set; }

    [ValidateObject]
    [JsonProperty("shopify_abandoned_cart_updated")]
    public WorkflowTrigger? ShopifyAbandonedCartUpdated { get; set; }

    [JsonConstructor]
    public WorkflowTriggers(
        WorkflowTrigger? conversationStatusChanged,
        WorkflowTrigger? contactCreated,
        WorkflowTrigger? contactLabelRelationshipsChanged,
        WorkflowTrigger? contactListRelationshipsChanged,
        WorkflowTrigger? contactUpdated,
        WorkflowTrigger? messageReceived,
        WorkflowTrigger? messageSent,
        WorkflowTrigger? webhook,
        WorkflowTrigger? fbIgPostCommentReceived,
        WorkflowTrigger? instagramMediaCommentReceived,
        WorkflowTrigger? salesforceAccountUpdated,
        WorkflowTrigger? salesforceAccountCreated,
        WorkflowTrigger? salesforceContactUpdated,
        WorkflowTrigger? salesforceContactCreated,
        WorkflowTrigger? salesforceLeadUpdated,
        WorkflowTrigger? salesforceLeadCreated,
        WorkflowTrigger? salesforceOpportunityUpdated,
        WorkflowTrigger? salesforceOpportunityCreated,
        WorkflowTrigger? salesforceCampaignUpdated,
        WorkflowTrigger? salesforceCampaignCreated,
        WorkflowTrigger? salesforceCustomObjectCreated,
        WorkflowTrigger? salesforceCustomObjectUpdated,
        WorkflowTrigger? salesforceAccountEnrolled,
        WorkflowTrigger? salesforceContactEnrolled,
        WorkflowTrigger? salesforceLeadEnrolled,
        WorkflowTrigger? salesforceOpportunityEnrolled,
        WorkflowTrigger? salesforceCampaignEnrolled,
        WorkflowTrigger? salesforceCustomObjectEnrolled,
        WorkflowTrigger? contactEnrolled,
        WorkflowTrigger? clickToWhatsAppAdsMessageReceived,
        WorkflowTrigger? schemafulObjectCreated,
        WorkflowTrigger? schemafulObjectUpdated,
        WorkflowTrigger? scheduledWorkflowContactEnrolled,
        WorkflowTrigger? contactManuallyEnrolled,
        WorkflowTrigger? schemafulObjectEnrolled,
        WorkflowTrigger? whatsappFlowSubmissionMessageReceived,
        WorkflowTrigger? messageStatusUpdated,
        WorkflowTrigger? ticketUpdated,
        WorkflowTrigger? googleSheetsRowCreated,
        WorkflowTrigger? googleSheetsRowUpdated,
        WorkflowTrigger? hubspotObjectEnrolled,
        WorkflowTrigger? hubspotObjectUpdated,
        WorkflowTrigger? hubspotObjectCreated,
        WorkflowTrigger? zohoObjectUpdated,
        WorkflowTrigger? zohoObjectCreated,
        WorkflowTrigger? zohoObjectEnrolled,
        WorkflowTrigger? tiktokAdsLeadReceived,
        WorkflowTrigger? salesforceObjectCreated,
        WorkflowTrigger? salesforceObjectUpdated,
        WorkflowTrigger? metaDetectedOutcomeReceived,
        WorkflowTrigger? vtexOrderCreated,
        WorkflowTrigger? vtexOrderStatusChanged,
        WorkflowTrigger? vtexOrderEnrolled,
        WorkflowTrigger? scheduledDateTimeReceived,
        WorkflowTrigger? shopifyOrderCreated,
        WorkflowTrigger? shopifyOrderUpdated,
        WorkflowTrigger? shopifyCustomerCreated,
        WorkflowTrigger? shopifyCustomerUpdated,
        WorkflowTrigger? shopifyOrderEnrolled,
        WorkflowTrigger? shopifyAbandonedCartCreated,
        WorkflowTrigger? shopifyAbandonedCartUpdated)
    {
        ConversationStatusChanged = conversationStatusChanged;
        ContactCreated = contactCreated;
        ContactLabelRelationshipsChanged = contactLabelRelationshipsChanged;
        ContactListRelationshipsChanged = contactListRelationshipsChanged;
        ContactUpdated = contactUpdated;
        MessageReceived = messageReceived;
        MessageSent = messageSent;
        Webhook = webhook;
        FbIgPostCommentReceived = fbIgPostCommentReceived;
        InstagramMediaCommentReceived = instagramMediaCommentReceived;
        SalesforceAccountUpdated = salesforceAccountUpdated;
        SalesforceAccountCreated = salesforceAccountCreated;
        SalesforceContactUpdated = salesforceContactUpdated;
        SalesforceContactCreated = salesforceContactCreated;
        SalesforceLeadUpdated = salesforceLeadUpdated;
        SalesforceLeadCreated = salesforceLeadCreated;
        SalesforceOpportunityUpdated = salesforceOpportunityUpdated;
        SalesforceOpportunityCreated = salesforceOpportunityCreated;
        SalesforceCampaignUpdated = salesforceCampaignUpdated;
        SalesforceCampaignCreated = salesforceCampaignCreated;
        SalesforceCustomObjectCreated = salesforceCustomObjectCreated;
        SalesforceCustomObjectUpdated = salesforceCustomObjectUpdated;
        SalesforceAccountEnrolled = salesforceAccountEnrolled;
        SalesforceContactEnrolled = salesforceContactEnrolled;
        SalesforceLeadEnrolled = salesforceLeadEnrolled;
        SalesforceOpportunityEnrolled = salesforceOpportunityEnrolled;
        SalesforceCampaignEnrolled = salesforceCampaignEnrolled;
        SalesforceCustomObjectEnrolled = salesforceCustomObjectEnrolled;
        ContactEnrolled = contactEnrolled;
        ClickToWhatsAppAdsMessageReceived = clickToWhatsAppAdsMessageReceived;
        SchemafulObjectCreated = schemafulObjectCreated;
        SchemafulObjectUpdated = schemafulObjectUpdated;
        ScheduledWorkflowContactEnrolled = scheduledWorkflowContactEnrolled;
        ContactManuallyEnrolled = contactManuallyEnrolled;
        SchemafulObjectEnrolled = schemafulObjectEnrolled;
        WhatsappFlowSubmissionMessageReceived = whatsappFlowSubmissionMessageReceived;
        MessageStatusUpdated = messageStatusUpdated;
        TicketUpdated = ticketUpdated;
        GoogleSheetsRowCreated = googleSheetsRowCreated;
        GoogleSheetsRowUpdated = googleSheetsRowUpdated;
        HubspotObjectEnrolled = hubspotObjectEnrolled;
        HubspotObjectUpdated = hubspotObjectUpdated;
        HubspotObjectCreated = hubspotObjectCreated;
        ZohoObjectUpdated = zohoObjectUpdated;
        ZohoObjectCreated = zohoObjectCreated;
        ZohoObjectEnrolled = zohoObjectEnrolled;
        TikTokAdsLeadReceived = tiktokAdsLeadReceived;
        SalesforceObjectCreated = salesforceObjectCreated;
        SalesforceObjectUpdated = salesforceObjectUpdated;
        MetaDetectedOutcomeReceived = metaDetectedOutcomeReceived;
        VtexOrderCreated = vtexOrderCreated;
        VtexOrderStatusChanged = vtexOrderStatusChanged;
        VtexOrderEnrolled = vtexOrderEnrolled;
        ShopifyOrderCreated = shopifyOrderCreated;
        ShopifyOrderUpdated = shopifyOrderUpdated;
        ShopifyCustomerCreated = shopifyCustomerCreated;
        ShopifyCustomerUpdated = shopifyCustomerUpdated;
        ShopifyOrderEnrolled = shopifyOrderEnrolled;
        ShopifyAbandonedCartCreated = shopifyAbandonedCartCreated;
        ShopifyAbandonedCartUpdated = shopifyAbandonedCartUpdated;
    }
}