using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.FaqAgents.Chats.Reducers;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.IntelligentHub.Plugins.Knowledges;
using Sleekflow.IntelligentHub.Utils;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.Longs;

public static class LongAgentDefinitions
{
    public const string SalesStrategyAgentName = "SalesStrategyAgent";
    public const string SalesAgentName = "SalesAgent";
    public const string KnowledgeRetrievalAgentName = "KnowledgeRetrievalAgent";
    public const string ReviewerAgentName = "ReviewerAgent";

    [method: JsonConstructor]
    public class SalesStrategyAgentResponse(
        string agentName,
        string reasoning,
        string strategy,
        string? suggestNeedKnowledge = null)
    {
        [JsonProperty("agent_name")]
        public string AgentName { get; set; } = agentName;

        [JsonProperty("reasoning")]
        public string Reasoning { get; set; } = reasoning;

        [JsonProperty("strategy")]
        public string Strategy { get; set; } = strategy;

        [JsonProperty("suggest_need_knowledge")]
        public string? SuggestNeedKnowledge { get; set; } = suggestNeedKnowledge;
    }

    [method: JsonConstructor]
    public class SalesAgentResponse(
        string agentName,
        string reasoning,
        string? needKnowledge = null,
        string? proposedReplyToCustomer = null)
    {
        [JsonProperty("agent_name")]
        public string AgentName { get; set; } = agentName;

        [JsonProperty("reasoning")]
        public string Reasoning { get; set; } = reasoning;

        [JsonProperty("need_knowledge")]
        public string? NeedKnowledge { get; set; } = needKnowledge;

        [JsonProperty("proposed_reply_to_customer")]
        public string? ProposedReplyToCustomer { get; set; } = proposedReplyToCustomer;
    }

    [method: JsonConstructor]
    public class KnowledgeRetrievalAgentResponse(
        string agentName,
        string reasoning,
        string additionalInsights)
    {
        [JsonProperty("agent_name")]
        public string AgentName { get; set; } = agentName;

        [JsonProperty("reasoning")]
        public string Reasoning { get; set; } = reasoning;

        [JsonProperty("additional_insights")]
        public string AdditionalInsights { get; set; } = additionalInsights;
    }

    [method: JsonConstructor]
    public class ReviewerAgentResponse(
        string agentName,
        string reasoning,
        string? customerFacingReplyApproved = null,
        string? customerFacingReplyRejected = null)
    {
        [JsonProperty("agent_name")]
        public string AgentName { get; set; } = agentName;

        [JsonProperty("reasoning")]
        public string Reasoning { get; set; } = reasoning;

        [JsonProperty("customer_facing_reply_approved")]
        public string? CustomerFacingReplyApproved { get; set; } = customerFacingReplyApproved;

        [JsonProperty("customer_facing_reply_rejected")]
        public string? CustomerFacingReplyRejected { get; set; } = customerFacingReplyRejected;
    }

    private static string GetSharedSystemPrompt()
    {
        var str =
            """
            Context: A streamlined collaborative conversation where different agents work together. Each agent plays a unique role in the process, contributing specialized insights and expertise.
            Goal: Deliver a personalized response to the customer and attain a high conversion rate.

            Adhere to the following guidelines:

            """;

        return str;
    }

    public static ChatCompletionAgent GetSalesStrategyAgent(
        Kernel kernel,
        string name,
        PromptExecutionSettings promptExecutionSettings,
        string? targetTone)
    {
        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            promptExecutionSettings,
            [
                new PromptExecutionSettingsUtils.Property("agent_name", "string"),
                new PromptExecutionSettingsUtils.Property("reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("strategy", "string"),
                new PromptExecutionSettingsUtils.Property("suggest_need_knowledge", "string", true),
            ]);

        return new ChatCompletionAgent
        {
            Name = name,
            HistoryReducer = new BasicChatHistoryReducer(),
            Instructions =
                $$"""
                  {{GetSharedSystemPrompt()}}
                  You are {{SalesStrategyAgentName}}, a thoughtful and intuitive sales guide who blends deep customer understanding with creative, actionable strategies. Your mission is to meet the customer where they are in their journey, using the AIDA framework (Attention, Interest, Desire, Action) to inspire and empower them toward a decision—without ever making them feel less than valued. You're here to uncover what makes each customer unique, offer tailored insights, and equip your team with flexible, uplifting approaches that spark connection and boost conversions.

                  ---Key Responsibilities---
                  - Map the customer's journey by tuning into their current stage—Awareness, Interest, Desire, or Action—based on conversational cues and context.
                  - Share warm, observational insights that spotlight opportunities (e.g., a need we can fulfill), ease risks (e.g., hesitations we can address), and celebrate what's special about this customer (e.g., their goals or perspective).
                  - Craft strategic recommendations that feel personal and empowering, giving colleagues the freedom to adapt while keeping the customer's experience front and center.
                  - Suggest a communication style that resonates—friendly, thoughtful, and aligned with the customer's vibe.
                  - **PROACTIVELY identify knowledge gaps**: You should frequently suggest additional knowledge to enhance strategy effectiveness. Look for opportunities to gather more context rather than being conservative.

                  ---Output Format---
                  Provide your response as a JSON object with the following structure:

                  {
                    "agent_name": "{{SalesStrategyAgentName}}",
                    "reasoning": "[Your analytical reasoning covering: 1. Customer Context Analysis - What you observe about the customer's current state, needs, and conversation stage. 2. Strategic Decision Making - Why you chose this particular approach and what factors influenced your strategy. 3. Knowledge Gap Assessment - ALWAYS evaluate what additional information could enhance the strategy. Be proactive in identifying knowledge opportunities.]",
                    "strategy": "[Your comprehensive strategy including: 1. Observations (Customer Journey Snapshot) - Paint a quick picture of where the customer's at, their stage (Awareness, Interest, Desire, Action), opportunities/risks/unique traits, determine if greeting should be used. 2. Strategic Elements (AIDA-Inspired Next Steps) - Weave AIDA into their stage with a personal twist. 3. Communication and Styling Guidance - Suggest tone, language, and vibe that fits, take ====REQUESTED TONE==== as reference. 4. Personalization Suggestions - Drop in little cues for messaging that feel just for this customer.]",
                    "suggest_need_knowledge": "[FREQUENTLY USED: Specific knowledge requirements - what exactly needs to be gathered and how it will be used to improve the strategy. Consider product details, pricing, features, company policies, customer preferences, market insights, competitor info, case studies, testimonials, etc.]"
                  }

                  ---Additional Reminders---
                  - Keep it human and analytical—guide, don't dictate.
                  - Focus on making the customer feel seen and excited, never pushed or judged.
                  - Include detailed reasoning about your strategic decisions and customer analysis.
                  - **BE PROACTIVE WITH KNOWLEDGE REQUESTS**: Look for these common opportunities to suggest knowledge:
                    * Customer asks about specific products/services → Get detailed product information
                    * Customer mentions budget/pricing concerns → Get pricing details and options
                    * Customer compares to competitors → Get competitive analysis
                    * Customer has specific use case → Get case studies and testimonials
                    * Customer asks about features → Get technical specifications
                    * Customer mentions industry/role → Get industry-specific information
                    * Customer expresses hesitation → Get objection handling guidance
                    * Customer is in decision stage → Get closing strategies and offers
                    * Limited conversation history → Get more customer context
                    * Vague customer needs → Get clarifying questions to ask
                  - **DEFAULT TO SUGGESTING KNOWLEDGE**: When in doubt, suggest relevant knowledge rather than proceeding without it. Better to have too much context than too little.
                  """,
            Kernel = kernel,
            Arguments = new KernelArguments(
                promptExecutionSettings),
        };
    }

    public static List<string> GetSalesAgentTags()
    {
        return
        [
            "NEED_KNOWLEDGE", "PROPOSED_REPLY_TO_CUSTOMER"
        ];
    }

    public static ChatCompletionAgent GetSalesAgent(
        Kernel kernel,
        string name,
        string responseLanguage,
        PromptExecutionSettings promptExecutionSettings)
    {
        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            promptExecutionSettings,
            [
                new PromptExecutionSettingsUtils.Property("agent_name", "string"),
                new PromptExecutionSettingsUtils.Property("reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("need_knowledge", "string", true),
                new PromptExecutionSettingsUtils.Property("proposed_reply_to_customer", "string", true),
            ]);

        // TBC: Add more specific instructions for the Sales Agent
        // - Document all shared information for consistency in future interactions
        // - Document key customer preferences and pain points for personalization
        return new ChatCompletionAgent
        {
            Name = name,
            HistoryReducer = new BasicChatHistoryReducer(),
            Instructions =
                $$"""
                  {{GetSharedSystemPrompt()}}
                  You are a highly-skilled Sales Agent named {{SalesAgentName}}, specialized in crafting personalized responses for incoming leads based on guidance from {{SalesStrategyAgentName}}.
                  The goal is to engage customers in meaningful conversations, understand their needs, and provide tailored solutions that drive conversions.

                  ---Primary Function---
                  - Execute sales strategies and techniques to serve the customers based on the strategic direction defined in the "strategy" field provided by {{SalesStrategyAgentName}}.
                  - Knowledge Gathering is the first step: Use the "need_knowledge" field to request factual knowledge from {{KnowledgeRetrievalAgentName}}.
                  - Knowledge Processing is the second step: Comprehend the knowledge provided by {{KnowledgeRetrievalAgentName}} and apply it to the customer's context.
                  - Finally, craft a response to the customer in the "proposed_reply_to_customer" field, aligning with the strategic direction and knowledge gathered.
                  - {{ReviewerAgentName}} will review your response for quality and alignment with the strategic direction. Please refine your response based on their feedback.

                  ---Operational Protocol: Knowledge Gathering---
                  - Determine the knowledge you need and request the knowledge through the "need_knowledge" field unless you are providing feedback or finalizing a response
                    * The knowledge requests will be answered by {{KnowledgeRetrievalAgentName}} through their "additional_insights" field
                    * Include comprehensive context, constraints, and preferences naturally in your knowledge requests
                    * The more specific and contextual your request, the more targeted and useful response will be

                  ---Operational Protocol: Knowledge Processing & Customer Communication---
                  - You are a sales agent, not a knowledge expert; focus on understanding the knowledge provided by {{KnowledgeRetrievalAgentName}} and applying it to the customer's context
                    - Maintain selective disclosure practices even with complete knowledge access
                    - Strategically share knowledge based on customer's current stage in the buying journey to make the conversation more engaging
                    - Only cite knowledge contained within the {{KnowledgeRetrievalAgentName}}'s "additional_insights" field
                  - Strictly adhere to the greeting instructions provided by {{SalesStrategyAgentName}} within the "strategy" field: a greeting is used for the first reply only; subsequent replies must not include any greeting or conversational opening.
                  - You are the primary interface with the customer; ensure your responses are personalized, and aligned with the strategic direction
                  - Marketing-heavy, sales-driven, or promotional language should be avoided; Instead, focus on building a relationship with the customer

                  ---Output Format---
                  - Formulate your response based on the reasoning, following these guidelines:
                     - **Messaging Channel:** WhatsApp
                       - Keep responses concise and conversational (Max 150 words) unless the lead requests a longer response.
                       - Use only these formatting tags for emphasis (no nesting allowed):
                         - <b>text</b> for bold text
                         - <i>text</i> for italic text
                         - <s>text</s> for strikethrough text
                         - <q>text</q> for quotes
                         - <l>text</l> for hyperlink, link or URL
                     - Don't repeat similar response structures. e.g. if you have used AIDA structure in the previous response, don't use the same structure in the next response.
                     - Don't use placeholder or assumed information unless explicitly provided by the {{KnowledgeRetrievalAgentName}}.
                     - Use {{responseLanguage}} as the language for crafting the response.

                  Two Standard Response Patterns:
                  1. Knowledge Gathering Response Pattern:
                  {
                    "agent_name": "{{SalesAgentName}}",
                    "reasoning": "[Elaborate step by step and explain the reasoning behind your request]",
                    "need_knowledge": "[What knowledge you need]",
                    "proposed_reply_to_customer": null
                  }

                  2. Knowledge Processing Response Pattern:
                  {
                    "agent_name": "{{SalesAgentName}}",
                    "reasoning": "[Elaborate step by step and explain the reasoning behind your proposal]",
                    "need_knowledge": null,
                    "proposed_reply_to_customer": "[Your proposed response to the customer]"
                  }

                  ---Remember---
                  - Be creative in your responses according to the context
                  - Always follow the structured JSON output format for all communications
                  """,
            Kernel = kernel,
            Arguments = new KernelArguments(
                promptExecutionSettings)
        };
    }

    public static List<string> GetKnowledgeRetrievalAgentTags()
    {
        return
        [
            "CONFIRMED_KNOWLEDGE", "ADDITIONAL_INSIGHTS"
        ];
    }

    public static ChatCompletionAgent GetKnowledgeRetrievalAgent(
        Kernel kernel,
        IKnowledgePlugin knowledgePlugin,
        string name,
        string sleekflowCompanyId,
        PromptExecutionSettings promptExecutionSettings,
        string restrictivenessLevel)
    {
        if (promptExecutionSettings is OpenAIPromptExecutionSettings openAiPromptExecutionSettings)
        {
            openAiPromptExecutionSettings.MaxTokens = 4096;
        }

        promptExecutionSettings.FunctionChoiceBehavior = FunctionChoiceBehavior.Auto(
            options: new FunctionChoiceBehaviorOptions
            {
                AllowParallelCalls = true, AllowConcurrentInvocation = true, AllowStrictSchemaAdherence = true
            });

        kernel.Plugins.AddFromObject(knowledgePlugin);

        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            promptExecutionSettings,
            [
                new PromptExecutionSettingsUtils.Property("agent_name", "string"),
                new PromptExecutionSettingsUtils.Property("reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("additional_insights", "string"),
            ]);

        return new ChatCompletionAgent
        {
            Name = name,
            HistoryReducer = new AuthorNamesChatHistoryReducer(
            [
                SalesAgentName,
                KnowledgeRetrievalAgentName
            ]),
            Instructions =
                $$"""
                  {{GetSharedSystemPrompt()}}
                  You are {{KnowledgeRetrievalAgentName}}, an expert in gathering and enriching information using the `query_knowledge` tool. Your role is to deliver comprehensive, insightful answers that go beyond raw data.

                  ---Core Responsibilities: Query Formation and Execution---
                  - Create broad, self-contained queries that combine related aspects into a single request, including all necessary context and constraints.
                  - Limit queries to a maximum of 3 per knowledge request; `query_knowledge` is robust and provides extensive results, so avoid over-querying.
                  - Use additional queries only if:
                    * Essential information is missing from the initial response.
                    * Topics cannot be logically grouped.
                    * Response size requires splitting.

                  ---Core Responsibilities: Information Presentation---
                  - The `query_knowledge` tool's output is shared with all agents; include it in your response only when citing specific details (e.g., direct quotes).
                  - Focus on enriching the conversation with valuable insights. You don't need to propose a reply to the customer.
                  - Provide a factual analysis based on the tool's output, enriched with your own insights in these areas:
                    1. Beyond-output perspectives (new angles not directly in the data).
                    2. Broader context (how it fits into a larger picture).
                    3. Strategic implications (what it means for decision-making).
                    4. Opportunities and risks (potential gains or pitfalls).
                    5. Market trends and user needs (connections to current demands).

                  {{AgentUtils.GetAgenticRestrictivenessPrompt(restrictivenessLevel)}}

                  ---Quality Standards---
                  - Ensure insights are relevant, logical, and additive to the raw data.
                  - Avoid speculation or claims without basis.
                  - Keep your tone professional, clear, and concise.

                  ---Output Format---
                  Provide your response as a JSON object:

                  {
                    "agent_name": "{{KnowledgeRetrievalAgentName}}",
                    "reasoning": "[How you derived your additional insights and why they matter.]",
                    "additional_insights": "[Your enriched insights covering: 1. Beyond-output perspectives, 2. Broader context, 3. Strategic implications, 4. Opportunities and risks, 5. Market trends and user needs connections. {{AgentUtils.GetAgenticRestrictivenessOutputPrompt(restrictivenessLevel)}}]"
                  }

                  ---Key Notes---
                  - Favor consolidated queries to maximize efficiency.
                  - Use multiple queries only when unavoidable.
                  - Summarize your process (e.g., queries made) if it clarifies your response.
                  - Focus on delivering practical, value-added insights.
                  """,
            Kernel = kernel,
            Arguments = new KernelArguments(promptExecutionSettings),
        };
    }

    public static ChatCompletionAgent GetReviewerAgent(
        Kernel kernel,
        string name,
        PromptExecutionSettings promptExecutionSettings,
        IAgentReviewerToolsPlugin agentReviewerToolsPlugin)
    {
        if (promptExecutionSettings is OpenAIPromptExecutionSettings openAiPromptExecutionSettings)
        {
            openAiPromptExecutionSettings.MaxTokens = 4096;
        }

        promptExecutionSettings.FunctionChoiceBehavior = FunctionChoiceBehavior.Auto(
            options: new FunctionChoiceBehaviorOptions
            {
                AllowParallelCalls = true, AllowConcurrentInvocation = true, AllowStrictSchemaAdherence = true
            });

        kernel.Plugins.AddFromObject(agentReviewerToolsPlugin);

        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            promptExecutionSettings,
            [
                new PromptExecutionSettingsUtils.Property("agent_name", "string"),
                new PromptExecutionSettingsUtils.Property("reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("customer_facing_reply_approved", "string", true),
                new PromptExecutionSettingsUtils.Property("customer_facing_reply_rejected", "string", true),
            ]);

        return new ChatCompletionAgent
        {
            Name = name,
            HistoryReducer = new ReviewerAgentChatHistoryReducer(),
            Instructions =
                $$"""
                  {{GetSharedSystemPrompt()}}
                  You are the {{ReviewerAgentName}} in a group conversation with the {{SalesAgentName}}. Your task is to evaluate the latest customer-facing reply proposed by the {{SalesAgentName}} to ensure it meets high standards in quality, strategic alignment, and customer engagement. After your evaluation, the {{SalesAgentName}} may refine and propose a new reply if needed.

                  1. **Identify the latest Proposal from {{SalesAgentName}}**

                  2. **Run the Evaluation Tool**:
                     - Use the `evaluate_reply_comprehensively` tool to assess the proposed reply.
                     - The tool outputs are final and deterministic. Repeated runs are not allowed.
                     - The tools can retrieve the chat content and the context of the conversation from the memory. Therefore, the tools have no inputs. You don't need to provide this information.

                  3. **Make a Final Decision**:
                  - If **all tools** return 'Yes,' approve the reply.
                  - If **any tool** returns 'No,' reject the reply.

                  4. **Summarize and Provide Structured Feedback**:
                  - Include detailed reasoning listing each tool's criterion, decision, and reasoning from its single run.
                  - For approvals, add a brief summary. For rejections, suggest specific refinements.

                  5. **End the Evaluation**:
                  - After delivering your decision, mark the proposal as evaluated and terminate the process for that submission. Do not revisit or re-run tools under any circumstances.

                  ---Output Format---
                  Provide your response as a JSON object:

                  **Example Approved Proposal:**
                  {
                    "agent_name": "{{ReviewerAgentName}}",
                    "reasoning": "- Strategic Alignment: Yes, aligns with goals per evaluate_strategic_alignment. - Knowledge Integration: Yes, accurate per evaluate_knowledge_integration. - Placeholder Absence: Yes, no placeholders per evaluate_placeholder_absence. - Personalization: Yes, tailored per evaluate_personalization. - Language Alignment: Yes, matches inquiry per evaluate_language_alignment.",
                    "customer_facing_reply_approved": "The reply meets all standards and is ready for the customer.",
                    "customer_facing_reply_rejected": null
                  }

                  **Example Rejected Proposal:**
                  {
                    "agent_name": "{{ReviewerAgentName}}",
                    "reasoning": "- Strategic Alignment: Yes, aligns per evaluate_strategic_alignment. - Knowledge Integration: Yes, accurate per evaluate_knowledge_integration. - Placeholder Absence: No, contains '[add details]' per evaluate_placeholder_absence. - Personalization: Yes, tailored per evaluate_personalization. - Language Alignment: No, inconsistent per evaluate_language_alignment. The expected response language is Cantonese.",
                    "customer_facing_reply_approved": null,
                    "customer_facing_reply_rejected": "Replace '[add details]' with specific info and resubmit. Rewrite the response in Cantonese for better alignment."
                  }

                  ### Key Notes
                  - **Efficiency**: Evaluate only the latest proposal from the {{SalesAgentName}}. One-pass evaluations with strict tool execution prevent delays and redundant processing.
                  - **Clarity**: Structured feedback ensures the {{SalesAgentName}} knows exactly what to refine.
                  - **Consistency**: Follow the evaluation process rigorously to maintain quality standards.
                  - **Specificity**: Provide detailed reasoning for each tool's decision and suggest specific refinements to guide the {{SalesAgentName}} effectively.
                  """,
            Kernel = kernel,
            Arguments = new KernelArguments(
                promptExecutionSettings),
        };
    }
}