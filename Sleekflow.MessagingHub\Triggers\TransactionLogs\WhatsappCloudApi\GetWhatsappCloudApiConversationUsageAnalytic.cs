using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.TransactionLogs.WhatsappCloudApi;

[TriggerGroup(ControllerNames.TransactionLogs)]
public class GetWhatsappCloudApiConversationUsageAnalytic
    : ITrigger<
        GetWhatsappCloudApiConversationUsageAnalytic.GetWhatsappCloudApiConversationUsageAnalyticInput,
        GetWhatsappCloudApiConversationUsageAnalytic.GetWhatsappCloudApiConversationUsageAnalyticOutput>
{
    private readonly ILogger<GetWhatsappCloudApiConversationUsageAnalytic> _logger;
    private readonly IBusinessBalanceTransactionLogService _businessBalanceTransactionLogService;
    private readonly IWabaService _wabaService;

    public GetWhatsappCloudApiConversationUsageAnalytic(
        ILogger<GetWhatsappCloudApiConversationUsageAnalytic> logger,
        IBusinessBalanceTransactionLogService businessBalanceTransactionLogService,
        IWabaService wabaService)
    {
        _logger = logger;
        _businessBalanceTransactionLogService = businessBalanceTransactionLogService;
        _wabaService = wabaService;
    }

    public class GetWhatsappCloudApiConversationUsageAnalyticInput
    {
        [JsonConstructor]
        public GetWhatsappCloudApiConversationUsageAnalyticInput(
            string sleekflowCompanyId,
            string facebookBusinessId,
            string facebookWabaId,
            DateTimeOffset start,
            DateTimeOffset end,
            string granularity,
            bool isDetailed)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FacebookBusinessId = facebookBusinessId;
            FacebookWabaId = facebookWabaId;
            Start = start;
            End = end;
            Granularity = granularity;
            IsDetailed = isDetailed;
        }

        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("facebook_business_id")]
        public string FacebookBusinessId { get; set; }

        [Required]
        [JsonProperty("facebook_waba_id")]
        public string FacebookWabaId { get; set; }

        [Required]
        [JsonProperty("start")]
        public DateTimeOffset Start { get; set; }

        [Required]
        [JsonProperty("end")]
        public DateTimeOffset End { get; set; }

        [Required]
        [RegularExpression("^(HALF_HOUR|DAILY|MONTHLY)$")]
        [JsonProperty("granularity")]
        public string Granularity { get; set; }

        [Required]
        [JsonProperty("is_detailed")]
        public bool IsDetailed { get; set; }
    }

    public class GetWhatsappCloudApiConversationUsageAnalyticOutput
    {
        [JsonProperty("conversation_usage_analytic")]
        public WhatsappCloudApiDetailedConversationUsageAnalyticDto ConversationUsageAnalytic { get; set; }

        [JsonProperty("facebook_business_id")]
        public string FacebookBusinessId { get; set; }

        [JsonProperty("facebook_business_name")]
        public string? FacebookBusinessName { get; set; }

        [JsonProperty("facebook_business_waba")]
        public FacebookBusinessWabaDto FacebookBusinessWaba { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiConversationUsageAnalyticOutput(
            WhatsappCloudApiDetailedConversationUsageAnalyticDto conversationUsageAnalytic,
            string facebookBusinessId,
            string? facebookBusinessName,
            FacebookBusinessWabaDto facebookBusinessWaba)
        {
            ConversationUsageAnalytic = conversationUsageAnalytic;
            FacebookBusinessId = facebookBusinessId;
            FacebookBusinessName = facebookBusinessName;
            FacebookBusinessWaba = facebookBusinessWaba;
        }
    }

    public async Task<GetWhatsappCloudApiConversationUsageAnalyticOutput> F(
        GetWhatsappCloudApiConversationUsageAnalyticInput input)
    {
        // Adjust End date for including the specified date
        switch (input.Granularity)
        {
            case "HALF_HOUR":
                input.End = input.End.AddMinutes(30);
                break;
            case "DAILY":
            case "MONTHLY":
                input.End = input.End.AddDays(1);
                break;
        }

        if (input.Start > input.End)
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        "Start cannot later then End",
                        new[]
                        {
                            nameof(input.Start),
                            nameof(input.End),
                        })
                });
        }

        if (input.End > input.Start.AddYears(1).AddDays(1))
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        "Fetch conversation analytic for over 1 year",
                        new[]
                        {
                            nameof(input.Start),
                            nameof(input.End),
                        })
                });
        }

        var wabas = await _wabaService.GetWabaWithFacebookBusinessIdAsync(input.FacebookBusinessId);

        if (wabas.Count == 0 || wabas.SelectMany(w => w.SleekflowCompanyIds).All(x => x != input.SleekflowCompanyId))
        {
            throw new SfUserFriendlyException("Facebook Business not found");
        }

        var waba = wabas.Find(x => x.FacebookWabaId == input.FacebookWabaId);

        if (waba == null)
        {
            throw new SfUserFriendlyException("Waba not found");
        }

        var analytic = await _businessBalanceTransactionLogService
            .GetWhatsappCloudApiConversationUsageAnalyticByWaba(
                input.FacebookBusinessId,
                waba,
                input.Start,
                input.End,
                input.Granularity,
                input.IsDetailed);

        var facebookTimezone =
            CloudApiUtils.GetFacebookTimezone(analytic.Waba.FacebookWabaSnapshot["timezone_id"].ToString());

        return new GetWhatsappCloudApiConversationUsageAnalyticOutput(
            analytic.ConversationUsageAnalyic,
            waba.FacebookBusinessId,
            waba.FacebookWabaBusinessName,
            new FacebookBusinessWabaDto(waba, facebookTimezone));
    }
}