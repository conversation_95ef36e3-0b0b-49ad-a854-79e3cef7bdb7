{"workflowApiVersion": "1.1", "metaData": {"icon": "images/sleekflow.png", "category": "message"}, "type": "REST", "lang": {"en-US": {"name": "Sleekflow Send Message", "description": "Send WhatsaApp Cloud Api Template Message with Journey Builder"}}, "arguments": {"execute": {"inArguments": [{"email": "{{Event.DEAudience-59338730-7246-9aaf-106b-4621a3e9396b.\"Em<PERSON>Address\"}}", "to": ""}], "outArguments": [], "url": "https://sleekflow-prod-api-uat.azurewebsites.net/JourneyBuilderCustomActivity/execute", "verb": "POST", "body": "", "format": "json", "useJwt": false, "timeout": 30000}}, "configurationArguments": {"applicationExtensionKey": "e962d1d4-8b07-4e76-89be-ecd1f1423b6d", "save": {"url": "https://sleekflow-prod-api-uat.azurewebsites.net/JourneyBuilderCustomActivity/save", "verb": "POST", "body": "", "format": "json", "useJwt": false, "timeout": 30000}, "publish": {"url": "https://sleekflow-prod-api-uat.azurewebsites.net/JourneyBuilderCustomActivity/publish", "verb": "POST", "body": "", "format": "json", "useJwt": false, "timeout": 30000}, "validate": {"url": "https://sleekflow-prod-api-uat.azurewebsites.net/JourneyBuilderCustomActivity/validate", "verb": "POST", "body": "", "format": "json", "useJwt": false, "timeout": 30000}, "stop": {"url": "https://sleekflow-prod-api-uat.azurewebsites.net/JourneyBuilderCustomActivity/stop", "verb": "POST", "body": "", "format": "json", "useJwt": false, "timeout": 30000}}, "wizardSteps": [{"label": "Create SMS Message", "key": "step1"}], "userInterfaces": {"configModal": {"height": 600, "width": 800, "fullscreen": false}}, "schema": {"arguments": {"execute": {"inArguments": [{"accountSid": {"dataType": "String", "isNullable": false, "direction": "out"}}, {"authToken": {"dataType": "String", "isNullable": false, "direction": "out"}}, {"from": {"dataType": "Phone", "isNullable": true, "direction": "out"}}, {"to": {"dataType": "Phone", "isNullable": false, "direction": "out"}}, {"body": {"dataType": "String", "isNullable": false, "direction": "out"}}, {"email": {"dataType": "Email", "isNullable": true, "direction": "out"}}], "outArguments": []}}}}