using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;

namespace Sleekflow.IntelligentHub.Models.Snapshots;

public class AgentRecommendReplySnapshot : AgentRecordUsageSnapshot
{
    [JsonProperty("conversation_context")]
    public string ConversationContext { get; set; }

    [JsonProperty("knowledge_base_entries")]
    public string KnowledgeBaseEntries { get; set; }

    [JsonProperty("output_message")]
    public string OutputMessage { get; set; }

    [JsonProperty("token_counts")]
    public Dictionary<string, int> TokenCounts { get; set; }

    [JsonProperty("collaboration_mode")]
    public string CollaborationMode { get; set; }

    [JsonProperty("confidence_score")]
    public int ConfidenceScore { get; set; }

    [JsonProperty("workflow_id")]
    public string? WorkflowId { get; set; }

    [JsonProperty("workflow_versioned_id")]
    public string? WorkflowVersionedId { get; set; }

    [JsonConstructor]
    public AgentRecommendReplySnapshot(
        string conversationContext,
        string knowledgeBaseEntries,
        string outputMessage,
        Dictionary<string, int> tokenCounts,
        string collaborationMode,
        string agentId,
        string? agentVersionedId,
        int confidenceScore,
        string? workflowId,
        string? workflowVersionedId)
        : base(agentId, agentVersionedId)
    {
        ConversationContext = conversationContext;
        KnowledgeBaseEntries = knowledgeBaseEntries;
        OutputMessage = outputMessage;
        TokenCounts = tokenCounts;
        CollaborationMode = collaborationMode;
        ConfidenceScore = confidenceScore;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
    }
}