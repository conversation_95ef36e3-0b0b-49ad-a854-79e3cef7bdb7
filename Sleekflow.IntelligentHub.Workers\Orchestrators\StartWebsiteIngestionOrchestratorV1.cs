using System.Collections.Concurrent;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.IntelligentHub.Workers.Activities;
using Exception = System.Exception;

namespace Sleekflow.IntelligentHub.Workers.Orchestrators;

public class StartWebsiteIngestionOrchestratorV1
{
    private readonly ILogger<StartWebsiteIngestionOrchestratorV1> _logger;

    public StartWebsiteIngestionOrchestratorV1(ILogger<StartWebsiteIngestionOrchestratorV1> logger)
    {
        _logger = logger;
    }

    public class ProcessWebsiteDocumentOrchestratorCustomStatusOutput
    {
        [JsonProperty("process_website_document_output")]
        public ProcessWebsiteDocument.ProcessWebsiteDocumentOutput? ProcessWebsiteDocumentOutput { get; set; }

        [JsonProperty("last_update_time")]
        public DateTime LastUpdateTime { get; set; }

        [JsonConstructor]
        public ProcessWebsiteDocumentOrchestratorCustomStatusOutput(
            ProcessWebsiteDocument.ProcessWebsiteDocumentOutput? processWebsiteDocumentOutput,
            DateTime lastUpdateTime)
        {
            ProcessWebsiteDocumentOutput = processWebsiteDocumentOutput;
            LastUpdateTime = lastUpdateTime;
        }
    }

    public class ProcessWebsiteDocumentOrchestratorOutput
    {
        [JsonConstructor]
        public ProcessWebsiteDocumentOrchestratorOutput()
        {
        }
    }

    [Function(nameof(StartWebsiteIngestionOrchestratorV1))]
    public async Task<ProcessWebsiteDocumentOrchestratorOutput> RunOrchestrator(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var processWebsiteDocumentInput = context.GetInput<StartWebsiteIngestionEvent>();

        var taskOptions = new TaskOptions(new TaskRetryOptions(new RetryPolicy(5, TimeSpan.FromSeconds(120), 1)));

        // Record conversion started timestamp
        await context
            .CallActivityAsync<UpdateDebugTimestamps.UpdateDebugTimestampsOutput>(
                nameof(UpdateDebugTimestamps),
                new UpdateDebugTimestamps.UpdateDebugTimestampsInput(
                    processWebsiteDocumentInput.SleekflowCompanyId,
                    processWebsiteDocumentInput.DocumentId,
                    conversionStarted: DateTimeOffset.UtcNow),
                taskOptions);

        try
        {
            await context
                .CallActivityAsync<PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusOutput>(
                    nameof(PatchProcessFileDocumentStatus),
                    new PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusInput(
                        processWebsiteDocumentInput.SleekflowCompanyId,
                        processWebsiteDocumentInput.DocumentId,
                        ProcessFileDocumentStatuses.Converting,
                        0.0),
                    taskOptions);

            var getWebsiteDocumentOutput = await context
                .CallActivityAsync<GetWebsiteDocument.GetWebsiteDocumentOutput>(
                    nameof(GetWebsiteDocument),
                    new GetWebsiteDocument.GetWebsiteDocumentInput(
                        processWebsiteDocumentInput.SleekflowCompanyId,
                        processWebsiteDocumentInput.DocumentId),
                    taskOptions);

            var websiteDocument = getWebsiteDocumentOutput.WebsiteDocument;
            var websiteUrls = websiteDocument.SelectedUrls
                .Where(selectedUrl => selectedUrl.Status == SelectedUrlStatuses.Pending)
                .Select(selectedUrl => selectedUrl.PageUrl)
                .ToList();

            _logger.LogInformation(
                "StartWebsiteIngestionOrchestrator Start processing {Count} urls",
                websiteUrls.Count);

            // Get the HTTP head for every URL so we can know if it is a file url or website url
            var webpageDict = await GetWebpageHttpHead(
                context,
                processWebsiteDocumentInput.SleekflowCompanyId,
                processWebsiteDocumentInput.DocumentId,
                websiteUrls,
                taskOptions);

            await UpdateProgressStatus(
                context,
                processWebsiteDocumentInput.SleekflowCompanyId,
                processWebsiteDocumentInput.DocumentId,
                taskOptions);

            // Separate file URLs from regular web page URLs
            var websiteFileUrls = webpageDict
                .Where(
                    kv => !kv.Value.MimeType.Contains(
                        "text/html",
                        StringComparison.OrdinalIgnoreCase))
                .ToList();

            await ProcessWebsiteFileUrls(
                context,
                processWebsiteDocumentInput.SleekflowCompanyId,
                processWebsiteDocumentInput.DocumentId,
                websiteFileUrls,
                taskOptions);

            await ProcessWebsiteHtmlUrls(
                context,
                processWebsiteDocumentInput.SleekflowCompanyId,
                processWebsiteDocumentInput.DocumentId,
                taskOptions);

            await context
                .CallActivityAsync<
                    PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusOutput>(
                    nameof(PatchProcessFileDocumentStatus),
                    new PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusInput(
                        processWebsiteDocumentInput.SleekflowCompanyId,
                        processWebsiteDocumentInput.DocumentId,
                        ProcessFileDocumentStatuses.ReadyToAssign,
                        100.0),
                    taskOptions);
        }
        catch (Exception)
        {
            await context
                .CallActivityAsync<
                    PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusOutput>(
                    nameof(PatchProcessFileDocumentStatus),
                    new PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusInput(
                        processWebsiteDocumentInput.SleekflowCompanyId,
                        processWebsiteDocumentInput.DocumentId,
                        ProcessFileDocumentStatuses.FailedToConvert,
                        0.0),
                    taskOptions);
        }

        // Record conversion ended timestamp
        await context
            .CallActivityAsync<UpdateDebugTimestamps.UpdateDebugTimestampsOutput>(
                nameof(UpdateDebugTimestamps),
                new UpdateDebugTimestamps.UpdateDebugTimestampsInput(
                    processWebsiteDocumentInput.SleekflowCompanyId,
                    processWebsiteDocumentInput.DocumentId,
                    conversionEnded: DateTimeOffset.UtcNow),
                taskOptions);

        await context
            .CallSubOrchestratorAsync(
                nameof(UploadFileDocumentToAllAssignedAgentsOrchestratorV1),
                new StartUploadToAgentKnowledgeBasesEvent(
                    processWebsiteDocumentInput.SleekflowCompanyId,
                    processWebsiteDocumentInput.DocumentId),
                taskOptions);

        return new ProcessWebsiteDocumentOrchestratorOutput();
    }

    private async Task UpdateProgressStatus(
        TaskOrchestrationContext context,
        string sleekflowCompanyId,
        string documentId,
        TaskOptions taskOptions)
    {
        var getWebsiteDocumentOutput = await context
            .CallActivityAsync<GetWebsiteDocument.GetWebsiteDocumentOutput>(
                nameof(GetWebsiteDocument),
                new GetWebsiteDocument.GetWebsiteDocumentInput(
                    sleekflowCompanyId,
                    documentId),
                taskOptions);

        var websiteDocument = getWebsiteDocumentOutput.WebsiteDocument;
        var totalUrlCount = websiteDocument.SelectedUrls.Count;
        var finishedUrls = websiteDocument.SelectedUrls
            .Count(
                selectedUrl => selectedUrl.Status == SelectedUrlStatuses.Converted ||
                               selectedUrl.Status == SelectedUrlStatuses.Failed);
        var progressPercentage = finishedUrls * 100.0 / totalUrlCount;

        _logger.LogInformation(
            "UpdateProgressStatus Total {TotalCount} URLs, {FinishedCount} failed. {ProgressPercentage}%",
            totalUrlCount,
            finishedUrls,
            progressPercentage);

        await context
            .CallActivityAsync<PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusOutput>(
                nameof(PatchProcessFileDocumentStatus),
                new PatchProcessFileDocumentStatus.PatchProcessFileDocumentStatusInput(
                    sleekflowCompanyId,
                    documentId,
                    ProcessFileDocumentStatuses.Converting,
                    progressPercentage),
                taskOptions);
    }

    private async Task ProcessWebsiteHtmlUrls(
        TaskOrchestrationContext context,
        string sleekflowCompanyId,
        string documentId,
        TaskOptions taskOptions)
    {
        while (true)
        {
            await context
                .CallActivityAsync<ProcessWebsiteDocument.ProcessWebsiteDocumentOutput>(
                    nameof(ProcessWebsiteDocument),
                    new ProcessWebsiteDocument.ProcessWebsiteDocumentInput(
                        sleekflowCompanyId,
                        documentId),
                    taskOptions);

            await UpdateProgressStatus(
                context,
                sleekflowCompanyId,
                documentId,
                taskOptions);

            // Get the WebsiteDocument to check if there are any pending URLs
            var updatedWebsiteDocumentOutput = await context
                .CallActivityAsync<GetWebsiteDocument.GetWebsiteDocumentOutput>(
                    nameof(GetWebsiteDocument),
                    new GetWebsiteDocument.GetWebsiteDocumentInput(
                        sleekflowCompanyId,
                        documentId),
                    taskOptions);

            var updatedWebsiteDocument = updatedWebsiteDocumentOutput.WebsiteDocument;

            // Check if there are any URLs still in pending state
            var hasPendingUrls = updatedWebsiteDocument.SelectedUrls
                .Any(selectedUrl => selectedUrl.Status == SelectedUrlStatuses.Pending);

            // If no pending URLs, break the loop
            if (!hasPendingUrls)
            {
                break;
            }
        }
    }

    private async Task ProcessWebsiteFileUrls(
        TaskOrchestrationContext context,
        string sleekflowCompanyId,
        string documentId,
        List<KeyValuePair<string, WebpageHead>> websiteFileUrls,
        TaskOptions taskOptions)
    {
        ProcessWebsiteFileDocument.ProcessWebsiteFileDocumentOutput? progressOutput = null;
        foreach (var (url, webpageHead) in websiteFileUrls)
        {
            try
            {
                await context
                    .CallActivityAsync<PatchWebsiteDocumentSelectedUrlStatuses.
                        PatchWebsiteDocumentSelectedUrlStatusesOutput>(
                        nameof(PatchWebsiteDocumentSelectedUrlStatuses),
                        new PatchWebsiteDocumentSelectedUrlStatuses.
                            PatchWebsiteDocumentSelectedUrlStatusesInput(
                                sleekflowCompanyId,
                                documentId,
                                [url],
                                SelectedUrlStatuses.Converting),
                        taskOptions);

                while (true)
                {
                    _logger.LogInformation(
                        "StartWebsiteIngestionOrchestrator progress {Progress}",
                        JsonConvert.SerializeObject(progressOutput));

                    var processWebsiteFileDocumentOutput = await context
                        .CallActivityAsync<ProcessWebsiteFileDocument.ProcessWebsiteFileDocumentOutput>(
                            nameof(ProcessWebsiteFileDocument),
                            new ProcessWebsiteFileDocument.ProcessWebsiteFileDocumentInput(
                                sleekflowCompanyId,
                                documentId,
                                url,
                                webpageHead.MimeType,
                                progressOutput?.FileIngestionProgress),
                            taskOptions);

                    progressOutput = processWebsiteFileDocumentOutput;

                    if (progressOutput.IsCompleted)
                    {
                        break;
                    }
                }

                await context
                    .CallActivityAsync<PatchWebsiteDocumentSelectedUrlStatuses.
                        PatchWebsiteDocumentSelectedUrlStatusesOutput>(
                        nameof(PatchWebsiteDocumentSelectedUrlStatuses),
                        new PatchWebsiteDocumentSelectedUrlStatuses.
                            PatchWebsiteDocumentSelectedUrlStatusesInput(
                                sleekflowCompanyId,
                                documentId,
                                [url],
                                SelectedUrlStatuses.Converted),
                        taskOptions);
            }
            catch (Exception)
            {
                await context
                    .CallActivityAsync<PatchWebsiteDocumentSelectedUrlStatuses.
                        PatchWebsiteDocumentSelectedUrlStatusesOutput>(
                        nameof(PatchWebsiteDocumentSelectedUrlStatuses),
                        new PatchWebsiteDocumentSelectedUrlStatuses.
                            PatchWebsiteDocumentSelectedUrlStatusesInput(
                                sleekflowCompanyId,
                                documentId,
                                [url],
                                SelectedUrlStatuses.Failed),
                        taskOptions);
            }

            await UpdateProgressStatus(
                context,
                sleekflowCompanyId,
                documentId,
                taskOptions);
        }
    }

    private async Task<ConcurrentDictionary<string, WebpageHead>> GetWebpageHttpHead(
        TaskOrchestrationContext context,
        string sleekflowCompanyId,
        string documentId,
        List<string> websiteUrls,
        TaskOptions taskOptions)
    {
        var webpageDict = new ConcurrentDictionary<string, WebpageHead>();
        var failedWebpages = new ConcurrentBag<string>();

        // Create tasks for each URL to call GetWebpageHead activity function
        var getWebpageHeadTasks = websiteUrls.Select(
            async url =>
            {
                try
                {
                    var getWebpageHeadOutput = await context.CallActivityAsync<GetWebpageHead.GetWebpageHeadOutput>(
                        nameof(GetWebpageHead),
                        new GetWebpageHead.GetWebpageHeadInput(
                            sleekflowCompanyId,
                            url),
                        taskOptions);

                    webpageDict.TryAdd(url, getWebpageHeadOutput.WebpageHead);
                }
                catch (Exception e)
                {
                    failedWebpages.Add(url);
                }
            });

        // Execute all tasks concurrently
        await Task.WhenAll(getWebpageHeadTasks);

        _logger.LogInformation(
            "StartWebsiteIngestionOrchestrator Retrieved HEAD for {Count} URLs, {FailedCount} failed.",
            webpageDict.Count,
            failedWebpages.Count);

        // Update failed URLs (where HEAD request failed) to "failed" status
        if (failedWebpages.Any())
        {
            _logger.LogInformation(
                "StartWebsiteIngestionOrchestrator Found {Count} failed URLs, updating status to failed",
                failedWebpages.Count);

            await context
                .CallActivityAsync<PatchWebsiteDocumentSelectedUrlStatuses.
                    PatchWebsiteDocumentSelectedUrlStatusesOutput>(
                    nameof(PatchWebsiteDocumentSelectedUrlStatuses),
                    new PatchWebsiteDocumentSelectedUrlStatuses.PatchWebsiteDocumentSelectedUrlStatusesInput(
                        sleekflowCompanyId,
                        documentId,
                        failedWebpages.ToList(),
                        SelectedUrlStatuses.Failed),
                    taskOptions);
        }

        return webpageDict;
    }
}