using Microsoft.Azure.Cosmos; // Added for PatchOperation
using Microsoft.Extensions.Logging;
using Moq;
using Sleekflow.DurablePayloads;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Commons.Workflows;
using Sleekflow.FlowHub.FlowHubConfigs;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.Workers.Services;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Tests.Workflows.BurstyWorkflows
{
    /// <summary>
    /// Contains unit tests for the BurstyWorkflowService class.
    /// These tests focus on validating the logic within BurstyWorkflowService in isolation,
    /// using Moq to mock its dependencies (IWorkflowOrchestrationService, IWorkflowService,
    /// IFlowHubConfigService, IWorkflowRecurringSettingsParser).
    /// Tests cover the following scenarios:
    /// TriggerScheduledWorkflowAsync:
    ///  - Happy path: Verifies successful triggering, durable payload retrieval, and workflow update via Patch.
    ///  - Validation checks: Ensures exceptions are thrown for missing ScheduledAt, existing DurablePayload,
    ///    and missing FlowHubConfig Origin.
    ///  - Old recurring schema handling: Confirms that the next occurrence time is calculated and used correctly, and workflow updated via Patch.
    /// TerminateScheduledWorkflowAsync:
    ///  - Happy path: Verifies successful termination call and clearing of the DurablePayload via Patch during workflow update.
    ///  - Validation checks: Ensures exceptions are thrown for missing WorkflowScheduleSettings or missing DurablePayload.
    /// Mock Verification: Tests verify that the correct methods on mocked dependencies are called with the expected arguments.
    /// </summary>
    [TestFixture]
    public class BurstyWorkflowServiceTests
    {
        // Mocks
        private Mock<IWorkflowOrchestrationService> _mockWorkflowOrchestrationService;
        private Mock<IWorkflowService> _mockWorkflowService;
        private Mock<IFlowHubConfigService> _mockFlowHubConfigService;
        private Mock<IWorkflowRecurringSettingsParser> _mockWorkflowRecurringSettingsParser;
        private Mock<ILogger<BurstyWorkflowService>> _mockLogger;
        private BurstyWorkflowService _service;
        private Mock<IPropertyOrchestrationService> _mockPropertyOrchestrationService;

        [SetUp]
        public void SetUp()
        {
            _mockWorkflowOrchestrationService = new Mock<IWorkflowOrchestrationService>();
            _mockWorkflowService = new Mock<IWorkflowService>();
            _mockFlowHubConfigService = new Mock<IFlowHubConfigService>();
            _mockWorkflowRecurringSettingsParser = new Mock<IWorkflowRecurringSettingsParser>();
            _mockLogger = new Mock<ILogger<BurstyWorkflowService>>();

            _service = new BurstyWorkflowService(
                _mockWorkflowOrchestrationService.Object,
                _mockWorkflowService.Object,
                _mockFlowHubConfigService.Object,
                _mockWorkflowRecurringSettingsParser.Object,
                _mockLogger.Object,
                _mockPropertyOrchestrationService.Object);
        }


        #region TriggerScheduledWorkflowAsync Tests

        [Test]
        public async Task TriggerScheduledWorkflowAsync_HappyPath_SchedulesAndUpdatesWorkflow()
        {
            // --- Arrange: Define all constructor args for ProxyWorkflow and its nested objects ---
            var companyId = "c-happy";
            var workflowId = "wf-happy";
            var workflowVersionedId = $"{workflowId}-v1";
            var proxyDbId = $"proxy-db-{workflowId}";
            var workflowName = "Test Workflow Happy";
            var workflowType = "Standard";
            string? workflowGroupId = null;
            var activationStatus = "Active";
            var version = "1.0";
            var proxyCreatedAt = DateTimeOffset.UtcNow.AddMinutes(-5);
            var proxyUpdatedAt = proxyCreatedAt; // Initial updated timestamp
            var staffId = "user-abc";
            List<string>? teamIds = null;
            var createdByStaff = new AuditEntity.SleekflowStaff(staffId, teamIds);
            AuditEntity.SleekflowStaff? updatedByStaff = null;
            var enrollmentPricingType = EnrolmentPricingTypes.Basic;

            var triggers = new WorkflowTriggers(
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null);
            var enrollmentSettings = new WorkflowEnrollmentSettings(false, false, true);
            DurablePayload? scheduleDurablePayloadArg = null;
            bool? scheduleIsNewScheduledWorkflowSchemaArg = true;
            bool? scheduleIsOldScheduledWorkflowSchemaFirstRecurringCompletedArg = null;
            DateTimeOffset? scheduleScheduledAtArg = DateTimeOffset.UtcNow;
            string? scheduleContactPropertyIdArg = null;
            string? scheduleSchemaIdArg = null;
            string? scheduleSchemafulObjectPropertyIdArg = null;
            string scheduleTypeArg = WorkflowScheduleTypes.PredefinedDateTime;
            WorkflowRecurringSettings? scheduleRecurringSettingsArg = null;

            var scheduleSettings = new WorkflowScheduleSettings(
                scheduleDurablePayloadArg,
                scheduleIsNewScheduledWorkflowSchemaArg,
                scheduleIsOldScheduledWorkflowSchemaFirstRecurringCompletedArg,
                scheduleScheduledAtArg,
                scheduleContactPropertyIdArg,
                scheduleSchemaIdArg,
                scheduleSchemafulObjectPropertyIdArg,
                scheduleTypeArg,
                scheduleRecurringSettingsArg,
                null);
            var steps = new List<Step>();
            var metadata = new Dictionary<string, object?>();

            var initialProxyWorkflow = new ProxyWorkflow(
                proxyDbId,
                companyId,
                workflowId,
                workflowVersionedId,
                workflowName,
                workflowType,
                workflowGroupId,
                triggers,
                null, // isDynamicVariableEnabled
                enrollmentSettings,
                scheduleSettings,
                activationStatus,
                createdByStaff,
                updatedByStaff,
                proxyCreatedAt,
                proxyUpdatedAt,
                steps,
                metadata,
                version,
                enrollmentPricingType)
            {
                ETag = "initial-etag-happy" // Need ETag for Patch
            };
            // --- End ProxyWorkflow Args ---

            // --- Arrange: Define args for FlowHubConfig constructor ---
            var configIsEnrolled = true;
            UsageLimit? configUsageLimit = null;
            UsageLimitOffset? configUsageLimitOffset = null;
            string? configETag = "etag-123";
            var configDbId = $"config-db-{companyId}";
            var configOrigin = "test-origin-happy";
            var configCreatedAt = DateTimeOffset.UtcNow.AddHours(-1);
            var configUpdatedAt = configCreatedAt;
            var configCreatedBy = new AuditEntity.SleekflowStaff("config-creator", null);
            AuditEntity.SleekflowStaff? configUpdatedBy = null;
            bool? configIsUsageLimitEnabled = false;
            bool? configIsUsageLimitOffsetEnabled = false;
            bool? configIsUsageLimitAutoScalingEnabled = false;
            GracePeriodUsageSettings? gracePeriodUsageSettings = null;

            // --- End FlowHubConfig Args ---

            // --- Arrange: Create FlowHubConfig using its specific constructor ---
            var flowHubConfig = new FlowHubConfig(
                configIsEnrolled,
                configUsageLimit,
                configUsageLimitOffset,
                configETag,
                configDbId,
                configOrigin,
                configCreatedAt,
                configUpdatedAt,
                companyId,
                configCreatedBy,
                configUpdatedBy,
                configIsUsageLimitEnabled,
                configIsUsageLimitOffsetEnabled,
                configIsUsageLimitAutoScalingEnabled,
                gracePeriodUsageSettings);
            // --- End FlowHubConfig creation ---

            // --- Arrange: DurablePayload for orchestration service return ---
            var expectedDurablePayloadId = "dp-happy-123";
            var expectedDurablePayloadUri = "http://test.uri/";

            var expectedDurablePayload = new DurablePayload(
                expectedDurablePayloadId,
                expectedDurablePayloadUri,
                expectedDurablePayloadUri,
                expectedDurablePayloadUri,
                expectedDurablePayloadUri,
                expectedDurablePayloadUri,
                expectedDurablePayloadUri);
            // --- End DurablePayload setup ---

            // Setup the GetLatestWorkflowAsync sequence
            _mockWorkflowService.SetupSequence(s => s.GetLatestWorkflowAsync(workflowId, companyId))
                .ReturnsAsync(initialProxyWorkflow) // First call returns the initial workflow
                .ReturnsAsync(
                    () => // Second call returns the workflow *after* the expected patch
                    {
                        // Create a new instance reflecting the patched state
                        var updatedScheduleSettings = new WorkflowScheduleSettings(
                            expectedDurablePayload, // Payload is now set
                            scheduleSettings.IsNewScheduledWorkflowSchema,
                            scheduleSettings.IsOldScheduledWorkflowSchemaFirstRecurringCompleted,
                            scheduleSettings.ScheduledAt,
                            scheduleSettings.ContactPropertyId,
                            scheduleSettings.SchemaId,
                            scheduleSettings.SchemafulObjectPropertyId,
                            scheduleSettings.ScheduleType,
                            scheduleSettings.RecurringSettings);

                        // Create a new instance reflecting the patched state using constructor
                        return new ProxyWorkflow(
                            proxyDbId,
                            companyId,
                            workflowId,
                            workflowVersionedId,
                            workflowName,
                            workflowType,
                            workflowGroupId,
                            triggers,
                            null, // isDynamicVariableEnabled
                            enrollmentSettings,
                            updatedScheduleSettings,
                            activationStatus,
                            createdByStaff,
                            updatedByStaff,
                            proxyCreatedAt,
                            DateTimeOffset.UtcNow, // UpdatedAt changes
                            steps,
                            metadata,
                            version,
                            enrollmentPricingType)
                        {
                            ETag = "updated-etag-happy" // ETag would change after patch
                        };
                    });


            _mockFlowHubConfigService.Setup(s => s.GetFlowHubConfigAsync(companyId))
                .ReturnsAsync(flowHubConfig);

            _mockWorkflowOrchestrationService.Setup(
                    s => s.TriggerScheduleWorkflowAsync(
                        companyId,
                        flowHubConfig.Origin,
                        workflowId,
                        workflowVersionedId,
                        scheduleSettings.ScheduledAt.Value,
                        scheduleSettings.RecurringSettings,
                        "v1"))
                .ReturnsAsync(expectedDurablePayload);

            // Setup PatchWorkflowAsync - Use a matcher that checks the operation structure
            _mockWorkflowService.Setup(
                    s => s.PatchWorkflowAsync(
                        workflowVersionedId,
                        companyId,
                        It.Is<List<PatchOperation>>(
                            ops =>
                                ops.Count == 2 &&
                                ops[0].OperationType == PatchOperationType.Set && // Check operation type
                                ops[0].Path == "/workflow_schedule_settings/durable_payload" && // Check path
                                ops[1].OperationType == PatchOperationType.Set && // Check operation type
                                ops[1].Path == $"/{IHasUpdatedAt.PropertyNameUpdatedAt}" // Check path
                        ),
                        initialProxyWorkflow.ETag, // Expect the initial ETag
                        null))
                .ReturnsAsync(1); // Indicate success (1 item patched)


            // Act
            var result = await _service.TriggerScheduledWorkflowAsync(companyId, workflowId);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.WorkflowScheduleSettings.DurablePayload);

            Assert.AreEqual(
                expectedDurablePayload.Id,
                result.WorkflowScheduleSettings.DurablePayload?.Id); // Assertion remains valid on the *final* result

            _mockWorkflowOrchestrationService.Verify(
                s => s.TriggerScheduleWorkflowAsync(
                    companyId,
                    flowHubConfig.Origin,
                    workflowId,
                    workflowVersionedId,
                    scheduleSettings.ScheduledAt.Value,
                    scheduleSettings.RecurringSettings,
                    "v1"),
                Times.Once);

            // Verify GetLatestWorkflowAsync was called twice
            _mockWorkflowService.Verify(s => s.GetLatestWorkflowAsync(workflowId, companyId), Times.Exactly(2));

            // Verify PatchWorkflowAsync was called - Use the same simplified matcher
            _mockWorkflowService.Verify(
                s => s.PatchWorkflowAsync(
                    workflowVersionedId,
                    companyId,
                    It.Is<List<PatchOperation>>(
                        ops =>
                            ops.Count == 2 &&
                            ops[0].OperationType == PatchOperationType.Set &&
                            ops[0].Path == "/workflow_schedule_settings/durable_payload" &&
                            ops[1].OperationType == PatchOperationType.Set &&
                            ops[1].Path == $"/{IHasUpdatedAt.PropertyNameUpdatedAt}"
                    ),
                    initialProxyWorkflow.ETag,
                    null),
                Times.Once);

            // Verify UpdateWorkflowAsync is NOT called
            _mockWorkflowService.Verify(
                s => s.UpdateWorkflowAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkflowTriggers>(),
                    It.IsAny<WorkflowEnrollmentSettings>(),
                    It.IsAny<WorkflowScheduleSettings>(),
                    It.IsAny<List<Step>>(),
                    It.IsAny<string>(),
                    It.IsAny<string?>(),
                    It.IsAny<AuditEntity.SleekflowStaff>(),
                    It.IsAny<Dictionary<string, object?>>(),
                    It.IsAny<string>(),
                    It.IsAny<string?>(),
                    It.IsAny<bool?>(),
                    It.IsAny<string?>(),
                    It.IsAny<string?>()
                   ));
        }

        [Test]
        public void TriggerScheduledWorkflowAsync_ThrowsException_WhenScheduledAtIsNull()
        {
            // --- Arrange ---
            var companyId = "c-no-sched";
            var workflowId = "wf-no-sched";
            var workflowVersionedId = $"{workflowId}-v1";
            var dbId = $"db-{workflowId}";
            var workflowName = "Test No Sched";
            var workflowType = "Standard";
            string? workflowGroupId = null;
            var activationStatus = "Active";
            var version = "1.0";
            var enrollmentPricingType = EnrolmentPricingTypes.Basic;
            var createdAt = DateTimeOffset.UtcNow;
            var updatedAt = createdAt;
            var staffId = "user-def";
            List<string>? teamIds = null;
            var createdByStaff = new AuditEntity.SleekflowStaff(staffId, teamIds);
            AuditEntity.SleekflowStaff? updatedByStaff = null;

            var triggers = new WorkflowTriggers(
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null);
            var enrollmentSettings = new WorkflowEnrollmentSettings(false, false, true);

            // Schedule settings with null ScheduledAt
            var scheduleSettings = new WorkflowScheduleSettings(
                null,
                true,
                null,
                null,
                null,
                null,
                null,
                WorkflowScheduleTypes.PredefinedDateTime,
                null); // ScheduledAt is null
            var steps = new List<Step>();
            var metadata = new Dictionary<string, object?>();

            var proxyWorkflow = new ProxyWorkflow(
                dbId,
                companyId,
                workflowId,
                workflowVersionedId,
                workflowName,
                workflowType,
                workflowGroupId,
                triggers,
                null, // isDynamicVariableEnabled
                enrollmentSettings,
                scheduleSettings,
                activationStatus,
                createdByStaff,
                updatedByStaff,
                createdAt,
                updatedAt,
                steps,
                metadata,
                version,
                enrollmentPricingType);

            _mockWorkflowService.Setup(s => s.GetLatestWorkflowAsync(workflowId, companyId))
                .ReturnsAsync(proxyWorkflow);

            // Act & Assert
            var ex = Assert.ThrowsAsync<SfFlowHubUserFriendlyException>(
                () => _service.TriggerScheduledWorkflowAsync(companyId, workflowId));
            Assert.AreEqual(UserFriendlyErrorCodes.UnmatchedCondition, ex.UserFriendlyErrorCode);
            StringAssert.Contains("missing ScheduledAt", ex.Message);
        }

        [Test]
        public void TriggerScheduledWorkflowAsync_ThrowsException_WhenDurablePayloadExists()
        {
            // --- Arrange ---
            var companyId = "c-payload-exists";
            var workflowId = "wf-payload-exists";
            var workflowVersionedId = $"{workflowId}-v1";
            var dbId = $"db-{workflowId}";
            var workflowName = "Test Payload Exists";
            var workflowType = "Standard";
            string? workflowGroupId = null;
            var activationStatus = "Active";
            var version = "1.0";
            var enrollmentPricingType = EnrolmentPricingTypes.Basic;
            var createdAt = DateTimeOffset.UtcNow;
            var updatedAt = createdAt;
            var staffId = "user-def2";
            List<string>? teamIds = null;
            var createdByStaff = new AuditEntity.SleekflowStaff(staffId, teamIds);
            AuditEntity.SleekflowStaff? updatedByStaff = null;

            var triggers = new WorkflowTriggers(
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null);
            var enrollmentSettings = new WorkflowEnrollmentSettings(false, false, true);

            // DurablePayload exists
            var existingDurablePayload = new DurablePayload(
                "dp-exist-456",
                "http://exist.uri/",
                "http://exist.uri/",
                "http://exist.uri/",
                "http://exist.uri/",
                "http://exist.uri/",
                "http://exist.uri/");

            // Schedule settings with existing payload
            var scheduleSettings = new WorkflowScheduleSettings(
                existingDurablePayload,
                true,
                null,
                DateTimeOffset.UtcNow,
                null,
                null,
                null,
                WorkflowScheduleTypes.PredefinedDateTime,
                null);
            var steps = new List<Step>();
            var metadata = new Dictionary<string, object?>();

            var proxyWorkflow = new ProxyWorkflow(
                dbId,
                companyId,
                workflowId,
                workflowVersionedId,
                workflowName,
                workflowType,
                workflowGroupId,
                triggers,
                null, // isDynamicVariableEnabled
                enrollmentSettings,
                scheduleSettings,
                activationStatus,
                createdByStaff,
                updatedByStaff,
                createdAt,
                updatedAt,
                steps,
                metadata,
                version,
                enrollmentPricingType);

            _mockWorkflowService.Setup(s => s.GetLatestWorkflowAsync(workflowId, companyId))
                .ReturnsAsync(proxyWorkflow);

            // Act & Assert
            var ex = Assert.ThrowsAsync<SfFlowHubUserFriendlyException>(
                () => _service.TriggerScheduledWorkflowAsync(companyId, workflowId));
            Assert.AreEqual(UserFriendlyErrorCodes.UnmatchedCondition, ex.UserFriendlyErrorCode);
            StringAssert.Contains("already scheduled", ex.Message);
        }

        [Test]
        public void TriggerScheduledWorkflowAsync_ThrowsException_WhenFlowHubConfigOriginIsNull()
        {
            // --- Arrange: ProxyWorkflow ---
            var companyId = "c-no-origin";
            var workflowId = "wf-no-origin";
            var workflowVersionedId = $"{workflowId}-v1";
            var proxyDbId = $"proxy-db-{workflowId}";
            var workflowName = "Test No Origin";
            var workflowType = "Standard";
            string? workflowGroupId = null;
            var activationStatus = "Active";
            var version = "1.0";
            var enrollmentPricingType = EnrolmentPricingTypes.Basic;
            var proxyCreatedAt = DateTimeOffset.UtcNow.AddMinutes(-5);
            var proxyUpdatedAt = proxyCreatedAt;
            var staffId = "user-def3";
            List<string>? teamIds = null;
            var createdByStaff = new AuditEntity.SleekflowStaff(staffId, teamIds);
            AuditEntity.SleekflowStaff? updatedByStaff = null;

            var triggers = new WorkflowTriggers(
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null);
            var enrollmentSettings = new WorkflowEnrollmentSettings(false, false, true);

            var scheduleSettings = new WorkflowScheduleSettings(
                null,
                true,
                null,
                DateTimeOffset.UtcNow,
                null,
                null,
                null,
                WorkflowScheduleTypes.PredefinedDateTime,
                null);
            var steps = new List<Step>();
            var metadata = new Dictionary<string, object?>();

            var proxyWorkflow = new ProxyWorkflow(
                proxyDbId,
                companyId,
                workflowId,
                workflowVersionedId,
                workflowName,
                workflowType,
                workflowGroupId,
                triggers,
                null, // isDynamicVariableEnabled
                enrollmentSettings,
                scheduleSettings,
                activationStatus,
                createdByStaff,
                updatedByStaff,
                proxyCreatedAt,
                proxyUpdatedAt,
                steps,
                metadata,
                version,
                enrollmentPricingType);

            // --- Arrange: FlowHubConfig with null Origin ---
            var configDbId = $"config-db-{companyId}";
            var configCreatedAt = DateTimeOffset.UtcNow.AddHours(-1);
            var configCreatedBy = new AuditEntity.SleekflowStaff("config-creator-2", null);

            var flowHubConfig = new FlowHubConfig(
                false,
                null,
                null,
                "etag-no-origin",
                configDbId,
                null, // Origin is null
                configCreatedAt,
                configCreatedAt,
                companyId,
                configCreatedBy,
                null,
                true,
                true,
                true,
                new GracePeriodUsageSettings(
                    TimeSpan.FromHours(48),
                    GracePeriodLifecycleStages.NotStarted,
                    GracePeriodWorkflowExecutionAllowanceRules.SumOfCurrentTierUsageLimitAndUsageLimitOffset,
                    null,
                    null,
                    null));

            _mockWorkflowService.Setup(s => s.GetLatestWorkflowAsync(workflowId, companyId))
                .ReturnsAsync(proxyWorkflow);

            _mockFlowHubConfigService.Setup(s => s.GetFlowHubConfigAsync(companyId))
                .ReturnsAsync(flowHubConfig);

            // Act & Assert
            var ex = Assert.ThrowsAsync<SfFlowHubUserFriendlyException>(
                () => _service.TriggerScheduledWorkflowAsync(companyId, workflowId));
            Assert.AreEqual(UserFriendlyErrorCodes.UnmatchedCondition, ex.UserFriendlyErrorCode);
            StringAssert.Contains("FlowHubConfig Origin not found", ex.Message);
        }

        // ====================================================================
        // Test Case for Old Recurring Schema
        // ====================================================================
        [Test]
        public async Task TriggerScheduledWorkflowAsync_CalculatesNextOccurrence_ForOldRecurringSchema()
        {
            // --- Arrange: Define args ---
            var companyId = "c-old-recur";
            var workflowId = "wf-old-recur";
            var workflowVersionedId = $"{workflowId}-v1";
            var proxyDbId = $"proxy-db-{workflowId}";
            var workflowName = "Test Old Recur";
            var workflowType = "Standard";
            string? workflowGroupId = null;
            var activationStatus = "Active";
            var version = "1.0";
            var enrolmentPricingType = EnrolmentPricingTypes.Basic;
            var proxyCreatedAt = DateTimeOffset.UtcNow.AddMinutes(-5);
            var proxyUpdatedAt = proxyCreatedAt; // Initial updated timestamp
            var staffId = "user-old";
            List<string>? teamIds = null;
            var createdByStaff = new AuditEntity.SleekflowStaff(staffId, teamIds);
            AuditEntity.SleekflowStaff? updatedByStaff = null;

            var triggers = new WorkflowTriggers(
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null);
            var enrollmentSettings = new WorkflowEnrollmentSettings(false, false, true);
            // Args for specific schedule settings
            DateTimeOffset? scheduleScheduledAtArg = DateTimeOffset.UtcNow.AddHours(-1);

            var scheduleRecurringSettingsArg = new WorkflowRecurringSettings(
                WorkflowRecurringTypes.Custom,
                1,
                null,
                null,
                null,
                null,
                null,
                null, null, null);

            var scheduleSettings = new WorkflowScheduleSettings(
                null,
                false,
                true,
                scheduleScheduledAtArg,
                null,
                null,
                null,
                WorkflowScheduleTypes.PredefinedDateTime,
                scheduleRecurringSettingsArg); // IsNew = false, IsOldCompleted = true
            var steps = new List<Step>();
            var metadata = new Dictionary<string, object?>();

            var initialProxyWorkflow = new ProxyWorkflow(
                proxyDbId,
                companyId,
                workflowId,
                workflowVersionedId,
                workflowName,
                workflowType,
                workflowGroupId,
                triggers,
                null, // isDynamicVariableEnabled
                enrollmentSettings,
                scheduleSettings,
                activationStatus,
                createdByStaff,
                updatedByStaff,
                proxyCreatedAt,
                proxyUpdatedAt,
                steps,
                metadata,
                version,
                enrolmentPricingType)
            {
                ETag = "initial-etag-old-recur" // Need ETag for Patch
            };
            // Args for FlowHubConfig
            var configOrigin = "test-origin-recur";
            var configDbId = $"config-db-{companyId}";
            var configCreatedAt = DateTimeOffset.UtcNow.AddHours(-2);
            var configCreatedBy = new AuditEntity.SleekflowStaff("config-creator-3", null);

            var flowHubConfig = new FlowHubConfig(
                true,
                null,
                null,
                "etag-old",
                configDbId,
                configOrigin,
                configCreatedAt,
                configCreatedAt,
                companyId,
                configCreatedBy,
                null,
                false,
                false,
                false,
                null);
            // Args for DurablePayload return
            var expectedDurablePayloadId = "dp-recur-789";
            var expectedDurablePayloadUri = "http://recur.uri/";

            var expectedDurablePayload = new DurablePayload(
                expectedDurablePayloadId,
                expectedDurablePayloadUri,
                expectedDurablePayloadUri,
                expectedDurablePayloadUri,
                expectedDurablePayloadUri,
                expectedDurablePayloadUri,
                expectedDurablePayloadUri);
            // Expected calculation result
            var nextOccurrence = scheduleScheduledAtArg.Value.AddDays(1);
            // --- End Args ---


            // Setup the GetLatestWorkflowAsync sequence
            _mockWorkflowService.SetupSequence(s => s.GetLatestWorkflowAsync(workflowId, companyId))
                .ReturnsAsync(initialProxyWorkflow) // First call returns the initial workflow
                .ReturnsAsync(
                    () => // Second call returns the workflow *after* the expected patch
                    {
                        // Create a new instance reflecting the patched state
                        var updatedScheduleSettings = new WorkflowScheduleSettings(
                            expectedDurablePayload, // Payload is now set
                            scheduleSettings.IsNewScheduledWorkflowSchema,
                            scheduleSettings.IsOldScheduledWorkflowSchemaFirstRecurringCompleted,
                            scheduleSettings
                                .ScheduledAt, // Note: ScheduledAt itself doesn't change in DB, only the trigger time
                            scheduleSettings.ContactPropertyId,
                            scheduleSettings.SchemaId,
                            scheduleSettings.SchemafulObjectPropertyId,
                            scheduleSettings.ScheduleType,
                            scheduleSettings.RecurringSettings);

                        // Create a new instance reflecting the patched state using constructor
                        return new ProxyWorkflow(
                            proxyDbId,
                            companyId,
                            workflowId,
                            workflowVersionedId,
                            workflowName,
                            workflowType,
                            workflowGroupId,
                            triggers,
                            null, // isDynamicVariableEnabled
                            enrollmentSettings,
                            updatedScheduleSettings,
                            activationStatus,
                            createdByStaff,
                            updatedByStaff,
                            proxyCreatedAt,
                            DateTimeOffset.UtcNow, // UpdatedAt changes
                            steps,
                            metadata,
                            version,
                            enrolmentPricingType)
                        {
                            ETag = "updated-etag-old-recur" // ETag would change after patch
                        };
                    });


            _mockFlowHubConfigService.Setup(s => s.GetFlowHubConfigAsync(companyId))
                .ReturnsAsync(flowHubConfig);

            _mockWorkflowRecurringSettingsParser.Setup(
                    p => p.GetNextOccurrenceAfter(
                        scheduleSettings.ScheduledAt.Value,
                        scheduleSettings.RecurringSettings,
                        It.Is<DateTimeOffset>(
                            dt => dt <= DateTimeOffset.UtcNow &&
                                  dt > DateTimeOffset.UtcNow
                                      .AddSeconds(-5)))) // Allow small time window for test execution
                .Returns(nextOccurrence);

            _mockWorkflowOrchestrationService.Setup(
                    s => s.TriggerScheduleWorkflowAsync(
                        companyId,
                        flowHubConfig.Origin,
                        workflowId,
                        workflowVersionedId,
                        nextOccurrence, // Use calculated time
                        scheduleSettings.RecurringSettings,
                        "v1"))
                .ReturnsAsync(expectedDurablePayload);

            // Setup PatchWorkflowAsync
            _mockWorkflowService.Setup(
                    s => s.PatchWorkflowAsync(
                        workflowVersionedId,
                        companyId,
                        It.Is<List<PatchOperation>>(
                            ops =>
                                ops.Count == 2 &&
                                ops[0].OperationType == PatchOperationType.Set &&
                                ops[0].Path == "/workflow_schedule_settings/durable_payload" &&
                                ops[1].OperationType == PatchOperationType.Set &&
                                ops[1].Path == $"/{IHasUpdatedAt.PropertyNameUpdatedAt}"
                        ),
                        initialProxyWorkflow.ETag,
                        null))
                .ReturnsAsync(1);


            // Act
            await _service.TriggerScheduledWorkflowAsync(companyId, workflowId);

            // Assert
            _mockWorkflowRecurringSettingsParser.Verify(
                p => p.GetNextOccurrenceAfter(
                    scheduleSettings.ScheduledAt.Value,
                    scheduleSettings.RecurringSettings,
                    It.IsAny<DateTimeOffset>()), // Allow any DateTimeOffset here for verification
                Times.Once);

            _mockWorkflowOrchestrationService.Verify(
                s => s.TriggerScheduleWorkflowAsync(
                    companyId,
                    flowHubConfig.Origin,
                    workflowId,
                    workflowVersionedId,
                    nextOccurrence, // Verify calculated time was passed
                    scheduleSettings.RecurringSettings,
                    "v1"),
                Times.Once);

            // Verify GetLatestWorkflowAsync was called twice
            _mockWorkflowService.Verify(s => s.GetLatestWorkflowAsync(workflowId, companyId), Times.Exactly(2));

            // Verify PatchWorkflowAsync was called
            _mockWorkflowService.Verify(
                s => s.PatchWorkflowAsync(
                    workflowVersionedId,
                    companyId,
                    It.Is<List<PatchOperation>>(
                        ops =>
                            ops.Count == 2 &&
                            ops[0].OperationType == PatchOperationType.Set &&
                            ops[0].Path == "/workflow_schedule_settings/durable_payload" &&
                            ops[1].OperationType == PatchOperationType.Set &&
                            ops[1].Path == $"/{IHasUpdatedAt.PropertyNameUpdatedAt}"
                    ),
                    initialProxyWorkflow.ETag,
                    null),
                Times.Once); // Use simplified matcher

            // Verify UpdateWorkflowAsync is NOT called
            _mockWorkflowService.Verify(
                s => s.UpdateWorkflowAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkflowTriggers>(),
                    It.IsAny<WorkflowEnrollmentSettings>(),
                    It.IsAny<WorkflowScheduleSettings>(),
                    It.IsAny<List<Step>>(),
                    It.IsAny<string>(),
                    It.IsAny<string?>(),
                    It.IsAny<AuditEntity.SleekflowStaff>(),
                    It.IsAny<Dictionary<string, object?>>(),
                    It.IsAny<string>(),
                    It.IsAny<string?>(),
                    It.IsAny<bool?>(),
                    It.IsAny<string?>(),
                    It.IsAny<string?>()
                   ),
                Times.Never);
        }
        // ====================================================================

        #endregion

        #region TerminateScheduledWorkflowAsync Tests

        [Test]
        public async Task TerminateScheduledWorkflowAsync_HappyPath_TerminatesAndUpdatesWorkflow()
        {
            // --- Arrange: Define all constructor args ---
            var companyId = "c-term-happy";
            var workflowId = "wf-term-happy";
            var workflowVersionedId = $"{workflowId}-v1";
            var proxyDbId = $"proxy-db-{workflowId}";
            var workflowName = "Test Term Happy";
            var workflowType = "Standard";
            string? workflowGroupId = null;
            var activationStatus = "Active";
            var version = "1.0";
            var enrolmentPricingType = EnrolmentPricingTypes.Basic;
            var proxyCreatedAt = DateTimeOffset.UtcNow.AddMinutes(-10);
            var proxyUpdatedAt = proxyCreatedAt; // Initial updated timestamp
            var staffId = "user-term";

            List<string>? teamIds = new List<string>
            {
                "teamA"
            };
            var createdByStaff = new AuditEntity.SleekflowStaff(staffId, teamIds);
            AuditEntity.SleekflowStaff? updatedByStaff = null;

            var triggers = new WorkflowTriggers(
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null);
            var enrollmentSettings = new WorkflowEnrollmentSettings(true, false, false);
            // Args for DurablePayload to be terminated
            var durablePayloadId = "dp-term-123";
            var durablePayloadUri = "http://term.uri/";

            var durablePayloadToTerminate = new DurablePayload(
                durablePayloadId,
                durablePayloadUri,
                durablePayloadUri,
                durablePayloadUri,
                durablePayloadUri,
                durablePayloadUri,
                durablePayloadUri);

            // Args for WorkflowScheduleSettings including the payload
            var scheduleSettings = new WorkflowScheduleSettings(
                durablePayloadToTerminate,
                true,
                null,
                DateTimeOffset.UtcNow.AddDays(-1),
                null,
                null,
                null,
                WorkflowScheduleTypes.PredefinedDateTime,
                null);
            var steps = new List<Step>();
            var metadata = new Dictionary<string, object?>();
            // --- End Args ---

            // Arrange: Create ProxyWorkflow using the primary constructor
            var initialProxyWorkflow = new ProxyWorkflow(
                proxyDbId,
                companyId,
                workflowId,
                workflowVersionedId,
                workflowName,
                workflowType,
                workflowGroupId,
                triggers,
                null, // isDynamicVariableEnabled
                enrollmentSettings,
                scheduleSettings,
                activationStatus,
                createdByStaff,
                updatedByStaff,
                proxyCreatedAt,
                proxyUpdatedAt,
                steps,
                metadata,
                version,
                enrolmentPricingType)
            {
                ETag = "initial-etag-term-happy" // Need ETag for Patch
            };


            // Setup GetLatestWorkflowAsync sequence
            _mockWorkflowService.SetupSequence(s => s.GetLatestWorkflowAsync(workflowId, companyId))
                .ReturnsAsync(initialProxyWorkflow) // First call returns the workflow with payload
                .ReturnsAsync(
                    () => // Second call returns the workflow *after* payload removal
                    {
                        var updatedScheduleSettings = new WorkflowScheduleSettings(
                            null, // Payload is now null
                            scheduleSettings.IsNewScheduledWorkflowSchema,
                            scheduleSettings.IsOldScheduledWorkflowSchemaFirstRecurringCompleted,
                            scheduleSettings.ScheduledAt,
                            scheduleSettings.ContactPropertyId,
                            scheduleSettings.SchemaId,
                            scheduleSettings.SchemafulObjectPropertyId,
                            scheduleSettings.ScheduleType,
                            scheduleSettings.RecurringSettings);

                        // Create new instance using constructor
                        return new ProxyWorkflow(
                            proxyDbId,
                            companyId,
                            workflowId,
                            workflowVersionedId,
                            workflowName,
                            workflowType,
                            workflowGroupId,
                            triggers,
                            null, // isDynamicVariableEnabled
                            enrollmentSettings,
                            updatedScheduleSettings,
                            activationStatus,
                            createdByStaff,
                            updatedByStaff,
                            proxyCreatedAt,
                            DateTimeOffset.UtcNow, // UpdatedAt changes
                            steps,
                            metadata,
                            version,
                            enrolmentPricingType)
                        {
                            ETag = "updated-etag-term-happy" // ETag would change
                        };
                    });


            _mockWorkflowOrchestrationService.Setup(
                    s => s.TerminateWorkflowDurableFunctionAsync(
                        companyId,
                        workflowId,
                        workflowVersionedId,
                        durablePayloadToTerminate))
                .ReturnsAsync(true);

            // Setup PatchWorkflowAsync instead of UpdateWorkflowAsync
            _mockWorkflowService.Setup(
                    s => s.PatchWorkflowAsync(
                        workflowVersionedId,
                        companyId,
                        It.Is<List<PatchOperation>>(
                            ops =>
                                ops.Count == 2 &&
                                ops[0].OperationType == PatchOperationType.Remove && // Check operation type
                                ops[0].Path == "/workflow_schedule_settings/durable_payload" && // Check path
                                ops[1].OperationType == PatchOperationType.Set && // Check operation type
                                ops[1].Path == $"/{IHasUpdatedAt.PropertyNameUpdatedAt}" // Check path
                        ),
                        initialProxyWorkflow.ETag, // Expect the initial ETag
                        null))
                .ReturnsAsync(1); // Indicate success


            // Act
            var result = await _service.TerminateScheduledWorkflowAsync(companyId, workflowId);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsNull(result.WorkflowScheduleSettings.DurablePayload); // Assert on final result

            _mockWorkflowOrchestrationService.Verify(
                s => s.TerminateWorkflowDurableFunctionAsync(
                    companyId,
                    workflowId,
                    workflowVersionedId,
                    It.Is<DurablePayload>(dp => dp.Id == durablePayloadId)),
                Times.Once);

            // Verify GetLatestWorkflowAsync was called twice
            _mockWorkflowService.Verify(s => s.GetLatestWorkflowAsync(workflowId, companyId), Times.Exactly(2));

            // Verify PatchWorkflowAsync was called instead of UpdateWorkflowAsync
            _mockWorkflowService.Verify(
                s => s.PatchWorkflowAsync(
                    workflowVersionedId,
                    companyId,
                    It.Is<List<PatchOperation>>(
                        ops =>
                            ops.Count == 2 &&
                            ops[0].OperationType == PatchOperationType.Remove &&
                            ops[0].Path == "/workflow_schedule_settings/durable_payload" &&
                            ops[1].OperationType == PatchOperationType.Set &&
                            ops[1].Path == $"/{IHasUpdatedAt.PropertyNameUpdatedAt}"
                    ),
                    initialProxyWorkflow.ETag,
                    null),
                Times.Once); // Use simplified matcher

            // Verify UpdateWorkflowAsync is NOT called
            _mockWorkflowService.Verify(
                s => s.UpdateWorkflowAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<WorkflowTriggers>(),
                    It.IsAny<WorkflowEnrollmentSettings>(),
                    It.IsAny<WorkflowScheduleSettings>(),
                    It.IsAny<List<Step>>(),
                    It.IsAny<string>(),
                    It.IsAny<string?>(),
                    It.IsAny<AuditEntity.SleekflowStaff>(),
                    It.IsAny<Dictionary<string, object?>>(),
                    It.IsAny<string?>(),
                    It.IsAny<string>(),
                    It.IsAny<bool?>(),
                    It.IsAny<string?>(),
                    It.IsAny<string?>()
                   ),
                Times.Never);
        }

        [Test]
        public void TerminateScheduledWorkflowAsync_ThrowsException_WhenScheduleSettingsIsNull()
        {
            // --- Arrange ---
            var companyId = "c-term-no-settings";
            var workflowId = "wf-term-no-settings";
            var workflowVersionedId = $"{workflowId}-v1";
            var proxyDbId = $"proxy-db-{workflowId}";
            var workflowName = "Test Term No Settings";
            var workflowType = "Standard";
            string? workflowGroupId = null;
            var activationStatus = "Active";
            var version = "1.0";
            var enrolmentPricingType = EnrolmentPricingTypes.Basic;
            var proxyCreatedAt = DateTimeOffset.UtcNow;
            var proxyUpdatedAt = proxyCreatedAt;
            var staffId = "user-term-ns";
            List<string>? teamIds = null;
            var createdByStaff = new AuditEntity.SleekflowStaff(staffId, teamIds);
            AuditEntity.SleekflowStaff? updatedByStaff = null;

            var triggers = new WorkflowTriggers(
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null);
            var enrollmentSettings = new WorkflowEnrollmentSettings(false, false, true);
            var steps = new List<Step>();
            var metadata = new Dictionary<string, object?>();

            // Create ProxyWorkflow with *valid* schedule settings first
            var validScheduleSettings = new WorkflowScheduleSettings(
                null,
                true,
                null,
                null,
                null,
                null,
                null,
                WorkflowScheduleTypes.None,
                null);

            var proxyWorkflow = new ProxyWorkflow(
                proxyDbId,
                companyId,
                workflowId,
                workflowVersionedId,
                workflowName,
                workflowType,
                workflowGroupId,
                triggers,
                null, // isDynamicVariableEnabled
                enrollmentSettings,
                validScheduleSettings,
                activationStatus,
                createdByStaff,
                updatedByStaff,
                proxyCreatedAt,
                proxyUpdatedAt,
                steps,
                metadata,
                version,
                enrolmentPricingType);
            // NOW set the property to null for the mock return
            proxyWorkflow.WorkflowScheduleSettings = null;

            _mockWorkflowService.Setup(s => s.GetLatestWorkflowAsync(workflowId, companyId))
                .ReturnsAsync(proxyWorkflow); // Return the object with null settings

            // Act & Assert
            var ex = Assert.ThrowsAsync<SfFlowHubUserFriendlyException>(
                () => _service.TerminateScheduledWorkflowAsync(companyId, workflowId));
            Assert.AreEqual(UserFriendlyErrorCodes.UnmatchedCondition, ex.UserFriendlyErrorCode);
            StringAssert.Contains("missing WorkflowScheduleSettings", ex.Message);
        }

        [Test]
        public void TerminateScheduledWorkflowAsync_ThrowsException_WhenDurablePayloadIsNull()
        {
            // --- Arrange ---
            var companyId = "c-term-no-payload";
            var workflowId = "wf-term-no-payload";
            var workflowVersionedId = $"{workflowId}-v1";
            var proxyDbId = $"proxy-db-{workflowId}";
            var workflowName = "Test Term No Payload";
            var workflowType = "Standard";
            string? workflowGroupId = null;
            var activationStatus = "Active";
            var version = "1.0";
            var enrolmentPricingType = EnrolmentPricingTypes.Basic;
            var proxyCreatedAt = DateTimeOffset.UtcNow;
            var proxyUpdatedAt = proxyCreatedAt;
            var staffId = "user-term-np";
            List<string>? teamIds = null;
            var createdByStaff = new AuditEntity.SleekflowStaff(staffId, teamIds);
            AuditEntity.SleekflowStaff? updatedByStaff = null;

            var triggers = new WorkflowTriggers(
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null);
            var enrollmentSettings = new WorkflowEnrollmentSettings(false, false, true);

            // Args for WorkflowScheduleSettings with DurablePayload = null
            var scheduleSettings = new WorkflowScheduleSettings(
                null,
                true,
                null,
                DateTimeOffset.UtcNow.AddDays(-1),
                null,
                null,
                null,
                WorkflowScheduleTypes.PredefinedDateTime,
                null); // DurablePayload is null
            var steps = new List<Step>();
            var metadata = new Dictionary<string, object?>();

            var proxyWorkflow = new ProxyWorkflow(
                proxyDbId,
                companyId,
                workflowId,
                workflowVersionedId,
                workflowName,
                workflowType,
                workflowGroupId,
                triggers,
                null, // isDynamicVariableEnabled
                enrollmentSettings,
                scheduleSettings,
                activationStatus,
                createdByStaff,
                updatedByStaff,
                proxyCreatedAt,
                proxyUpdatedAt,
                steps,
                metadata,
                version,
                enrolmentPricingType);

            _mockWorkflowService.Setup(s => s.GetLatestWorkflowAsync(workflowId, companyId))
                .ReturnsAsync(proxyWorkflow);

            // Act & Assert
            var ex = Assert.ThrowsAsync<SfFlowHubUserFriendlyException>(
                () => _service.TerminateScheduledWorkflowAsync(companyId, workflowId));
            Assert.AreEqual(UserFriendlyErrorCodes.UnmatchedCondition, ex.UserFriendlyErrorCode);
            StringAssert.Contains("missing DurablePayload", ex.Message);
        }

        #endregion

    }
}