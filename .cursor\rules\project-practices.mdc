---
description:
globs:
alwaysApply: true
---

# Rules of Everything

1.  **Precision in Understanding:**
    * **Seek Clarity First:** Before taking any action, ensure you have a crystal-clear understanding of the user's intent. If anything is ambiguous, ask direct, specific questions.
    * **Validate Assumptions:** Explicitly state any assumptions you are making and ask the user to confirm them.
    * **Leverage Knowledge Systematically:** When unsure about APIs, libraries, or file structures, prioritize checking the relevant documentation or the actual file content using provided tools *before* making any assumptions.

2.  **Comprehensive Problem Solving:**
    * **Persistence to Completion:** Stay focused on the task until you have demonstrably fulfilled *all* aspects of the user's request.
    * **Verification Protocol:** After each step and at the end of the process, actively verify that the changes made align precisely with the user's goals. This might involve running tests, inspecting the output, or asking the user for confirmation.
    * **Constructive Guidance When Stuck:** If you encounter a roadblock, clearly articulate the problem, the steps you've taken, and propose specific, actionable alternatives or questions for the user to help you move forward.

3.  **Strategic and Deliberate Execution:**
    * **Structured Problem Decomposition:** Break down complex editing tasks into logical, sequential sub-tasks. Outline these steps before making any modifications.
    * **Proactive Planning and Justification:** For each planned step, briefly explain *why* you are taking that action and what outcome you expect.
    * **Iterative Evaluation and Adaptation:** After completing each sub-task, pause to assess the results. Did it achieve the intended outcome? Does it necessitate adjusting the subsequent steps? Be prepared to revise your plan based on these evaluations.
    * **Thoughtful Action Over Rapid Tool Use:** Avoid making a flurry of tool calls without carefully analyzing the results of each. Prioritize understanding the information retrieved before proceeding.

4.  **Contextual and Accessible References in Markdown:**
    * **Precise Relative Paths:** When generating Markdown that refers to specific classes, always include the full relative path from the root of the project to ensure the references are accurate and navigable. For example, `src/components/Button/Button.js`.

# Sleekflow Project Practices

This document outlines Sleekflow's development practices, service patterns, and implementation examples that complement the core architecture described in [Architecture.md](mdc:Architecture.md).

## Full Project Practices is available in [ProjectPractices.md](mdc:ProjectPractices.md)

## Contents

- [Development Standards](mdc:ProjectPractices-DevelopmentStandards.md) - C# coding standards, nullability, and formatting
- [Implementation Patterns](mdc:ProjectPractices-ImplementationPatterns.md) - Service lifetimes, dependency injection, database access, HTTP, caching, and gateway implementations
- [Error Handling & Observability](mdc:ProjectPractices-ErrorHandling.md) - Exception handling, request auditing, and distributed tracing
- [API Versioning and Documentation](mdc:ProjectPractices-APIVersioning.md) - Swagger generation and API versioning standards
- [Scalability Practices](mdc:ProjectPractices-Scalability.md) - Distributed ID generation and deployment architecture
- [Security Practices](mdc:ProjectPractices-Security.md) - Authentication and configuration security
- [Implementation Examples](mdc:ProjectPractices-ImplementationExamples.md) - Concrete examples of the architecture in action

- @Testing Practices - Unit testing, integration testing, test data generation, and AI agent testing