openapi: 3.0.1
info:
  title: Sleekflow 1.0
  version: '1.0'
servers:
  - url: https://localhost:7080
    description: Local
paths:
  /Public/healthz:
    get:
      tags:
        - Public
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /Public/Zapier/Triggers/OnSchemafulObjectCreated:
    post:
      tags:
        - Public
      parameters:
        - name: sleekflow_company_id
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Location
          in: header
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/CreateZapierTriggerOnSchemafulObjectCreatedRequest'
          application/json:
            schema:
              $ref: '#/components/schemas/CreateZapierTriggerOnSchemafulObjectCreatedRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/CreateZapierTriggerOnSchemafulObjectCreatedRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/CreateZapierTriggerOnSchemafulObjectCreatedRequest'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/CreateZapierTriggerResponse'
            application/json:
              schema:
                $ref: '#/components/schemas/CreateZapierTriggerResponse'
            text/json:
              schema:
                $ref: '#/components/schemas/CreateZapierTriggerResponse'
    delete:
      tags:
        - Public
      parameters:
        - name: sleekflow_company_id
          in: header
          schema:
            type: string
        - name: zap_id
          in: query
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
    get:
      tags:
        - Public
      parameters:
        - name: sleekflow_company_id
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Location
          in: header
          schema:
            type: string
        - name: schema_unique_name
          in: query
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /Public/Zapier/Triggers/OnSchemafulObjectUpdated:
    post:
      tags:
        - Public
      parameters:
        - name: sleekflow_company_id
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Location
          in: header
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/CreateZapierTriggerOnSchemafulObjectUpdatedRequest'
          application/json:
            schema:
              $ref: '#/components/schemas/CreateZapierTriggerOnSchemafulObjectUpdatedRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/CreateZapierTriggerOnSchemafulObjectUpdatedRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/CreateZapierTriggerOnSchemafulObjectUpdatedRequest'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/CreateZapierTriggerResponse'
            application/json:
              schema:
                $ref: '#/components/schemas/CreateZapierTriggerResponse'
            text/json:
              schema:
                $ref: '#/components/schemas/CreateZapierTriggerResponse'
    delete:
      tags:
        - Public
      parameters:
        - name: sleekflow_company_id
          in: header
          schema:
            type: string
        - name: zap_id
          in: query
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
    get:
      tags:
        - Public
      parameters:
        - name: sleekflow_company_id
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Location
          in: header
          schema:
            type: string
        - name: schema_unique_name
          in: query
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
components:
  schemas:
    ActionMessageObject:
      type: object
      properties:
        flow_token:
          type: string
          nullable: true
        flow_action_data:
          type: object
          additionalProperties: { }
          nullable: true
      additionalProperties: false
    AudioMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        caption:
          type: string
          nullable: true
        filename:
          type: string
          nullable: true
        provider:
          $ref: '#/components/schemas/MediaMessageObjectProvider'
      additionalProperties: false
    AuthenticateZapierApiKeyInput:
      required:
        - api_key
      type: object
      properties:
        api_key:
          minLength: 1
          type: string
      additionalProperties: false
    AuthenticateZapierApiKeyOutput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
      additionalProperties: false
    ButtonReplyMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
      additionalProperties: false
    ContactConversation:
      required:
        - conversation_id
        - conversation_status
      type: object
      properties:
        conversation_status:
          minLength: 1
          type: string
        conversation_id:
          minLength: 1
          type: string
        last_message_channel:
          type: string
          nullable: true
        last_message_channel_id:
          type: string
          nullable: true
      additionalProperties: false
    ContactDetail:
      required:
        - contact
      type: object
      properties:
        contact:
          type: object
          additionalProperties:
            nullable: true
        contact_owner:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        lists:
          type: array
          items:
            $ref: '#/components/schemas/ContactList'
          nullable: true
        conversation:
          $ref: '#/components/schemas/ContactConversation'
      additionalProperties: false
    ContactList:
      type: object
      properties:
        id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        added_at:
          type: string
          format: date-time
        is_imported:
          type: boolean
      additionalProperties: false
    ContactMessageObject:
      type: object
      properties:
        addresses:
          type: array
          items:
            $ref: '#/components/schemas/ContactMessageObjectAddress'
          nullable: true
        birthday:
          type: string
          nullable: true
        emails:
          type: array
          items:
            $ref: '#/components/schemas/ContactMessageObjectEmail'
          nullable: true
        name:
          $ref: '#/components/schemas/ContactMessageObjectName'
        org:
          $ref: '#/components/schemas/ContactMessageObjectOrg'
        ims:
          type: array
          items:
            $ref: '#/components/schemas/ContactMessageObjectIm'
          nullable: true
        phones:
          type: array
          items:
            $ref: '#/components/schemas/ContactMessageObjectPhone'
          nullable: true
        urls:
          type: array
          items:
            $ref: '#/components/schemas/ContactMessageObjectUrl'
          nullable: true
      additionalProperties: false
    ContactMessageObjectAddress:
      type: object
      properties:
        street:
          type: string
          nullable: true
        city:
          type: string
          nullable: true
        state:
          type: string
          nullable: true
        zip:
          type: string
          nullable: true
        country:
          type: string
          nullable: true
        country_code:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    ContactMessageObjectEmail:
      type: object
      properties:
        email:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    ContactMessageObjectIm:
      type: object
      properties:
        service:
          type: string
          nullable: true
        user_id:
          type: string
          nullable: true
      additionalProperties: false
    ContactMessageObjectName:
      type: object
      properties:
        formatted_name:
          type: string
          nullable: true
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        middle_name:
          type: string
          nullable: true
        suffix:
          type: string
          nullable: true
        prefix:
          type: string
          nullable: true
      additionalProperties: false
    ContactMessageObjectOrg:
      type: object
      properties:
        company:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        department:
          type: string
          nullable: true
      additionalProperties: false
    ContactMessageObjectPhone:
      type: object
      properties:
        phone:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        wa_id:
          type: string
          nullable: true
      additionalProperties: false
    ContactMessageObjectUrl:
      type: object
      properties:
        url:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    ConversationChannelLastMessage:
      type: object
      properties:
        conversation_id:
          type: string
          nullable: true
        message_id:
          type: string
          nullable: true
        message_unique_id:
          type: string
          nullable: true
        message_status:
          type: string
          nullable: true
        message_type:
          type: string
          nullable: true
        message_delivery_type:
          type: string
          nullable: true
        channel:
          type: string
          nullable: true
        channel_id:
          type: string
          nullable: true
        message_content:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    ConversationMessageOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
        message_unique_id:
          type: string
          nullable: true
        message_content:
          type: string
          nullable: true
        is_sent_from_sleekflow:
          type: boolean
        created_at:
          type: string
          format: date-time
          nullable: true
        files:
          type: array
          items:
            $ref: '#/components/schemas/ConversationMessageOutputFileDto'
          nullable: true
      additionalProperties: false
    ConversationMessageOutputFileDto:
      type: object
      properties:
        url:
          type: string
          nullable: true
        mime_type:
          type: string
          nullable: true
        file_size:
          type: integer
          format: int64
      additionalProperties: false
    CreateContactAndConversationInput:
      required:
        - channel
        - page_id
        - sender_id
        - sender_name
      type: object
      properties:
        sender_id:
          minLength: 1
          type: string
        page_id:
          minLength: 1
          type: string
        sender_name:
          minLength: 1
          type: string
        channel:
          minLength: 1
          type: string
        reserved_contact_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateZapierTriggerOnSchemafulObjectCreatedRequest:
      required:
        - hook_url
        - schema_unique_name
        - zap_id
      type: object
      properties:
        schema_unique_name:
          minLength: 1
          type: string
        zap_id:
          minLength: 1
          type: string
        hook_url:
          minLength: 1
          type: string
      additionalProperties: false
    CreateZapierTriggerOnSchemafulObjectUpdatedRequest:
      required:
        - hook_url
        - schema_unique_name
        - zap_id
      type: object
      properties:
        schema_unique_name:
          minLength: 1
          type: string
        zap_id:
          minLength: 1
          type: string
        hook_url:
          minLength: 1
          type: string
      additionalProperties: false
    CreateZapierTriggerResponse:
      type: object
      properties:
        subscribe_data:
          $ref: '#/components/schemas/ZapierSubscribeData'
      additionalProperties: false
    CurrencyMessageObject:
      type: object
      properties:
        fallback_value:
          type: string
          nullable: true
        code:
          type: string
          nullable: true
        amount_1000:
          type: integer
          format: int32
      additionalProperties: false
    CustomData:
      type: object
      properties:
        currency:
          type: string
          nullable: true
        value:
          type: number
          format: decimal
      additionalProperties: false
    DateTimeMessageObject:
      type: object
      properties:
        fallback_value:
          type: string
          nullable: true
      additionalProperties: false
    DocumentMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        caption:
          type: string
          nullable: true
        filename:
          type: string
          nullable: true
        provider:
          $ref: '#/components/schemas/MediaMessageObjectProvider'
      additionalProperties: false
    EvaluationAdditionalData:
      type: object
      properties:
        can_proceed_with_ai_agent_workflow:
          type: boolean
          nullable: true
      additionalProperties: false
    EvaluationContext:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        object_id:
          type: string
          nullable: true
        workflow_id:
          type: string
          nullable: true
        workflow_versioned_id:
          type: string
          nullable: true
        object_type:
          type: string
          nullable: true
        origin:
          type: string
          nullable: true
        additional_data:
          $ref: '#/components/schemas/EvaluationAdditionalData'
      additionalProperties: false
    FacebookMessengerMessageObject:
      type: object
      properties:
        message:
          $ref: '#/components/schemas/FacebookPageMessengerMessageObject'
        messaging_type:
          type: string
          nullable: true
        tag:
          type: string
          nullable: true
      additionalProperties: false
    FacebookPageMessengerAttachmentObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        payload:
          $ref: '#/components/schemas/FacebookPageMessengerPayloadObject'
      additionalProperties: false
    FacebookPageMessengerMessageObject:
      type: object
      properties:
        attachment:
          $ref: '#/components/schemas/FacebookPageMessengerAttachmentObject'
        text:
          type: string
          nullable: true
      additionalProperties: false
    FacebookPageMessengerPayloadObject:
      type: object
      properties:
        url:
          type: string
          nullable: true
        is_reusable:
          type: boolean
          nullable: true
      additionalProperties: false
    FacebookPageMessengerSendMessageInputFromTo:
      allOf:
        - $ref: '#/components/schemas/SendMessageInputFromTo'
        - type: object
          properties:
            from_facebook_page_id:
              type: string
              nullable: true
            to_facebook_id:
              type: string
              nullable: true
            facebook_post_comment_details:
              $ref: '#/components/schemas/FacebookPostCommentDetailsObject'
          additionalProperties: false
    FacebookPostCommentDetailsObject:
      type: object
      properties:
        from_facebook_post_id:
          type: string
          nullable: true
        to_facebook_comment_id:
          type: string
          nullable: true
      additionalProperties: false
    FromFacebookSender:
      type: object
      properties:
        facebook_id:
          type: string
          nullable: true
        facebook_name:
          type: string
          nullable: true
      additionalProperties: false
    FromInstagramSender:
      type: object
      properties:
        instagram_id:
          type: string
          nullable: true
        instagram_user_name:
          type: string
          nullable: true
      additionalProperties: false
    GetCompanyTimeZoneIdInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetCompanyUsageCycleInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetCompanyUsageCycleOutput:
      type: object
      properties:
        from_date_time:
          type: string
          format: date-time
        to_date_time:
          type: string
          format: date-time
      additionalProperties: false
    GetContactPropertiesInput:
      required:
        - contact_id
        - state_identity
      type: object
      properties:
        state_identity:
          $ref: '#/components/schemas/StateIdentity'
        contact_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetContactPropertiesOutput:
      type: object
      properties:
        contact_properties:
          type: object
          additionalProperties:
            type: string
          nullable: true
      additionalProperties: false
    GetContactPropertyValueByContactIdsInput:
      required:
        - contact_ids
        - contact_property_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        contact_ids:
          type: array
          items:
            type: string
        contact_property_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetContactPropertyValueByContactIdsOutput:
      required:
        - property_values
      type: object
      properties:
        property_values:
          type: object
          additionalProperties:
            type: string
      additionalProperties: false
    GetContactsByBatchInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        last_contact_created_at:
          type: string
          format: date-time
          nullable: true
        last_contact_id:
          type: string
          nullable: true
        batch_size:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    GetContactsByBatchOutput:
      required:
        - contacts
      type: object
      properties:
        contacts:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/ContactDetail'
        next_batch:
          $ref: '#/components/schemas/NextBatch'
      additionalProperties: false
    GetConversationLastMessagesInput:
      required:
        - contact_id
        - limit
        - offset
        - state_identity
        - target_channels
      type: object
      properties:
        state_identity:
          $ref: '#/components/schemas/StateIdentity'
        contact_id:
          minLength: 1
          type: string
        target_channels:
          type: array
          items:
            type: string
        target_channel_id:
          type: string
          nullable: true
        offset:
          maximum: 999
          minimum: 0
          type: integer
          format: int32
        limit:
          maximum: 999
          minimum: 0
          type: integer
          format: int32
        retrieval_window_timestamp:
          type: string
          format: date-time
          nullable: true
        agent_session_start_at_timestamp:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    GetConversationLastMessagesOutput:
      type: object
      properties:
        conversation_messages:
          type: array
          items:
            $ref: '#/components/schemas/ConversationMessageOutput'
          nullable: true
      additionalProperties: false
    GetFacebookPageAccessTokenInput:
      required:
        - facebook_page_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        facebook_page_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetFacebookPageAccessTokenOutput:
      type: object
      properties:
        facebook_page_access_token:
          type: string
          nullable: true
      additionalProperties: false
    GetInstagramPageAccessTokenInput:
      required:
        - instagram_page_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        instagram_page_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetInstagramPageAccessTokenOutput:
      type: object
      properties:
        instagram_page_access_token:
          type: string
          nullable: true
      additionalProperties: false
    GetIntelligentHubUsageFilterInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetIntelligentHubUsageFilterOutput:
      type: object
      properties:
        intelligent_hub_usage_filter:
          $ref: '#/components/schemas/IntelligentHubUsageFilterOutput'
      additionalProperties: false
    GetIsCompanyHasAiPocPlanInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetIsCompanyHasAiPocPlanOutput:
      type: object
      properties:
        is_company_has_ai_poc_plan:
          type: boolean
      additionalProperties: false
    GetSchemaIdByUniqueNameInput:
      required:
        - schema_unique_name
        - sleekflow_company_id
      type: object
      properties:
        schema_unique_name:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetSchemaIdByUniqueNameOutput:
      type: object
      properties:
        schemaId:
          type: string
          nullable: true
      additionalProperties: false
    GetSchemafulObjectZapierSampleInput:
      required:
        - schema_unique_name
        - sleekflow_company_id
      type: object
      properties:
        schema_unique_name:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetSchemafulObjectZapierSampleOutput:
      type: object
      properties:
        schemafulObjectZapierViewModel:
          nullable: true
      additionalProperties: false
    GetTicketExportDataInput:
      required:
        - id
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: '#/components/schemas/StateIdentity'
        id:
          minLength: 1
          type: string
      additionalProperties: false
    GetTicketExportDataOutput:
      type: object
      properties:
        ticket:
          $ref: '#/components/schemas/GetTicketExportDataOutputTicket'
      additionalProperties: false
    GetTicketExportDataOutputChannel:
      type: object
      properties:
        channel_type:
          type: string
          nullable: true
        channel_identity_id:
          type: string
          nullable: true
        channel_name:
          type: string
          nullable: true
      additionalProperties: false
    GetTicketExportDataOutputTicket:
      type: object
      properties:
        created_at:
          type: string
          format: date-time
        created_by:
          type: string
          nullable: true
        created_by_email:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        channel:
          $ref: '#/components/schemas/GetTicketExportDataOutputChannel'
        status_id:
          type: string
          nullable: true
        status_name:
          type: string
          nullable: true
        priority_id:
          type: string
          nullable: true
        priority_name:
          type: string
          nullable: true
        due_date:
          type: string
          format: date-time
          nullable: true
        type_id:
          type: string
          nullable: true
        type_name:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        first_assignee:
          type: string
          nullable: true
        first_assignee_email:
          type: string
          nullable: true
        current_assignee:
          type: string
          nullable: true
        current_assignee_email:
          type: string
          nullable: true
        contact_name:
          type: string
          nullable: true
        contact_phone_number:
          type: string
          nullable: true
        contact_email:
          type: string
          nullable: true
        first_respondent:
          type: string
          nullable: true
        first_respondent_email:
          type: string
          nullable: true
        first_response_timestamp:
          type: string
          format: date-time
          nullable: true
        resolution_agent:
          type: string
          nullable: true
        resolution_agent_email:
          type: string
          nullable: true
        resolution_timestamp:
          type: string
          format: date-time
          nullable: true
        ticket_reassigned_before_resolution:
          type: boolean
      additionalProperties: false
    ImageMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        caption:
          type: string
          nullable: true
        filename:
          type: string
          nullable: true
        provider:
          $ref: '#/components/schemas/MediaMessageObjectProvider'
      additionalProperties: false
    InstagramMediaCommentDetailsObject:
      type: object
      properties:
        from_instagram_media_id:
          type: string
          nullable: true
        to_instagram_comment_id:
          type: string
          nullable: true
      additionalProperties: false
    InstagramMessengerMessageObject:
      type: object
      properties:
        message:
          $ref: '#/components/schemas/InstagramPageMessengerMessageObject'
        messaging_type:
          type: string
          nullable: true
        tag:
          type: string
          nullable: true
      additionalProperties: false
    InstagramPageMessengerAttachmentDataObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        payload:
          $ref: '#/components/schemas/InstagramPageMessengerPayloadObject'
        is_reusable:
          type: boolean
          nullable: true
      additionalProperties: false
    InstagramPageMessengerMessageObject:
      type: object
      properties:
        attachment:
          $ref: '#/components/schemas/InstagramPageMessengerAttachmentDataObject'
        text:
          type: string
          nullable: true
      additionalProperties: false
    InstagramPageMessengerPayloadObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
      additionalProperties: false
    InstagramPageMessengerSendMessageInputFromTo:
      allOf:
        - $ref: '#/components/schemas/SendMessageInputFromTo'
        - type: object
          properties:
            from_instagram_page_id:
              type: string
              nullable: true
            to_instagram_id:
              type: string
              nullable: true
            instagram_media_comment_details:
              $ref: '#/components/schemas/InstagramMediaCommentDetailsObject'
          additionalProperties: false
    IntelligentHubUsageFilterOutput:
      type: object
      properties:
        from_date_time:
          type: string
          format: date-time
          nullable: true
        to_date_time:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    InteractiveMessageObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        header:
          $ref: '#/components/schemas/InteractiveMessageObjectHeader'
        body:
          $ref: '#/components/schemas/InteractiveMessageObjectBody'
        footer:
          $ref: '#/components/schemas/InteractiveMessageObjectFooter'
        action:
          $ref: '#/components/schemas/InteractiveMessageObjectAction'
      additionalProperties: false
    InteractiveMessageObjectAction:
      type: object
      properties:
        button:
          type: string
          nullable: true
        buttons:
          type: array
          items:
            $ref: '#/components/schemas/InteractiveMessageObjectActionButton'
          nullable: true
        sections:
          type: array
          items:
            $ref: '#/components/schemas/InteractiveMessageObjectActionSection'
          nullable: true
        catalog_id:
          type: string
          nullable: true
        product_retailer_id:
          type: string
          nullable: true
      additionalProperties: false
    InteractiveMessageObjectActionButton:
      type: object
      properties:
        type:
          type: string
          nullable: true
        reply:
          $ref: '#/components/schemas/InteractiveMessageObjectActionButtonReply'
      additionalProperties: false
    InteractiveMessageObjectActionButtonReply:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
      additionalProperties: false
    InteractiveMessageObjectActionSection:
      type: object
      properties:
        title:
          type: string
          nullable: true
        product_items:
          type: array
          items:
            $ref: '#/components/schemas/InteractiveMessageObjectActionSectionProductItem'
          nullable: true
        rows:
          type: array
          items:
            $ref: '#/components/schemas/InteractiveMessageObjectActionSectionRow'
          nullable: true
      additionalProperties: false
    InteractiveMessageObjectActionSectionProductItem:
      type: object
      properties:
        product_retailer_id:
          type: string
          nullable: true
      additionalProperties: false
    InteractiveMessageObjectActionSectionRow:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
      additionalProperties: false
    InteractiveMessageObjectBody:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    InteractiveMessageObjectFooter:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    InteractiveMessageObjectHeader:
      type: object
      properties:
        type:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
        video:
          $ref: '#/components/schemas/VideoMessageObject'
        image:
          $ref: '#/components/schemas/ImageMessageObject'
        document:
          $ref: '#/components/schemas/DocumentMessageObject'
      additionalProperties: false
    InteractiveReplyMessageObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        button_reply:
          $ref: '#/components/schemas/ButtonReplyMessageObject'
        list_reply:
          $ref: '#/components/schemas/ListReplyMessageObject'
        nfm_reply:
          $ref: '#/components/schemas/NfmReplyMessageObject'
      additionalProperties: false
    LineMessageObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
      additionalProperties: false
    ListReplyMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
      additionalProperties: false
    LiveChatMessageObject:
      type: object
      properties:
        message:
          type: string
          nullable: true
      additionalProperties: false
    LocationMessageObject:
      type: object
      properties:
        latitude:
          type: number
          format: double
          nullable: true
        longitude:
          type: number
          format: double
          nullable: true
        name:
          type: string
          nullable: true
        address:
          type: string
          nullable: true
      additionalProperties: false
    MediaMessageObjectProvider:
      type: object
      properties:
        name:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        config:
          $ref: '#/components/schemas/MediaMessageObjectProviderConfig'
      additionalProperties: false
    MediaMessageObjectProviderConfig:
      type: object
      properties:
        basic:
          $ref: '#/components/schemas/MediaMessageObjectProviderConfigBasic'
        bearer:
          $ref: '#/components/schemas/MediaMessageObjectProviderConfigBearer'
      additionalProperties: false
    MediaMessageObjectProviderConfigBasic:
      type: object
      properties:
        username:
          type: string
          nullable: true
        password:
          type: string
          nullable: true
      additionalProperties: false
    MediaMessageObjectProviderConfigBearer:
      type: object
      properties:
        bearer:
          type: string
          nullable: true
      additionalProperties: false
    MessageBody:
      type: object
      properties:
        audio_message:
          $ref: '#/components/schemas/AudioMessageObject'
        contacts_message:
          type: array
          items:
            $ref: '#/components/schemas/ContactMessageObject'
          nullable: true
        currency_message:
          $ref: '#/components/schemas/CurrencyMessageObject'
        document_message:
          $ref: '#/components/schemas/DocumentMessageObject'
        image_message:
          $ref: '#/components/schemas/ImageMessageObject'
        location_message:
          $ref: '#/components/schemas/LocationMessageObject'
        reaction_message:
          $ref: '#/components/schemas/ReactionMessageObject'
        text_message:
          $ref: '#/components/schemas/TextMessageObject'
        video_message:
          $ref: '#/components/schemas/VideoMessageObject'
        date_time_message:
          $ref: '#/components/schemas/DateTimeMessageObject'
        interactive_message:
          $ref: '#/components/schemas/InteractiveMessageObject'
        template_message:
          $ref: '#/components/schemas/TemplateMessageObject'
        interactive_reply_message:
          $ref: '#/components/schemas/InteractiveReplyMessageObject'
        order_message:
          $ref: '#/components/schemas/OrderMessageObject'
        facebook_messenger_message:
          $ref: '#/components/schemas/FacebookMessengerMessageObject'
        instagram_messenger_message:
          $ref: '#/components/schemas/InstagramMessengerMessageObject'
        telegram_messenger_message:
          $ref: '#/components/schemas/TelegramMessengerMessageObject'
        wechat_messenger_message:
          $ref: '#/components/schemas/WeChatMessengerMessageObject'
        live_chat_message:
          $ref: '#/components/schemas/LiveChatMessageObject'
        viber_message:
          $ref: '#/components/schemas/ViberMessageObject'
        line_message:
          $ref: '#/components/schemas/LineMessageObject'
        sms_message:
          $ref: '#/components/schemas/SmsMessageObject'
      additionalProperties: false
    NextBatch:
      type: object
      properties:
        last_contact_created_at:
          type: string
          format: date-time
          nullable: true
        last_contact_id:
          type: string
          nullable: true
      additionalProperties: false
    NfmReplyMessageObject:
      type: object
      properties:
        name:
          type: string
          nullable: true
        body:
          type: string
          nullable: true
        response_json:
          type: string
          nullable: true
      additionalProperties: false
    OnClickToWhatsAppAdsMessageReceivedEventBody:
      required:
        - channel_id
        - contact
        - contact_id
        - conversation_id
        - created_at
        - is_new_contact
        - message
        - message_id
      type: object
      properties:
        message:
          $ref: '#/components/schemas/OnClickToWhatsAppAdsMessageReceivedEventBodyMessage'
        channel:
          type: string
          nullable: true
        channel_id:
          minLength: 1
          type: string
        conversation_id:
          minLength: 1
          type: string
        message_id:
          minLength: 1
          type: string
        message_unique_id:
          type: string
          nullable: true
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        is_new_contact:
          type: boolean
        is_sent_from_sleekflow:
          type: boolean
          readOnly: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnClickToWhatsAppAdsMessageReceivedEventBodyMessage:
      type: object
      properties:
        id:
          type: string
          nullable: true
        ctwa_clid:
          type: string
          nullable: true
        source_id:
          type: string
          nullable: true
        source_url:
          type: string
          nullable: true
        headline:
          type: string
          nullable: true
        body:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        message_body:
          $ref: '#/components/schemas/MessageBody'
        message_type:
          type: string
          nullable: true
        message_content:
          type: string
          nullable: true
        message_status:
          type: string
          nullable: true
        message_timestamp:
          type: integer
          format: int64
          readOnly: true
        message_delivery_type:
          type: string
          nullable: true
      additionalProperties: false
    OnContactConversationStatusChangedEventBody:
      required:
        - contact
        - contact_id
        - conversation_id
        - created_at
        - new_status
        - original_status
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        original_status:
          minLength: 1
          type: string
        new_status:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        conversation_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_identity_id:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnContactCreatedEventBody:
      required:
        - contact
        - contact_id
        - created_at
        - created_contact
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        created_contact:
          type: object
          additionalProperties:
            nullable: true
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_identity_id:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnContactEnrolledEventBody:
      required:
        - contact
        - contact_id
        - created_at
        - workflow_id
        - workflow_versioned_id
      type: object
      properties:
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnContactLabelRelationshipsChangedEventBody:
      required:
        - added_to_labels
        - contact
        - contact_id
        - created_at
        - labels
        - removed_from_labels
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        added_to_labels:
          type: array
          items:
            $ref: '#/components/schemas/OnContactLabelRelationshipsChangedEventBodyContactLabel'
        removed_from_labels:
          type: array
          items:
            $ref: '#/components/schemas/OnContactLabelRelationshipsChangedEventBodyContactLabel'
        labels:
          type: array
          items:
            $ref: '#/components/schemas/OnContactLabelRelationshipsChangedEventBodyContactLabel'
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_identity_id:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnContactLabelRelationshipsChangedEventBodyContactLabel:
      type: object
      properties:
        label_value:
          type: string
          nullable: true
        label_color:
          type: string
          nullable: true
        label_type:
          type: string
          nullable: true
      additionalProperties: false
    OnContactListRelationshipsChangedEventBody:
      required:
        - added_to_lists
        - contact
        - contact_id
        - created_at
        - lists
        - removed_from_lists
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        added_to_lists:
          type: array
          items:
            $ref: '#/components/schemas/OnContactListRelationshipsChangedEventBodyContactList'
        removed_from_lists:
          type: array
          items:
            $ref: '#/components/schemas/OnContactListRelationshipsChangedEventBodyContactList'
        lists:
          type: array
          items:
            $ref: '#/components/schemas/OnContactListRelationshipsChangedEventBodyContactList'
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_identity_id:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnContactListRelationshipsChangedEventBodyContactList:
      type: object
      properties:
        list_id:
          type: string
          nullable: true
        list_name:
          type: string
          nullable: true
      additionalProperties: false
    OnContactManuallyEnrolledEventBody:
      required:
        - contact
        - contact_id
        - created_at
        - workflow_id
        - workflow_versioned_id
      type: object
      properties:
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnContactUpdatedEventBody:
      required:
        - change_entries
        - contact
        - contact_id
        - created_at
        - post_updated_contact
        - pre_updated_contact
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        post_updated_contact:
          type: object
          additionalProperties:
            nullable: true
        pre_updated_contact:
          type: object
          additionalProperties:
            nullable: true
        change_entries:
          type: array
          items:
            $ref: '#/components/schemas/OnContactUpdatedEventBodyChangeEntry'
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_identity_id:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnContactUpdatedEventBodyChangeEntry:
      type: object
      properties:
        property_name:
          type: string
          nullable: true
        property_id:
          type: string
          nullable: true
        from_value:
          nullable: true
        to_value:
          nullable: true
      additionalProperties: false
    OnDateAndTimeArrivedCommonEventBody:
      required:
        - contact
        - contact_id
        - contact_owner
        - created_at
        - lists
        - workflow_id
        - workflow_versioned_id
      type: object
      properties:
        contact_id:
          minLength: 1
          type: string
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        contact_owner:
          type: object
          additionalProperties:
            nullable: true
        lists:
          type: array
          items:
            $ref: '#/components/schemas/ContactList'
        conversation:
          $ref: '#/components/schemas/ContactConversation'
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnFbIgPostCommentReceivedEventBody:
      required:
        - channel
        - comment_id
        - created_at
        - is_new_contact
        - page_id
        - post_comment
      type: object
      properties:
        post_comment:
          $ref: '#/components/schemas/OnFbIgPostCommentReceivedEventBodyMessage'
        page_id:
          minLength: 1
          type: string
        channel:
          minLength: 1
          type: string
        comment_id:
          minLength: 1
          type: string
        is_new_contact:
          type: boolean
        conversation_id:
          type: string
          nullable: true
        contact_id:
          type: string
          nullable: true
        contact:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnFbIgPostCommentReceivedEventBodyMessage:
      type: object
      properties:
        comment_id:
          type: string
          nullable: true
        post_id:
          type: string
          nullable: true
        from:
          $ref: '#/components/schemas/FromFacebookSender'
        text:
          type: string
          nullable: true
      additionalProperties: false
    OnGoogleSheetsRowCreatedEventBody:
      required:
        - connection_id
        - created_at
        - row
        - row_id
        - spreadsheet_id
        - worksheet_id
      type: object
      properties:
        connection_id:
          minLength: 1
          type: string
        spreadsheet_id:
          minLength: 1
          type: string
        worksheet_id:
          minLength: 1
          type: string
        row_id:
          minLength: 1
          type: string
        row:
          type: object
          additionalProperties:
            nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnGoogleSheetsRowUpdatedEventBody:
      required:
        - connection_id
        - created_at
        - row
        - row_id
        - spreadsheet_id
        - worksheet_id
      type: object
      properties:
        connection_id:
          minLength: 1
          type: string
        spreadsheet_id:
          minLength: 1
          type: string
        worksheet_id:
          minLength: 1
          type: string
        row_id:
          minLength: 1
          type: string
        row:
          type: object
          additionalProperties:
            nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnHubspotObjectCreatedEventBody:
      required:
        - connection_id
        - created_at
        - entity_type_name
        - object_dict
      type: object
      properties:
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        object_dict:
          type: object
          additionalProperties:
            nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnHubspotObjectEnrolledEventBody:
      required:
        - connection_id
        - created_at
        - entity_type_name
        - object_dict
        - workflow_id
        - workflow_versioned_id
      type: object
      properties:
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        object_dict:
          type: object
          additionalProperties:
            nullable: true
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnHubspotObjectUpdatedEventBody:
      required:
        - connection_id
        - created_at
        - entity_type_name
        - object_dict
      type: object
      properties:
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        object_dict:
          type: object
          additionalProperties:
            nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnInstagramMediaCommentReceivedEventBody:
      required:
        - comment_id
        - created_at
        - instagram_media_comment
        - instagram_page_id
        - is_new_contact
      type: object
      properties:
        instagram_media_comment:
          $ref: '#/components/schemas/OnInstagramMediaCommentReceivedEventBodyMessage'
        instagram_page_id:
          minLength: 1
          type: string
        comment_id:
          minLength: 1
          type: string
        is_new_contact:
          type: boolean
        conversation_id:
          type: string
          nullable: true
        contact_id:
          type: string
          nullable: true
        contact:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnInstagramMediaCommentReceivedEventBodyMessage:
      type: object
      properties:
        comment_id:
          type: string
          nullable: true
        media_id:
          type: string
          nullable: true
        From:
          $ref: '#/components/schemas/FromInstagramSender'
        media_product_type:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
      additionalProperties: false
    OnMessageReceivedEventBody:
      required:
        - channel_id
        - contact
        - contact_id
        - conversation_id
        - created_at
        - is_new_contact
        - message
        - message_id
      type: object
      properties:
        message:
          $ref: '#/components/schemas/OnMessageReceivedEventBodyMessage'
        channel:
          type: string
          nullable: true
        channel_id:
          minLength: 1
          type: string
        conversation_id:
          minLength: 1
          type: string
        message_id:
          minLength: 1
          type: string
        message_unique_id:
          type: string
          nullable: true
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        is_new_contact:
          type: boolean
        is_sent_from_sleekflow:
          type: boolean
          readOnly: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnMessageReceivedEventBodyMessage:
      type: object
      properties:
        id:
          type: string
          nullable: true
        quoted_message:
          $ref: '#/components/schemas/QuotedMessage'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        message_body:
          $ref: '#/components/schemas/MessageBody'
        message_type:
          type: string
          nullable: true
        message_content:
          type: string
          nullable: true
        message_status:
          type: string
          nullable: true
        message_timestamp:
          type: integer
          format: int64
          readOnly: true
        message_delivery_type:
          type: string
          nullable: true
      additionalProperties: false
    OnMessageSentEventBody:
      required:
        - channel_id
        - contact
        - contact_id
        - conversation_id
        - created_at
        - message
        - message_id
      type: object
      properties:
        message:
          $ref: '#/components/schemas/OnMessageSentEventBodyMessage'
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        channel:
          type: string
          nullable: true
        channel_id:
          minLength: 1
          type: string
        conversation_id:
          minLength: 1
          type: string
        message_id:
          minLength: 1
          type: string
        message_unique_id:
          type: string
          nullable: true
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_identity_id:
          type: string
          nullable: true
        is_sent_from_sleekflow:
          type: boolean
          readOnly: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnMessageSentEventBodyMessage:
      type: object
      properties:
        quoted_message:
          $ref: '#/components/schemas/QuotedMessage'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        message_body:
          $ref: '#/components/schemas/MessageBody'
        message_type:
          type: string
          nullable: true
        message_content:
          type: string
          nullable: true
        message_status:
          type: string
          nullable: true
        message_timestamp:
          type: integer
          format: int64
          readOnly: true
        message_delivery_type:
          type: string
          nullable: true
      additionalProperties: false
    OnMessageStatusUpdatedEventBody:
      required:
        - channel_id
        - contact
        - contact_id
        - conversation_id
        - created_at
        - message
        - message_id
      type: object
      properties:
        message:
          $ref: '#/components/schemas/OnMessageStatusUpdatedEventBodyMessage'
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        channel:
          type: string
          nullable: true
        channel_id:
          minLength: 1
          type: string
        conversation_id:
          minLength: 1
          type: string
        message_id:
          minLength: 1
          type: string
        message_unique_id:
          type: string
          nullable: true
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_identity_id:
          type: string
          nullable: true
        is_sent_from_sleekflow:
          type: boolean
          readOnly: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnMessageStatusUpdatedEventBodyMessage:
      type: object
      properties:
        quoted_message:
          $ref: '#/components/schemas/QuotedMessage'
        analytic_tags:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        message_body:
          $ref: '#/components/schemas/MessageBody'
        message_type:
          type: string
          nullable: true
        message_content:
          type: string
          nullable: true
        message_status:
          type: string
          nullable: true
        message_timestamp:
          type: integer
          format: int64
          readOnly: true
        message_delivery_type:
          type: string
          nullable: true
      additionalProperties: false
    OnMetaDetectedOutcomeReceivedEventBody:
      required:
        - created_at
        - detected_outcome
      type: object
      properties:
        detected_outcome:
          $ref: '#/components/schemas/OnMetaDetectedOutcomeReceivedEventBodyDetectedOutcome'
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnMetaDetectedOutcomeReceivedEventBodyDetectedOutcome:
      type: object
      properties:
        id:
          type: string
          nullable: true
        event_name:
          type: string
          nullable: true
        timestamp:
          type: integer
          format: int64
        ctwa_clid:
          type: string
          nullable: true
        custom_data:
          $ref: '#/components/schemas/CustomData'
      additionalProperties: false
    OnSalesforceObjectCreatedEventBody:
      required:
        - connection_id
        - created_at
        - entity_type_name
        - is_custom_object
        - object_dict
        - object_type
        - salesforce_connection_id
      type: object
      properties:
        salesforce_connection_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        object_type:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        is_custom_object:
          type: boolean
        object_dict:
          type: object
          additionalProperties:
            nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnSalesforceObjectEnrolledEventBody:
      required:
        - created_at
        - is_custom_object
        - object_dict
        - object_type
        - salesforce_connection_id
        - workflow_id
        - workflow_versioned_id
      type: object
      properties:
        salesforce_connection_id:
          minLength: 1
          type: string
        object_type:
          minLength: 1
          type: string
        is_custom_object:
          type: boolean
        object_dict:
          type: object
          additionalProperties:
            nullable: true
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnSalesforceObjectUpdatedEventBody:
      required:
        - connection_id
        - created_at
        - entity_type_name
        - is_custom_object
        - object_dict
        - object_type
        - salesforce_connection_id
      type: object
      properties:
        salesforce_connection_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        object_type:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        is_custom_object:
          type: boolean
        object_dict:
          type: object
          additionalProperties:
            nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnScheduledWorkflowEnrollmentEventBody:
      required:
        - contact
        - contact_id
        - contact_owner
        - created_at
        - lists
        - workflow_id
        - workflow_versioned_id
      type: object
      properties:
        contact_id:
          minLength: 1
          type: string
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        contact_owner:
          type: object
          additionalProperties:
            nullable: true
        lists:
          type: array
          items:
            $ref: '#/components/schemas/ContactList'
        conversation:
          $ref: '#/components/schemas/ContactConversation'
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnSchemafulObjectCreatedEventBody:
      required:
        - contact_id
        - created_at
        - primary_property_value
        - property_values
        - schema_id
        - schemaful_object_id
        - sleekflow_staff_id
      type: object
      properties:
        schemaful_object_id:
          minLength: 1
          type: string
        schema_id:
          minLength: 1
          type: string
        primary_property_value:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        property_values:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnSchemafulObjectEnrolledEventBody:
      required:
        - contact_id
        - created_at
        - primary_property_value
        - property_values
        - schema_id
        - schemaful_object_id
        - workflow_id
        - workflow_versioned_id
      type: object
      properties:
        schemaful_object_id:
          minLength: 1
          type: string
        schema_id:
          minLength: 1
          type: string
        primary_property_value:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        property_values:
          type: object
          additionalProperties:
            nullable: true
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnSchemafulObjectUpdatedEventBody:
      required:
        - change_entries
        - contact_id
        - created_at
        - post_updated_contact_id
        - post_updated_property_values
        - pre_updated_contact_id
        - pre_updated_property_values
        - primary_property_value
        - property_values
        - schema_id
        - schemaful_object_id
        - sleekflow_staff_id
      type: object
      properties:
        schemaful_object_id:
          minLength: 1
          type: string
        schema_id:
          minLength: 1
          type: string
        primary_property_value:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        pre_updated_contact_id:
          minLength: 1
          type: string
        post_updated_contact_id:
          minLength: 1
          type: string
        property_values:
          type: object
          additionalProperties:
            nullable: true
        pre_updated_property_values:
          type: object
          additionalProperties:
            nullable: true
        post_updated_property_values:
          type: object
          additionalProperties:
            nullable: true
        change_entries:
          type: array
          items:
            $ref: '#/components/schemas/OnSchemafulObjectUpdatedEventBodyChangeEntry'
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnSchemafulObjectUpdatedEventBodyChangeEntry:
      type: object
      properties:
        property_name:
          type: string
          nullable: true
        property_id:
          type: string
          nullable: true
        from_value:
          nullable: true
        to_value:
          nullable: true
      additionalProperties: false
    OnShopifyAbandonedCartCreatedEventBody:
      required:
        - created_at
      type: object
      properties:
        shopify_config_id:
          type: string
          nullable: true
        checkout_id:
          type: string
          nullable: true
        checkout:
          $ref: '#/components/schemas/ShopifyCheckout'
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnShopifyAbandonedCartUpdatedEventBody:
      required:
        - created_at
      type: object
      properties:
        shopify_config_id:
          type: string
          nullable: true
        checkout_id:
          type: string
          nullable: true
        checkout:
          $ref: '#/components/schemas/ShopifyCheckout'
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnShopifyCustomerCreatedEventBody:
      required:
        - created_at
      type: object
      properties:
        shopify_config_id:
          type: string
          nullable: true
        customer_id:
          type: string
          nullable: true
        customer:
          $ref: '#/components/schemas/ShopifyCustomer'
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnShopifyCustomerUpdatedEventBody:
      required:
        - created_at
      type: object
      properties:
        shopify_config_id:
          type: string
          nullable: true
        customer_id:
          type: string
          nullable: true
        customer:
          $ref: '#/components/schemas/ShopifyCustomer'
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnShopifyOrderCreatedEventBody:
      required:
        - created_at
      type: object
      properties:
        shopify_config_id:
          type: string
          nullable: true
        order_id:
          type: string
          nullable: true
        order:
          $ref: '#/components/schemas/ShopifyOrder'
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnShopifyOrderEnrolledEventBody:
      required:
        - created_at
      type: object
      properties:
        shopify_config_id:
          type: string
          nullable: true
        order_id:
          type: string
          nullable: true
        order:
          $ref: '#/components/schemas/ShopifyOrder'
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnShopifyOrderUpdatedEventBody:
      required:
        - created_at
      type: object
      properties:
        shopify_config_id:
          type: string
          nullable: true
        order_id:
          type: string
          nullable: true
        order:
          $ref: '#/components/schemas/ShopifyOrder'
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnTicketCommonEventBody:
      type: object
      properties:
        created_at:
          type: string
          format: date-time
        created_by:
          type: string
          nullable: true
        created_by_email:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        channel:
          $ref: '#/components/schemas/OnTicketCommonEventBodyTicketChannel'
        status_name:
          type: string
          nullable: true
        status_id:
          type: string
          nullable: true
        priority_name:
          type: string
          nullable: true
        priority_id:
          type: string
          nullable: true
        due_date:
          type: string
          format: date-time
          nullable: true
        type_name:
          type: string
          nullable: true
        type_id:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        first_assignee:
          type: string
          nullable: true
        first_assignee_email:
          type: string
          nullable: true
        contact_name:
          type: string
          nullable: true
        contact_phone_number:
          type: string
          nullable: true
        contact_email:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          type: string
          nullable: true
      additionalProperties: false
    OnTicketCommonEventBodyTicketChannel:
      type: object
      properties:
        channel_type:
          type: string
          nullable: true
        channel_identity_id:
          type: string
          nullable: true
        channel_name:
          type: string
          nullable: true
      additionalProperties: false
    OnTicketCreatedEventBody:
      required:
        - contact
        - contact_id
        - created_at
        - ticket
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_identity_id:
          type: string
          nullable: true
        ticket:
          $ref: '#/components/schemas/OnTicketCommonEventBody'
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnTicketUpdatedEventBody:
      required:
        - contact
        - contact_id
        - created_at
        - ticket
        - updated_properties
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        updated_properties:
          type: object
          additionalProperties:
            nullable: true
        updated_property_list:
          type: array
          items:
            type: string
          nullable: true
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_identity_id:
          type: string
          nullable: true
        ticket:
          $ref: '#/components/schemas/OnTicketUpdatedPropertyEventBody'
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnTicketUpdatedPropertyEventBody:
      type: object
      properties:
        current_assignee:
          type: string
          nullable: true
        current_assignee_email:
          type: string
          nullable: true
        first_respondent:
          type: string
          nullable: true
        first_respondent_email:
          type: string
          nullable: true
        first_response_timestamp:
          type: string
          format: date-time
          nullable: true
        resolution_agent:
          type: string
          nullable: true
        resolution_agent_email:
          type: string
          nullable: true
        resolution_timestamp:
          type: string
          format: date-time
          nullable: true
        ticket_reassigned_before_resolution:
          type: boolean
        created_at:
          type: string
          format: date-time
        created_by:
          type: string
          nullable: true
        created_by_email:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        channel:
          $ref: '#/components/schemas/OnTicketCommonEventBodyTicketChannel'
        status_name:
          type: string
          nullable: true
        status_id:
          type: string
          nullable: true
        priority_name:
          type: string
          nullable: true
        priority_id:
          type: string
          nullable: true
        due_date:
          type: string
          format: date-time
          nullable: true
        type_name:
          type: string
          nullable: true
        type_id:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        first_assignee:
          type: string
          nullable: true
        first_assignee_email:
          type: string
          nullable: true
        contact_name:
          type: string
          nullable: true
        contact_phone_number:
          type: string
          nullable: true
        contact_email:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          type: string
          nullable: true
      additionalProperties: false
    OnTikTokAdsLeadReceivedEventBody:
      required:
        - advertiser_id
        - connection_id
        - created_at
        - lead_fields
        - page_id
      type: object
      properties:
        connection_id:
          minLength: 1
          type: string
        advertiser_id:
          minLength: 1
          type: string
        page_id:
          minLength: 1
          type: string
        campaign_id:
          type: string
          nullable: true
        campaign_name:
          type: string
          nullable: true
        ad_group_id:
          type: string
          nullable: true
        ad_group_name:
          type: string
          nullable: true
        ad_id:
          type: string
          nullable: true
        ad_name:
          type: string
          nullable: true
        lead_fields:
          type: object
          additionalProperties:
            type: string
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnVtexOrderCreatedEventBody:
      required:
        - created_at
      type: object
      properties:
        vtex_authentication_id:
          type: string
          nullable: true
        status_code:
          type: string
          nullable: true
        order_id:
          type: string
          nullable: true
        order:
          $ref: '#/components/schemas/VtexOrderOverview'
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnVtexOrderStatusChangedEventBody:
      required:
        - created_at
      type: object
      properties:
        vtex_authentication_id:
          type: string
          nullable: true
        status_code:
          type: string
          nullable: true
        order_id:
          type: string
          nullable: true
        order:
          $ref: '#/components/schemas/VtexOrderOverview'
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnWebhookEventBody:
      required:
        - created_at
        - request_body_str
        - workflow_id
      type: object
      properties:
        request_body_str:
          minLength: 1
          type: string
        workflow_id:
          minLength: 1
          type: string
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnWhatsappFlowSubmissionMessageReceivedEventBody:
      required:
        - channel_id
        - contact
        - contact_id
        - conversation_id
        - created_at
        - is_new_contact
        - message
        - message_id
      type: object
      properties:
        message:
          $ref: '#/components/schemas/OnWhatsappFlowSubmissionMessageReceivedEventBodyMessage'
        channel:
          type: string
          nullable: true
        channel_id:
          minLength: 1
          type: string
        conversation_id:
          minLength: 1
          type: string
        message_id:
          minLength: 1
          type: string
        message_unique_id:
          type: string
          nullable: true
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        is_new_contact:
          type: boolean
        is_sent_from_sleekflow:
          type: boolean
          readOnly: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnWhatsappFlowSubmissionMessageReceivedEventBodyMessage:
      type: object
      properties:
        id:
          type: string
          nullable: true
        whatsapp_flow_id:
          type: string
          nullable: true
        whatsapp_template_id:
          type: string
          nullable: true
        whatsapp_flow_submission_data:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        message_body:
          $ref: '#/components/schemas/MessageBody'
        message_type:
          type: string
          nullable: true
        message_content:
          type: string
          nullable: true
        message_status:
          type: string
          nullable: true
        message_timestamp:
          type: integer
          format: int64
          readOnly: true
        message_delivery_type:
          type: string
          nullable: true
      additionalProperties: false
    OnZohoObjectCreatedEventBody:
      required:
        - connection_id
        - created_at
        - entity_type_name
        - object_dict
      type: object
      properties:
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        object_dict:
          type: object
          additionalProperties:
            nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnZohoObjectEnrolledEventBody:
      required:
        - created_at
        - object_dict
        - object_type
        - workflow_id
        - workflow_versioned_id
        - zoho_connection_id
      type: object
      properties:
        zoho_connection_id:
          minLength: 1
          type: string
        object_type:
          minLength: 1
          type: string
        object_dict:
          type: object
          additionalProperties:
            nullable: true
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnZohoObjectUpdatedEventBody:
      required:
        - connection_id
        - created_at
        - entity_type_name
        - object_dict
      type: object
      properties:
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        object_dict:
          type: object
          additionalProperties:
            nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OrderMessageObject:
      type: object
      properties:
        catalog_id:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
        product_items:
          type: array
          items:
            $ref: '#/components/schemas/OrderMessageProductItem'
          nullable: true
        product_items_json:
          type: string
          nullable: true
      additionalProperties: false
    OrderMessageProductItem:
      type: object
      properties:
        product_retailer_id:
          type: string
          nullable: true
        quantity:
          type: integer
          format: int32
          nullable: true
        item_price:
          type: number
          format: decimal
          nullable: true
        currency:
          type: string
          nullable: true
      additionalProperties: false
    QuotedMessage:
      type: object
      properties:
        message_id:
          type: string
          nullable: true
        message_unique_id:
          type: string
          nullable: true
        message_content:
          type: string
          nullable: true
      additionalProperties: false
    ReactionMessageObject:
      type: object
      properties:
        message_id:
          type: string
          nullable: true
        emoji:
          type: string
          nullable: true
      additionalProperties: false
    ScaleWorkflowExecutionLimitInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    ScaleWorkflowExecutionLimitOutput:
      type: object
      properties:
        is_success:
          type: boolean
      additionalProperties: false
    SendMessageInput:
      required:
        - channel
        - from_to
        - message_body
        - message_type
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: '#/components/schemas/StateIdentity'
        channel:
          minLength: 1
          type: string
        from_to:
          oneOf:
            - $ref: '#/components/schemas/WhatsappCloudApiSendMessageInputFromTo'
            - $ref: '#/components/schemas/FacebookPageMessengerSendMessageInputFromTo'
            - $ref: '#/components/schemas/InstagramPageMessengerSendMessageInputFromTo'
        message_type:
          minLength: 1
          type: string
        message_body:
          $ref: '#/components/schemas/MessageBody'
        delivery_type:
          type: integer
          format: int32
          nullable: true
        staff_id:
          type: string
          nullable: true
      additionalProperties: false
    SendMessageInputFromTo:
      required:
        - from_to_type
      type: object
      properties:
        from_to_type:
          type: string
          nullable: true
      additionalProperties: false
      discriminator:
        propertyName: from_to_type
        mapping:
          WhatsappCloudApiSendMessageInputFromTo: '#/components/schemas/WhatsappCloudApiSendMessageInputFromTo'
          FacebookPageMessengerSendMessageInputFromTo: '#/components/schemas/FacebookPageMessengerSendMessageInputFromTo'
          InstagramPageMessengerSendMessageInputFromTo: '#/components/schemas/InstagramPageMessengerSendMessageInputFromTo'
    ShopifyAddress:
      type: object
      properties:
        id:
          type: string
          nullable: true
        customer_id:
          type: string
          nullable: true
        address1:
          type: string
          nullable: true
        address2:
          type: string
          nullable: true
        city:
          type: string
          nullable: true
        company:
          type: string
          nullable: true
        country:
          type: string
          nullable: true
        country_code:
          type: string
          nullable: true
        country_name:
          type: string
          nullable: true
        default:
          type: boolean
          nullable: true
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        latitude:
          type: number
          format: decimal
          nullable: true
        longitude:
          type: number
          format: decimal
          nullable: true
        name:
          type: string
          nullable: true
        phone:
          type: string
          nullable: true
        province:
          type: string
          nullable: true
        province_code:
          type: string
          nullable: true
        zip:
          type: string
          nullable: true
      additionalProperties: false
    ShopifyCheckout:
      type: object
      properties:
        id:
          type: string
          nullable: true
        normalized_line_items:
          type: string
          nullable: true
        token:
          type: string
          nullable: true
        cart_token:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        gateway:
          type: string
          nullable: true
        buyer_accepts_marketing:
          type: boolean
          nullable: true
        created_at:
          type: string
          format: date-time
          nullable: true
        updated_at:
          type: string
          format: date-time
          nullable: true
        landing_site:
          type: string
          nullable: true
        note:
          type: string
          nullable: true
        note_attributes:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyNoteAttribute'
          nullable: true
        referring_site:
          type: string
          nullable: true
        shipping_lines:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyShippingLine'
          nullable: true
        taxes_included:
          type: boolean
          nullable: true
        total_weight:
          type: integer
          format: int64
          nullable: true
        currency:
          type: string
          nullable: true
        completed_at:
          type: string
          format: date-time
          nullable: true
        closed_at:
          type: string
          format: date-time
          nullable: true
        user_id:
          type: string
          nullable: true
        location_id:
          type: string
          nullable: true
        source_identifier:
          type: string
          nullable: true
        source_url:
          type: string
          nullable: true
        device_id:
          type: string
          nullable: true
        phone:
          type: string
          nullable: true
        customer_locale:
          type: string
          nullable: true
        line_items:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyLineItem'
          nullable: true
        name:
          type: string
          nullable: true
        source:
          type: string
          nullable: true
        abandoned_checkout_url:
          type: string
          nullable: true
        discount_codes:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyDiscountCode'
          nullable: true
        source_name:
          type: string
          nullable: true
        presentment_currency:
          type: string
          nullable: true
        buyer_accepts_sms_marketing:
          type: boolean
          nullable: true
        sms_marketing_phone:
          type: string
          nullable: true
        total_discounts:
          type: string
          nullable: true
        total_line_items_price:
          type: string
          nullable: true
        total_price:
          type: string
          nullable: true
        total_tax:
          type: string
          nullable: true
        subtotal_price:
          type: string
          nullable: true
        total_duties:
          type: string
          nullable: true
        shipping_address:
          $ref: '#/components/schemas/ShopifyAddress'
        customer:
          $ref: '#/components/schemas/ShopifyCustomer'
        billing_address:
          $ref: '#/components/schemas/ShopifyAddress'
      additionalProperties: false
    ShopifyClientDetails:
      type: object
      properties:
        accept_language:
          type: string
          nullable: true
        browser_height:
          type: integer
          format: int64
          nullable: true
        browser_ip:
          type: string
          nullable: true
        browser_width:
          type: integer
          format: int64
          nullable: true
        session_hash:
          type: string
          nullable: true
        user_agent:
          type: string
          nullable: true
      additionalProperties: false
    ShopifyCompany:
      type: object
      properties:
        id:
          type: string
          nullable: true
        location_id:
          type: string
          nullable: true
      additionalProperties: false
    ShopifyCustomer:
      type: object
      properties:
        id:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        phone:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
          nullable: true
        updated_at:
          type: string
          format: date-time
          nullable: true
        last_order_date:
          type: string
          format: date-time
          nullable: true
        last_order_id:
          type: string
          nullable: true
        last_order_name:
          type: string
          nullable: true
        total_spent:
          type: number
          format: decimal
          nullable: true
        orders_count:
          type: integer
          format: int64
          nullable: true
        state:
          type: string
          nullable: true
        note:
          type: string
          nullable: true
        verified_email:
          type: boolean
          nullable: true
        multipass_identifier:
          type: string
          nullable: true
        accepts_marketing:
          type: boolean
          nullable: true
        accepts_marketing_updated_at:
          type: string
          format: date-time
          nullable: true
        marketing_opt_in_level:
          type: string
          nullable: true
        tax_exempt:
          type: boolean
          nullable: true
        tax_exemptions:
          type: array
          items:
            type: string
          nullable: true
        tags:
          type: string
          nullable: true
        currency:
          type: string
          nullable: true
        addresses:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyAddress'
          nullable: true
        default_address:
          $ref: '#/components/schemas/ShopifyAddress'
        sms_marketing_consent:
          $ref: '#/components/schemas/ShopifySmsMarketingConsent'
        email_marketing_consent:
          $ref: '#/components/schemas/ShopifyEmailMarketingConsent'
        admin_graphql_api_id:
          type: string
          nullable: true
        metafields:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyMetafield'
          nullable: true
      additionalProperties: false
    ShopifyDiscountAllocation:
      type: object
      properties:
        amount:
          type: number
          format: decimal
          nullable: true
        discount_application_index:
          type: integer
          format: int64
          nullable: true
        amount_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
      additionalProperties: false
    ShopifyDiscountApplication:
      type: object
      properties:
        type:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        value:
          type: number
          format: decimal
          nullable: true
        value_type:
          type: string
          nullable: true
        allocation_method:
          type: string
          nullable: true
        target_selection:
          type: string
          nullable: true
        target_type:
          type: string
          nullable: true
        code:
          type: string
          nullable: true
      additionalProperties: false
    ShopifyDiscountCode:
      type: object
      properties:
        code:
          type: string
          nullable: true
        amount:
          type: number
          format: decimal
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    ShopifyDuty:
      type: object
      properties:
        id:
          type: string
          nullable: true
        harmonized_system_code:
          type: string
          nullable: true
        country_code_of_origin:
          type: string
          nullable: true
        shop_money:
          $ref: '#/components/schemas/ShopifyMoney'
        presentment_money:
          $ref: '#/components/schemas/ShopifyMoney'
        tax_lines:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyTaxLine'
          nullable: true
        admin_graphql_api_id:
          type: string
          nullable: true
      additionalProperties: false
    ShopifyEmailMarketingConsent:
      type: object
      properties:
        state:
          type: string
          nullable: true
        opt_in_level:
          type: string
          nullable: true
        consent_updated_at:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    ShopifyFulfillment:
      type: object
      properties:
        id:
          type: string
          nullable: true
        order_id:
          type: string
          nullable: true
        status:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
          nullable: true
        service:
          type: string
          nullable: true
        updated_at:
          type: string
          format: date-time
          nullable: true
        tracking_company:
          type: string
          nullable: true
        shipment_status:
          type: string
          nullable: true
        location_id:
          type: string
          nullable: true
        line_items:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyLineItem'
          nullable: true
        tracking_number:
          type: string
          nullable: true
        tracking_numbers:
          type: array
          items:
            type: string
          nullable: true
        tracking_url:
          type: string
          nullable: true
        tracking_urls:
          type: array
          items:
            type: string
          nullable: true
        receipt:
          $ref: '#/components/schemas/ShopifyReceipt'
        name:
          type: string
          nullable: true
      additionalProperties: false
    ShopifyLineItem:
      type: object
      properties:
        id:
          type: string
          nullable: true
        variant_id:
          type: string
          nullable: true
        product_id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        variant_title:
          type: string
          nullable: true
        sku:
          type: string
          nullable: true
        vendor:
          type: string
          nullable: true
        quantity:
          type: integer
          format: int64
          nullable: true
        price:
          type: number
          format: decimal
          nullable: true
        grams:
          type: number
          format: decimal
          nullable: true
        requires_shipping:
          type: boolean
          nullable: true
        taxable:
          type: boolean
          nullable: true
        fulfillment_service:
          type: string
          nullable: true
        fulfillment_status:
          type: string
          nullable: true
        gift_card:
          type: boolean
          nullable: true
        name:
          type: string
          nullable: true
        variant_inventory_management:
          type: string
          nullable: true
        properties:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyLineItemProperty'
          nullable: true
        product_exists:
          type: boolean
          nullable: true
        total_discount:
          type: number
          format: decimal
          nullable: true
        tax_lines:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyTaxLine'
          nullable: true
        discount_allocations:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyDiscountAllocation'
          nullable: true
        duties:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyDuty'
          nullable: true
        origin_location:
          $ref: '#/components/schemas/ShopifyOriginLocation'
        price_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
        total_discount_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
      additionalProperties: false
    ShopifyLineItemProperty:
      type: object
      properties:
        name:
          type: string
          nullable: true
        value:
          type: string
          nullable: true
      additionalProperties: false
    ShopifyMetafield:
      type: object
      properties:
        id:
          type: string
          nullable: true
        namespace:
          type: string
          nullable: true
        key:
          type: string
          nullable: true
        value:
          type: string
          nullable: true
        value_type:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        owner_id:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
          nullable: true
        updated_at:
          type: string
          format: date-time
          nullable: true
        owner_resource:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        admin_graphql_api_id:
          type: string
          nullable: true
      additionalProperties: false
    ShopifyMoney:
      type: object
      properties:
        amount:
          type: number
          format: decimal
          nullable: true
        currency_code:
          type: string
          nullable: true
      additionalProperties: false
    ShopifyNoteAttribute:
      type: object
      properties:
        name:
          type: string
          nullable: true
        value:
          type: string
          nullable: true
      additionalProperties: false
    ShopifyOrder:
      type: object
      properties:
        id:
          type: string
          nullable: true
        normalized_line_items:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
          nullable: true
        updated_at:
          type: string
          format: date-time
          nullable: true
        processed_at:
          type: string
          format: date-time
          nullable: true
        cancelled_at:
          type: string
          format: date-time
          nullable: true
        closed_at:
          type: string
          format: date-time
          nullable: true
        name:
          type: string
          nullable: true
        number:
          type: integer
          format: int64
          nullable: true
        order_number:
          type: integer
          format: int64
          nullable: true
        email:
          type: string
          nullable: true
        phone:
          type: string
          nullable: true
        financial_status:
          type: string
          nullable: true
        fulfillment_status:
          type: string
          nullable: true
        currency:
          type: string
          nullable: true
        current_total_price:
          type: number
          format: decimal
          nullable: true
        current_subtotal_price:
          type: number
          format: decimal
          nullable: true
        current_total_tax:
          type: number
          format: decimal
          nullable: true
        current_total_discounts:
          type: number
          format: decimal
          nullable: true
        current_total_duties:
          type: number
          format: decimal
          nullable: true
        total_price:
          type: number
          format: decimal
          nullable: true
        subtotal_price:
          type: number
          format: decimal
          nullable: true
        total_tax:
          type: number
          format: decimal
          nullable: true
        total_discounts:
          type: number
          format: decimal
          nullable: true
        total_weight:
          type: number
          format: decimal
          nullable: true
        total_line_items_price:
          type: number
          format: decimal
          nullable: true
        total_shipping_price:
          type: number
          format: decimal
          nullable: true
        total_tip_received:
          type: number
          format: decimal
          nullable: true
        total_outstanding:
          type: number
          format: decimal
          nullable: true
        customer_locale:
          type: string
          nullable: true
        note:
          type: string
          nullable: true
        cart_token:
          type: string
          nullable: true
        checkout_token:
          type: string
          nullable: true
        checkout_id:
          type: string
          nullable: true
        token:
          type: string
          nullable: true
        tags:
          type: string
          nullable: true
        source_name:
          type: string
          nullable: true
        source_identifier:
          type: string
          nullable: true
        device_id:
          type: string
          nullable: true
        browser_ip:
          type: string
          nullable: true
        landing_site:
          type: string
          nullable: true
        referring_site:
          type: string
          nullable: true
        location_id:
          type: string
          nullable: true
        user_id:
          type: string
          nullable: true
        app_id:
          type: string
          nullable: true
        test:
          type: boolean
          nullable: true
        buyer_accepts_marketing:
          type: boolean
          nullable: true
        taxes_included:
          type: boolean
          nullable: true
        confirmed:
          type: boolean
          nullable: true
        cancel_reason:
          type: string
          nullable: true
        presentment_currency:
          type: string
          nullable: true
        customer:
          $ref: '#/components/schemas/ShopifyCustomer'
        billing_address:
          $ref: '#/components/schemas/ShopifyAddress'
        shipping_address:
          $ref: '#/components/schemas/ShopifyAddress'
        line_items:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyLineItem'
          nullable: true
        shipping_lines:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyShippingLine'
          nullable: true
        tax_lines:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyTaxLine'
          nullable: true
        discount_codes:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyDiscountCode'
          nullable: true
        discount_applications:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyDiscountApplication'
          nullable: true
        fulfillments:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyFulfillment'
          nullable: true
        refunds:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyRefund'
          nullable: true
        note_attributes:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyNoteAttribute'
          nullable: true
        payment_gateway_names:
          type: array
          items:
            type: string
          nullable: true
        client_details:
          $ref: '#/components/schemas/ShopifyClientDetails'
        company:
          $ref: '#/components/schemas/ShopifyCompany'
        estimated_taxes:
          type: boolean
          nullable: true
        order_status_url:
          type: string
          nullable: true
        po_number:
          type: string
          nullable: true
        current_subtotal_price_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
        current_total_discounts_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
        current_total_duties_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
        current_total_price_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
        current_total_tax_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
        subtotal_price_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
        total_discounts_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
        total_line_items_price_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
        total_price_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
        total_shipping_price_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
        total_tax_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
        payment_terms:
          $ref: '#/components/schemas/ShopifyPaymentTerms'
        tax_exempt:
          type: boolean
          nullable: true
      additionalProperties: false
    ShopifyOrderAdjustment:
      type: object
      properties:
        id:
          type: string
          nullable: true
        order_id:
          type: string
          nullable: true
        refund_id:
          type: string
          nullable: true
        amount:
          type: number
          format: decimal
          nullable: true
        tax_amount:
          type: number
          format: decimal
          nullable: true
        kind:
          type: string
          nullable: true
        reason:
          type: string
          nullable: true
        amount_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
        tax_amount_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
      additionalProperties: false
    ShopifyOriginLocation:
      type: object
      properties:
        id:
          type: string
          nullable: true
        country_code:
          type: string
          nullable: true
        province_code:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        address1:
          type: string
          nullable: true
        address2:
          type: string
          nullable: true
        city:
          type: string
          nullable: true
        zip:
          type: string
          nullable: true
      additionalProperties: false
    ShopifyPaymentSchedule:
      type: object
      properties:
        amount:
          type: number
          format: decimal
          nullable: true
        currency:
          type: string
          nullable: true
        issued_at:
          type: string
          format: date-time
          nullable: true
        due_at:
          type: string
          format: date-time
          nullable: true
        completed_at:
          type: string
          format: date-time
          nullable: true
        expected_payment_method:
          type: string
          nullable: true
      additionalProperties: false
    ShopifyPaymentTerms:
      type: object
      properties:
        amount:
          type: number
          format: decimal
          nullable: true
        currency:
          type: string
          nullable: true
        due_in_days:
          type: integer
          format: int32
          nullable: true
        payment_terms_name:
          type: string
          nullable: true
        payment_terms_type:
          type: string
          nullable: true
        payment_schedules:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyPaymentSchedule'
          nullable: true
      additionalProperties: false
    ShopifyPriceSet:
      type: object
      properties:
        shop_money:
          $ref: '#/components/schemas/ShopifyMoney'
        presentment_money:
          $ref: '#/components/schemas/ShopifyMoney'
      additionalProperties: false
    ShopifyReceipt:
      type: object
      properties:
        testcase:
          type: boolean
          nullable: true
        authorization:
          type: string
          nullable: true
      additionalProperties: false
    ShopifyRefund:
      type: object
      properties:
        id:
          type: string
          nullable: true
        order_id:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
          nullable: true
        note:
          type: string
          nullable: true
        user_id:
          type: string
          nullable: true
        processed_at:
          type: string
          format: date-time
          nullable: true
        restock:
          type: boolean
          nullable: true
        duties:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyDuty'
          nullable: true
        refund_line_items:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyRefundLineItem'
          nullable: true
        transactions:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyTransaction'
          nullable: true
        order_adjustments:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyOrderAdjustment'
          nullable: true
      additionalProperties: false
    ShopifyRefundLineItem:
      type: object
      properties:
        id:
          type: string
          nullable: true
        quantity:
          type: integer
          format: int64
          nullable: true
        line_item_id:
          type: string
          nullable: true
        location_id:
          type: string
          nullable: true
        restock_type:
          type: string
          nullable: true
        subtotal:
          type: number
          format: decimal
          nullable: true
        total_tax:
          type: number
          format: decimal
          nullable: true
        subtotal_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
        total_tax_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
        line_item:
          $ref: '#/components/schemas/ShopifyLineItem'
      additionalProperties: false
    ShopifyShippingLine:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        price:
          type: number
          format: decimal
          nullable: true
        code:
          type: string
          nullable: true
        source:
          type: string
          nullable: true
        carrier_identifier:
          type: string
          nullable: true
        requested_fulfillment_service_id:
          type: string
          nullable: true
        price_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
        discounted_price:
          type: number
          format: decimal
          nullable: true
        discounted_price_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
        tax_lines:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyTaxLine'
          nullable: true
      additionalProperties: false
    ShopifySmsMarketingConsent:
      type: object
      properties:
        state:
          type: string
          nullable: true
        opt_in_level:
          type: string
          nullable: true
        consent_updated_at:
          type: string
          format: date-time
          nullable: true
        consent_collected_from:
          type: string
          nullable: true
      additionalProperties: false
    ShopifyTaxLine:
      type: object
      properties:
        title:
          type: string
          nullable: true
        price:
          type: number
          format: decimal
          nullable: true
        rate:
          type: number
          format: decimal
          nullable: true
        price_set:
          $ref: '#/components/schemas/ShopifyPriceSet'
        channel_liable:
          type: boolean
          nullable: true
      additionalProperties: false
    ShopifyTransaction:
      type: object
      properties:
        id:
          type: string
          nullable: true
        order_id:
          type: string
          nullable: true
        kind:
          type: string
          nullable: true
        gateway:
          type: string
          nullable: true
        status:
          type: string
          nullable: true
        message:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
          nullable: true
        test:
          type: boolean
          nullable: true
        authorization:
          type: string
          nullable: true
        location_id:
          type: string
          nullable: true
        user_id:
          type: string
          nullable: true
        parent_id:
          type: string
          nullable: true
        processed_at:
          type: string
          format: date-time
          nullable: true
        device_id:
          type: string
          nullable: true
        error_code:
          type: string
          nullable: true
        source_name:
          type: string
          nullable: true
        amount:
          type: number
          format: decimal
          nullable: true
        currency:
          type: string
          nullable: true
        admin_graphql_api_id:
          type: string
          nullable: true
      additionalProperties: false
    SmsMessageObject:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    StateIdentity:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        object_id:
          type: string
          nullable: true
        workflow_id:
          type: string
          nullable: true
        workflow_versioned_id:
          type: string
          nullable: true
        object_type:
          type: string
          nullable: true
      additionalProperties: false
    TelegramMessengerMessageObject:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    TemplateMessageObject:
      type: object
      properties:
        template_name:
          type: string
          nullable: true
        language:
          type: string
          nullable: true
        components:
          type: array
          items:
            $ref: '#/components/schemas/TemplateMessageObjectComponent'
          nullable: true
      additionalProperties: false
    TemplateMessageObjectComponent:
      type: object
      properties:
        type:
          type: string
          nullable: true
        sub_type:
          type: string
          nullable: true
        index:
          type: integer
          format: int32
        parameters:
          type: array
          items:
            $ref: '#/components/schemas/TemplateMessageObjectComponentParameter'
          nullable: true
      additionalProperties: false
    TemplateMessageObjectComponentParameter:
      type: object
      properties:
        type:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
        payload:
          type: string
          nullable: true
        image:
          $ref: '#/components/schemas/ImageMessageObject'
        audio:
          $ref: '#/components/schemas/AudioMessageObject'
        document:
          $ref: '#/components/schemas/DocumentMessageObject'
        video:
          $ref: '#/components/schemas/VideoMessageObject'
        location:
          $ref: '#/components/schemas/LocationMessageObject'
        currency:
          $ref: '#/components/schemas/CurrencyMessageObject'
        date_time:
          $ref: '#/components/schemas/DateTimeMessageObject'
        action:
          $ref: '#/components/schemas/ActionMessageObject'
      additionalProperties: false
    TextMessageObject:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    ViberMessageObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
      additionalProperties: false
    VideoMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        caption:
          type: string
          nullable: true
        filename:
          type: string
          nullable: true
        provider:
          $ref: '#/components/schemas/MediaMessageObjectProvider'
      additionalProperties: false
    VtexClientProfileOverview:
      type: object
      properties:
        email:
          type: string
          nullable: true
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        phone:
          type: string
          nullable: true
      additionalProperties: false
    VtexOrderItemOverview:
      type: object
      properties:
        id:
          type: string
          nullable: true
        product_id:
          type: string
          nullable: true
        ref_id:
          type: string
          nullable: true
        sku_ame:
          type: string
          nullable: true
        image_url:
          type: string
          nullable: true
        quantity:
          type: integer
          format: int32
        price:
          type: number
          format: double
      additionalProperties: false
    VtexOrderOverview:
      type: object
      properties:
        order_id:
          type: string
          nullable: true
        status:
          type: string
          nullable: true
        status_code:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
          nullable: true
        is_completed:
          type: boolean
        is_checked_in:
          type: boolean
        authorized_date:
          type: string
          format: date-time
          nullable: true
        invoiced_date:
          type: string
          format: date-time
          nullable: true
        cancel_reason:
          type: string
          nullable: true
        total_value:
          type: number
          format: double
        total_items:
          type: number
          format: double
        total_discount:
          type: number
          format: double
        total_tax:
          type: number
          format: double
        total_shipping:
          type: number
          format: double
        order_items:
          type: array
          items:
            $ref: '#/components/schemas/VtexOrderItemOverview'
          nullable: true
        normalized_order_itmes:
          type: string
          nullable: true
        store_preferences_data:
          $ref: '#/components/schemas/VtexStorePreferencesOverview'
        shipping_data:
          $ref: '#/components/schemas/VtexShippingOverview'
        client_profile_data:
          $ref: '#/components/schemas/VtexClientProfileOverview'
      additionalProperties: false
    VtexShippingOverview:
      type: object
      properties:
        receiver_name:
          type: string
          nullable: true
        postal_code:
          type: string
          nullable: true
        city:
          type: string
          nullable: true
        state:
          type: string
          nullable: true
        country:
          type: string
          nullable: true
        street:
          type: string
          nullable: true
      additionalProperties: false
    VtexStorePreferencesOverview:
      type: object
      properties:
        country_code:
          type: string
          nullable: true
        currency_code:
          type: string
          nullable: true
        currency_symbol:
          type: string
          nullable: true
        time_zone:
          type: string
          nullable: true
      additionalProperties: false
    WeChatMessengerMessageObject:
      type: object
      properties:
        msgtype:
          type: string
          nullable: true
        text:
          $ref: '#/components/schemas/WeChatTextMessage'
      additionalProperties: false
    WeChatTextMessage:
      type: object
      properties:
        content:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiSendMessageInputFromTo:
      allOf:
        - $ref: '#/components/schemas/SendMessageInputFromTo'
        - type: object
          properties:
            from_phone_number:
              type: string
              nullable: true
            to_phone_number:
              type: string
              nullable: true
            to_contact_id:
              type: string
              nullable: true
            from_channel_id:
              type: string
              nullable: true
          additionalProperties: false
    ZapierSubscribeData:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false