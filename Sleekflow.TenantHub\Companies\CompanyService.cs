using Microsoft.AspNetCore.Mvc;
using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.TenantHub;
using Sleekflow.Ids;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;
using Sleekflow.Persistence;
using Sleekflow.TenantHub.Auth0;
using Sleekflow.TenantHub.Authorizations;
using Sleekflow.TenantHub.Client;
using Sleekflow.TenantHub.Models.Companies;
using Sleekflow.TenantHub.Models.Companies.Analytics;
using Sleekflow.TenantHub.Models.Constants;
using Sleekflow.TenantHub.Models.TravisBackend;
using Sleekflow.TenantHub.Models.Users;
using Sleekflow.TenantHub.Rbac;
using Sleekflow.TenantHub.Roles;
using Sleekflow.TenantHub.Users;

namespace Sleekflow.TenantHub.Companies;

public interface ICompanyService
{
    Task<Company> CreateAndGetAsync(
        AnalyticsMetadata analyticsMetadata,
        string serverLocation,
        string companyLocation,
        string name,
        string ownerId,
        List<string> platforms,
        string sleekflowStaffId,
        List<string>? sleekflowTeamIds,
        Dictionary<string, object?> metadata);

    Task<Company> CreateAndGetAsync(
        string sleekflowCompanyId,
        AnalyticsMetadata analyticsMetadata,
        string serverLocation,
        string companyLocation,
        string name,
        string ownerId,
        List<string> platforms,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        Dictionary<string, object?> metadata,
        AuditEntity.SleekflowStaff? createdBy = null,
        AuditEntity.SleekflowStaff? updatedBy = null);

    Task<Company> GetAsync(string id);

    Task<Company?> GetOrDefaultAsync(string id);

    Task<List<Company>> GetObjectsAsync(string platform);

    Task<(List<Company> Companies, string? ContinuationToken)> GetAllCompanies(
        string? serverLocation,
        string? continuationToken,
        int limit);

    Task<bool> IsCompanyCreatedAsync(string sleekflowCompanyId);

    Task DeleteCompanyStaffAsync(
        string staffId,
        string userId,
        string companyId);
}

public class CompanyService : ICompanyService, IScopedService
{
    private readonly IIdService _idService;
    private readonly ICompanyRepository _companyRepository;
    private readonly ITravisBackendClient _travisBackendClient;
    private readonly IUserService _userService;
    private readonly ILogger<CompanyService> _logger;
    private readonly IAuth0UserManagementService _auth0UserManagementService;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;

    public CompanyService(
        IIdService idService,
        ICompanyRepository companyRepository,
        ITravisBackendClient travisBackendClient,
        IUserService userService,
        ILogger<CompanyService> logger,
        IAuth0UserManagementService auth0UserManagementService,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer)
    {
        _idService = idService;
        _companyRepository = companyRepository;
        _travisBackendClient = travisBackendClient;
        _userService = userService;
        _logger = logger;
        _auth0UserManagementService = auth0UserManagementService;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
    }

    public async Task<Company> CreateAndGetAsync(
        AnalyticsMetadata analyticsMetadata,
        string serverLocation,
        string companyLocation,
        string name,
        string ownerId,
        List<string> platforms,
        string sleekflowStaffId,
        List<string>? sleekflowTeamIds,
        Dictionary<string, object?> metadata)
    {
        var id = _idService.GetId(SysTypeNames.Company);

        if (platforms.Contains(PlatformTypes.Headless))
        {
            id = $"HEADLESS_{id}";
        }

        var dateTimeOffset = DateTimeOffset.UtcNow;

        return await CreateAndGetAsync(
            id,
            analyticsMetadata,
            serverLocation,
            companyLocation,
            name,
            ownerId,
            platforms,
            dateTimeOffset,
            dateTimeOffset,
            metadata,
            new AuditEntity.SleekflowStaff(sleekflowStaffId, sleekflowTeamIds));
    }

    public async Task<Company> CreateAndGetAsync(
        string sleekflowCompanyId,
        AnalyticsMetadata analyticsMetadata,
        string serverLocation,
        string companyLocation,
        string name,
        string ownerId,
        List<string> platforms,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        Dictionary<string, object?> metadata,
        AuditEntity.SleekflowStaff? createdBy = null,
        AuditEntity.SleekflowStaff? updatedBy = null)
    {
        return await _companyRepository.CreateAndGetAsync(
            new Company(
                sleekflowCompanyId,
                analyticsMetadata,
                serverLocation,
                companyLocation,
                name,
                ownerId,
                platforms,
                metadata,
                new List<string>
                {
                    RecordStatuses.Active
                },
                createdAt,
                updatedAt,
                createdBy,
                updatedBy),
            sleekflowCompanyId);
    }

    public async Task<Company> GetAsync(string id)
    {
        return await _companyRepository.GetAsync(id, id);
    }

    public async Task<Company?> GetOrDefaultAsync(string id)
    {
        return await _companyRepository.GetOrDefaultAsync(id, id);
    }

    public async Task<List<Company>> GetObjectsAsync(string platform)
    {
        return await _companyRepository.GetObjectsAsync(c => c.Platforms.Contains(platform));
    }

    public async Task<(List<Company> Companies, string? ContinuationToken)> GetAllCompanies(
        string? serverLocation,
        string? continuationToken,
        int limit)
    {
        if (!string.IsNullOrWhiteSpace(serverLocation))
        {
            return await _companyRepository.GetContinuationTokenizedObjectsAsync(
                c => c.ServerLocation == serverLocation,
                continuationToken,
                limit);
        }

        return await _companyRepository.GetContinuationTokenizedObjectsAsync(
            c => true,
            continuationToken,
            limit);
    }

    public async Task<bool> IsCompanyCreatedAsync(string sleekflowCompanyId)
    {
        return (await _companyRepository.GetObjectsAsync(c => c.Id == sleekflowCompanyId)).Any();
    }

    public async Task DeleteCompanyStaffAsync(string staffId, string userId, string companyId)
    {
        try
        {
            // Retrieve the TenantHub user based on the Sleekflow user ID
            var tenantHubUser = await _userService.GetUserBySleekflowUserIdAsync(userId);

            // If the user doesn't exist in TenantHub, delete the non-migrated user
            if (tenantHubUser is null)
            {
                // Delete a non-migrated user from the Travis backend and Auth0
                await DeleteNonMigratedUserAsync(companyId, staffId, userId);
                return;
            }

            // If the user has no workspaces, delete the user from Auth0
            if (!tenantHubUser.UserWorkspaces.Any())
            {
                // Delete user from tenantHub
                await _userService.DeleteAsync(userId);

                // Delete a non-migrated user from the Travis backend and Auth0
                await DeleteNonMigratedUserAsync(companyId, staffId, userId);
                return;
            }

            // Get the specific user workspace for the given company and staff
            var userWorkspace = GetUserWorkspace(tenantHubUser, companyId, staffId);

            try
            {
                // Process the deletion of the user workspace
                await ProcessUserWorkspaceDeletion(tenantHubUser, userWorkspace);
            }
            catch (Exception e)
            {
                var errorMessage = $"Error when deleting user workspace({userWorkspace.SleekflowCompanyId}, {userWorkspace.SleekflowUserId}): {e.Message}";
                _logger.LogError(e, errorMessage);

                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.TenantHubCompanyStaffDeleteFailed,
                    new Dictionary<string, string>
                    {
                        { "company_id", companyId },
                        { "staff_id", staffId },
                        { "user_id", userId },
                        { "error", e.Message },
                        { "error_type", e.GetType().Name }
                    });

                throw new SfInternalErrorException(e, errorMessage);
            }

            // After deletion, check if the user has any remaining workspaces
            // and handle accordingly (e.g., delete the user if no workspaces left)
            await HandleUserWithNoWorkspaces(tenantHubUser);

            // Track successful deletion in telemetry
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.TenantHubCompanyStaffDeleteCompleted,
                new Dictionary<string, string>
                {
                    { "company_id", companyId },
                    { "staff_id", staffId },
                    { "user_id", userId }
                });
        }
        catch (Exception ex) when (!(ex is SfInternalErrorException))
        {
            _logger.LogError(ex, "Error deleting company staff: {Message}", ex.Message);

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.TenantHubCompanyStaffDeleteFailed,
                new Dictionary<string, string>
                {
                    { "company_id", companyId },
                    { "staff_id", staffId },
                    { "user_id", userId },
                    { "error", ex.Message },
                    { "error_type", ex.GetType().Name }
                });

            throw new SfInternalErrorException(ex, $"Error deleting company staff. CompanyId: {companyId}, StaffId: {staffId}, UserId: {userId}");
        }
    }

    // Retrieves the specific UserWorkspace for a given user, company, and staff
    private UserWorkspace GetUserWorkspace(User user, string companyId, string staffId)
    {
        var userWorkspace = user.UserWorkspaces.Find(
            uw => uw.SleekflowCompanyId == companyId && uw.SleekflowStaffId == staffId);

        if (userWorkspace is null)
        {
            // Log the error and throw an exception
            var errorMessage = $"[{nameof(GetUserWorkspace)}] User workspace not found.";
            _logger.LogError(errorMessage);
            throw new SfInternalErrorException(errorMessage);
        }

        return userWorkspace;
    }

    // Processes the deletion of a user's workspace
    private async Task ProcessUserWorkspaceDeletion(User user, UserWorkspace workspace)
    {
        // Retrieve the company associated with the workspace
        var company = await GetOrDefaultAsync(workspace.SleekflowCompanyId);

        if (company is null)
        {
            // If company not found, delete staff from Travis backend and throw an exception
            await DeleteStaffFromTravisBackend(workspace.SleekflowCompanyId, workspace.SleekflowStaffId);
        }

        // Check if the user exists in the Travis backend
        try
        {
            var deleteUser = await _travisBackendClient.GetUserAsync(workspace.SleekflowUserId, company?.ServerLocation ?? null);

            if (deleteUser is not null)
            {
                // If user exists and company is found, delete staff from Travis backend
                await DeleteStaffFromTravisBackend(
                    workspace.SleekflowCompanyId,
                    workspace.SleekflowStaffId,
                    company.ServerLocation);
            }
        }
        catch (Exception ex)
        {
            // Log the exception with company ID, staff ID and error message
            _logger.LogError(
                ex,
                "Error occurred while checking or deleting user from Travis backend. CompanyId: {CompanyId}, StaffId: {StaffId}, Error: {Error}",
                workspace.SleekflowCompanyId,
                workspace.SleekflowStaffId,
                ex.Message);

            // Track the failure in telemetry
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.TenantHubCompanyStaffDeleteFailed,
                new Dictionary<string, string>
                {
                    { "company_id", workspace.SleekflowCompanyId },
                    { "staff_id", workspace.SleekflowStaffId },
                    { "error", ex.Message },
                    { "context", "travis_backend_operation" }
                });

            // Continue with workspace removal despite Travis backend error
        }

        // Remove the workspace from the user and update the user
        RemoveUserWorkspace(user, workspace);
        await _userService.UpdateAndGetAsync(user);
    }

    // Deletes a non-migrated user from the Travis backend and Auth0
    private async Task DeleteNonMigratedUserAsync(string companyId, string staffId, string userId)
    {
        try
        {
            var targetUser = await _auth0UserManagementService.FindUserAsync(userId);
            if (targetUser is null || string.IsNullOrEmpty(targetUser.Email))
            {
                var errorMessage = targetUser is null
                    ? $"[{nameof(DeleteCompanyStaffAsync)}] User not found in Auth0."
                    : $"[{nameof(DeleteCompanyStaffAsync)}] User email is null or empty";

                _logger.LogError(errorMessage);
                throw new SfInternalErrorException(errorMessage);
            }

            await _travisBackendClient.DeleteCompanyStaffAsync(companyId, staffId);
            await _auth0UserManagementService.DeleteUserAsync(targetUser.Email);

            // Track successful deletion of non-migrated user
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.TenantHubCompanyStaffDeleteCompleted,
                new Dictionary<string, string>
                {
                    { "company_id", companyId },
                    { "staff_id", staffId },
                    { "user_id", userId },
                    { "context", "non_migrated_user" }
                });
        }
        catch (Exception ex) when (!(ex is SfInternalErrorException))
        {
            _logger.LogError(ex, "Error deleting non-migrated user: {Message}", ex.Message);

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.TenantHubCompanyStaffDeleteFailed,
                new Dictionary<string, string>
                {
                    { "company_id", companyId },
                    { "staff_id", staffId },
                    { "user_id", userId },
                    { "error", ex.Message },
                    { "error_type", ex.GetType().Name },
                    { "context", "non_migrated_user_deletion" }
                });

            throw new SfInternalErrorException(ex, $"Error deleting non-migrated user. CompanyId: {companyId}, StaffId: {staffId}, UserId: {userId}");
        }
    }

    // Deletes a staff member from the Travis backend
    private async Task DeleteStaffFromTravisBackend(string companyId, string staffId, string? serverLocation = null)
    {
        await _travisBackendClient.DeleteCompanyStaffAsync(companyId, staffId, serverLocation);
    }

    // Removes a workspace from a user and logs the action
    private void RemoveUserWorkspace(User user, UserWorkspace workspace)
    {
        user.UserWorkspaces.Remove(workspace);
        _logger.LogInformation(
            "[{MethodName}] Removed workspace of TenantHubUserId: {Id}, SleekflowUserId: {UserId}, CompanyId: {CompanyId}",
            nameof(DeleteCompanyStaffAsync),
            user.Id,
            workspace.SleekflowUserId,
            workspace.SleekflowCompanyId);
    }

    // Handles the case when a user has no remaining workspaces
    private async Task HandleUserWithNoWorkspaces(User user)
    {
        if (!user.UserWorkspaces.Any())
        {
            // Log the information about the user being deleted
            _logger.LogInformation(
                "[{MethodName}] No workspace left for user {Email}, user id: {Id}, deleting user",
                nameof(DeleteCompanyStaffAsync),
                user.Email,
                user.Id);

            // Delete the user from the system and Auth0
            await _userService.DeleteAsync(user.Id);
            await _auth0UserManagementService.DeleteUserAsync(user.Email);
        }
    }
}