using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Utils;
using AppConfiguration = Pulumi.AzureNative.AppConfiguration;
using Insights = Pulumi.AzureNative.Insights;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;
using Random = Pulumi.Random;
using Storage = Pulumi.AzureNative.Storage;
using Web = Pulumi.AzureNative.Web;
using Cache = Pulumi.AzureNative.Cache;

namespace Sleekflow.Infras.Components.FlowHub;

public class FlowHubOnWorkflowExecutionEndedPostProcessRequestedEventConsumer
{
    private readonly ResourceGroup _resourceGroup;
    private readonly FlowHubDb.FlowHubDbOutput _flowHubDbOutput;
    private readonly AppConfiguration.ConfigurationStore _appConfig;
    private readonly List<ManagedEnvAndAppsTuple> _managedEnvAndAppsTuples;
    private readonly Db.DbOutput _dbOutput;
    private readonly MyConfig _myConfig;
    private readonly GcpConfig _gcpConfig;
    private readonly FlowHubSharedResources _flowHubSharedResources;

    public FlowHubOnWorkflowExecutionEndedPostProcessRequestedEventConsumer(
        ResourceGroup resourceGroup,
        FlowHubDb.FlowHubDbOutput flowHubDbOutput,
        AppConfiguration.ConfigurationStore appConfig,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        MyConfig myConfig,
        GcpConfig gcpConfig,
        FlowHubSharedResources flowHubSharedResources)
    {
        _resourceGroup = resourceGroup;
        _flowHubDbOutput = flowHubDbOutput;
        _appConfig = appConfig;
        _managedEnvAndAppsTuples = managedEnvAndAppsTuples;
        _dbOutput = dbOutput;
        _myConfig = myConfig;
        _gcpConfig = gcpConfig;
        _flowHubSharedResources = flowHubSharedResources;
    }

    public Dictionary<string, Web.WebApp> InitOnWorkflowExecutionEndedPostProcessRequestedEventConsumer()
    {
        var apps = new Dictionary<string, Web.WebApp>();

        foreach (var managedEnvAndAppsTuple in _managedEnvAndAppsTuples)
        {
            if (managedEnvAndAppsTuple.IsExcludedFromManagedEnv(
                    ServiceNames.FlowHubOnWorkflowExecutionEndedPostProcessRequestedEventConsumer))
            {
                continue;
            }

            var serviceBus = managedEnvAndAppsTuple.ServiceBus;
            var highTrafficServiceBus = managedEnvAndAppsTuple.HighTrafficServiceBus;

            var eventHub = managedEnvAndAppsTuple.EventHub;
            var logAnalyticsWorkspace = managedEnvAndAppsTuple.LogAnalyticsWorkspace;
            var massTransitBlobStorage = managedEnvAndAppsTuple.MassTransitBlobStorage;

            var listRedisKeysOutput = Output
                .Tuple(
                    _resourceGroup.Name,
                    _flowHubSharedResources.RedisConfig.Name,
                    _flowHubSharedResources.RedisConfig.Id)
                .Apply(
                    t => Cache.ListRedisKeys.InvokeAsync(
                        new Cache.ListRedisKeysArgs
                        {
                            ResourceGroupName = t.Item1, Name = t.Item2
                        }));

            var workspaceSharedKeys = Output
                .Tuple(_resourceGroup.Name, logAnalyticsWorkspace.Name)
                .Apply(
                    items => OperationalInsights.GetSharedKeys.InvokeAsync(
                        new OperationalInsights.GetSharedKeysArgs
                        {
                            ResourceGroupName = items.Item1, WorkspaceName = items.Item2,
                        }));

            // Primary,Secondary,Primary Read Only,Secondary Read Only
            var primaryAppConfigReadOnlyConnStr = Output
                .Tuple(_resourceGroup.Name, _appConfig.Name)
                .Apply(
                    items => AppConfiguration.ListConfigurationStoreKeys.Invoke(
                        new AppConfiguration.ListConfigurationStoreKeysInvokeArgs
                        {
                            ResourceGroupName = items.Item1, ConfigStoreName = items.Item2
                        }))
                .Apply(o => o.Value.First(v => v.Name == "Primary Read Only").ConnectionString);

            var randomId = new Random.RandomId(
                "sleekflow-fh-on-workflow-execution-ended-post-process-requested-event-consumer-storage-account-random-id",
                new Random.RandomIdArgs
                {
                    ByteLength = 4,
                    Keepers =
                    {
                        {
                            "fh-on-workflow-execution-ended-post-process-requested-event-consumer", "fh-value"
                        }
                    },
                });

            var storageAccount = new Storage.StorageAccount(
                "sleekflow-fh-on-workflow-execution-ended-post-process-requested-event-consumer-storage-account",
                new Storage.StorageAccountArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    Sku = new Storage.Inputs.SkuArgs
                    {
                        Name = Storage.SkuName.Standard_LRS,
                    },
                    Kind = Storage.Kind.StorageV2,
                    AccountName = randomId.Hex.Apply(h => "s" + h)
                },
                new CustomResourceOptions
                {
                    Parent = _resourceGroup
                });

            var container = new Storage.BlobContainer(
                "sleekflow-fh-on-workflow-execution-ended-post-process-requested-event-consumer-zips-container",
                new Storage.BlobContainerArgs
                {
                    AccountName = storageAccount.Name,
                    PublicAccess = Storage.PublicAccess.None,
                    ResourceGroupName = _resourceGroup.Name,
                    ContainerName = "zips-container"
                },
                new CustomResourceOptions
                {
                    Parent = storageAccount
                });

            var path =
                "../Sleekflow.FlowHub.OnWorkflowExecutionEndedPostProcessRequestedEventConsumer/bin/Release/net8.0/publish";

            var ymdhms = new DirectoryInfo(path)
                .EnumerateFiles("*", SearchOption.AllDirectories)
                .Max(fi => fi.CreationTimeUtc)
                .ToString("yyyyMMddHHmmss");

            var blob = new Storage.Blob(
                "fh_on_workflow_execution_ended_post_process_requested_event_consumer_zip_" + ymdhms,
                new Storage.BlobArgs
                {
                    AccountName = storageAccount.Name,
                    ContainerName = container.Name,
                    ResourceGroupName = _resourceGroup.Name,
                    Type = Storage.BlobType.Block,
                    Source = new FileArchive(path)
                },
                new CustomResourceOptions
                {
                    Parent = container
                });

            var codeBlobUrl = StorageUtils.SignedBlobReadUrl(blob, container, storageAccount, _resourceGroup);

            var appInsights = new Insights.Component(
                "sleekflow-fh-on-workflow-execution-ended-post-process-requested-event-consumer-app-insight",
                new Insights.ComponentArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    ApplicationType = Insights.ApplicationType.Web,
                    FlowType = "Redfield",
                    RequestSource = "IbizaWebAppExtensionCreate",
                    Kind = "Web",
                    WorkspaceResourceId = logAnalyticsWorkspace.Id
                },
                new CustomResourceOptions
                {
                    Parent = _resourceGroup
                });

            var appServicePlan = new Web.AppServicePlan(
                "sleekflow-fh-on-workflow-post-event-plan",
                new Web.AppServicePlanArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    Kind = "elastic",
                    Sku = new Web.Inputs.SkuDescriptionArgs
                    {
                        Tier = "ElasticPremium", Name = "EP1", Capacity = 1 // Minimum Always-On instances
                    },
                    MaximumElasticWorkerCount = 5 // Maximum burstable instances
                },
                new CustomResourceOptions
                {
                    Parent = _resourceGroup
                });

            var functionAppName = "sf-fh-post-event-consumer";

            var functionApp = new Web.WebApp(
                functionAppName,
                new Web.WebAppArgs
                {
                    Kind = "FunctionApp",
                    ResourceGroupName = _resourceGroup.Name,
                    ServerFarmId = appServicePlan.Id,
                    SiteConfig = new Web.Inputs.SiteConfigArgs
                    {
                        AppSettings = EnvironmentVariablesUtils.GetDeduplicateNameValuePairs(
                            new[]
                            {
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "ASPNETCORE_ENVIRONMENT", Value = "Production",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "FUNCTIONS_EXTENSION_VERSION", Value = "~4",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "FUNCTIONS_WORKER_RUNTIME", Value = "dotnet-isolated",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "FUNCTIONS_INPROC_NET8_ENABLED", Value = "1",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "APPINSIGHTS_INSTRUMENTATIONKEY", Value = appInsights.InstrumentationKey
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "APPLICATIONINSIGHTS_CONNECTION_STRING",
                                    Value = appInsights.ConnectionString,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "AzureWebJobsStorage",
                                    Value = StorageUtils.GetConnectionString(_resourceGroup.Name, storageAccount.Name),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "WEBSITE_CONTENTAZUREFILECONNECTIONSTRING",
                                    Value = StorageUtils.GetConnectionString(_resourceGroup.Name, storageAccount.Name),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "WEBSITE_CONTENTSHARE", Value = functionAppName + "96ed",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "WEBSITE_RUN_FROM_PACKAGE", Value = codeBlobUrl,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "APP_CONFIGURATION_CONN_STR", Value = primaryAppConfigReadOnlyConnStr,
                                },

                                #region AppConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "INTERNALS_KEY",
                                    Value =
                                        "BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP",
                                },

                                #endregion

                                #region DbConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_ENDPOINT",
                                    Value = Output.Format(
                                        $"https://{_dbOutput.AccountName}.documents.azure.com:443/"),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_KEY", Value = _dbOutput.AccountKey,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_DATABASE_ID", Value = _dbOutput.DatabaseId,
                                },

                                #endregion

                                #region FlowHubDbConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_FLOW_HUB_DB_ENDPOINT",
                                    Value = Output.Format(
                                        $"https://{_flowHubDbOutput.AccountName}.documents.azure.com:443/"),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_FLOW_HUB_DB_KEY", Value = _flowHubDbOutput.AccountKey,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_FLOW_HUB_DB_DATABASE_ID", Value = _flowHubDbOutput.DatabaseId,
                                },

                                #endregion

                                #region ServiceBusConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "SERVICE_BUS_CONN_STR", Value = serviceBus.CrmHubPolicyKeyPrimaryConnStr,
                                },

                                #endregion

                                #region HighTrafficServiceBusConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "HIGH_TRAFFIC_SERVICE_BUS_CONN_STR", Value = highTrafficServiceBus.CrmHubPolicyKeyPrimaryConnStr,
                                },
                                #endregion

                                #region EventHubConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "EVENT_HUB_CONN_STR", Value = eventHub.NamespacePrimaryConnStr,
                                },

                                #endregion

                                #region Loggings

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_IS_LOG_ANALYTICS_ENABLED", Value = "FALSE",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_WORKSPACE_ID", Value = logAnalyticsWorkspace.CustomerId,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_AUTHENTICATION_ID",
                                    Value = workspaceSharedKeys.Apply(r => r.PrimarySharedKey!),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED", Value = "TRUE",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_GOOGLE_CLOUD_PROJECT_ID", Value = _gcpConfig.ProjectId,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON", Value = _gcpConfig.CredentialJson,
                                },

                                #endregion

                                #region MassTransitStorageConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "MESSAGE_DATA_CONN_STR", Value = massTransitBlobStorage.StorageAccountConnStr
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "MESSAGE_DATA_CONTAINER_NAME", Value = massTransitBlobStorage.ContainerName
                                },

                                #endregion

                                #region CacheConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "CACHE_PREFIX", Value = "Sleekflow.FlowHub"
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "REDIS_CONN_STR",
                                    Value = Output
                                        .Tuple(listRedisKeysOutput, _flowHubSharedResources.RedisConfig.HostName)
                                        .Apply(
                                            t =>
                                                $"{t.Item2}:6380,password={t.Item1.PrimaryKey},ssl=True,abortConnect=False"),
                                },

                                #endregion

                                #region StorageConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "FILE_STORAGE_CONN_STR",
                                    Value = StorageUtils.GetConnectionString(
                                        _resourceGroup.Name,
                                        _flowHubSharedResources.FileStorageAccount.Name),
                                },

                                #endregion

                                #region AppConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "CORE_INTERNALS_ENDPOINT",
                                    Value = _myConfig.Name == "production"
                                        ? "https://sleekflow-core-app-eas-production.azurewebsites.net/FlowHub/Internals"
                                        : "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net/FlowHub/Internals"
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "CORE_INTERNALS_KEY",
                                    Value = "O19dnDlZhSZkZ5TigIXiolCTqSl461kyBCotXGOJGUMA6eiHfyxRQVwQTFN5qMr1",
                                },

                                #endregion

                                #region WorkflowRateLimitConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "WORKFLOW_ENROLLMENT_RATE_LIMIT_WINDOW_SECONDS", Value = "60"
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "WORKFLOW_ENROLLMENT_RATE_LIMIT_MAX_ALLOWED_WITHIN_WINDOW", Value = "30"
                                },

                                #endregion WorkflowRateLimitConfig

                                #region WorkflowEnrollmentRateLimitEmailNotificationConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name =
                                        "WORKFLOW_BLOCKED_ENROLLMENT_EMAIL_NOTIFICATION_RATE_LIMIT_WINDOW_SECONDS",
                                    Value = "1800"
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name =
                                        "WORKFLOW_BLOCKED_ENROLLMENT_EMAIL_NOTIFICATION_RATE_LIMIT_MAX_ALLOWED_WITHIN_WINDOW",
                                    Value = "1"
                                },

                                #endregion WorkflowEnrollmentRateLimitEmailNotificationConfig

                                #region StateAllStepsRequestRateCountConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "STATE_ALL_STEPS_REQUEST_WINDOW_SECONDS", Value = "60"
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "STATE_ALL_STEPS_REQUEST_WARNING_MAX_WITHIN_WINDOW", Value = "100"
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "STATE_ALL_STEPS_REQUEST_ERROR_MAX_WITHIN_WINDOW", Value = "500"
                                },

                                #endregion StateAllStepsRequestRateCountConfig

                                #region WorkflowPotentialInfiniteLoopConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "SPECIFIC_STEP_REQUEST_RATE_LIMIT_WINDOW_SECONDS", Value = "60"
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "SPECIFIC_STEP_REQUEST_MAX_ALLOWED_WITHIN_WINDOW", Value = "10",
                                },

                                #endregion WorkflowPotentialInfiniteLoopConfig

                                #region WorkflowDeletionConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "WORKFLOW_VERSION_DELETION_CLEANUP_DELAY_DAYS", Value = "30"
                                },

                                #endregion WorkflowDeletionConfig

                                #region ApplicationInsightTelemetryConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED", Value = "FALSE",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED", Value = "FALSE",
                                },

                                #endregion

                            }),
                        Use32BitWorkerProcess = true,
                        NetFrameworkVersion = "v8.0",
                    },
                },
                new CustomResourceOptions
                {
                    Parent = appServicePlan
                });

            managedEnvAndAppsTuple.WorkerApps.Add(
                ServiceNames.FlowHubOnWorkflowExecutionEndedPostProcessRequestedEventConsumer,
                functionApp);

            apps.Add(
                ServiceNames.GetWorkerName(
                    ServiceNames.FlowHubOnWorkflowExecutionEndedPostProcessRequestedEventConsumer),
                functionApp);
        }

        return apps;
    }
}