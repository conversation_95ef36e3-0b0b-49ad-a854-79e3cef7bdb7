namespace Sleekflow.UserEventAnalyticsHub.DataCompactor.Models;

public class ProcessingResult
{
    public bool Success { get; set; } = true;
    public int TotalFilesProcessed { get; set; }
    public long TotalRecordsMigrated { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime StartTime { get; set; } = DateTime.UtcNow;
    public DateTime? EndTime { get; set; }

    // Company-level tracking
    public int CompaniesProcessed { get; set; }
    public int CompaniesSuccessful { get; set; }
    public int CompaniesWithErrors { get; set; }

    public TimeSpan Duration => EndTime?.Subtract(StartTime) ?? TimeSpan.Zero;



    public override string ToString()
    {
        return $"ProcessingResult [Success={Success}, Files={TotalFilesProcessed}, Records={TotalRecordsMigrated}, Duration={Duration}]";
    }
}