using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Commons.Workflows;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Contacts;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.Utils;

namespace Sleekflow.FlowHub.WorkflowExecutions;

public interface IWorkflowStateService
{
    Task CreateStateAndExecutedWorkflowAsync(
        EventBody eventBody,
        string objectId,
        string objectType,
        string sleekflowCompanyId,
        string initialStateStatus,
        ProxyWorkflow workflow,
        FlowHubConfig flowHubConfig,
        string? workflowExecutionReasonCode,
        bool isReenrollment,
        ContactDetail? contactDetail = null,
        List<ProxyState>? runningStates = null);
}

public class WorkflowStateService(
    IStateService stateService,
    IStateAggregator stateAggregator,
    IWorkflowRuntimeService workflowRuntimeService,
    IWorkflowRecurringSettingsParser workflowRecurringSettingsParser,
    ILogger<WorkflowStateService> logger,
    ICoreCommander coreCommander)
    : IWorkflowStateService, IScopedService
{
    public async Task CreateStateAndExecutedWorkflowAsync(
        EventBody eventBody,
        string objectId,
        string objectType,
        string sleekflowCompanyId,
        string initialStateStatus,
        ProxyWorkflow workflow,
        FlowHubConfig flowHubConfig,
        string? workflowExecutionReasonCode,
        bool isReenrollment,
        ContactDetail? contactDetail = null,
        List<ProxyState>? runningStates = null)
    {
        string? parentStateId = null;
        if (workflow.WorkflowType == WorkflowType.AIAgent)
        {
            try
            {
                var currentRunningStates = runningStates
                                           ?? await stateService.GetRunningStatesAsync(
                                               objectId,
                                               objectType,
                                               sleekflowCompanyId,
                                               eventBody);
                parentStateId = StateUtils.GetParentState(currentRunningStates, workflow.WorkflowId, logger).ParentState.Id;
            }
            catch (Exception exception)
            {
                logger.LogWarning(exception, "No Parent State Found.");
            }
        }

        if (eventBody.EventName == EventNames.OnFbIgPostCommentReceived
            && eventBody is OnFbIgPostCommentReceivedEventBody { IsNewContact: true } fbIgEventBody)
        {
            if (fbIgEventBody.PostComment == null || fbIgEventBody.PostComment.From == null)
            {
                throw new Exception($"event body is illegal. fbIgEventBody: {JsonConvert.SerializeObject(fbIgEventBody)}");
            }
            // if it's a new contact, should create contact first for the coming step's using
            logger.LogInformation("begin to create new contact, contactId: {ContactId}, facebookInstagramUserInfo: {SenderInfo}", fbIgEventBody.ContactId, JsonConvert.SerializeObject(fbIgEventBody.PostComment.From));
            await CreateContactConversation(flowHubConfig.Origin, fbIgEventBody.PageId, fbIgEventBody.Channel, fbIgEventBody.PostComment.From.FacebookId, fbIgEventBody.PostComment.From!.FacebookName, fbIgEventBody.ContactId);
        }

        logger.LogInformation("begin to create state, eventBody {EventBody}", JsonConvert.SerializeObject(eventBody));
        var state = await stateService.CreateStateAsync(
            objectId,
            objectType,
            sleekflowCompanyId,
            initialStateStatus,
            null,
            null,
            isReenrollment,
            workflow,
            eventBody,
            flowHubConfig,
            contactDetail,
            parentStateId);

        if (parentStateId != null)
        {
            logger.LogInformation("Create ai agent workflow state {StateId} with parentStateId: {ParentStateId}", state.Id, parentStateId);
        }

        if (workflow.WorkflowScheduleSettings is { ScheduleType: WorkflowScheduleTypes.PredefinedDateTime })
        {
            var waitUntil = workflow.WorkflowScheduleSettings.ScheduledAt!.Value;

            // old scheduled workflow schema && using in step enrollment condition check logic
            if (eventBody is OnContactCreatedEventBody onContactCreatedEventBody
                && workflow.WorkflowScheduleSettings.RecurringSettings is not null
                && onContactCreatedEventBody.CreatedAt > workflow.WorkflowScheduleSettings.ScheduledAt.Value
                && !workflow.WorkflowScheduleSettings.IsNonOldScheduledWorkflowSchema())
            {
                waitUntil = workflowRecurringSettingsParser.GetNextOccurrence(
                    workflow.WorkflowScheduleSettings.ScheduledAt.Value,
                    workflow.WorkflowScheduleSettings.RecurringSettings);
            }
            else if (eventBody is OnContactRecurrentlyEnrolledEventBody onContactRecurrentlyEnrolledEventBody)
            {
                waitUntil = onContactRecurrentlyEnrolledEventBody.NextRecurrence;
            }

            await stateAggregator.SetSysVarStringAsync(
                state,
                StateSystemVarNames.PredefinedDateTimeWorkflowEnrollment,
                waitUntil.ToString("o"));
        }

        await workflowRuntimeService.ExecuteWorkflowAsync(
            state,
            workflowExecutionReasonCode);
    }

    private async Task CreateContactConversation(
        string origin,
        string pageId,
        string channel,
        string senderId,
        string? senderName,
        string? reservedContactId)
    {
        var getContactsByBatchInput = new CreateContactAndConversationInput(
            senderId,
            pageId,
            senderName ?? string.Empty,
            channel,
            reservedContactId);
        logger.LogInformation("begin to query {URI}, parameter: {Parameters}", origin, JsonConvert.SerializeObject(getContactsByBatchInput));
        await coreCommander.ExecuteAsync<string>(origin, "CreateContactAndConversation", getContactsByBatchInput);
    }
}