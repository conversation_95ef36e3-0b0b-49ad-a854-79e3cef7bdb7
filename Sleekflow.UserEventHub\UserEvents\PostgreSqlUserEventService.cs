using System.Text.Json;
using Npgsql;
using Sleekflow.DependencyInjection;
using Sleekflow.UserEventHub.Configs;
using Sleekflow.UserEventHub.Models.UserEvents;
using Sleekflow.UserEventHub.Utils;

namespace Sleekflow.UserEventHub.UserEvents;

public interface IPostgreSqlUserEventService
{
    Task WriteUserEventAsync(
        string id,
        string sleekflowCompanyId,
        string eventType,
        string objectId,
        string objectType,
        string source,
        UserEventProperties properties,
        UserEventMetadata metadata,
        DateTimeOffset createdAt);
}

public class PostgreSqlUserEventService : IPostgreSqlUserEventService, IScopedService
{
    private readonly ILogger<PostgreSqlUserEventService> _logger;
    private readonly IPostgreSqlConfig _postgreSqlConfig;

    public PostgreSqlUserEventService(
        ILogger<PostgreSqlUserEventService> logger,
        IPostgreSqlConfig postgreSqlConfig)
    {
        _logger = logger;
        _postgreSqlConfig = postgreSqlConfig;
    }

    public async Task WriteUserEventAsync(
        string id,
        string sleekflowCompanyId,
        string eventType,
        string objectId,
        string objectType,
        string source,
        UserEventProperties properties,
        UserEventMetadata metadata,
        DateTimeOffset createdAt)
    {
        try
        {
            await using var connection = new NpgsqlConnection(_postgreSqlConfig.ConnectionString);
            await connection.OpenAsync();

            // First ensure the company-specific table exists
            await EnsureCompanyTableExistsAsync(connection, sleekflowCompanyId);

            // Get company-specific table name
            var eventsTableName = PostgreSqlTableNameHelper.GetEventsTableName(sleekflowCompanyId);

            // Convert timestamp to milliseconds since epoch (matching DataCompactor format)
            var timestampMilliseconds = createdAt.ToUnixTimeMilliseconds();

            // Extract partition information from timestamp
            var year = createdAt.Year;
            var month = createdAt.Month;
            var day = createdAt.Day;
            var hour = createdAt.Hour;

            var sql = $@"
                INSERT INTO {eventsTableName} (
                    id,
                    eventType,
                    sleekflowCompanyId,
                    objectId,
                    objectType,
                    source,
                    properties,
                    metadata,
                    timestamp,
                    year,
                    month,
                    day,
                    hour
                ) VALUES (
                    @id,
                    @eventType,
                    @sleekflowCompanyId,
                    @objectId,
                    @objectType,
                    @source,
                    @properties::jsonb,
                    @metadata::jsonb,
                    @timestamp,
                    @year,
                    @month,
                    @day,
                    @hour
                )
                ON CONFLICT (id) DO UPDATE SET
                    eventType = EXCLUDED.eventType,
                    sleekflowCompanyId = EXCLUDED.sleekflowCompanyId,
                    objectId = EXCLUDED.objectId,
                    objectType = EXCLUDED.objectType,
                    source = EXCLUDED.source,
                    properties = EXCLUDED.properties,
                    metadata = EXCLUDED.metadata,
                    timestamp = EXCLUDED.timestamp,
                    year = EXCLUDED.year,
                    month = EXCLUDED.month,
                    day = EXCLUDED.day,
                    hour = EXCLUDED.hour;";

            await using var command = new NpgsqlCommand(sql, connection);
            command.Parameters.AddWithValue("@id", id);
            command.Parameters.AddWithValue("@eventType", eventType);
            command.Parameters.AddWithValue("@sleekflowCompanyId", sleekflowCompanyId);
            command.Parameters.AddWithValue("@objectId", objectId);
            command.Parameters.AddWithValue("@objectType", objectType);
            command.Parameters.AddWithValue("@source", source);
            command.Parameters.AddWithValue("@properties", JsonSerializer.Serialize(properties));
            command.Parameters.AddWithValue("@metadata", JsonSerializer.Serialize(metadata));
            command.Parameters.AddWithValue("@timestamp", timestampMilliseconds);
            command.Parameters.AddWithValue("@year", year);
            command.Parameters.AddWithValue("@month", month);
            command.Parameters.AddWithValue("@day", day);
            command.Parameters.AddWithValue("@hour", hour);

            await command.ExecuteNonQueryAsync();

            _logger.LogDebug(
                "Successfully wrote user event to PostgreSQL table: {TableName}. Id: {Id}, CompanyId: {CompanyId}, EventType: {EventType}",
                eventsTableName, id, sleekflowCompanyId, eventType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Failed to write user event to PostgreSQL. Id: {Id}, CompanyId: {CompanyId}, EventType: {EventType}",
                id, sleekflowCompanyId, eventType);

            // Don't throw the exception to avoid breaking the main flow
            // The original Cosmos DB write should still succeed
        }
    }

    private async Task EnsureCompanyTableExistsAsync(NpgsqlConnection connection, string sleekflowCompanyId)
    {
        var eventsTableName = PostgreSqlTableNameHelper.GetEventsTableName(sleekflowCompanyId);

        // Create events table with DataCompactor schema
        var createTableSql = $@"
            CREATE TABLE IF NOT EXISTS {eventsTableName} (
                id VARCHAR PRIMARY KEY,
                eventType VARCHAR,
                sleekflowCompanyId VARCHAR NOT NULL,
                objectId VARCHAR,
                objectType VARCHAR,
                source VARCHAR,
                properties JSONB,
                metadata JSONB,
                timestamp BIGINT NOT NULL,
                year INTEGER NOT NULL,
                month INTEGER NOT NULL,
                day INTEGER NOT NULL,
                hour INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT NOW()
            );

            -- Create indexes for better query performance (matching DataCompactor)
            CREATE INDEX IF NOT EXISTS {PostgreSqlTableNameHelper.GetEventsTableIndexName(sleekflowCompanyId, "timestamp")} ON {eventsTableName} (timestamp);
            CREATE INDEX IF NOT EXISTS {PostgreSqlTableNameHelper.GetEventsTableIndexName(sleekflowCompanyId, "eventtype")} ON {eventsTableName} (eventType);
            CREATE INDEX IF NOT EXISTS {PostgreSqlTableNameHelper.GetEventsTableIndexName(sleekflowCompanyId, "objectid")} ON {eventsTableName} (objectId);
            CREATE INDEX IF NOT EXISTS {PostgreSqlTableNameHelper.GetEventsTableIndexName(sleekflowCompanyId, "partitions")} ON {eventsTableName} (year, month, day, hour);";

        await using var command = new NpgsqlCommand(createTableSql, connection);
        await command.ExecuteNonQueryAsync();

        _logger.LogDebug("Created/verified events table and indexes: {TableName}", eventsTableName);
    }
}