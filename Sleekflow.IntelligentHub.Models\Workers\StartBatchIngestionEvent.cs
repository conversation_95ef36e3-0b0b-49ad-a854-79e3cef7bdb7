using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.Workers;

public interface IDocumentIngestionParams
{
    string DocumentId { get; set; }
}

public class FileDocumentIngestionParams : IDocumentIngestionParams
{
    [Required]
    [JsonProperty("document_id")]
    public string DocumentId { get; set; }

    [Required]
    [JsonProperty("sas_download_url")]
    public string SasDownloadUrl { get; set; }

    [JsonConstructor]
    public FileDocumentIngestionParams(
        string documentId,
        string sasDownloadUrl)
    {
        DocumentId = documentId;
        SasDownloadUrl = sasDownloadUrl;
    }
}

public class WebsiteDocumentIngestionParams : IDocumentIngestionParams
{
    [Required]
    [JsonProperty("document_id")]
    public string DocumentId { get; set; }

    [JsonConstructor]
    public WebsiteDocumentIngestionParams(string documentId)
    {
        DocumentId = documentId;
    }
}

public class WebpageDocumentIngestionParams : IDocumentIngestionParams
{
    [Required]
    [JsonProperty("document_id")]
    public string DocumentId { get; set; }

    [JsonConstructor]
    public WebpageDocumentIngestionParams(string documentId)
    {
        DocumentId = documentId;
    }
}

public class StartBatchIngestionEvent : IHasSleekflowCompanyId
{
    [Required]
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("file_document_ingestion_params")]
    public FileDocumentIngestionParams[] FileDocumentIngestionParams { get; set; }

    [JsonProperty("website_document_ingestion_params")]
    public WebsiteDocumentIngestionParams[] WebsiteDocumentIngestionParams { get; set; }

    [JsonProperty("webpage_document_ingestion_params")]
    public WebpageDocumentIngestionParams[] WebpageDocumentIngestionParams { get; set; }

    [JsonConstructor]
    public StartBatchIngestionEvent(
        string sleekflowCompanyId,
        FileDocumentIngestionParams[] fileDocumentIngestionParams,
        WebsiteDocumentIngestionParams[] websiteDocumentIngestionParams,
        WebpageDocumentIngestionParams[] webpageDocumentIngestionParams)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        FileDocumentIngestionParams = fileDocumentIngestionParams;
        WebsiteDocumentIngestionParams = websiteDocumentIngestionParams;
        WebpageDocumentIngestionParams = webpageDocumentIngestionParams;
    }
}