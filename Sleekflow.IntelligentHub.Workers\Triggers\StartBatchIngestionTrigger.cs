using Azure.Messaging.ServiceBus;
using MassTransit;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.IntelligentHub.Workers.Orchestrators;

namespace Sleekflow.IntelligentHub.Workers.Triggers;

public class StartBatchIngestionTrigger
{
    private readonly ILogger<StartBatchIngestionTrigger> _logger;
    private const string QueueName = "start-batch-ingestion-event";
    private readonly IMessageReceiver _messageReceiver;

    public StartBatchIngestionTrigger(
        ILogger<StartBatchIngestionTrigger> logger,
        IMessageReceiver messageReceiver)
    {
        _logger = logger;
        _messageReceiver = messageReceiver;
    }

    [Function(nameof(StartBatchIngestionTrigger))]
    public async Task RunAsync(
        [ServiceBusTrigger(QueueName, Connection = "SERVICE_BUS_CONN_STR")]
        ServiceBusReceivedMessage message,
        [DurableClient]
        DurableTaskClient starter,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("StartBatchIngestionTrigger triggered with message body {MessageBody}", message.Body);

            // Parse the full message to extract the nested "message" property
            var fullMessage = JObject.Parse(message.Body.ToString());
            var messageContent = fullMessage["message"]?.ToString();

            _logger.LogInformation(
                "StartBatchIngestionTrigger message content: {Message}",
                messageContent);

            if (string.IsNullOrEmpty(messageContent))
            {
                _logger.LogError("Message content is missing or empty in the received message");
                return;
            }

            var input = JsonConvert.DeserializeObject<StartBatchIngestionEvent>(messageContent);
            if (input == null)
            {
                _logger.LogError("Failed to deserialize StartBatchIngestionEvent");
                return;
            }

            var instanceId = await starter.ScheduleNewOrchestrationInstanceAsync(
                nameof(StartBatchIngestionOrchestrator),
                input);

            _logger.LogInformation("Started batch ingestion orchestrator with instance ID {InstanceId}", instanceId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Service Bus message for batch ingestion");
            throw;
        }
    }
}