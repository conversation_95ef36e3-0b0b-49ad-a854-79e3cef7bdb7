using System.Collections.Concurrent;
using Azure.Storage.Blobs.Models;
using MassTransit;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.TenantHub;
using Sleekflow.Ids;
using Sleekflow.TenantHub.Client;
using Sleekflow.TenantHub.Companies;
using Sleekflow.TenantHub.Features;
using Sleekflow.TenantHub.Features.EnabledFeatures;
using Sleekflow.TenantHub.Models.Constants;
using Sleekflow.TenantHub.Models.Events;
using Sleekflow.TenantHub.Models.Rbac;
using Sleekflow.TenantHub.Models.Roles;
using Sleekflow.TenantHub.Models.Users;
using Sleekflow.TenantHub.Roles;
using Sleekflow.TenantHub.Users;
using User = Sleekflow.TenantHub.Models.Users.User;
using System.Threading;

namespace Sleekflow.TenantHub.Rbac;

public interface IRbacService
{
    Task<bool> IsRbacFeatureEnabledForCompanyAsync(string companyId);

    Task<RbacRole> CreateCustomRoleAsync(
        string roleName,
        string companyId,
        string? defaultRoleId,
        string? description,
        bool? isEnabled = true);

    Task<List<RbacRole>> GetRolesAsync(string companyId, List<string> roleIds);

    public bool GetBlobStatus();

    public Task<BlobContainerProperties> GetProperties();

    Task<bool> SaveCompanyPoliciesAsync(
        string companyId,
        Dictionary<string, Dictionary<string, List<string>>> rolePermission);

    Task<List<string>> LoadPoliciesAsync(string companyId, string role);

    Task<List<RbacRole>> SearchRolesByNameAsync(string companyId, string roleName);

    Task<(bool Allowed, string? Permission)> VerifyPermissionAsync(
        string method,
        string path,
        string company,
        string role);

    Task<(bool Allowed, string? Permission)> VerifyPermissionAsync(
        string method,
        string path,
        string company,
        List<string> roles);

    Task<List<RbacRolePermissionsItem>> GetAllRolePermissionsInCompanyAsync(string companyId);

    Task<List<RbacRole>> GetAllRolesInCompanyAsync(string companyId);

    Task<List<RbacRoleOverviewItem>> GetAllRoleDetailsInCompanyAsync(string companyId);

    Task<List<RbacUserRoles>> GetRolesByStaffIdsAsync(string companyId, List<string> staffIds);

    Task<List<RbacUserRolePermissions>> GetRolesWithPermissionsByStaffIds(
        string companyId,
        List<string> staffIds,
        List<string>? permissionFilter);

    Task<List<RbacUserRolePermissions>> GetAllStaffRolesWithPermissionsAsync(string companyId, List<string>? permissionFilter);

    Task<List<RbacUserRoles>> GetAllStaffRolesAsync(string companyId, List<string>? permissionFilter);

    Task<RbacUserRoles> GetRolesByStaffIdAsync(string companyId, string staffId);

    Task<(bool Success, string? Message)> AssignUserToMultipleRolesAsync(
        string userId,
        List<string> roleIds,
        string companyId);

    Task<(bool Success, string? Message)> AssignUserToRoleAsync(
        string userId,
        string roleId,
        string companyId);

    Task<RbacRole> EditCustomRoleAsync(
        string roleId,
        string companyId,
        string? name = null,
        string? description = null);

    Task<(bool Success, string? Message, string? ErrorKey)> RemoveUserFromRoleAsync(string userId, string roleId, string companyId);

    Task<(string Name, string Description, List<string> Permissions)> GetRoleDetailAsync(
        string roleId,
        string companyId);

    Task<bool> DeleteRoleAsync(string roleId, string companyId);

    Task<RbacRole> DuplicateRoleAsync(string roleId, string companyId);

    Task<bool> ModifyRolePermissionsAsync(
        string companyId,
        List<(string Permission, List<string> RoleIdsToAdd, List<string> RoleIdsToRemove)> modifications);

    Task<List<string>> GetRoleNamesByIdsAsync(List<string> roleIds);

    Task<(bool IsDefault, string? RoleName)> IsDefaultRoleAsync(string roleId);

    Task<(bool IsDefault, string? RoleId)> IsDefaultRoleAsync(string roleName, bool isRoleName);

    Task<string?> GetDefaultRoleAsync(string roleId);

    Task<bool> HasDefaultRoleAsync(List<string> roleIds);

    Task OnRbacEnabled(string sleekflowCompanyId, bool isFirstTimeEnabled);

    Task OnRbacDisabled(string sleekflowCompanyId);
}

public class RbacService : IRbacService, IScopedService
{
    public static readonly string RbacFeatureName = "RBAC_internal_test";
    private static string? _cachedRbacFeatureId;
    private static readonly SemaphoreSlim _cacheSemaphore = new SemaphoreSlim(1, 1);

    private readonly IIdService _idService;
    private readonly IRoleService _roleService;
    private readonly IRbacRoleRepository _rbacRoleRepository;
    private readonly IPoliciesBlobService _policiesBlobService;
    private readonly ICacheService _cacheService;
    private readonly ILogger<RbacService> _logger;
    private readonly IOpalClient _opalClient;
    private readonly IOpaClient _opaClient;
    private readonly IUserService _userService;
    private readonly ITravisBackendClient _travisBackendClient;
    private readonly ICompanyService _companyService;
    private readonly IRbacCachingService _rbacCachingService;
    private readonly IBus _bus;

    public RbacService(
        IIdService idService,
        IRoleService roleService,
        IRbacRoleRepository rbacRoleRepository,
        IPoliciesBlobService policiesBlobService,
        ICacheService cacheService,
        ILogger<RbacService> logger,
        IOpalClient opalClient,
        IOpaClient opaClient,
        IUserService userService,
        ITravisBackendClient travisBackendClient,
        ICompanyService companyService,
        IRbacCachingService rbacCachingService,
        IBus bus)
    {
        _idService = idService;
        _roleService = roleService;
        _rbacRoleRepository = rbacRoleRepository;
        _cacheService = cacheService;
        _logger = logger;
        _policiesBlobService = policiesBlobService;
        _opalClient = opalClient;
        _opaClient = opaClient;
        _userService = userService;
        _travisBackendClient = travisBackendClient;
        _companyService = companyService;
        _rbacCachingService = rbacCachingService;
        _bus = bus;
    }

    public static async Task<string> GetRbacFeatureIdAsync(IFeatureService featureService)
    {
        // Fast path: Check memory cache first without acquiring the lock
        if (!string.IsNullOrEmpty(_cachedRbacFeatureId))
        {
            return _cachedRbacFeatureId;
        }

        // Slow path: Acquire lock and check again (double-check locking pattern)
        await _cacheSemaphore.WaitAsync();
        try
        {
            // Double-check after acquiring lock
            if (!string.IsNullOrEmpty(_cachedRbacFeatureId))
            {
                return _cachedRbacFeatureId;
            }

            // Get the feature ID from the feature service
            var feature = await featureService.GetObjectByNameAsync(RbacFeatureName);
            if (feature == null)
            {
                throw new SfInternalErrorException($"Feature {RbacFeatureName} not found");
            }

            // Cache the result
            _cachedRbacFeatureId = feature.Id;
            return _cachedRbacFeatureId;
        }
        finally
        {
            _cacheSemaphore.Release();
        }
    }

    private async Task<List<Role>> GetDefaultRolesAsync()
    {
        var roles = await _cacheService.CacheAsync<List<Role>>(
            "defaultRoles",
            async () => await _roleService.GetAllDefaultRolesAsync());

        return roles;
    }

    public async Task<bool> IsRbacFeatureEnabledForCompanyAsync(string companyId)
    {
        return await _rbacCachingService.IsCompanyEnabledAsync(companyId);
    }

    public async Task<RbacRole> CreateCustomRoleAsync(
        string roleName,
        string companyId,
        string? defaultRoleId,
        string? description,
        bool? isEnabled = true)
    {
        var rbacRole = new RbacRole(
            id: _idService.GetId(SysTypeNames.RbacRole),
            name: roleName,
            sleekflowCompanyId: companyId,
            description: description,
            defaultRoleId: defaultRoleId,
            isEnabled: isEnabled ?? true,
            createdAt: DateTimeOffset.UtcNow,
            updatedAt: DateTimeOffset.UtcNow);

        return await _rbacRoleRepository.CreateAndGetAsync(rbacRole, companyId);
    }

    public async Task<List<RbacRole>> GetRolesAsync(string companyId, List<string> roleIds)
    {
        var defaultRoles = await GetDefaultRolesAsync();
        var foundRoles = defaultRoles.Where(f => roleIds.Contains(f.Id)).ToList();
        var returnRoles = (foundRoles.Count == 0)
            ? new List<RbacRole>()
            : foundRoles.Select(
                    f => new RbacRole(
                        id: f.Id,
                        name: f.Name,
                        sleekflowCompanyId: companyId,
                        description: f.Description,
                        defaultRoleId: f.Id,
                        isEnabled: true,
                        createdAt: DateTimeOffset.UtcNow,
                        updatedAt: DateTimeOffset.UtcNow))
                .ToList();

        if (foundRoles.Count == roleIds.Count)
        {
            // return result when all mapping is completed
            return returnRoles;
        }

        // Combine the results from custom role if the company has created it.
        var customRoles = await _rbacRoleRepository.GetObjectsAsync(
            f => f.SleekflowCompanyId == companyId && roleIds.Contains(f.Id));
        returnRoles = returnRoles.Concat(customRoles ?? []).ToList();

        return returnRoles;
    }

    public bool GetBlobStatus()
    {
        return _policiesBlobService.IsBlobExistsAsync().Result;
    }

    public async Task<BlobContainerProperties> GetProperties()
    {
        var result = await _policiesBlobService.GetStatusAsync();
        return result;
    }

    public async Task<bool> SaveCompanyPoliciesAsync(
        string companyId,
        Dictionary<string, Dictionary<string, List<string>>> rolePermission)
    {
        // Remove excessive logging of entire object
        _logger.LogDebug("Saving policies for company {CompanyId} with {RoleCount} roles",
            companyId, rolePermission.Count);

        // Load existing policies from blob storage
        var blobPolicies = await _policiesBlobService.GetCompanyPoliciesAsync(companyId);
        if (blobPolicies == null || blobPolicies.RolePermission == null)
        {
            blobPolicies = new Policies
            {
                RolePermission = new Dictionary<string, Dictionary<string, List<string>>>()
            };
        }

        // Update the role permissions
        foreach (var kvp in rolePermission)
        {
            blobPolicies.RolePermission[kvp.Key] = kvp.Value;
        }

        var policiesJson = JsonConvert.SerializeObject(blobPolicies);

        // Save to blob storage
        var blobSaveResult = await _policiesBlobService.SaveCompanyPolicies(
            companyId,
            policiesJson);

        if (!blobSaveResult)
        {
            _logger.LogError($"Failed to save policies to blob storage for company {companyId}");
            return false;
        }

        try
        {
            // Update Opal
            await _opalClient.UpdatePermissionAsync(companyId, rolePermission);
            _logger.LogInformation("Successfully updated permissions in Opal for company {CompanyId}", companyId);

            // Publish the event when policies are saved
            await _bus.Publish(new OnCompanyPoliciesSavedEvent(
                companyId,
                rolePermission,
                DateTimeOffset.UtcNow));

            _logger.LogInformation(
                "Published OnCompanyPoliciesSavedEvent for company {CompanyId} with {RoleCount} roles",
                companyId,
                rolePermission.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to update permissions in Opal for company {companyId}");
            return false;
        }

        return true;
    }

    public async Task<Dictionary<string, List<string>>> LoadPoliciesAsync(string companyId, List<string> roles)
    {
        var rolePermissions = new Dictionary<string, List<string>>();
        foreach (var role in roles)
        {
            var permissions = await LoadPoliciesAsync(companyId, role);
            rolePermissions[role] = permissions;
        }

        return rolePermissions;
    }

    public async Task<List<string>> LoadPoliciesAsync(string companyId, string role)
    {

        // Get default permissions from OPA
        var defaultPermissions = await _opaClient.GetDefaultPermissionAsync(role);

        // Load policies from blob storage
        var blobPolicies = await _policiesBlobService.GetCompanyPoliciesAsync(companyId);

        // Filter blob policies by role
        Dictionary<string, List<string>> filteredBlobPolicies = new Dictionary<string, List<string>>();
        if (blobPolicies.RolePermission != null && blobPolicies.RolePermission.ContainsKey(role))
        {
            filteredBlobPolicies = blobPolicies.RolePermission[role];
        }

        // Combine default permissions with filtered blob policies
        var combinedPermissions = MergePolicies(defaultPermissions, filteredBlobPolicies);

        // Transform combined permissions into the desired format
        var formattedPermissions = new List<string>();
        foreach (var kvp in combinedPermissions)
        {
            string resource = kvp.Key;
            foreach (var action in kvp.Value)
            {
                formattedPermissions.Add($"{action}:{resource}");
            }
        }

        return formattedPermissions;
    }

    // This method merges two policy dictionaries, giving priority to blob policies
    private Dictionary<string, List<string>> MergePolicies(
        Dictionary<string, List<string>> defaultPolicies,
        Dictionary<string, List<string>> blobPolicies)
    {
        // Create a new dictionary starting with the default policies
        var mergedPolicies = new Dictionary<string, List<string>>(defaultPolicies);

        // Iterate through each key-value pair in the blob policies
        foreach (var kvp in blobPolicies)
        {
            // If the key already exists in the merged policies
            if (mergedPolicies.ContainsKey(kvp.Key))
            {
                // Merge the values, removing duplicates
                mergedPolicies[kvp.Key] = mergedPolicies[kvp.Key].Union(kvp.Value).ToList();
            }
            else
            {
                // If the key doesn't exist, add it with its values
                mergedPolicies[kvp.Key] = kvp.Value;
            }
        }

        // Return the merged policies
        return mergedPolicies;
    }

    public async Task<List<RbacRole>> SearchRolesByNameAsync(string companyId, string roleName)
    {
        var query = new QueryDefinition(
                "SELECT * " + "FROM %%CONTAINER_NAME%% r " + "WHERE r.sleekflow_company_id = @sleekflow_company_id " +
                "AND CONTAINS(r.name, @role_name, true)").WithParameter("@sleekflow_company_id", companyId)
            .WithParameter("@role_name", roleName);

        var roles = await _rbacRoleRepository.GetObjectsAsync(query);
        return roles;
    }

    public async Task<(bool Allowed, string? Permission)> VerifyPermissionAsync(
        string method,
        string path,
        string company,
        string role)
    {
        try
        {

            // Call the OPA client to verify the permission
            var (allowed, permission) = await _opaClient.VerifyPermissionAsync(method, path, company, role);

            return (allowed, permission);
        }
        catch (Exception ex)
        {
            // Log any errors that occur during the verification process
            _logger.LogError(
                ex,
                "Error verifying permission for method: {Method}, path: {Path}, company: {Company}, role: {Role}",
                method, path, company, role);

            // Re-throw the exception to be handled by the caller
            throw;
        }
    }

    public async Task<(bool Allowed, string? Permission)> VerifyPermissionAsync(
        string method,
        string path,
        string company,
        List<string> roles)
    {
        try
        {
            // Log the incoming request details at debug level only
            _logger.LogDebug(
                "Verifying permission: {Method} {Path}, company: {Company}, roles count: {RolesCount}",
                method, path, company, roles.Count);

            // Call the OPA client to verify the permission
            var (allowed, permission) = await _opaClient.VerifyPermissionAsync(method, path, company, roles);

            return (allowed, permission);
        }
        catch (Exception ex)
        {
            // Log any errors that occur during the verification process
            _logger.LogError(
                ex,
                "Error verifying permission for method: {Method}, path: {Path}, company: {Company}",
                method, path, company);

            // Re-throw the exception to be handled by the caller
            throw;
        }
    }

    public async Task<List<RbacRolePermissionsItem>> GetAllRolePermissionsInCompanyAsync(string companyId)
    {
        var rolePermissionsItems = new List<RbacRolePermissionsItem>();
        var roles = await GetAllRolesInCompanyAsync(companyId);
        var roleNames = roles.Select(r => r.Name).ToList();
        var permissions = await LoadPoliciesAsync(companyId, roleNames!);
        foreach (var role in roles)
        {
            var rolePermissionItem = new RbacRolePermissionsItem(
                role.Id,
                role.Name,
                !string.IsNullOrEmpty(role.DefaultRoleId),
                permissions[role.Name]);
            rolePermissionsItems.Add(rolePermissionItem);
        }

        return rolePermissionsItems;
    }

    public async Task<List<RbacRole>> GetAllRolesInCompanyAsync(string companyId)
    {
        // Get roles from RoleService
        var defaultRoles = await _roleService.GetAllDefaultRolesAsync();
        var defaultRolesToRbacRoles = defaultRoles.Select(
            r => new RbacRole(
                id: r.Id,
                name: r.Name,
                sleekflowCompanyId: companyId,
                description: r.Description,
                defaultRoleId: r.Id,
                isEnabled: true,
                createdAt: DateTimeOffset.UtcNow,
                updatedAt: DateTimeOffset.UtcNow)).ToList();

        // Get roles from RbacRoleRepository
        var customRoles = await _rbacRoleRepository.GetObjectsAsync(r => r.SleekflowCompanyId == companyId);
        var combinedRoles = defaultRolesToRbacRoles.Concat(customRoles).ToList();

        return combinedRoles;
    }

    public async Task<List<RbacRoleOverviewItem>>
        GetAllRoleDetailsInCompanyAsync(
            string companyId)
    {
        // Get roles from RoleService
        var defaultRoles = await _roleService.GetAllDefaultRolesAsync();

        // Get roles from RbacRoleRepository
        var customRoles = await _rbacRoleRepository.GetObjectsAsync(r => r.SleekflowCompanyId == companyId);

        var rolesWithUserCount =
            new List<RbacRoleOverviewItem>();
            // new List<(string Id, string Name, string Description, int UserCount, DateTimeOffset CreatedAt)>();

        foreach (var role in defaultRoles)
        {
            var userCount = await _userService.GetUserCountInRoleForCompanyAsync(companyId, role.Id);
            rolesWithUserCount.Add(
                new RbacRoleOverviewItem(
                    role.Id,
                    role.Name,
                    role.Description ?? string.Empty,
                    userCount,
                    true,
                    role.CreatedAt));
        }

        foreach (var role in customRoles)
        {
            var userCount = await _userService.GetUserCountInRoleAsync(role.Id);
            rolesWithUserCount.Add(
                new RbacRoleOverviewItem(
                    role.Id,
                    role.Name,
                    role.Description ?? string.Empty,
                    userCount,
                    false,
                    role.CreatedAt));

                // (role.Id, role.Name, role.Description ?? string.Empty, userCount, role.CreatedAt));
        }

        // Sort roles: "Super Admin" first, then by CreatedAt
        var sortedRoles = rolesWithUserCount
            .OrderByDescending(r => r.Name?.Equals("SuperAdmin")) // Ensure "Super Admin" is at the start
            .ThenBy(r => r.CreatedAt) // Then sort by CreatedAt
            // .Select(r => (r.Id, r.Name, r.Description, r.UserCount)) // Project back to the original tuple
            .ToList();

        return sortedRoles;
    }

    public async Task<List<RbacUserRoles>> GetRolesByStaffIdsAsync(string companyId, List<string> staffIds)
    {
        var allRoles = await GetAllRolesInCompanyAsync(companyId);
        var userWorkspaces = await _userService.GetUserWorkspacesAsync(companyId, staffIds);

        var results = new List<RbacUserRoles>();

        foreach (var userWorkspace in userWorkspaces)
        {
            var roleItems = new List<RbacRoleItem>();
            foreach (var roleId in userWorkspace.SleekflowRoleIds)
            {
                var roleItem = allRoles?.Find(r => r.Id == roleId);
                if (roleItem != null)
                {
                    roleItems.Add(
                        new RbacRoleItem(
                            roleId,
                            roleItem.Name,
                            !string.IsNullOrEmpty(roleItem.DefaultRoleId)));
                }
                else
                {
                    _logger.LogWarning(
                        "[{RolesByStaffIdsAsyncName}]Cannot found role with id {RoleId}",
                        nameof(GetRolesByStaffIdsAsync),
                        roleId);
                }
            }

            results.Add(
                new RbacUserRoles(userWorkspace.SleekflowUserId, userWorkspace.SleekflowStaffId, roleItems));
        }

        return results;
    }

    public async Task<List<RbacUserRolePermissions>> GetRolesWithPermissionsByStaffIds(
        string companyId,
        List<string> staffIds,
        List<string>? permissionFilter)
    {
        var users = await GetRolesByStaffIdsAsync(companyId, staffIds);

        var allRoleNames = users
            .SelectMany(r => r.Roles.Select(roleItem => roleItem.RoleName))
            .Distinct()
            .ToList();

        var companyRolePermissionDictionary = await LoadPoliciesAsync(companyId, allRoleNames);

        if (permissionFilter != null && permissionFilter.Count != 0)
        {
            foreach (var companyRolePermission in companyRolePermissionDictionary)
            {
                companyRolePermissionDictionary[companyRolePermission.Key] =
                    companyRolePermission.Value.Intersect(permissionFilter).ToList();
            }
        }

        var userRolesPermissions = new List<RbacUserRolePermissions>();

        foreach (var user in users)
        {
            var roles = new List<RbacRolePermissionsItem>();

            foreach (var userRole in user.Roles)
            {
                if (companyRolePermissionDictionary.TryGetValue(userRole.RoleName, out var permissions))
                {
                    roles.Add(new RbacRolePermissionsItem(userRole, permissions));
                }
            }

            userRolesPermissions.Add(
                new RbacUserRolePermissions(
                    user.SleekflowUserId,
                    user.SleekflowStaffId,
                    roles));
        }

        return userRolesPermissions;
    }

    public async Task<List<RbacUserRolePermissions>> GetAllStaffRolesWithPermissionsAsync(string companyId, List<string>? permissionFilter)
    {
        var users = await GetAllStaffRolesAsync(companyId, permissionFilter);
        var allRoleNames = users
            .SelectMany(r => r.Roles.Select(roleItem => roleItem.RoleName))
            .Distinct()
            .ToList();

        var companyRolePermissionDictionary = await LoadPoliciesAsync(companyId, allRoleNames);

        if (permissionFilter != null && permissionFilter.Count != 0)
        {
            foreach (var companyRolePermission in companyRolePermissionDictionary)
            {
                companyRolePermissionDictionary[companyRolePermission.Key] =
                    companyRolePermission.Value.Intersect(permissionFilter).ToList();
            }
        }

        var userRolesPermissions = new List<RbacUserRolePermissions>();

        foreach (var user in users)
        {
            var roles = new List<RbacRolePermissionsItem>();

            foreach (var userRole in user.Roles)
            {
                if (companyRolePermissionDictionary.TryGetValue(userRole.RoleName, out var permissions))
                {
                    roles.Add(new RbacRolePermissionsItem(userRole, permissions));
                }
            }

            userRolesPermissions.Add(
                new RbacUserRolePermissions(
                    user.SleekflowUserId,
                    user.SleekflowStaffId,
                    roles));
        }

        return userRolesPermissions;
    }

    public async Task<List<RbacUserRoles>> GetAllStaffRolesAsync(string companyId, List<string>? permissionFilter)
    {
        var allRoles = await GetAllRolesInCompanyAsync(companyId);
        var userWorkspaces = await _userService.GetUserWorkspacesAsync(companyId);

        var results = new ConcurrentBag<RbacUserRoles>();

        Parallel.ForEach(userWorkspaces, userWorkspace =>
        {
            var roleItems = new List<RbacRoleItem>();
            foreach (var roleId in userWorkspace.SleekflowRoleIds)
            {
                var roleItem = allRoles?.Find(r => r.Id == roleId);
                if (roleItem != null)
                {
                    roleItems.Add(
                        new RbacRoleItem(
                            roleId,
                            roleItem.Name,
                            !string.IsNullOrEmpty(roleItem.DefaultRoleId)));
                }
                else
                {
                    _logger.LogWarning(
                        "[{RolesByStaffIdsAsyncName}]Cannot found role with id {RoleId}",
                        nameof(GetRolesByStaffIdsAsync),
                        roleId);
                }
            }

            results.Add(
                new RbacUserRoles(userWorkspace.SleekflowUserId, userWorkspace.SleekflowStaffId, roleItems));
        });

        return results.ToList();
    }

    public async Task<RbacUserRoles> GetRolesByStaffIdAsync(string companyId, string staffId)
    {
        // var defaultRoles = await GetDefaultRolesAsync();
        var allRoles = await GetAllRolesInCompanyAsync(companyId);
        var userWorkspace = await _userService.GetUserWorkspaceAsync(companyId, staffId);
        var roleItems = new List<RbacRoleItem>();

        foreach (var roleId in userWorkspace.SleekflowRoleIds)
        {
            var roleItem = allRoles?.Find(r => r.Id == roleId);
            if (roleItem != null)
            {
                roleItems.Add(
                    new RbacRoleItem(
                        roleId,
                        roleItem.Name,
                        !string.IsNullOrEmpty(roleItem.DefaultRoleId)));
            }
            else
            {
                _logger.LogWarning(
                    "[{RolesByStaffIdsAsyncName}]Cannot found role with id {RoleId}",
                    nameof(GetRolesByStaffIdAsync),
                    roleId);
            }
        }

        return new RbacUserRoles(userWorkspace.SleekflowUserId, userWorkspace.SleekflowStaffId, roleItems);
    }

    public async Task<(bool Success, string? Message)> AssignUserToMultipleRolesAsync(
        string userId,
        List<string> roleIds,
        string companyId)
    {
        string? message = null;
        var user = await _userService.GetUserBySleekflowUserIdAsync(userId);
        if (user == null)
        {
            message = $"User with ID {userId} not found.";
            _logger.LogError("User with ID {UserId} not found", userId);
            return (false, message);
        }

        var userWorkspace = user.UserWorkspaces.Find(uw => uw.SleekflowCompanyId == companyId);
        if (userWorkspace == null)
        {
            message = $"Workspace for company ID {companyId} not found for user {userId}";
            _logger.LogError("Workspace for company ID {CompanyId} not found for user {UserId}", companyId, userId);
            return (false, message);
        }

        var allCompanyRoles = await GetAllRolesInCompanyAsync(companyId);
        var allCompanyDefaultRoles = await GetDefaultRolesAsync();
        var assignedDefaultRoles = roleIds
            .Select(roleId => allCompanyDefaultRoles.Find(r => r.Id == roleId))
            .Where(role => role != null)
            .ToList();

        if (assignedDefaultRoles.Count > 1)
        {
            message = $"User {userId} cannot be assigned more than one default role";
            _logger.LogError("User {UserId} cannot be assigned more than one default role", userId);
            return (false, message);
        }

        try
        {
            userWorkspace.SleekflowRoleIds = roleIds;
            var result = await _userService.UpdateAndGetAsync(user);
            var updatedRoles = allCompanyRoles.Where(r => roleIds.Contains(r.Id)).ToList();

            _logger.LogInformation(
                "[{MethodName}]Successfully assigned role {Join} to user {UserEmail}({UserId}) in company {CompanyId}",
                nameof(AssignUserToMultipleRolesAsync),
                string.Join(", ", updatedRoles?.Select(r => r.Name).ToList() ?? new List<string>()),
                user.Email,
                userId,
                companyId);

            if (assignedDefaultRoles.Any()) // Check if default role is assigned
            {
                var company = await _companyService.GetAsync(companyId);
                var defaultRole = assignedDefaultRoles[0];
                var updateRes = await _travisBackendClient.UpdateStaffRoleAsync(
                    userWorkspace.SleekflowStaffId,
                    userWorkspace.SleekflowCompanyId,
                    defaultRole!.Name,
                    company.ServerLocation);

                if (!updateRes.Success)
                {
                    throw new SfTravisBackendErrorResponseException(
                        $"[{nameof(AssignUserToMultipleRolesAsync)}] Failed to update staff role in Travis Backend. Message: {updateRes.Message}");
                }
            }

            // Create role mapping dictionary
            var roleIdToNameMap = new Dictionary<string, string>();
            foreach (var roleId in roleIds)
            {
                var role = allCompanyRoles.FirstOrDefault(r => r.Id == roleId);
                if (role != null)
                {
                    roleIdToNameMap[roleId] = role.Name;
                }
            }

            // Publish the event when role is assigned
            if (roleIdToNameMap.Any())
            {
                await _bus.Publish(new OnUserRoleAssignedEvent(
                    companyId,
                    userId,
                    userWorkspace.SleekflowStaffId,
                    roleIdToNameMap,
                    DateTimeOffset.UtcNow));

                _logger.LogInformation(
                    "Published OnUserRoleAssignedEvent for user {UserId} in company {CompanyId} with {RoleCount} roles",
                    userId,
                    companyId,
                    roleIdToNameMap.Count);
            }

            return (true, null);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                "[{MethodName}]Failed to assign roles to user {UserId}, message: {Message}",
                nameof(AssignUserToMultipleRolesAsync),
                userId,
                ex.Message);
            return (false, ex.Message);
        }
    }

    public async Task<(bool Success, string? Message)> AssignUserToRoleAsync(
        string userId,
        string roleId,
        string companyId)
    {
        // Fetch the user by userId
        var user = await _userService.GetUserBySleekflowUserIdAsync(userId);
        string? message = null;
        if (user == null)
        {
            message = "User not found.";
            _logger.LogError("User with ID {UserId} not found", userId);
            return (false, message);
        }

        // Add the role's ID to the user's workspace for the specified company
        var userWorkspace = user.UserWorkspaces.FirstOrDefault(uw => uw.SleekflowCompanyId == companyId);
        if (userWorkspace == null)
        {
            message = "Workspace for this company not found for the user.";
            _logger.LogError(
                "Workspace for company ID {CompanyId} not found for user {UserId}",
                companyId,
                userId);
            return (false, message);
        }

        // Check if the role to be assigned is a default role
        var allCompanyDefaultRoles = await GetDefaultRolesAsync();
        var roleIsDefault = allCompanyDefaultRoles.Any(r => r.Id == roleId);

        if (roleIsDefault)
        {
            // Check if user already has any default role assigned
            var userExistingDefaultRoles = userWorkspace.SleekflowRoleIds
                .Select(existingRoleId => allCompanyDefaultRoles.Find(r => r.Id == existingRoleId))
                .Where(role => role != null)
                .ToList();

            if (userExistingDefaultRoles.Any() && !userExistingDefaultRoles.Exists(r => r?.Id == roleId))
            {
                message = "Cannot assign more than one default role to a user";
                _logger.LogError("User {UserId} cannot be assigned more than one default role", userId);
                return (false, message);
            }
        }

        if (!userWorkspace.SleekflowRoleIds.Contains(roleId))
        {
            // Add the role ID to the workspace's role IDs
            userWorkspace.SleekflowRoleIds.Add(roleId);

            // Update the user with the modified workspaces
            try
            {
                var result = await _userService.UpdateAndGetAsync(user);
                if (result is null)
                {
                    message = "Failed to assign role.";
                    _logger.LogError(
                        "Failed to assign role {RoleId} to user {UserId}",
                        roleId,
                        userId);
                    return (false, message);
                }

                var (isDefaultRole, defaultRoleName) = await IsDefaultRoleAsync(roleId);
                if (isDefaultRole)
                {
                    var company = await _companyService.GetAsync(companyId);

                    await _travisBackendClient.UpdateStaffRoleAsync(
                        userWorkspace.SleekflowStaffId,
                        userWorkspace.SleekflowCompanyId,
                        defaultRoleName!,
                        company.ServerLocation);
                }

                _logger.LogInformation($"Successfully assigned role {roleId} to user {userId} in company {companyId}.");
                return (true, "ok");
            }
            catch (SfQueryException ex)
            {
                message = "Failed to assign role due to a database error.";
                _logger.LogError(
                    ex,
                    "Failed to assign role {RoleId} to user {UserId}",
                    roleId,
                    userId);
                return (false, message);
            }
        }

        message = "Role ID already exists in the workspace.";
        _logger.LogInformation("Role ID already exists in the workspace");
        return (false, message);
    }

    public async Task<RbacRole> EditCustomRoleAsync(
        string roleId,
        string companyId,
        string? name = null,
        string? description = null)
    {
        // Fetch the existing role from the repository
        var rbacRole = await _rbacRoleRepository.GetAsync(roleId, companyId);

        if (rbacRole == null)
        {
            throw new ArgumentException($"Role with ID {roleId} not found in company {companyId}.");
        }

        // Update the role properties if new values are provided
        if (!string.IsNullOrEmpty(name))
        {
            rbacRole.Name = name;
        }

        rbacRole.Description = description;
        // Set the updated timestamp
        rbacRole.UpdatedAt = DateTimeOffset.UtcNow;

        // Save the updated role back to the repository
        await _rbacRoleRepository.UpsertAndGetAsync(rbacRole, companyId);

        return rbacRole;
    }

    public async Task<(bool Success, string? Message, string? ErrorKey)> RemoveUserFromRoleAsync(string userId, string roleId, string companyId)
    {
        // Fetch the user by userId
        var user = await _userService.GetUserBySleekflowUserIdAsync(userId);
        if (user == null)
        {
            _logger.LogError("User with ID {UserId} not found", userId);
            return (false, $"User with ID {userId} not found", RbacErrorKeys.USER_NOT_FOUND);
        }

        // Get the workspace for the specified company
        var userWorkspace = user.UserWorkspaces.FirstOrDefault(uw => uw.SleekflowCompanyId == companyId);
        if (userWorkspace == null)
        {
            _logger.LogError("Workspace for company ID {CompanyId} not found for user {UserId}", companyId, userId);
            return (false, $"Workspace for company ID {companyId} not found for user {userId}", RbacErrorKeys.WORKSPACE_NOT_FOUND);
        }

        // Check if the role ID exists in the workspace's role IDs
        if (!userWorkspace.SleekflowRoleIds.Contains(roleId))
        {
            _logger.LogWarning("Role ID {RoleId} does not exist in the workspace for user {UserId}", roleId, userId);
            return (true, "ok", null); // Return true as no change is needed
        }

        if (userWorkspace.SleekflowRoleIds.Count <= 1)
        {
            _logger.LogWarning("Cannot remove the last role from user {UserId} in company {CompanyId}", userId, companyId);
            return (false, $"Cannot remove the last role from user {userId} in company {companyId}", RbacErrorKeys.CANNOT_REMOVE_LAST_ROLE);
        }

        // Remove the role ID from the workspace's role IDs
        userWorkspace.SleekflowRoleIds.Remove(roleId);

        // Update the user with the modified workspaces
        var result = await _userService.UpdateAndGetAsync(user);
        if (result is null)
        {
            _logger.LogError("Failed to remove role {RoleId} from user {UserId}", roleId, userId);
            return (false, $"Failed to remove role {roleId} from user {userId}", RbacErrorKeys.ROLE_REMOVAL_FAILED);
        }

        _logger.LogInformation(
            "Successfully removed role {RoleId} from user {UserId} in company {CompanyId}", roleId, userId, companyId);
        return (true, "ok", null);
    }

    public async Task<(string Name, string Description, List<string> Permissions)> GetRoleDetailAsync(
        string roleId,
        string companyId)
    {
        RbacRole role = null;
        try
        {
            // Fetch the role from the RbacRole repository first
            role = await _rbacRoleRepository.GetAsync(roleId, companyId);
        }
        catch (Exception ex)
        {
            var defaultRole = await _roleService.GetAsync(roleId);
            if (defaultRole == null)
            {
                _logger.LogError($"Role with ID {roleId} not found.");
                throw new SfNotFoundObjectException($"Role with ID {roleId} not found.");
            }

            role = new RbacRole(
                id: defaultRole.Id,
                name: defaultRole.Name,
                sleekflowCompanyId: companyId,
                description: defaultRole.Description,
                defaultRoleId: defaultRole.Id,
                isEnabled: true,
                createdAt: DateTimeOffset.UtcNow,
                updatedAt: DateTimeOffset.UtcNow);
        }

        // Fetch permissions for the role from the policies blob service
        var policies = await _policiesBlobService.GetCompanyPoliciesAsync(role.SleekflowCompanyId);
        var rolePermissions = policies?.RolePermission.ContainsKey(roleId) == true
            ? policies.RolePermission[roleId].SelectMany(kvp => kvp.Value.Select(action => $"{action}:{kvp.Key}"))
                .ToList()
            : new List<string>();

        return (role.Name, role.Description ?? string.Empty, rolePermissions);
    }

    public async Task<bool> DeleteRoleAsync(string roleId, string companyId)
    {
        // Fetch the role to ensure it exists
        var role = await _rbacRoleRepository.GetAsync(roleId, companyId);
        if (role == null)
        {
            _logger.LogError($"Role with ID {roleId} not found in company {companyId}.");
            throw new ArgumentException($"Role with ID {roleId} not found.");
        }

        // Check if any users are assigned to this role
        var usersWithRole = await _userService.GetUsersByRoleIdAsync(companyId, roleId);
        if (usersWithRole.Any())
        {
            _logger.LogWarning($"Cannot delete role {roleId} because there are users assigned to it.");
            return false;
        }

        // Delete the role if no users are assigned
        await _rbacRoleRepository.DeleteAsync(roleId, companyId);
        _logger.LogInformation($"Successfully deleted role {roleId} from company {companyId}.");
        return true;
    }

    public async Task<RbacRole> DuplicateRoleAsync(string roleId, string companyId)
    {
        // Check the number of existing roles for the company before doing anything else
        var allRoles = await GetAllRolesInCompanyAsync(companyId);
        if (allRoles.Count >= 20)
        {
            throw new InvalidOperationException(
                $"Cannot duplicate role. The number of roles for company {companyId} has reached the limit of 20.");
        }

        RbacRole existingRole = null;

        // Attempt to fetch the role as an RbacRole
        try
        {
            existingRole = await _rbacRoleRepository.GetAsync(roleId, companyId);
        }
        catch (Exception ex)
        {
            // Handle the case where the RbacRole does not exist
            _logger.LogWarning(
                ex,
                $"Failed to fetch RbacRole with ID {roleId} for company {companyId}. Attempting to fetch as default role.");
        }

        // If the RbacRole doesn't exist, try to fetch it as a default role
        if (existingRole == null)
        {
            var defaultRole = await _roleService.GetAsync(roleId);
            if (defaultRole != null)
            {
                existingRole = new RbacRole(
                    id: _idService.GetId(SysTypeNames.RbacRole),
                    name: defaultRole.Name,
                    sleekflowCompanyId: companyId,
                    description: defaultRole.Description,
                    defaultRoleId: defaultRole.Id,
                    isEnabled: true,
                    createdAt: DateTimeOffset.UtcNow,
                    updatedAt: DateTimeOffset.UtcNow);
            }
            else
            {
                throw new ArgumentException($"Role with ID {roleId} not found in company {companyId}.");
            }
        }

        // Prepare potential new role name
        var newRoleName = $"{existingRole.Name}(Copy)";

        // Check if a role with the new name already exists
        var existingRolesWithName = await SearchRolesByNameAsync(companyId, newRoleName);
        if (existingRolesWithName.Count > 0)
        {
            // If role with the name already exists, append a number to make it unique
            newRoleName = $"{existingRole.Name}(Copy)({existingRolesWithName.Count + 1})";
        }

        // Create a new role with a modified name
        var newRole = new RbacRole(
            id: _idService.GetId(SysTypeNames.RbacRole),
            name: newRoleName,
            sleekflowCompanyId: companyId,
            description: existingRole.Description,
            defaultRoleId: existingRole.DefaultRoleId,
            isEnabled: existingRole.IsEnabled,
            createdAt: DateTimeOffset.UtcNow,
            updatedAt: DateTimeOffset.UtcNow);

        // Load and duplicate permissions using LoadPoliciesAsync
        var existingPermissions = await LoadPoliciesAsync(companyId, existingRole.Name);

        // Prepare the role permissions dictionary
        var rolePermissions = new Dictionary<string, Dictionary<string, List<string>>>
        {
            {
                newRole.Name, existingPermissions
                    .GroupBy(p => p.Split(':')[1])
                    .ToDictionary(
                        g => g.Key,
                        g => g.Select(p => p.Split(':')[0]).ToList()
                    )
            }
        };

        _logger.LogInformation(
            $"Existing permissions for role {existingRole.Name}: {JsonConvert.SerializeObject(rolePermissions)}");

        // Save the updated policies with the new role's permissions
        var isSuccess = await SaveCompanyPoliciesAsync(companyId, rolePermissions);
        if (!isSuccess)
        {
            throw new InvalidOperationException(
                $"Failed to save permissions for duplicated role in company {companyId}");
        }

        return await _rbacRoleRepository.CreateAndGetAsync(newRole, companyId);
    }


    public async Task<bool> ModifyRolePermissionsAsync(
        string companyId,
        List<(string Permission, List<string> RoleIdsToAdd, List<string> RoleIdsToRemove)> modifications)
    {
        try
        {
            // Load existing policies from blob storage
            var blobPolicies = await _policiesBlobService.GetCompanyPoliciesAsync(companyId);
            _logger.LogDebug("Loaded policies for company {CompanyId} with {RoleCount} roles",
                companyId, blobPolicies.RolePermission?.Count ?? 0);

            if (blobPolicies == null || blobPolicies.RolePermission == null)
            {
                blobPolicies = new Policies
                {
                    RolePermission = new Dictionary<string, Dictionary<string, List<string>>>()
                };
            }

            foreach (var modification in modifications)
            {
                var parts = modification.Permission.Split(':');
                if (parts.Length != 2) continue;

                var action = parts[0];
                var resource = parts[1];

                // Fetch role names for roles to add
                var roleNamesToAdd = await GetRoleNamesByIdsAsync(modification.RoleIdsToAdd);

                // Process roles to add
                foreach (var roleNameToAdd in roleNamesToAdd)
                {
                    if (!blobPolicies.RolePermission.ContainsKey(roleNameToAdd))
                    {
                        // Initialize permissions for the role if it doesn't exist
                        blobPolicies.RolePermission[roleNameToAdd] = new Dictionary<string, List<string>>();
                    }

                    if (!blobPolicies.RolePermission[roleNameToAdd].ContainsKey(resource))
                    {
                        blobPolicies.RolePermission[roleNameToAdd][resource] = new List<string>();
                    }

                    // Ensure no duplicate actions are added
                    if (!blobPolicies.RolePermission[roleNameToAdd][resource].Contains(action))
                    {
                        blobPolicies.RolePermission[roleNameToAdd][resource].Add(action);
                    }
                }

                // Fetch role names for roles to remove
                var roleNamesToRemove = await GetRoleNamesByIdsAsync(modification.RoleIdsToRemove);

                // Process roles to remove
                foreach (var roleNameToRemove in roleNamesToRemove)
                {
                    if (blobPolicies.RolePermission.ContainsKey(roleNameToRemove) &&
                        blobPolicies.RolePermission[roleNameToRemove].ContainsKey(resource))
                    {
                        // Remove the action if it exists
                        blobPolicies.RolePermission[roleNameToRemove][resource].Remove(action);

                        // Remove any resources with empty permissions lists
                        if (!blobPolicies.RolePermission[roleNameToRemove][resource].Any())
                        {
                            blobPolicies.RolePermission[roleNameToRemove].Remove(resource);
                        }

                        // If the role has no more permissions, remove the role itself
                        if (!blobPolicies.RolePermission[roleNameToRemove].Any())
                        {
                            blobPolicies.RolePermission.Remove(roleNameToRemove);
                        }
                    }
                }
            }

            // Serialize the updated policies
            var policiesJson = JsonConvert.SerializeObject(blobPolicies);

            // Save the updated policies to blob storage
            var blobSaveResult = await _policiesBlobService.SaveCompanyPolicies(companyId, policiesJson);

            if (!blobSaveResult)
            {
                _logger.LogError("Failed to save modified policies to blob storage for company {CompanyId}", companyId);
                return false;
            }

            try
            {
                // Update Opal with the new permissions
                await _opalClient.UpdatePermissionAsync(companyId, blobPolicies.RolePermission);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update permissions in Opal for company {CompanyId}", companyId);
                return false;
            }

            if (modifications.Count > 0)
            {
                // Publish the event when role permissions are modified
                 await _bus.Publish(new OnCompanyPoliciesSavedEvent(
                    companyId,
                    blobPolicies.RolePermission,
                    DateTimeOffset.UtcNow));


            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error modifying role permissions for company {CompanyId}", companyId);
            throw;
        }
    }


    public async Task<List<string>> GetRoleNamesByIdsAsync(List<string> roleIds)
    {
        if (roleIds == null || !roleIds.Any())
        {
            // This is a common condition, no need to log it
            return new List<string>();
        }

        try
        {
            // Fetch all default roles
            var allDefaultRoles = await _roleService.GetAllDefaultRolesAsync();

            // Filter default roles by provided IDs
            var foundDefaultRoles = allDefaultRoles
                .Where(role => roleIds.Contains(role.Id))
                .ToList();

            // Extract default role names
            var defaultRoleNames = foundDefaultRoles
                .Select(role => role.Name)
                .ToList();

            // Determine the ids not found in default roles
            var idsNotInDefaultRoles = roleIds.Except(foundDefaultRoles.Select(role => role.Id)).ToList();

            // Fetch the remaining custom roles
            var customRoles = await _rbacRoleRepository.GetObjectsAsync(r => idsNotInDefaultRoles.Contains(r.Id));
            var customRoleNames = customRoles.Select(r => r.Name).ToList();

            // Combine both lists of role names
            var combinedRoleNames = defaultRoleNames.Concat(customRoleNames).ToList();


            return combinedRoleNames;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving role names for provided IDs");
            throw;
        }
    }

    public List<RbacRole> ClearDefaultRoleIdsIfNotMatching(List<RbacRole> roles)
    {
        // Define the set of allowed role names
        var allowedRoleNames = new HashSet<string>
        {
            "Staff", "Admin", "TeamAdmin", "SuperAdmin"
        };

        // Select and modify roles
        return roles.Select(
            role =>
            {
                if (!allowedRoleNames.Contains(role.Name))
                {
                    // Assign null to the DefaultRoleId if the role name is not allowed
                    role.DefaultRoleId = null;
                }

                return role;
            }).ToList();
    }

    public async Task<(bool IsDefault, string? RoleName)> IsDefaultRoleAsync(string roleId)
    {
        if (string.IsNullOrEmpty(roleId))
        {
            return (false, null);
        }

        var defaultRoles = await GetDefaultRolesAsync();
        var role = defaultRoles.Find(r => r.Id == roleId);

        return (role != null, role?.Name);
    }

    public async Task<(bool IsDefault, string? RoleId)> IsDefaultRoleAsync(string roleName, bool isRoleName)
    {
        if (string.IsNullOrEmpty(roleName))
        {
            return (false, null);
        }

        // Quick check using the DefaultRoleNames class first
        bool isDefaultByName = DefaultRoleNames.IsDefaultRole(roleName);

        if (!isDefaultByName)
        {
            return (false, null);
        }

        // If it passes the quick check, find the actual role ID
        var defaultRoles = await GetDefaultRolesAsync();
        var role = defaultRoles.Find(r => string.Equals(r.Name, roleName, StringComparison.OrdinalIgnoreCase));

        return (role != null, role?.Id);
    }

    public async Task<string?> GetDefaultRoleAsync(string roleId)
    {
        var defaultRoles = await GetDefaultRolesAsync();
        var defaultRole = defaultRoles.Find(r => r.Id == roleId);

        return defaultRole?.Name ?? string.Empty;
    }

    public async Task<bool> HasDefaultRoleAsync(List<string> roleIds)
    {
        if (roleIds == null || !roleIds.Any())
        {
            return false;
        }

        var defaultRoles = await GetDefaultRolesAsync();
        return defaultRoles.Exists(defaultRole => roleIds.Contains(defaultRole.Id));
    }

    public async Task OnRbacEnabled(string sleekflowCompanyId, bool isFirstTimeEnabled)
    {
        try
        {
            _logger.LogInformation(
                "RBAC feature enabled for company {CompanyId}, IsFirstTime: {IsFirstTime}",
                sleekflowCompanyId,
                isFirstTimeEnabled);

            // Publish the event when RBAC is enabled
            await _bus.Publish(new OnRbacFeatureEnabledEvent(
                sleekflowCompanyId,
                isFirstTimeEnabled,
                DateTimeOffset.UtcNow));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish RBAC feature enabled event for company {CompanyId}", sleekflowCompanyId);
        }
    }

    public async Task OnRbacDisabled(string sleekflowCompanyId)
    {
        try
        {
            _logger.LogInformation(
                "RBAC feature disabled for company {CompanyId}",
                sleekflowCompanyId);

            // Publish the event when RBAC is disabled
            await _bus.Publish(new OnRbacFeatureDisabledEvent(
                sleekflowCompanyId,
                DateTimeOffset.UtcNow));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish RBAC feature disabled event for company {CompanyId}", sleekflowCompanyId);
        }
    }
}