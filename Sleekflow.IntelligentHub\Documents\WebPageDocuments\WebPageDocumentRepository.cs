﻿using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Documents.WebPageDocuments;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Documents.WebPageDocuments;

public interface IWebPageDocumentRepository : IDynamicFiltersRepository<WebPageDocument>
{
    Task<List<WebPageDocument>> GetDocumentsAsync(
        string sleekflowCompanyId,
        string blobId);
}

public class WebPageDocumentRepository
    : DynamicFiltersBaseRepository<WebPageDocument>, IWebPageDocumentRepository, IScopedService
{
    public WebPageDocumentRepository(
        ILogger<WebPageDocumentRepository> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
    }

    public async Task<List<WebPageDocument>> GetDocumentsAsync(
        string sleekflowCompanyId,
        string blobId)
    {
        return await GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId
                && c.BlobId == blobId);
    }
}