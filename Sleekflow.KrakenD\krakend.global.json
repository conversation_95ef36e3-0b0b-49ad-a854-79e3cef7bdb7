{
  "endpoints": [
    {
      "endpoint": "/v1/healthz",
      "backend": [
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "encoding": "string",
          "group": "tenant-hub",
          "method": "GET"
        }
      ],
      "extra_config": {
        "qos/ratelimit/router": {
          "client_max_rate": 5.0,
          "key": "X-Azure-ClientIP",
          "max_rate": 100.0,
          "strategy": "ip"
        }
      },
      "method": "GET"
    },
    {
      "endpoint": "/v1/tenant-hub/Companies/{method}",
      "backend": [
        {
          "url_pattern": "/Companies/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/Users/<USER>",
      "backend": [
        {
          "url_pattern": "/Users/<USER>",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/EnabledFeatures/{method}",
      "backend": [
        {
          "url_pattern": "/EnabledFeatures/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/Roles/{method}",
      "backend": [
        {
          "url_pattern": "/Roles/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/Migrations/{method}",
      "backend": [
        {
          "url_pattern": "/Migrations/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/Features/{method}",
      "backend": [
        {
          "url_pattern": "/Features/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/IpWhitelists/{method}",
      "backend": [
        {
          "url_pattern": "/IpWhitelists/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/management/Companies/{method}",
      "backend": [
        {
          "url_pattern": "/management/Companies/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/management/Users/<USER>",
      "backend": [
        {
          "url_pattern": "/management/Users/<USER>",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/management/EnabledFeatures/{method}",
      "backend": [
        {
          "url_pattern": "/management/EnabledFeatures/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/management/Roles/{method}",
      "backend": [
        {
          "url_pattern": "/management/Roles/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/management/Features/{method}",
      "backend": [
        {
          "url_pattern": "/management/Features/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/management/IpWhitelists/{method}",
      "backend": [
        {
          "url_pattern": "/management/IpWhitelists/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/management/Rbac/{method}",
      "backend": [
        {
          "url_pattern": "/management/Rbac/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/management/ImportUser/{method}",
      "backend": [
        {
          "url_pattern": "/management/ImportUser/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/UserWorkspaces/{method}",
      "backend": [
        {
          "url_pattern": "/UserWorkspaces/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/Workspaces/{method}",
      "backend": [
        {
          "url_pattern": "/Workspaces/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/Companies/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/Companies/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/Users/<USER>",
      "backend": [
        {
          "url_pattern": "/authorized/Users/<USER>",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/Plans/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/Plans/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/Roles/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/Roles/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/Features/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/Features/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/PlanDefinitions/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/PlanDefinitions/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/Subscriptions/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/Subscriptions/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/UserWorkspaces/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/UserWorkspaces/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/IpWhitelists/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/IpWhitelists/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/ExperimentalFeatures/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/ExperimentalFeatures/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/Rbac/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/Rbac/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/Blobs/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/Blobs/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/EnabledFeatures/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/EnabledFeatures/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/Geolocations/{method}",
      "backend": [
        {
          "url_pattern": "Geolocations/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/Webhooks/Auth0/{method}",
      "backend": [
        {
          "url_pattern": "Webhooks/Auth0/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/invite/{method}",
      "backend": [
        {
          "url_pattern": "invite/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/Register/Companies/{method}",
      "backend": [
        {
          "url_pattern": "Register/Companies/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-Email",
        "X-Sleekflow-Connection-Strategy"
      ],
      "method": "POST"
    }
  ],
  "extra_config": {
    "router": {
      "auto_options": true,
      "hide_version_header": true,
      "logger_skip_paths": [
        "/__health",
        "/v1/tenant-hub/management/Rbac/IsRbacEnabled",
        "/v1/audit-hub/SystemAuditLogs/CreateSystemAuditLog",
        "/v1/user-event-hub/SignalRWebhook",
        "/v1/user-event-hub/Messages/SendMessageToUsers",
        "/v1/user-event-hub/Notifications/SendPushNotification",
        "/v1/user-event-hub/ReliableMessage/negotiate",
        "/v1/flow-hub/internals/CheckWorkflowContactEnrolmentConditionV2"
      ]
    },
    "telemetry/logging": {
      "level": "INFO",
      "prefix": "[KRAKEND]",
      "stdout": true,
      "syslog": false
    }
  },
  "plugin": {
    "folder": "./plugins/",
    "pattern": ".so"
  },
  "timeout": "60000ms",
  "version": 3
}