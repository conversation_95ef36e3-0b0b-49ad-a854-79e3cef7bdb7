﻿using MassTransit;
using Sleekflow.Constants;
using Sleekflow.Events.ServiceBus.HighTrafficServiceBus;

namespace Sleekflow.Events.ServiceBus;

public interface IServiceBusManager
{
    Task PublishAsync<T>(T @event, CancellationToken cancellationToken = default)
        where T : class;

    Task PublishAsync<T>(T @event, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default)
        where T : class;

    Task PublishAsync<T>(
        T @event,
        Type serviceBusType,
        Action<PublishContext<T>>? publishAction = null,
        CancellationToken cancellationToken = default)
        where T : class;

    Task<Response<TResponse>> GetResponseAsync<TRequest, TResponse>(
        TRequest request,
        CancellationToken cancellationToken = default,
        RequestTimeout timeout = default)
        where TRequest : class
        where TResponse : class;
}

public class ServiceBusManager : IServiceBusManager
{
    private readonly IServiceBusProvider _serviceBusProvider;

    public ServiceBusManager(
        IServiceBusProvider serviceBusProvider)
    {
        _serviceBusProvider = serviceBusProvider;
    }

    public async Task PublishAsync<T>(T @event, CancellationToken cancellationToken = default) where T : class
    {
        // Determine the service bus configuration type based on event type
        var serviceBusConfigType = DetermineServiceBusConfigType(typeof(T));

        // Resolve the appropriate Service Bus instance from the provider
        var bus = _serviceBusProvider.GetServiceBus(serviceBusConfigType);

        // Publish the event
        await bus.Publish(@event, cancellationToken);
    }

    public async Task PublishAsync<T>(
        T @event,
        DateTimeOffset scheduledTime,
        CancellationToken cancellationToken = default)
        where T : class
    {
        // Determine the service bus configuration type based on event type
        var serviceBusConfigType = DetermineServiceBusConfigType(typeof(T));

        // Resolve the appropriate Service Bus instance from the provider
        var bus = _serviceBusProvider.GetServiceBus(serviceBusConfigType);
        var universalTime = scheduledTime.UtcDateTime;
        await bus.Publish<T>(@event, cxt => cxt.SetScheduledEnqueueTime(universalTime), cancellationToken);
    }

    public async Task PublishAsync<T>(
        T @event,
        Type serviceBusType,
        Action<PublishContext<T>>? publishAction = null,
        CancellationToken cancellationToken = default)
        where T : class
    {
        // Determine the service bus configuration type based on event type
        var serviceBusConfigType = DetermineServiceBusConfigType(serviceBusType);

        var bus = _serviceBusProvider.GetServiceBus(serviceBusConfigType);
        if (publishAction != null)
        {
            await bus.Publish(
                @event,
                publishAction,
                cancellationToken);
        }
        else
        {
            await bus.Publish(
                @event,
                cancellationToken);
        }
    }

    public async Task<Response<TResponse>> GetResponseAsync<TRequest, TResponse>(
        TRequest request,
        CancellationToken cancellationToken = default,
        RequestTimeout timeout = default)
        where TRequest : class
        where TResponse : class
    {
        // Determine the service bus configuration type for the request
        var serviceBusConfigType = DetermineServiceBusConfigType(typeof(TRequest));

        // Resolve the appropriate Service Bus instance
        var bus = _serviceBusProvider.GetServiceBus(serviceBusConfigType);

        // Resolve the IRequestClient dynamically from the container
        var requestClient = ResolveRequestClient<TRequest, TResponse>(bus);

        // Send the request and wait for the response
        return await requestClient.GetResponse<TResponse>(request, cancellationToken, timeout);
    }

    private IRequestClient<TRequest> ResolveRequestClient<TRequest, TResponse>(IBus bus)
        where TRequest : class
        where TResponse : class
    {
        // Use the built-in MassTransit service provider feature to resolve the IRequestClient
        return bus.CreateRequestClient<TRequest>();
    }

    private string DetermineServiceBusConfigType(Type eventType)
    {
        switch (eventType)
        {
            case Type _ when typeof(IHighTrafficEvent).IsAssignableFrom(eventType):
                return ServiceBusConfigTypes.HighTrafficServiceBus;
            default:
                return ServiceBusConfigTypes.DefaultServiceBus;
        }
    }
}