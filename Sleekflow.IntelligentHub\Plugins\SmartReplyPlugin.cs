using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.IntelligentHub;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Models.Chats;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Utils;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.Plugins;

public interface ISmartReplyPlugin
{
    IAsyncEnumerable<string> SmartReplyStreaming(
        Kernel kernel,
        string companyName,
        string language,
        string sources,
        string chatHistory,
        string additionalPrompt);

    IAsyncEnumerable<string> FactualSmartReplyStreaming(
        Kernel kernel,
        string companyName,
        string language,
        string sources,
        string chatHistory,
        string additionalPrompt);

    Task<(string? SourceStr, IAsyncEnumerable<string> MultiAgentChatStream)> SingleAgentSmartReplyStreaming(
        Kernel kernel,
        string companyName,
        string? sources,
        List<SfChatEntry> sfChatEntries,
        ReplyGenerationContext replyGenerationContext,
        CompanyAgentConfig agentConfig);

    Task<(string? SourceStr, IAsyncEnumerable<string> MultiAgentChatStream, Dictionary<string, int> TokenCounts)>
        ChatAgentSmartReplyStreaming(
            Kernel kernel,
            List<SfChatEntry> sfChatEntries,
            ReplyGenerationContext replyGenerationContext,
            CompanyAgentConfig agentConfig);
}

public class SmartReplyPlugin
    : ISmartReplyPlugin, IScopedService
{
    private readonly KernelFunction _smartReplyFunction;
    private readonly KernelFunction _agentSmartReplyFunction;
    private readonly KernelFunction _factualSmartReplyFunction;

    private readonly ILogger<SmartReplyPlugin> _logger;
    private readonly IGroupChatService _groupChatService;
    private readonly ILanguagePlugin _languagePlugin;
    private readonly ITokenCountingService _tokenCountingService;
    private readonly IAgentDurationTracker _agentDurationTracker;

    public SmartReplyPlugin(
        Kernel kernel,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        ILogger<SmartReplyPlugin> logger,
        IGroupChatService groupChatService,
        ILanguagePlugin languagePlugin,
        ITokenCountingService tokenCountingService,
        IAgentDurationTracker agentDurationTracker)
    {
        _smartReplyFunction = CreateSmartReplyFunction(kernel, promptExecutionSettingsService);
        _agentSmartReplyFunction = CreateAgentSmartReplyFunction(kernel, promptExecutionSettingsService);
        _factualSmartReplyFunction = CreateFactualSmartReplyFunction(kernel, promptExecutionSettingsService);

        _logger = logger;
        _groupChatService = groupChatService;
        _languagePlugin = languagePlugin;
        _tokenCountingService = tokenCountingService;
        _agentDurationTracker = agentDurationTracker;
    }

    public IAsyncEnumerable<string> SmartReplyStreaming(
        Kernel kernel,
        string companyName,
        string language,
        string sources,
        string chatHistory,
        string additionalPrompt)
    {
        var asyncEnumerable = _smartReplyFunction.InvokeStreamingAsync<string>(
            kernel,
            new KernelArguments(_smartReplyFunction.ExecutionSettings!.First().Value)
            {
                {
                    "company_name", companyName
                },
                {
                    "language", language
                },
                {
                    "sources", sources
                },
                {
                    "chat_history", chatHistory
                },
                {
                    "additional_prompt", additionalPrompt
                },
            });

        return asyncEnumerable;
    }

    public IAsyncEnumerable<string> FactualSmartReplyStreaming(
        Kernel kernel,
        string companyName,
        string language,
        string sources,
        string chatHistory,
        string additionalPrompt)
    {
        var asyncEnumerable = _factualSmartReplyFunction.InvokeStreamingAsync<string>(
            kernel,
            new KernelArguments(_factualSmartReplyFunction.ExecutionSettings!.First().Value)
            {
                {
                    "company_name", companyName
                },
                {
                    "language", language
                },
                {
                    "sources", sources
                },
                {
                    "chat_history", chatHistory
                },
                {
                    "additional_prompt", additionalPrompt
                },
            });

        return asyncEnumerable;
    }

    public async
        Task<(string? SourceStr, IAsyncEnumerable<string> MultiAgentChatStream, Dictionary<string, int> TokenCounts)>
        ChatAgentSmartReplyStreaming(
            Kernel kernel,
            List<SfChatEntry> sfChatEntries,
            ReplyGenerationContext replyGenerationContext,
            CompanyAgentConfig agentConfig)
    {
        var sleekflowCompanyId = replyGenerationContext.SleekflowCompanyId;
        var groupChatIdStr = kernel.Data[KernelDataKeys.GROUP_CHAT_ID] as string ?? Guid.NewGuid().ToString();

        string? sourceStr;
        string? reply;
        Dictionary<string, int> tokenCounts;

        try
        {
            (sourceStr, reply) =
                await _groupChatService.HandleMultiAgentChatStream(
                    sleekflowCompanyId,
                    sfChatEntries,
                    agentConfig,
                    replyGenerationContext);
        }
        catch (SfKnowledgeBaseSourceNotFoundException ex)
        {
            throw;
        }
        catch (SfAgentUnableToFindRelevantSourceException ex)
        {
            throw;
        }
        catch (Exception ex)
        {
            sourceStr = string.Empty;
            reply = string.Empty;

            _logger.LogError(ex, "Error in multi-agent chat stream");

#pragma warning disable S6667
            _logger.LogInformation(
                _agentDurationTracker.GetMetricReport(agentConfig.EffectiveCollaborationMode));
#pragma warning restore S6667
        }
        finally
        {
            tokenCounts = _tokenCountingService.GetTokenCounts(groupChatIdStr);

            _logger.LogInformation(
                "Generated an item with the multi-agent chat stream. Tokens: {Tokens}",
                JsonConvert.SerializeObject(
                    new SortedDictionary<string, int>(tokenCounts)));
        }

        return (sourceStr, YieldAsync(), tokenCounts);

        async IAsyncEnumerable<string> YieldAsync()
        {
            yield return reply;
        }
    }

    public async Task<(string? SourceStr, IAsyncEnumerable<string> MultiAgentChatStream)>
        SingleAgentSmartReplyStreaming(
            Kernel kernel,
            string companyName,
            string? sources,
            List<SfChatEntry> sfChatEntries,
            ReplyGenerationContext replyGenerationContext,
            CompanyAgentConfig agentConfig)
    {
        var (_, chatHistoryStr) = SfChatEntryUtils.ToQuestionAndChatHistoryStr(sfChatEntries);

        var detectAppropriateResponseLanguageResponse =
            await _languagePlugin.DetectAppropriateResponseLanguageAsync(kernel, chatHistoryStr);

        var defaultPromptInstruction = new PromptInstruction();
        var promptInstruction = agentConfig.PromptInstruction ?? defaultPromptInstruction;

        return (
            sources,
            _agentSmartReplyFunction.InvokeStreamingAsync<string>(
                kernel,
                new KernelArguments(_agentSmartReplyFunction.ExecutionSettings!.First().Value)
                {
                    {
                        "company_name", companyName
                    },
                    {
                        "language", detectAppropriateResponseLanguageResponse.ResponseLanguageName
                    },
                    {
                        "sources", sources
                    },
                    {
                        "chat_history", chatHistoryStr
                    },
                    {
                        "name", agentConfig.Name
                    },
                    {
                        "tone", promptInstruction.Tone ?? defaultPromptInstruction.Tone!
                    },
                    {
                        "response_length", AgentUtils.GetResponseLength(
                            promptInstruction.ResponseLevel ?? defaultPromptInstruction.ResponseLevel!)
                    },
                    {
                        "restrictiveness_level", AgentUtils.GetAgenticRestrictivenessPrompt(
                            promptInstruction.RestrictivenessLevel ?? defaultPromptInstruction.RestrictivenessLevel!)
                    },
                    {
                        "greeting_message", promptInstruction.GreetingMessage
                    },
                    {
                        "additional_prompt", promptInstruction.AdditionalInstructionCore
                    },
                    {
                        "guardrails", AgentUtils.TranslateGuardrailsToInstructions(promptInstruction.Guardrails)
                    },
                    {
                        "contact_properties", JsonConvert.SerializeObject(
                            replyGenerationContext.ContactProperties ?? new Dictionary<string, string>())
                    },
                }));
    }

    private static KernelFunction CreateSmartReplyFunction(
        Kernel kernel,
        IPromptExecutionSettingsService promptExecutionSettingsService)
    {
        var promptExecutionSettings =
            promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.DEFAULT_SERVICE_ID);

        return kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "SmartReply",
                Description =
                    "Generate a response based on the provided context.",
                Template =
                    """
                    <message role="system">
                    ### Context
                    You are a seasoned customer support agent designed to respond to inquiries from customers about {{$company_name}}.

                    ### Instructions
                    - Always use Markdown when formatting.
                    - For generic conversational inquiries that do not require domain-specific information about {{$company_name}} from the Sources provided below, such as "Hi", "Are you there?", "Are you able to help me with my question?" or "I need some help", you should respond conversationally as if you are a seasoned support agent in a conversational but polite tone. Analyze the conversation flow and user's intent and craft a polite and natural language response that maintains a conversational tone. You can use greetings, small talk, or acknowledge the user's sentiment (for example, for customer inquiries like "Hi, are you there?", a suitable response could be "Hello! Yes, I'm here. How can I assist you today?").
                    - For specific questions or inquiries that require domain knowledge specific to {{$company_name}} and its provided services, such as "Can you tell me more about ABC feature/product", you should consistently verify the accuracy and ensure the response is factually accurate. Make sure the recommended reply is based on the provided conversation context and answer the questions using ONLY facts from the Sources provided below. If relevant information is found, summarize it in a clear, concise, and user-friendly manner. If the Sources provide insufficient information to answer the question, DO NOT make up answers that is not found in the provided sources and refuse to answer in a polite manner.

                    ### Additional Instructions throughout the Chat:
                    - If asking the user a clarifying question would help you provide a more accurate answer, ask the clarifying question.
                    - The sources are provided under Sources below. Each of the source has its filename followed by a colon `:` and the actual information. When using information from the provided sources, always adhere to presenting the information accurately and avoid introducing any information not found within the provided sources.
                    - Respond in the language: [{{$language}}], unless the user has specified which language they want for the answer in their question. Your response should be in accordance with the user's language preference, if provided.
                    - You should strive to present information clearly and concisely so that users can easily grasp the main points. Your answers should be self-contain
                    {{$additional_prompt}}
                    </message>

                    <message role="user">
                    Sources:```
                    [info1.pdf]: Microsoft's cloud revenue for 2022 was $69.9 billion.
                    ```

                    Chat History:```
                    User: What was Microsoft's cloud revenue for 2022?
                    ```
                    </message>
                    <message role="assistant">
                    Based on the sources, Microsoft's cloud revenue for 2022 was $69.9 billion. [info1.pdf]
                    </message>

                    <message role="user">
                    Sources:```
                    [info1.pdf]: Microsoft's cloud revenue for 2022 was $69.9 billion.
                    ```

                    Chat History:```
                    User: What was Microsoft's cloud revenue for 2022?
                    Assistant: Based on the sources, Microsoft's cloud revenue for 2022 was $69.9 billion. [info1.pdf]
                    User: I see. What was the cloud revenue for 2023?
                    ```
                    </message>
                    <message role="assistant">
                    I'm sorry, but my sources do not contain details about Microsoft's cloud revenue in 2023. Is there anything else I can help you with?
                    </message>

                    <message role="user">
                    Sources:```
                    [guide3.pdf]: 香港的米其林指南新增了五間獲得星級的餐廳。
                    [guide4.pdf]: 米其林指南對提升香港的餐飲場景發揮了重要作用，從街頭小吃攤到高檔餐廳，多樣化的餐廳被授予米其林星級。
                    ```

                    Chat History:```
                    User: 2018年香港新增了多少間米其林星級餐廳？
                    Assistant: 對不起，我的來源中沒有提供2018年的相關信息。
                    User: 我明白了。那有沒有米其林對香港的影響？
                    ```
                    </message>
                    <message role="assistant">
                    米其林指南對香港的餐飲場景確實產生了顯著影響。通過對從街頭小吃攤到高檔餐廳的多樣化餐廳的認可，米其林指南不僅提升了這些餐廳的國際知名度，也進一步豐富了香港的美食文化。[guide4.pdf]
                    </message>

                    <message role="user">
                    Sources:```
                    {{$sources}}
                    ```

                    Chat History:```
                    {{$chat_history}}
                    ```
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "company_name", Description = "The name of the company",
                    },
                    new InputVariable
                    {
                        Name = "language", Description = "The language of the response",
                    },
                    new InputVariable
                    {
                        Name = "sources", Description = "The sources",
                    },
                    new InputVariable
                    {
                        Name = "chat_history", Description = "The chat history",
                    },
                    new InputVariable
                    {
                        Name = "additional_prompt", Description = "The additional prompt for modifying the context",
                    },
                },
                OutputVariable = new OutputVariable
                {
                    Description = "A good response",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });
    }

    private static KernelFunction CreateFactualSmartReplyFunction(
        Kernel kernel,
        IPromptExecutionSettingsService promptExecutionSettingsService)
    {
        var promptExecutionSettings =
            promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.DEFAULT_SERVICE_ID);

        return kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "FactualSmartReply",
                Description =
                    "Generate a response based on the provided context.",
                Template =
                    """
                    <message role="system">
                    ### Context
                    Assistant is an intelligent chatbot designed to answer questions from the users about {{$company_Name}}.
                    Answer only questions related to {{$company_Name}} and its provided services.
                    Always focus on providing accurate and factual information.
                    Always use Markdown when formatting.
                    Additional Instructions throughout the Chat:
                    - Answer the questions using ONLY facts from the Sources provided below.
                    - DO NOT make up answers that do not depend on the provided sources.
                    - Respond in the language: [{{$language}}], unless the user has specified which language they want for the answer in their question. Your response should be in accordance with the user's language preference, if provided.
                    - If the Sources below provide insufficient information to answer the question, refuse to answer in a polite manner.
                    - If asking the user a clarifying question would help you provide a more accurate answer, ask the clarifying question.
                    - The sources are provided in the Sources below. Each of the source has a name followed by a colon `:` and the actual information. When using any information from the provided sources, always adhere to the following requirements to accurately cite the sources: 1. Include the source name to present any facts or details obtained from that source; 2. Enclose the source name within square brackets (e.g., [info1.txt]) to clearly indicate where the information was obtained; 3. If multiple sources need to be referenced for different pieces of information, list each source separately and do not combine them (e.g., [info1.txt][info2.pdf]).
                    - You should strive to present information clearly and concisely so that users can easily grasp the main points. Your answers should be self-contained and comprehensive, providing all the necessary details to help users understand the topic without requiring them to examine it themselves or be redirected to external resources.
                    {{$additional_prompt}}
                    </message>

                    <message role="user">
                    Sources:```
                    [info1.pdf]: Microsoft's cloud revenue for 2022 was $69.9 billion.
                    ```

                    Chat History:```
                    User: What was Microsoft's cloud revenue for 2022?
                    ```
                    </message>
                    <message role="assistant">
                    Based on the sources, Microsoft's cloud revenue for 2022 was $69.9 billion. [info1.pdf]
                    </message>

                    <message role="user">
                    Sources:```
                    [info1.pdf]: Microsoft's cloud revenue for 2022 was $69.9 billion.
                    ```

                    Chat History:```
                    User: What was Microsoft's cloud revenue for 2022?
                    Assistant: Based on the sources, Microsoft's cloud revenue for 2022 was $69.9 billion. [info1.pdf]
                    User: I see. What was the cloud revenue for 2023?
                    ```
                    </message>
                    <message role="assistant">
                    I'm sorry, but my sources do not contain details about Microsoft's cloud revenue in 2023. Is there anything else I can help you with?
                    </message>

                    <message role="user">
                    Sources:```
                    [guide3.pdf]: 香港的米其林指南新增了五間獲得星級的餐廳。
                    [guide4.pdf]: 米其林指南對提升香港的餐飲場景發揮了重要作用，從街頭小吃攤到高檔餐廳，多樣化的餐廳被授予米其林星級。
                    ```

                    Chat History:```
                    User: 2018年香港新增了多少間米其林星級餐廳？
                    Assistant: 對不起，我的來源中沒有提供2018年的相關信息。
                    User: 我明白了。那有沒有米其林對香港的影響？
                    ```
                    </message>
                    <message role="assistant">
                    米其林指南對香港的餐飲場景確實產生了顯著影響。通過對從街頭小吃攤到高檔餐廳的多樣化餐廳的認可，米其林指南不僅提升了這些餐廳的國際知名度，也進一步豐富了香港的美食文化。[guide4.pdf]
                    </message>

                    <message role="user">
                    Sources:```
                    {{$sources}}
                    ```

                    Chat History:```
                    {{$chat_history}}
                    ```
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "company_name", Description = "The name of the company",
                    },
                    new InputVariable
                    {
                        Name = "language", Description = "The language of the response",
                    },
                    new InputVariable
                    {
                        Name = "additional_prompt", Description = "The additional prompt for modifying the context",
                    },
                    new InputVariable
                    {
                        Name = "sources", Description = "The sources",
                    },
                    new InputVariable
                    {
                        Name = "chat_history", Description = "The chat history",
                    },
                },
                OutputVariable = new OutputVariable
                {
                    Description = "A good response",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });
    }

    private static KernelFunction CreateAgentSmartReplyFunction(
        Kernel kernel,
        IPromptExecutionSettingsService promptExecutionSettingsService)
    {
        var promptExecutionSettings =
            promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.DEFAULT_SERVICE_ID);

        return kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "AgentSmartReply",
                Description =
                    "Generate a response based on the provided context.",
                Template =
                    """
                    <message role="system">
                    ### Context
                    You are a seasoned customer support agent designed to respond to inquiries from customers about {{$company_name}}. Your name is {{$name}}.

                    ### Instructions
                    - Always use Markdown when formatting.
                    - For generic conversational inquiries that do not require domain-specific information about {{$company_name}} from the Sources provided below, such as "Hi", "Are you there?", "Are you able to help me with my question?" or "I need some help", you should respond conversationally as if you are a seasoned support agent in a conversational but polite tone. Analyze the conversation flow and user's intent and craft a polite and natural language response that maintains a conversational tone. You can use greetings, small talk, or acknowledge the user's sentiment (for example, for customer inquiries like "Hi, are you there?", a suitable response could be "Hello! Yes, I'm here. How can I assist you today?").

                    ### Additional Instructions throughout the Chat:
                    - If asking the user a clarifying question would help you provide a more accurate answer, ask the clarifying question.
                    - The sources are provided under Sources below. Each of the source has its filename followed by a colon `:` and the actual information.
                    - Respond in the language: [{{$language}}], unless the user has specified which language they want for the answer in their question. Your response should be in accordance with the user's language preference, if provided.
                    - You should strive to present information clearly and concisely so that users can easily grasp the main points. Your answers should be self-containing.
                    - If the user provides contact properties, you should consider these properties in your responses. For example, if the user's name is provided, you can use it in your responses to personalize the interaction.
                    - The response tone should be polite, professional, and helpful. Always maintain a positive and supportive tone in your responses. unless the user has specified the about {{$tone}} they want for the answer in their question. Your response should be in accordance with the user's tone preference, if provided.
                    - If the user provides a response length, you should ensure that your response does not exceed the maximum word count specified by the user.
                    - Restrictiveness level on the knowledge sources can be either "normal" or "relaxed":
                        "normal": For specific questions or inquiries that require domain knowledge specific to {{$company_name}} and its provided services, such as "Can you tell me more about ABC feature/product", you should consistently verify the accuracy and ensure the response is factually accurate. Make sure the recommended reply is based on the provided conversation context and answer the questions using ONLY facts from the Sources provided below. If relevant information is found, summarize it in a clear, concise, and user-friendly manner. If the Sources provide insufficient information to answer the question, DO NOT make up answers that is not found in the provided sources and refuse to answer in a polite manner.
                        "relaxed": You must try your best to include relevant information from ALL available sources, including the Internet. Do not limit yourself to only data in the provided sources.
                    - {{$additional_prompt}}
                    ###
                    </message>

                    <message role="user">
                    Sources:```
                    [info1.pdf]: Microsoft's cloud revenue for 2022 was $69.9 billion.
                    ```

                    Tone:```
                    Professional
                    ```

                    Response Length:```
                    100
                    ```

                    Restrictiveness Level:```
                    normal
                    ```

                    Contact Properties:
                    ```
                    {"user_name": "John Doe", "email": "<EMAIL>", "phone": "+852 1234 5678", "company": "Sleekflow", "position": "Marketing Manager"}
                    ```

                    Greeting Message:```
                    Hi there! I am {{$name}}, your customer support agent. How can I assist you today?
                    ```

                    Chat History:```
                    User: What was Microsoft's cloud revenue for 2022?
                    ```
                    </message>
                    <message role="assistant">
                    Based on the sources, Microsoft's cloud revenue for 2022 was $69.9 billion. [info1.pdf]
                    </message>

                    <message role="user">
                    Tone:```
                    Professional
                    ```

                    Response Level:```
                    100
                    ```

                    Restrictiveness Level:```
                    normal
                    ```

                    Greeting Message:```
                    Hi there! I am {{$name}}, your customer support agent. How can I assist you today?
                    ```

                    Sources:```
                    [info1.pdf]: Microsoft's cloud revenue for 2022 was $69.9 billion.
                    ```

                    Chat History:```
                    User: What was Microsoft's cloud revenue for 2022?
                    Assistant: Hi there! I am {{$name}}, your customer support agent. Based on the sources, Microsoft's cloud revenue for 2022 was $69.9 billion. [info1.pdf]
                    User: I see. What was the cloud revenue for 2023?
                    ```
                    </message>
                    <message role="assistant">
                    I'm sorry, but my sources do not contain details about Microsoft's cloud revenue in 2023. Is there anything else I can help you with?
                    </message>

                    <message role="user">
                    Tone:```
                    Professional
                    ```

                    Response Length:```
                    100
                    ```

                    Restrictiveness Level:```
                    normal
                    ```

                    Contact Properties:
                    ```
                    {"user_name": "John Doe", "email": "<EMAIL>", "phone": "+852 1234 5678", "company": "Sleekflow", "position": "Marketing Manager"}
                    ```

                    Greeting Message:```
                    Hi there! I am {{$name}}, your customer support agent. How can I assist you today?
                    ```

                    Sources:```
                    [guide3.pdf]: 香港的米其林指南新增了五間獲得星級的餐廳。
                    [guide4.pdf]: 米其林指南對提升香港的餐飲場景發揮了重要作用，從街頭小吃攤到高檔餐廳，多樣化的餐廳被授予米其林星級。
                    ```

                    Chat History:```
                    User: 2018年香港新增了多少間米其林星級餐廳？
                    Assistant: 對不起，我的來源中沒有提供2018年的相關信息。
                    User: 我明白了。那有沒有米其林對香港的影響？
                    ```
                    </message>
                    <message role="assistant">
                    米其林指南對香港的餐飲場景確實產生了顯著影響。通過對從街頭小吃攤到高檔餐廳的多樣化餐廳的認可，米其林指南不僅提升了這些餐廳的國際知名度，也進一步豐富了香港的美食文化。[guide4.pdf]
                    </message>

                    <message role="user">
                    Tone:```
                    Professional
                    ```

                    Response Length:```
                    100
                    ```

                    Restrictiveness Level:```
                    relaxed
                    ```

                    Contact Properties:
                    ```
                    {"user_name": "John Doe", "email": "<EMAIL>", "phone": "+852 1234 5678", "company": "Sleekflow", "position": "Marketing Manager"}
                    ```

                    Greeting Message:```
                    Hi there! I am {{$name}}, your customer support agent. How can I assist you today?
                    ```

                    Sources:```
                    [guide3.pdf]: 香港的米其林指南新增了五間獲得星級的餐廳。
                    [guide4.pdf]: 米其林指南對提升香港的餐飲場景發揮了重要作用，從街頭小吃攤到高檔餐廳，多樣化的餐廳被授予米其林星級。
                    ```

                    Chat History:```
                    User: How many children does Elon Musk have?
                    ```
                    </message>
                    <message role="assistant">
                    Elon Musk has fathered 12 children with three different women. One of his children died at 10 weeks old as a result of SIDS (Sudden Infant Death Syndrome). His surviving children range in age from newborn to about 20 years old.
                    </message>

                    <message role="user">
                    Tone:```
                    {{$tone}}
                    ```

                    Response Length:```
                    {{$response_length}}
                    ```

                    Restrictiveness Level:```
                    {{$restrictiveness_level}}
                    ```

                    Contact Properties:
                    ```
                    {{$contact_properties}}
                    ```

                    Greeting Message:```
                    {{$greeting_message}}
                    ```

                    Sources:```
                    {{$sources}}
                    ```

                    Chat History:```
                    {{$chat_history}}
                    ```
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "company_name", Description = "The name of the company",
                    },
                    new InputVariable
                    {
                        Name = "language", Description = "The language of the response",
                    },
                    new InputVariable
                    {
                        Name = "sources", Description = "The sources",
                    },
                    new InputVariable
                    {
                        Name = "chat_history", Description = "The chat history",
                    },
                    new InputVariable
                    {
                        Name = "name", Description = "The name of the agent",
                    },
                    new InputVariable
                    {
                        Name = "tone", Description = "The tone of the response",
                    },
                    new InputVariable
                    {
                        Name = "response_length", Description = "The length of the response",
                    },
                    new InputVariable
                    {
                        Name = "restrictiveness_level",
                        Description = "The level of restrictiveness of the knowledge source",
                    },
                    new InputVariable
                    {
                        Name = "greeting_message", Description = "The greeting message",
                    },
                    new InputVariable
                    {
                        Name = "additional_prompt", Description = "The additional prompt for modifying the context",
                    },
                    new InputVariable
                    {
                        Name = "contact_properties", Description = "The contact properties",
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "A good response",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });
    }
}