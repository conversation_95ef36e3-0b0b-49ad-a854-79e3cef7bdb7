﻿using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.Statistics;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;

[Resolver(typeof(IIntelligentHubDbResolver))]
[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.FileDocument)]
public class FileDocument : KbDocument
{
    public const string PropertyNameBlobId = "blob_id";
    public const string PropertyNameFileName = "file_name";
    public const string PropertyNameBlobType = "blob_type";
    public const string PropertyNameDocumentStatistics = "document_statistics";
    public const string PropertyNameLanguageIsoCode = "language_iso_code";

    [JsonProperty(PropertyNameBlobId)]
    public string BlobId { get; set; }

    [JsonProperty(PropertyNameFileName)]
    public string FileName { get; set; }

    [JsonProperty(PropertyNameBlobType)]
    public string BlobType { get; set; }

    [JsonProperty(PropertyNameDocumentStatistics)]
    public DocumentStatistics DocumentStatistics { get; set; }

    [JsonProperty(PropertyNameLanguageIsoCode)]
    public string LanguageIsoCode { get; set; }

    [JsonConstructor]
    public FileDocument(
        string id,
        string sleekflowCompanyId,
        string blobId,
        string blobType,
        string contentType,
        DocumentStatistics documentStatistics,
        Dictionary<string, object?> metadata,
        string languageIsoCode,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string fileDocumentProcessStatus,
        double? fileDocumentProcessPercentage,
        string fileName,
        string uploadedBy,
        List<AgentAssignment>? agentAssignments,
        DebugTimestamps? debugTimestamps)
        : base(
            id,
            sleekflowCompanyId,
            contentType,
            metadata,
            fileDocumentProcessStatus,
            fileDocumentProcessPercentage,
            agentAssignments,
            createdAt,
            updatedAt,
            uploadedBy,
            debugTimestamps,
            SysTypeNames.FileDocument)
    {
        BlobId = blobId;
        BlobType = blobType;
        LanguageIsoCode = languageIsoCode;
        DocumentStatistics = documentStatistics;
        FileName = fileName;
    }

    public override int GetCharacterCount()
    {
        return DocumentStatistics.TotalCharacters;
    }
}