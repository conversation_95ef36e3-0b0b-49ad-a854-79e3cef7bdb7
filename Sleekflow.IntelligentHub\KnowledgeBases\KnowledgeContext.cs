﻿namespace Sleekflow.IntelligentHub.KnowledgeBases;

public class KnowledgeContext
{
    public string Query { get; set; }

    public string KnowledgeSource { get; set; }

    public List<string> ExtractedChunks { get; set; }

    public KnowledgeContext(string query, string knowledgeSource, List<string> extractedChunks)
    {
        Query = query;
        KnowledgeSource = knowledgeSource;
        ExtractedChunks = extractedChunks;
    }
}