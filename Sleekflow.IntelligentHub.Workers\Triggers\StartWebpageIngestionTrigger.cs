using Azure.Messaging.ServiceBus;
using MassTransit;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.IntelligentHub.Workers.Orchestrators;

namespace Sleekflow.IntelligentHub.Workers.Triggers;

public class StartWebpageIngestionTrigger
{
    private readonly ILogger<StartWebpageIngestionTrigger> _logger;
    private const string QueueName = "start-webpage-ingestion-event";
    private readonly IMessageReceiver _messagereceiver;

    public StartWebpageIngestionTrigger(
        ILogger<StartWebpageIngestionTrigger> logger,
        IMessageReceiver messagereceiver)
    {
        _logger = logger;
        _messagereceiver = messagereceiver;
    }

    [Function(nameof(StartWebpageIngestionTrigger))]
    public async Task RunAsync(
        [ServiceBusTrigger(QueueName, Connection = "SERVICE_BUS_CONN_STR")]
        ServiceBusReceivedMessage message,
        [DurableClient]
        DurableTaskClient starter,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("StartWebpageIngestionTrigger message: {Message}", message.Body.ToString());

            // Parse the full message to extract the nested "message" property
            var fullMessage = JObject.Parse(message.Body.ToString());
            var messageContent = fullMessage["message"]?.ToString();

            _logger.LogInformation(
                "StartWebpageIngestionTrigger message content: {Message}",
                messageContent);

            if (string.IsNullOrEmpty(messageContent))
            {
                _logger.LogError("Message content is missing or empty in the received message");
                return;
            }

            var input = JsonConvert.DeserializeObject<StartWebpageIngestionEvent>(messageContent);
            if (input == null)
            {
                _logger.LogError("Failed to deserialize StartWebpageIngestionEvent");
                return;
            }

            var instanceId = await starter.ScheduleNewOrchestrationInstanceAsync(
                nameof(StartWebpageIngestionOrchestrator),
                input);

            _logger.LogInformation($"Started StartWebpageIngestionOrchestrator with ID = [{instanceId}]");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Service Bus message for webpage ingestion");
            throw;
        }
    }
}