using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Documents.WebpageDocuments;

public interface IWebpageDocumentChunkRepository : IRepository<WebpageDocumentChunk>
{
    Task<List<string>> GetWebpageDocumentChunkIdsAsync(string sleekflowCompanyId, string documentId);
}

public class WebpageDocumentChunkRepository
    : BaseRepository<WebpageDocumentChunk>, IScopedService, IWebpageDocumentChunkRepository
{
    public WebpageDocumentChunkRepository(
        ILogger<WebpageDocumentChunkRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public async Task<List<string>> GetWebpageDocumentChunkIdsAsync(string sleekflowCompanyId, string documentId)
    {
        var webpageDocumentChunks = await GetObjectsAsync(
            e =>
                e.SleekflowCompanyId == sleekflowCompanyId
                && e.DocumentId == documentId);
        return webpageDocumentChunks.Select(e => e.Id).ToList();
    }
}