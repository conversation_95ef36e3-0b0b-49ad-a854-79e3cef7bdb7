using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Sleekflow.FlowHub.Models.Workers;
using Sleekflow.FlowHub.Workers.Utils;

namespace Sleekflow.FlowHub.Workers.Triggers;

public class ExecuteTryCatchStep
{
    private readonly ILogger<ExecuteTryCatchStep> _logger;

    public ExecuteTryCatchStep(
        ILogger<ExecuteTryCatchStep> logger)
    {
        _logger = logger;
    }

    [Function("ExecuteTryCatchStep")]
    public async Task<IActionResult> RunAsync(
        [HttpTrigger(AuthorizationLevel.Function, "post")]
        HttpRequest req,
        [DurableClient]
        DurableTaskClient starter)
    {
        return await Func.Run2Async<ExecuteTryCatchStepInput, HttpManagementPayload, DurableTaskClient>(
            req,
            _logger,
            starter,
            F);
    }

    private async Task<HttpManagementPayload> F(
        (ExecuteTryCatchStepInput Input, ILogger Logger, DurableTaskClient Starter) tuple)
    {
        var (executeTryCatchStepInput, logger, starter) = tuple;

        var instanceId = await starter.ScheduleNewOrchestrationInstanceAsync(
            "ExecuteTryCatchStep_Orchestrator",
            executeTryCatchStepInput);

        logger.LogInformation($"Started ExecuteTryCatchStep_Orchestrator with ID = [{instanceId}]");

        var httpManagementPayload = starter.CreateHttpManagementPayload(instanceId);
        if (httpManagementPayload == null)
        {
            throw new Exception("Unable to get ExecuteTryCatchStep_Orchestrator httpManagementPayload");
        }

        return httpManagementPayload;
    }
}