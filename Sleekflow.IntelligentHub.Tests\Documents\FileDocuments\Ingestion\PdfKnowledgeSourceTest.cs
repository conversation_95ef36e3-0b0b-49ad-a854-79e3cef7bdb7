using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Moq;
using Sleekflow.IntelligentHub.Documents.FileDocuments.Ingestion;
using Sleekflow.IntelligentHub.Documents.Statistics;
using Sleekflow.IntelligentHub.Models.Workers.FileIngestion;
using Sleekflow.Mvc.Tests;

namespace Sleekflow.IntelligentHub.Tests.Documents.FileDocuments.Ingestion;

[TestFixture]
[TestOf(typeof(PdfKnowledgeSourceTest))]
public class PdfKnowledgeSourceTest
{
    // Relative path from the test execution directory to the Binaries folder
    private const string PdfFilePath = "../../../Binaries/G2ZF03-001_20250307_202503070952.pdf";

    private static readonly string[] BigPdfFilePaths =
    [
        "../../../Binaries/FINAL A5 PG Prospectus UoC CCC 2024-14 MAY 2024-lowres.pdf",
        "../../../Binaries/Prospectus - Undergraduate 2025.pdf",
        "../../../Binaries/GymPanda.pdf",
        "../../../Binaries/Healthy Panda Eats.pdf",
        "../../../Binaries/Zenith Wellness Clinic.pdf",
    ];

    private const string MultiTablesPdfFilePath =
        "../../../Binaries/Products Price List  [ MASTER DOC ] - Cash Sale - Mar2025(1).pdf";

    private const string TestTranslationPdfFilePath =
        "../../../Binaries/CPFA05-003_20250506_202505062147.pdf";

    [SetUp]
    public void Setup()
    {
        if (BaseTestHost.IsGithubAction)
        {
            Assert.Ignore("Test takes too long in git action");
        }
    }

    [Test]
    public async Task PdfKnowledgeSourceIngestTest()
    {
        using var scope = Application.Host.Services.CreateScope();
        var kernel = scope.ServiceProvider.GetRequiredService<Kernel>();
        var logger = new Mock<ILogger<PdfKnowledgeSource>>().Object;

        // Setup mock for document statistics calculator
        var mockDocumentCounterService = new Mock<IDocumentCounterService>();
        var documentStatisticsCalculatorFactory =
            new DocumentStatisticsCalculatorFactory(mockDocumentCounterService.Object);

        var pdfKnowledgeSource = new PdfKnowledgeSource(
            logger,
            kernel,
            documentStatisticsCalculatorFactory);

        var allMarkdowns = new List<string>();
        IFileIngestionProgress? progress = null;

        do
        {
            await using var fileStream = new FileStream(
                TestTranslationPdfFilePath,
                FileMode.Open,
                FileAccess.Read,
                FileShare.Read);

            var (markdowns, updatedProgress) = await pdfKnowledgeSource.Ingest(fileStream, progress);

            // Add new markdowns to our collection
            allMarkdowns.AddRange(markdowns);

            // Update progress for next iteration
            progress = updatedProgress;

            // Continue until all pages are processed
        }
        while (!progress.IsCompleted());

        Assert.That(allMarkdowns, Is.Not.Empty);
        Assert.That(progress.IsCompleted(), Is.True);
        Assert.That(progress.GetProgressPercentage(), Is.EqualTo(100.0));
    }

    private static IEnumerable<string> GetBigPdfFilePaths()
    {
        return BigPdfFilePaths;
    }

    [TestCaseSource(nameof(GetBigPdfFilePaths))]
    [Parallelizable(ParallelScope.Children)]
    public async Task PdfKnowledgeSourceIngestTest_BigPdf(string pdfFilePath)
    {
        using var scope = Application.Host.Services.CreateScope();
        var kernel = scope.ServiceProvider.GetRequiredService<Kernel>();
        var logger = new Mock<ILogger<PdfKnowledgeSource>>().Object;

        // Setup mock for document statistics calculator
        var mockDocumentCounterService = new Mock<IDocumentCounterService>();
        var documentStatisticsCalculatorFactory =
            new DocumentStatisticsCalculatorFactory(mockDocumentCounterService.Object);

        var pdfKnowledgeSource = new PdfKnowledgeSource(
            logger,
            kernel,
            documentStatisticsCalculatorFactory);

        var allMarkdowns = new List<string>();
        IFileIngestionProgress? progress = null;

        do
        {
            await using var fileStream = new FileStream(
                pdfFilePath,
                FileMode.Open,
                FileAccess.Read,
                FileShare.Read);

            var (markdowns, updatedProgress) = await pdfKnowledgeSource.Ingest(fileStream, progress);

            // Add new markdowns to our collection
            allMarkdowns.AddRange(markdowns);

            // Update progress for next iteration
            progress = updatedProgress;

            // Continue until all pages are processed
        }
        while (!progress.IsCompleted());

        Assert.That(allMarkdowns, Is.Not.Empty);
        Assert.That(progress.IsCompleted(), Is.True);
        Assert.That(progress.GetProgressPercentage(), Is.EqualTo(100.0));
    }

    [Test]
    public async Task MultiTablePdfKnowledgeSourceIngestTest()
    {
        using var scope = Application.Host.Services.CreateScope();
        var kernel = scope.ServiceProvider.GetRequiredService<Kernel>();
        var logger = new Mock<ILogger<PdfKnowledgeSource>>().Object;

        // Setup mock for document statistics calculator
        var mockDocumentCounterService = new Mock<IDocumentCounterService>();
        var documentStatisticsCalculatorFactory =
            new DocumentStatisticsCalculatorFactory(mockDocumentCounterService.Object);

        var pdfKnowledgeSource = new PdfKnowledgeSource(
            logger,
            kernel,
            documentStatisticsCalculatorFactory);

        var allMarkdowns = new List<string>();
        IFileIngestionProgress? progress = null;

        do
        {
            await using var fileStream = new FileStream(
                MultiTablesPdfFilePath,
                FileMode.Open,
                FileAccess.Read,
                FileShare.Read);

            var (markdowns, updatedProgress) = await pdfKnowledgeSource.Ingest(fileStream, progress);

            // Add new markdowns to our collection
            allMarkdowns.AddRange(markdowns);

            // Update progress for next iteration
            progress = updatedProgress;

            // Continue until all pages are processed
        }
        while (!progress.IsCompleted());

        Assert.That(allMarkdowns, Is.Not.Empty);
        Assert.That(progress.IsCompleted(), Is.True);
        Assert.That(progress.GetProgressPercentage(), Is.EqualTo(100.0));
    }
}