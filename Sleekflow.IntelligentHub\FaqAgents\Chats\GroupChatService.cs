using System.Diagnostics.CodeAnalysis;
using DocumentFormat.OpenXml.Office2010.ExcelAc;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Microsoft.SemanticKernel.Agents.Chat;
using Microsoft.SemanticKernel.Agents.Orchestration.GroupChat;
using Microsoft.SemanticKernel.Agents.Runtime.InProcess;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.IntelligentHub;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.Faq;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.ManagerLeadNurturings;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.ReActs;
using Sleekflow.IntelligentHub.FaqAgents.Chats.Enrichers;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.IntelligentHub.Plugins.Knowledges;
using Sleekflow.IntelligentHub.Services;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats;

public interface IGroupChatService
{
    Task<(string SourceStr, string Reply)> HandleMultiAgentChatStream(
        string sleekflowCompanyId,
        List<SfChatEntry> chatEntries,
        CompanyAgentConfig agentConfig,
        ReplyGenerationContext replyGenerationContext);
}

public class GroupChatService : IGroupChatService, IScopedService
{
    private readonly Kernel _kernel;
    private readonly ILogger _logger;
    private readonly IAgentCollaborationDefinitionService _agentCollaborationDefinitionService;
    private readonly IAgentDurationTracker _agentDurationTracker;
    private readonly IUserInteractionTrackingService _userInteractionTracker;
    private readonly IPhaticPlugin _phaticPlugin;
    private readonly ISourceOnlyKnowledgePlugin _sourceOnlyKnowledgePlugin;

    private const string KnowledgeNotFoundResponse = "Sorry, I'm not able to provide an answer to that question.";

    public GroupChatService(
        Kernel kernel,
        ILogger<GroupChatService> logger,
        IAgentCollaborationDefinitionService agentCollaborationDefinitionService,
        IAgentDurationTracker agentDurationTracker,
        IUserInteractionTrackingService userInteractionTracker,
        IPhaticPlugin phaticPlugin,
        ISourceOnlyKnowledgePlugin sourceOnlyKnowledgePlugin)
    {
        _logger = logger;
        _agentCollaborationDefinitionService = agentCollaborationDefinitionService;
        _agentDurationTracker = agentDurationTracker;
        _kernel = kernel;
        _userInteractionTracker = userInteractionTracker;
        _phaticPlugin = phaticPlugin;
        _sourceOnlyKnowledgePlugin = sourceOnlyKnowledgePlugin;
    }

    [Experimental("SKEXP0070")]
    public async Task<(string SourceStr, string Reply)> HandleMultiAgentChatStream(
        string sleekflowCompanyId,
        List<SfChatEntry> chatEntries,
        CompanyAgentConfig agentConfig,
        ReplyGenerationContext replyGenerationContext)
    {
        var groupChatIdStr = (_kernel.Data[KernelDataKeys.GROUP_CHAT_ID] as string)!;

        // Track user message for follow-up messaging logic
        // Get the latest user message from chatEntries (last entry should be the current user message)
        var latestUserMessage = chatEntries?.LastOrDefault()?.User;
        if (!string.IsNullOrEmpty(latestUserMessage))
        {
            try
            {
                // Parse groupChatId to extract objectId for tracking
                var groupChatId = GroupChatId.Parse(groupChatIdStr);
                await _userInteractionTracker.RecordUserMessageAsync(
                    sleekflowCompanyId,
                    groupChatId.ObjectId,
                    latestUserMessage);
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Failed to track user message for follow-up logic in GroupChatService");
                // Don't fail the main chat processing if tracking fails
            }
        }

        // Select agent definition
        var agentCollaborationDefinition =
            _agentCollaborationDefinitionService.GetAgentCollaborationDefinition(
                agentConfig.EffectiveCollaborationMode);
        if (agentCollaborationDefinition is LeadNurturingAgentCollaborationDefinition
            || agentCollaborationDefinition is ManagerLeadNurturingCollaborationDefinition)
        {
            _kernel.Data[KernelDataKeys.LEAD_NURTURING_TOOLS_CONFIG] =
                agentConfig.LeadNurturingTools;
            _kernel.Data[KernelDataKeys.TOOLS_CONFIG] =
                agentConfig.ToolsConfig;
            _kernel.Data[KernelDataKeys.WEB_SEARCH_PLUGIN_CONFIG] =
                agentConfig.KnowledgeRetrievalConfig?.WebSearch;
            _kernel.Data[KernelDataKeys.STATIC_SEARCH_PLUGIN_CONFIG] =
                agentConfig.KnowledgeRetrievalConfig?.StaticSearch;
        }
        else if (agentCollaborationDefinition is IReActAgentCollaborationDefinition)
        {
            _kernel.Data[KernelDataKeys.TOOLS_CONFIG] =
                agentConfig.ToolsConfig;
            _kernel.Data[KernelDataKeys.WEB_SEARCH_PLUGIN_CONFIG] =
                agentConfig.KnowledgeRetrievalConfig?.WebSearch;
            _kernel.Data[KernelDataKeys.STATIC_SEARCH_PLUGIN_CONFIG] =
                agentConfig.KnowledgeRetrievalConfig?.StaticSearch;
        }

        var defaultPromptInstruction = new PromptInstruction();
        var promptInstruction = agentConfig.PromptInstruction ?? defaultPromptInstruction;

        // Create a copy of the enricher configs from agent config
        var enricherConfigs = new List<EnricherConfig>(agentConfig.EnricherConfigs);

        // Add ContactPropertiesEnricher if it doesn't already exist in the list
        if (agentCollaborationDefinition is LeadNurturingAgentCollaborationDefinition
            && !enricherConfigs.Any(e => e.Type.Equals("contact_properties", StringComparison.OrdinalIgnoreCase)))
        {
            enricherConfigs.Insert(
                0,
                new EnricherConfig
                {
                    Type = "contact_properties",
                    IsEnabled = true,
                    Parameters = new Dictionary<string, string>()
                    {
                        {
                            ContactPropertiesEnricherConstants.PERMITTED_CONTACT_PROPERTIES_KEY,
                            JsonConvert.SerializeObject(
                                new List<PermittedContactProperty>
                                {
                                    new ("firstName", "The customer's first name"),
                                    new ("lastName", "The customer's last name"),
                                    new ("PhoneNumber", "The customer's phone number"),
                                    new ("Email", "The customer's email address"),
                                })
                        }
                    }
                });
        }

        var agentCollaborationConfig = new AgentCollaborationConfig(
            promptInstruction.Tone ?? defaultPromptInstruction.Tone!,
            promptInstruction.RestrictivenessLevel ?? defaultPromptInstruction.RestrictivenessLevel!,
            "English",
            promptInstruction.AdditionalInstructionCore,
            promptInstruction.AdditionalInstructionStrategy,
            promptInstruction.AdditionalInstructionResponse,
            enricherConfigs,
            agentConfig.LeadNurturingTools?.IsFollowUpAgentEnabled ?? false,
            agentConfig.IsTranscriptionEnabled);

        BaseInternalAgentCreationConfiguration? internalAgentCreationConfiguration = null;

        if (agentCollaborationDefinition is FaqAgentCollaborationDefinition)
        {
            internalAgentCreationConfiguration = await GetFaqInternalAgentCreationConfiguration(
                _kernel,
                chatEntries!,
                agentConfig.PromptInstruction?.GreetingMessage);

            if (internalAgentCreationConfiguration is FaqInternalAgentCreationConfiguration
                {
                    PhaticResult.IsPhaticOnly: true
                } configuration)
            {
                return (string.Empty, configuration.PhaticResult.ReplyToCustomer);
            }
        }

        // Create agents dynamically
        var agents = await agentCollaborationDefinition.CreateAgents(
            _kernel,
            chatEntries,
            sleekflowCompanyId,
            agentCollaborationConfig,
            internalAgentCreationConfiguration);

        // Track duration of agent responses
        _agentDurationTracker.StartTracking();

        var groupChatManager = agentCollaborationDefinition.GetGroupChatManager();
        if (groupChatManager is not null)
        {
            ChatHistory history = [];

            // Initialize chat history
            var (chatHistoryStr, context) = await agentCollaborationDefinition.InitializeChatHistoryAsync(
                null,
                groupChatIdStr,
                chatEntries,
                replyGenerationContext,
                agentCollaborationConfig,
                agentConfig);

            var orchestration = new GroupChatOrchestration(
                groupChatManager,
                agents.ToArray())
            {
                ResponseCallback = async (response) =>
                {
                    await agentCollaborationDefinition.InterceptAgentReplyAsync(response, groupChatIdStr);

                    history.Add(response);
                }
            };

            var runtime = new InProcessRuntime();
            await runtime.StartAsync();

            var result = await orchestration.InvokeAsync(
                context,
                runtime);

            var output = await result.GetValueAsync(TimeSpan.FromSeconds(120));

            _logger.LogInformation("result: {Result}", result);
            _logger.LogInformation("output: {Output}", output);

            var sourceStr = await agentCollaborationDefinition.GetSourceAsync(history, groupChatIdStr);
            var finalReply = await agentCollaborationDefinition.GetFinalReplyAsync(history);

            return (sourceStr, finalReply);
        }
        else
        {
            var selectionStrategy =
                agentCollaborationDefinition.CreateSelectionStrategy(_kernel)
                ?? throw new NotImplementedException();
            var terminationStrategy =
                agentCollaborationDefinition.CreateTerminationStrategy(agents)
                ?? throw new NotImplementedException();

            // Configure group chat with dynamic settings
            var agentGroupChat = new AgentGroupChat(agents.ToArray())
            {
                ExecutionSettings = new AgentGroupChatSettings
                {
                    SelectionStrategy = selectionStrategy, TerminationStrategy = terminationStrategy,
                }
            };

            // Initialize chat history
            await agentCollaborationDefinition.InitializeChatHistoryAsync(
                agentGroupChat,
                groupChatIdStr,
                chatEntries,
                replyGenerationContext,
                agentCollaborationConfig,
                agentConfig);

            // Process chat responses
            await foreach (var response in agentGroupChat.InvokeAsync())
            {
                var shouldContinue =
                    await agentCollaborationDefinition.InterceptAgentReplyAsync(response, groupChatIdStr);
                if (!shouldContinue)
                {
                    return (string.Empty, string.Empty);
                }
            }

            // Log the final results
            _logger.LogInformation(
                _agentDurationTracker.GetMetricReport(agentConfig.EffectiveCollaborationMode));

            var chatHistory = await agentGroupChat.GetChatMessagesAsync().ToListAsync();
            var sourceStr = await agentCollaborationDefinition.GetSourceAsync(
                new ChatHistory(chatHistory),
                groupChatIdStr);
            var finalReply = await agentCollaborationDefinition.GetFinalReplyAsync(new ChatHistory(chatHistory));

            if (!agentGroupChat.IsComplete)
            {
                _logger.LogWarning("No consensus reached. Using last reply: {FinalReply}", finalReply);
            }

            return (sourceStr, finalReply);
        }
    }

    private async Task<FaqInternalAgentCreationConfiguration> GetFaqInternalAgentCreationConfiguration(
        Kernel kernel,
        List<SfChatEntry> chatEntries,
        string? agentWelcomeMessage)
    {
        var lastFiveChatEntries = chatEntries.TakeLast(5).ToList();
        var sanitizedChatResult = GroupChatHistoryUtils.GetSanitizedChatEntries(lastFiveChatEntries);
        var entryTexts = sanitizedChatResult.EntryTexts;

        // Execute all analysis tasks in parallel
        var chatHistory = string.Join("--\n", entryTexts);

        // Start phatic analysis and full conversation query in parallel
        var phaticAnalysisTask = _phaticPlugin.AnalyzePhaticExpressionAsync(kernel, chatHistory, agentWelcomeMessage);
        var (fullConversationQueryTask, lastMessageQueryTask) = SourceOnlyKnowledgeQueriesAsync(kernel, entryTexts);

        // Wait for all tasks to complete
        if (lastMessageQueryTask != null)
        {
            await Task.WhenAll(phaticAnalysisTask, fullConversationQueryTask, lastMessageQueryTask);
        }
        else
        {
            await Task.WhenAll(phaticAnalysisTask, fullConversationQueryTask);
        }

        // Extract results
        var phaticResult = phaticAnalysisTask.Result;

        if (phaticResult.IsPhaticOnly)
        {
            return new FaqInternalAgentCreationConfiguration(phaticResult, string.Empty);
        }

        // Extract and collect all available results
        var fullConversationResult = fullConversationQueryTask.Result;
        var lastMessageResult = lastMessageQueryTask?.Result;

        // Collect all non-null results
        var sources = new List<string>
        {
            fullConversationResult
        };

        if (lastMessageResult is not null)
        {
            sources.Add(lastMessageResult);
        }

        // Check if we have any valid knowledge sources
        var hasValidKnowledge = lastMessageResult is not null
            ? sources.TrueForAll(s => !s.Contains(KnowledgeNotFoundResponse))
            : !fullConversationResult.Contains(KnowledgeNotFoundResponse);

        if (!hasValidKnowledge)
        {
            throw new SfKnowledgeBaseSourceNotFoundException(
                lastMessageResult is not null ? [chatHistory, entryTexts.Last()] : [chatHistory],
                AgentCollaborationModes.Faq);
        }

        return new FaqInternalAgentCreationConfiguration(
            phaticResult,
            string.Join("\n", sources).Replace(KnowledgeNotFoundResponse, string.Empty));
    }

    private (Task<string> FullConversationQueryTask, Task<string>? LastMessageQueryTask)
        SourceOnlyKnowledgeQueriesAsync(
            Kernel kernel,
            List<string> entryTexts)
    {
        var fullConversationQueryTask = _sourceOnlyKnowledgePlugin.QueryKnowledgeSourcesAsync(
            kernel,
            string.Join("--\n", entryTexts),
            10);

        if (entryTexts.Count <= 1)
        {
            return (fullConversationQueryTask, null);
        }

        // conversation has 2 or more messages, we do a separate query on the last message to improve relevance
        var lastMessageQueryTask = _sourceOnlyKnowledgePlugin.QueryKnowledgeSourcesAsync(
            kernel,
            entryTexts.Last(),
            10);

        return (fullConversationQueryTask, lastMessageQueryTask);
    }
}