using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.Workers.Triggers.Activities;

namespace Sleekflow.FlowHub.Workers.Triggers.Orchestrators;

public class PropertyDateTimeWorkflowOrchestrator
{
    public static double RECURRING_INTEVAL_HOURS = 24;
    private readonly ILogger<PropertyDateTimeWorkflowOrchestrator> _logger;

    private const int InitialBatchSize = 500;
    private const int MinBatchSize = 10; // Minimum batch size for retries


    public PropertyDateTimeWorkflowOrchestrator(
        ILogger<PropertyDateTimeWorkflowOrchestrator> logger)
    {
        _logger = logger;
    }


    [Function("PropertyDateTimeWorkflow_Orchestrator")]
    public async Task RunAsync(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var currentUtcTime = context.CurrentUtcDateTime;
        var triggerPropertyWorkflowInput = context.GetInput<TriggerPropertyWorkflowInput>();
        if (triggerPropertyWorkflowInput == null)
        {
            _logger.LogError("TriggerPropertyWorkflowInput is null");
            throw new SfInternalErrorException("TriggerPropertyWorkflowInput is null");
        }

        _logger.LogInformation(
            "[{IsReplaying}][ContactProperty] Starting process an ScheduledDataTimeWorkflow for company {SleekflowCompanyId} with workflow {WorkflowVersionedId}",
            context.IsReplaying ? "Replaying" : "Normal Running",
            triggerPropertyWorkflowInput!.SleekflowCompanyId,
            triggerPropertyWorkflowInput!.WorkflowVersionedId);
        // check if the workflow is still active
        if (string.IsNullOrEmpty(triggerPropertyWorkflowInput.SleekflowCompanyId)
            || string.IsNullOrEmpty(triggerPropertyWorkflowInput.WorkflowVersionedId))
        {
            _logger.LogError(
                "SleekflowCompanyId or WorkflowVersionedId is empty. SleekflowCompanyId: {SleekflowCompanyId}, WorkflowVersionedId: {WorkflowVersionedId}",
                triggerPropertyWorkflowInput.SleekflowCompanyId,
                triggerPropertyWorkflowInput.WorkflowVersionedId);
            throw new SfInternalErrorException("WorkflowVersionedId is null or SleekflowCompanyId is null");
        }

        string workflowStatus = await checkWorkflowStatus(context, triggerPropertyWorkflowInput.SleekflowCompanyId, triggerPropertyWorkflowInput.WorkflowVersionedId);
        _logger.LogInformation(
            "[{IsReplaying}] The status of workflow {WorkflowVersionId} is: {WorkflowStatus}",
            context.IsReplaying ? "Replaying" : "Normal Running",
            triggerPropertyWorkflowInput.WorkflowVersionedId, workflowStatus);
        if (workflowStatus != WorkflowActivationStatuses.Active)
        {
            return;
        }

        _logger.LogInformation("[{IsReplaying}][ContactProperty] Begin to execute PropertyDateTimeWorkflow_Orchestrator, current time: {CurrentUtcTime}, workflowVersionId: {WorkflowVersionId}", context.IsReplaying ? "Replaying" : "Normal Running", currentUtcTime, triggerPropertyWorkflowInput.WorkflowVersionedId);
        try
        {
            string triggerName= ScheduledTriggerV2Names.GetTriggerNameByScheduledType(triggerPropertyWorkflowInput.ScheduledType);
            if (string.IsNullOrEmpty(triggerName))
            {
                _logger.LogError("trigger name is empty, scheduled type: {ScheduledType}, workflowVersionId: {WorkflowVersionId}", triggerPropertyWorkflowInput.ScheduledType, triggerPropertyWorkflowInput.WorkflowVersionedId);
                throw new SfInternalErrorException("trigger name is empty");
            }
            _logger.LogInformation("[{IsReplaying}][ContactProperty] Begin to get trigger condition. TriggerName: {TriggerName}, workflowVersionId: {WorkflowVersionId}", context.IsReplaying ? "Replaying" : "Normal Running", triggerName, triggerPropertyWorkflowInput.WorkflowVersionedId);
            var triggerConditionOutput =
                await context.CallActivityAsync<GetTriggerConditionOutput>(
                    "GetTriggerCondition",
                    new GetTriggerConditionInput(
                        triggerPropertyWorkflowInput!.SleekflowCompanyId,
                        triggerPropertyWorkflowInput.WorkflowVersionedId,
                        triggerName));
            if (triggerConditionOutput == null)
            {
                _logger.LogError("trigger condition is empty, workflowVersionId: {WorkflowVersionId}", triggerPropertyWorkflowInput.WorkflowVersionedId);
                throw new SfInternalErrorException("Trigger condition output is null");
            }

            _logger.LogInformation(
                "[{IsReplaying}] Enrollment Condition: {EnrollmentCondition}, workflowVersionId: {WorkflowVersionId}",
                context.IsReplaying ? "Replaying" : "Normal Running",
                JsonConvert.SerializeObject(triggerConditionOutput), triggerPropertyWorkflowInput.WorkflowVersionedId);
            DateTimeOffset? currentLastContactCreatedAt = null;
            string? currentLastContactId = null;
            var moreDataToProcess = true;
            var currentConfiguredBatchSize = InitialBatchSize;

            // get timezone id
            var timezoneId = await TryGetCompanyTimeZoneId(context, triggerPropertyWorkflowInput.SleekflowCompanyId, triggerPropertyWorkflowInput.Origin);
            _logger.LogInformation("[{IsReplaying}] time zone of workflow {WorkflowVersionId} is {TimeZoneId}", context.IsReplaying ? "Replaying" : "Normal Running", triggerPropertyWorkflowInput.WorkflowVersionedId, timezoneId);
            while (moreDataToProcess)
            {
                var attemptBatchSize = currentConfiguredBatchSize;
                var segmentSuccessfullyProcessed = false;
                CheckEligibilityAndScheduleByBatch.CheckEligibilityAndScheduleByBatchOutput? activityOutput;
                while (!segmentSuccessfullyProcessed && attemptBatchSize >= MinBatchSize)
                {
                    var activityInput =
                        new CheckEligibilityAndScheduleByBatch.CheckEligibilityAndScheduleByBatchInput(
                            triggerPropertyWorkflowInput.Origin,
                            triggerPropertyWorkflowInput.SleekflowCompanyId,
                            currentLastContactCreatedAt,
                            currentLastContactId,
                            attemptBatchSize,
                            triggerPropertyWorkflowInput.WorkflowId,
                            triggerPropertyWorkflowInput.WorkflowVersionedId,
                            triggerConditionOutput.WorkflowName,
                            triggerConditionOutput.Condition,
                            triggerPropertyWorkflowInput.ContactPropertyDateTimeSettings,
                            triggerPropertyWorkflowInput.CustomObjectDateTimeSettings,
                            triggerPropertyWorkflowInput.ScheduledType,
                            triggerPropertyWorkflowInput.WorkflowRecurringSettings,
                            currentUtcTime,
                            timezoneId);

                    try
                    {
                        _logger.LogInformation(
                            "[{IsReplaying}][ContactProperty] Begin to process contact batch for workflow {WorkflowVersionedId}. BatchSize: {AttemptBatchSize}, LastContactCreatedAt: {LastContactCreatedAt}, LastContactId: {LastContactId}",
                            context.IsReplaying ? "Replaying" : "Normal Running",
                            triggerPropertyWorkflowInput.WorkflowVersionedId,
                            attemptBatchSize,
                            currentLastContactCreatedAt,
                            currentLastContactId);

                        activityOutput = await context
                            .CallActivityAsync<CheckEligibilityAndScheduleByBatch.CheckEligibilityAndScheduleByBatchOutput>(
                                "CheckEligibilityAndScheduleByBatch",
                                activityInput);

                        currentLastContactCreatedAt = activityOutput.NextBatchLastContactCreatedAt;
                        currentLastContactId = activityOutput.NextBatchLastContactId;
                        segmentSuccessfullyProcessed = true;

                        moreDataToProcess = activityOutput.NextBatchLastContactId != null ||
                                            activityOutput.NextBatchLastContactCreatedAt != null;

                        _logger.LogInformation(
                            "[{IsReplaying}][ContactProperty] Successfully processed batch for workflow {WorkflowVersionedId}. Contacts in fetched batch: {ContactsFetched}. Contacts enrolled: {ContactsEnrolled}. Next cursor: CreatedAt={NextCreatedAt}, Id={NextId}",
                            context.IsReplaying ? "Replaying" : "Normal Running",
                            triggerPropertyWorkflowInput.WorkflowVersionedId,
                            activityOutput.ContactsInFetchedBatch,
                            activityOutput.ContactsEnrolled,
                            currentLastContactCreatedAt,
                            currentLastContactId);

                        currentConfiguredBatchSize = InitialBatchSize;
                    }
                    catch (OutOfMemoryException oomEx)
                    {
                        _logger.LogError(
                            oomEx,
                            "[ContactProperty] OutOfMemoryException processing contact batch for workflow {WorkflowVersionedId} with BatchSize {AttemptBatchSize}. Reducing batch size. Current segment cursors: CreatedAt={LastContactCreatedAt}, Id={LastContactId}",
                            triggerPropertyWorkflowInput.WorkflowVersionedId,
                            attemptBatchSize,
                            currentLastContactCreatedAt,
                            currentLastContactId);

                        attemptBatchSize /= 2;

                        if (attemptBatchSize < MinBatchSize)
                        {
                            attemptBatchSize = MinBatchSize;
                        }

                        currentConfiguredBatchSize = attemptBatchSize;

                        if (attemptBatchSize == MinBatchSize && !segmentSuccessfullyProcessed)
                        {
                            _logger.LogCritical(
                                oomEx,
                                "[ContactProperty] Critical error: OutOfMemoryException persisted for workflow {WorkflowVersionedId} even with MinBatchSize {MinBatchSize}. Stopping processing. Segment cursors: CreatedAt={LastContactCreatedAt}, Id={LastContactId}",
                                triggerPropertyWorkflowInput.WorkflowVersionedId,
                                MinBatchSize,
                                currentLastContactCreatedAt,
                                currentLastContactId);
                            moreDataToProcess = false;

                            throw new Exception(
                                $"[ContactProperty] Failed to process segment due to OutOfMemoryException for workflow {triggerPropertyWorkflowInput.WorkflowVersionedId} even with MinBatchSize {MinBatchSize}. Cursors: CreatedAt={currentLastContactCreatedAt}, Id={currentLastContactId}",
                                oomEx);
                        }
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(
                            e,
                            "[ContactProperty] Exception occurred in ProcessContactEligibilityCheckByBatch for workflow {WorkflowVersionedId} with BatchSize {AttemptBatchSize}. Current segment cursors: CreatedAt={LastContactCreatedAt}, Id={LastContactId}. This segment will be marked as failed, and the orchestration will stop processing further segments for this run.",
                            triggerPropertyWorkflowInput.WorkflowVersionedId,
                            attemptBatchSize,
                            currentLastContactCreatedAt,
                            currentLastContactId);
                        moreDataToProcess = false;
                    }
                }

                if (!segmentSuccessfullyProcessed && moreDataToProcess)
                {
                    _logger.LogError(
                        "[{IsReplaying}][ContactProperty] Failed to process segment for workflow {WorkflowVersionedId} after all retries. Stopping. Last attempted cursors: CreatedAt={LastContactCreatedAt}, Id={LastContactId}",
                        context.IsReplaying ? "Replaying" : "Normal Running",
                        triggerPropertyWorkflowInput.WorkflowVersionedId,
                        currentLastContactCreatedAt,
                        currentLastContactId);
                    moreDataToProcess = false;
                }
            }

            _logger.LogInformation(
                "[{IsReplaying}][ContactProperty] Completed all data processing for workflow {WorkflowVersionedId} for company {SleekflowCompanyId}.",
                context.IsReplaying ? "Replaying" : "Normal Running",
                triggerPropertyWorkflowInput.WorkflowVersionedId,
                triggerPropertyWorkflowInput.SleekflowCompanyId);
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "[ContactProperty] Orchestration failed for ScheduledDataTimeWorkflow {WorkflowVersionedId} for {SleekflowCompanyId}",
                triggerPropertyWorkflowInput.WorkflowVersionedId,
                triggerPropertyWorkflowInput.SleekflowCompanyId);
        }
        finally
        {
            if (triggerPropertyWorkflowInput.WorkflowRecurringSettings == null)
            {
                _logger.LogInformation(
                    "[{IsReplaying}][ContactProperty] No recurring settings found for Workflow {WorkflowVersionedId} / Company {SleekflowCompanyId}. Orchestration will complete.",
                    context.IsReplaying ? "Replaying" : "Normal Running",
                    triggerPropertyWorkflowInput.WorkflowVersionedId,
                    triggerPropertyWorkflowInput.SleekflowCompanyId);
            }
            else
            {
                var nextRunTime = currentUtcTime.AddHours(RECURRING_INTEVAL_HOURS);
                _logger.LogInformation(
                    "[{IsReplaying}][ContactProperty] Scheduling next run for Workflow {WorkflowVersionedId} for Company {SleekflowCompanyId} at {NextRunTime} UTC based on recurring settings.",
                    context.IsReplaying ? "Replaying" : "Normal Running",
                    triggerPropertyWorkflowInput.WorkflowVersionedId,
                    triggerPropertyWorkflowInput.SleekflowCompanyId,
                    nextRunTime);

                await context.CreateTimer(nextRunTime, CancellationToken.None);

                _logger.LogInformation(
                    "[{IsReplaying}][ContactProperty] Timer fired for Workflow {WorkflowVersionedId} / Company {SleekflowCompanyId}. Continuing as new.",
                    context.IsReplaying ? "Replaying" : "Normal Running",
                    triggerPropertyWorkflowInput.WorkflowVersionedId,
                    triggerPropertyWorkflowInput.SleekflowCompanyId);
                context.ContinueAsNew(triggerPropertyWorkflowInput);
            }
        }
    }

    private async Task<string> TryGetCompanyTimeZoneId(TaskOrchestrationContext context, string sleekflowCompanyId, string origin)
    {
        _logger.LogInformation("[ContactProperty] Begin to get timezone id. CompanyId: {CompanyId}, origin: {Origin}", sleekflowCompanyId, origin);
        var timezoneId = await context.CallActivityAsync<string>(
            "GetCompanyTimeZoneId",
            new GetCompanyTimeZoneIdActivityInput(
                sleekflowCompanyId,
                origin));
        _logger.LogInformation("[ContactProperty] Got timezone id: {TimezoneId}", timezoneId);
        return timezoneId;
    }

    private async Task<string> checkWorkflowStatus(TaskOrchestrationContext context, string sleekflowCompanyId, string workflowVersionedId)
    {
        var workflowStatusOutput =
            await context.CallActivityAsync<GetWorkflowStatusOutput>(
                "GetWorkflowStatus",
                new GetWorkflowStatusInput(
                    sleekflowCompanyId,
                    workflowVersionedId));
        _logger.LogInformation(
            "Workflow Status: {WorkflowStatus}", JsonConvert.SerializeObject(workflowStatusOutput));
        return workflowStatusOutput.WorkflowStatus;
    }
}