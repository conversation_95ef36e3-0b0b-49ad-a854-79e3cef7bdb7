using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Components.Utils;
using DocumentDB = Pulumi.AzureNative.DocumentDB;

namespace Sleekflow.Infras.Components.IntelligentHub;

public class IntelligentHubDb
{
    private readonly ResourceGroup _resourceGroup;
    private readonly DocumentDB.DatabaseAccount _databaseAccount;
    private readonly MyConfig _myConfig;

    public IntelligentHubDb(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount databaseAccount,
        MyConfig myConfig)
    {
        _resourceGroup = resourceGroup;
        _databaseAccount = databaseAccount;
        _myConfig = myConfig;
    }

    public class IntelligentHubDbOutput
    {
        public Output<string> AccountName { get; }

        public Output<string> AccountKey { get; }

        public string DatabaseId { get; }

        public IntelligentHubDbOutput(
            Output<string> accountName,
            Output<string> accountKey,
            string databaseId)
        {
            AccountName = accountName;
            AccountKey = accountKey;
            DatabaseId = databaseId;
        }
    }

    public IntelligentHubDbOutput InitIntelligentHubDb()
    {
        const string cosmosDbId = "intelligenthubdb";
        var cosmosDb = new DocumentDB.SqlResourceSqlDatabase(
            cosmosDbId,
            new DocumentDB.SqlResourceSqlDatabaseArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = _databaseAccount.Name,
                Resource = new DocumentDB.Inputs.SqlDatabaseResourceArgs
                {
                    Id = cosmosDbId,
                },
                Options = new DocumentDB.Inputs.CreateUpdateOptionsArgs()
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        // Sleekflow.Cosmos.IntelligentHubDb.IIntelligentHubDbService
        var containerParams = new ContainerParam[]
        {
            new ContainerParam(
                "message_processing_checkpoint",
                "message_processing_checkpoint",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: 1000),

            new ContainerParam(
                "message_processing_history",
                "message_processing_history",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: 1000),
            new ContainerParam(
                "file_document",
                "file_document",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: 1000),
            new ContainerParam(
                "file_document_chunk",
                "file_document_chunk",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: 1000),
            new ContainerParam(
                "webpage_document_chunk",
                "webpage_document_chunk",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: 1000),
            new ContainerParam(
                "knowledge_base_entry",
                "knowledge_base_entry",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: 1000),
            new ContainerParam(
                "sys_changefeed_lease",
                "sys_changefeed_lease",
                new List<string>
                {
                    "/id"
                },
                MaxThroughput: 1000),

            new ContainerParam(
                "web_scraper",
                "web_scraper",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: 1000),

            new ContainerParam(
                "web_scraper_run",
                "web_scraper_run",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: 1000),

            new ContainerParam(
                "intelligent_hub_config",
                "intelligent_hub_config",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: _myConfig.Name == "production" ? 10000 : 1000),

            new ContainerParam(
                "intelligent_hub_usage",
                "intelligent_hub_usage",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                Ttl: 3600 * 24 * 60,
                MaxThroughput: _myConfig.Name == "production" ? 10000 : 1000),

            new ContainerParam(
                "blob_upload_history",
                "blob_upload_history",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: 1000),

            new ContainerParam(
                "topic_analytics_topic",
                "topic_analytics_topic",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: 1000),

            new ContainerParam(
                "company_config",
                "company_config",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: 1000),

            new ContainerParam(
                "company_agent_config",
                "company_agent_config",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: 1000),

            new ContainerParam(
                "playground",
                "playground",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: 1000),

            new ContainerParam(
                "file_content_cache",
                "file_content_cache",
                new List<string>
                {
                    "/file_url"
                },
                Ttl: 30 * 24 * 3600, // 1 month TTL
                MaxThroughput: 1000),

            new ContainerParam(
                "web_crawling_session",
                "web_crawling_session",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: 1000),

            new ContainerParam(
                "website_document_chunk",
                "website_document_chunk",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: 1000),

            new ContainerParam(
                "company_agent_config_snapshot",
                "company_agent_config_snapshot",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: 1000),
        };

        var containerIdToContainer = ContainerUtils.CreateSqlResourceSqlContainers(
            _resourceGroup,
            _databaseAccount,
            cosmosDb,
            cosmosDbId,
            containerParams);

        var cosmosDbAccountKeys = DocumentDB.ListDatabaseAccountKeys.Invoke(
            new DocumentDB.ListDatabaseAccountKeysInvokeArgs
            {
                AccountName = _databaseAccount.Name, ResourceGroupName = _resourceGroup.Name
            });
        var cosmosDbAccountName = _databaseAccount.Name;
        var cosmosDbAccountKey = cosmosDbAccountKeys.Apply(accountKeys => accountKeys.PrimaryMasterKey);

        return new IntelligentHubDbOutput(
            cosmosDbAccountName,
            cosmosDbAccountKey,
            cosmosDbId);
    }
}