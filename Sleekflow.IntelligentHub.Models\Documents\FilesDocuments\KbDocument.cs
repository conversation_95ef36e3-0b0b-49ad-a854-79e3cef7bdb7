﻿using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;

[Resolver(typeof(IIntelligentHubDbResolver))]
[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.FileDocument)]
public abstract class KbDocument : Entity, IHasSleekflowCompanyId, IHasCreatedAt, IHasUpdatedAt, IHasMetadata
{
    public const string PropertyNameContentType = "content_type";
    public const string PropertyNameMetadata = "metadata";
    public const string PropertyNameFileDocumentProcessStatus = "file_document_process_status";
    public const string PropertyNameFileDocumentProcessPercentage = "file_document_process_percentage";
    public const string PropertyNameAgentAssignments = "agent_assignments";
    public const string PropertyNameUploadedBy = "uploaded_by";
    public const string PropertyNameDebugTimestamps = "debug_timestamps";

    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(PropertyNameContentType)]
    public string ContentType { get; set; }

    [JsonProperty(PropertyNameMetadata)]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonProperty(PropertyNameFileDocumentProcessStatus)]
    public string FileDocumentProcessStatus { get; set; }

    [JsonProperty(PropertyNameFileDocumentProcessPercentage)]
    public double FileDocumentProcessPercentage { get; set; }

    [JsonProperty(PropertyNameAgentAssignments)]
    public List<AgentAssignment>? AgentAssignments { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty(PropertyNameUploadedBy)]
    public string UploadedBy { get; set; }

    [JsonProperty(PropertyNameDebugTimestamps)]
    public DebugTimestamps? DebugTimestamps { get; set; }

    [JsonConstructor]
    protected KbDocument(
        string id,
        string sleekflowCompanyId,
        string contentType,
        Dictionary<string, object?> metadata,
        string fileDocumentProcessStatus,
        double? fileDocumentProcessPercentage,
        List<AgentAssignment>? agentAssignments,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string uploadedBy,
        DebugTimestamps? debugTimestamps,
        string sysTypeName)
        : base(id, sysTypeName)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        ContentType = contentType;
        Metadata = metadata;
        FileDocumentProcessStatus = fileDocumentProcessStatus;
        FileDocumentProcessPercentage = fileDocumentProcessPercentage ?? 0.0;
        AgentAssignments = agentAssignments;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        UploadedBy = uploadedBy;
        DebugTimestamps = debugTimestamps;
    }

    public abstract int GetCharacterCount();
}