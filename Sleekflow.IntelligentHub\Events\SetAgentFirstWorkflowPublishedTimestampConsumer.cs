using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Microsoft.Azure.Cosmos;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.Models.WorkflowSteps;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Events;

public class SetAgentFirstWorkflowPublishedTimestampConsumerDefinition : ConsumerDefinition<SetAgentFirstWorkflowPublishedTimestampConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<SetAgentFirstWorkflowPublishedTimestampConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class SetAgentFirstWorkflowPublishedTimestampConsumer(
    ILogger<SetAgentFirstWorkflowPublishedTimestampConsumer> logger,
    ICompanyAgentConfigService companyAgentConfigService)
    : IConsumer<SetAgentFirstWorkflowPublishedTimestampRequest>
{
    public async Task Consume(ConsumeContext<SetAgentFirstWorkflowPublishedTimestampRequest> context)
    {
        var message = context.Message;
        var sleekflowCompanyId = message.SleekflowCompanyId;
        var companyAgentConfigId = message.CompanyAgentConfigId;

        logger.LogInformation(
            "Setting agent first workflow published timestamp for {CompanyAgentConfigId} in company {SleekflowCompanyId}",
            companyAgentConfigId,
            sleekflowCompanyId);

        try
        {
            var agentConfig = await companyAgentConfigService.GetOrDefaultAsync(companyAgentConfigId, sleekflowCompanyId);

            if (agentConfig == null)
            {
                logger.LogWarning(
                    "Agent config {CompanyAgentConfigId} not found in company {SleekflowCompanyId}",
                    companyAgentConfigId,
                    sleekflowCompanyId);

                await context.RespondAsync(new SetAgentFirstWorkflowPublishedTimestampResponse(
                    false,
                    $"Agent config {companyAgentConfigId} not found"));
                return;
            }

            if (agentConfig.FirstWorkflowPublishedAt.HasValue)
            {
                await context.RespondAsync(new SetAgentFirstWorkflowPublishedTimestampResponse(
                    true,
                    "FirstWorkflowPublishedAt already set"));
                return;
            }

            var currentTimestamp = DateTimeOffset.UtcNow;

            var updatedAgentConfig = await companyAgentConfigService.PatchAndGetAsync(
                agentConfig.Id,
                sleekflowCompanyId,
                agentConfig.ETag!,
                agentConfig.CreatedBy?.SleekflowStaffId ?? string.Empty,
                agentConfig.CreatedBy?.SleekflowStaffTeamIds,
                firstWorkflowPublishedAt: currentTimestamp);

            logger.LogInformation(
                "Successfully set FirstWorkflowPublishedAt to {Timestamp} for agent config {CompanyAgentConfigId}",
                currentTimestamp,
                companyAgentConfigId);

            await context.RespondAsync(new SetAgentFirstWorkflowPublishedTimestampResponse(
                updatedAgentConfig.FirstWorkflowPublishedAt.HasValue,
                "FirstWorkflowPublishedAt timestamp set successfully"));
        }
        catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.PreconditionFailed)
        {
            logger.LogWarning(
            ex,
            "ETag mismatch when updating agent config {CompanyAgentConfigId}. The object has been modified by another process.",
            companyAgentConfigId);

            await context.RespondAsync(new SetAgentFirstWorkflowPublishedTimestampResponse(
                false,
                "ETag mismatch - object modified by another process"));
        }
        catch (Exception ex)
        {
            logger.LogError(
                ex,
                "Error setting agent first workflow published timestamp for {CompanyAgentConfigId} in company {SleekflowCompanyId}",
                companyAgentConfigId,
                sleekflowCompanyId);

            await context.RespondAsync(new SetAgentFirstWorkflowPublishedTimestampResponse(
                false,
                $"Error setting FirstWorkflowPublishedAt: {ex.Message}"));
        }
    }
}