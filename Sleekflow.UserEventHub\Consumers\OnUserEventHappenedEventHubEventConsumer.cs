﻿using MassTransit;
using Sleekflow.Events.EventHub;
using Sleekflow.Models.Events;
using Sleekflow.UserEventHub.Models.UserEvents;
using Sleekflow.UserEventHub.UserEvents;

namespace Sleekflow.UserEventHub.Consumers;

public class OnUserEventHappenedEventHubEventConsumerDefinition
    : ConsumerDefinition<OnUserEventHappenedEventHubEventConsumer>, IRiderConsumer
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnUserEventHappenedEventHubEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IEventHubReceiveEndpointConfigurator eventHubReceiveEndpointConfigurator)
        {
            eventHubReceiveEndpointConfigurator.CheckpointInterval = TimeSpan.FromSeconds(15);
            eventHubReceiveEndpointConfigurator.ConcurrentMessageLimit = 20;
            eventHubReceiveEndpointConfigurator.PrefetchCount = 20;
        }
    }
}

public class OnUserEventHappenedEventHubEventConsumer
    : IConsumer<OnUserEventHappenedEventHubEvent>, IRiderConsumer
{
    private readonly IUserEventService _userEventService;
    private readonly IPostgreSqlUserEventService _postgreSqlUserEventService;
    private readonly ILogger<OnUserEventHappenedEventHubEventConsumer> _logger;

    public OnUserEventHappenedEventHubEventConsumer(
        IUserEventService userEventService,
        IPostgreSqlUserEventService postgreSqlUserEventService,
        ILogger<OnUserEventHappenedEventHubEventConsumer> logger)
    {
        _userEventService = userEventService;
        _postgreSqlUserEventService = postgreSqlUserEventService;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<OnUserEventHappenedEventHubEvent> context)
    {
        var @event = context.Message;

        var properties = new UserEventProperties(@event.Properties.CampaignId);
        var metadata = new UserEventMetadata(
            @event.Metadata.SourceWorkflowId,
            @event.Metadata.SourceWorkflowVersionedId,
            @event.Metadata.SourceChannel,
            @event.Metadata.SourceDevice,
            @event.Metadata.SourceLocation,
            @event.Metadata.SourceIpAddress);

        try
        {
            // Write to Cosmos DB (existing functionality)
            var userEvent = await _userEventService.CreateAndGetUserEventAsync(
                @event.Id,
                @event.SleekflowCompanyId,
                @event.EventType,
                @event.ObjectId,
                @event.ObjectType,
                @event.Source,
                properties,
                metadata);

            // Write to PostgreSQL (new functionality)
            await _postgreSqlUserEventService.WriteUserEventAsync(
                @event.Id,
                @event.SleekflowCompanyId,
                @event.EventType,
                @event.ObjectId,
                @event.ObjectType,
                @event.Source,
                properties,
                metadata,
                userEvent.CreatedAt);

            _logger.LogDebug(
                "Successfully processed user event. Id: {Id}, CompanyId: {CompanyId}, EventType: {EventType}",
                @event.Id, @event.SleekflowCompanyId, @event.EventType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Failed to process user event. Id: {Id}, CompanyId: {CompanyId}, EventType: {EventType}",
                @event.Id, @event.SleekflowCompanyId, @event.EventType);
            throw;
        }
    }
}