﻿using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using Azure;
using Azure.AI.FormRecognizer.DocumentAnalysis;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.SemanticKernel;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.IntelligentHub.Blobs;
using Sleekflow.IntelligentHub.Configs;
using Sleekflow.IntelligentHub.Documents.FileDocuments;
using Sleekflow.IntelligentHub.Documents.FileDocuments.StatisticsCalculators;
using Sleekflow.IntelligentHub.Documents.Statistics;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.Models.Documents.Statistics;
using Sleekflow.IntelligentHub.Models.FaqAgents;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.IntelligentHub.Plugins;

namespace Sleekflow.IntelligentHub.Documents;

public interface IDocumentProcessingService
{
    Task<DocumentStatistics> CalculateDocumentStatistics(
        string sleekflowCompanyId,
        string blobId,
        string blobType);

    Task<FileDocument> CreateDocumentAsync(
        string sleekflowCompanyId,
        string blobId,
        string blobType,
        List<string> assignToAgentIds);
}

public class DocumentProcessingService : IScopedService, IDocumentProcessingService
{
    private readonly Kernel _kernel;
    private readonly ILogger<DocumentProcessingService> _logger;
    private readonly IAzureFormRecognizerConfig _azureFormRecognizerConfig;
    private readonly IAzureBlobClientFactory _azureBlobClientFactory;
    private readonly IDocumentStatisticsCalculatorFactory _documentStatisticsCalculatorFactory;
    private readonly IBlobService _blobService;
    private readonly IFileDocumentService _fileDocumentService;
    private readonly IBlobUploadHistoryService _blobUploadHistoryService;
    private readonly ISummaryPlugin _summaryPlugin;
    private readonly IKnowledgeBaseIngestionService _knowledgeBaseIngestionService;
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;
    private readonly IIntelligentHubCharacterCountService _intelligentHubCharacterCountService;

    public DocumentProcessingService(
        Kernel kernel,
        ILogger<DocumentProcessingService> logger,
        IAzureFormRecognizerConfig azureFormRecognizerConfig,
        IAzureBlobClientFactory azureBlobClientFactory,
        IDocumentStatisticsCalculatorFactory documentStatisticsCalculatorFactory,
        IBlobService blobService,
        IFileDocumentService fileDocumentService,
        IBlobUploadHistoryService blobUploadHistoryService,
        ISummaryPlugin summaryPlugin,
        IKnowledgeBaseIngestionService knowledgeBaseIngestionService,
        IIntelligentHubConfigService intelligentHubConfigService,
        IIntelligentHubUsageService intelligentHubUsageService,
        IIntelligentHubCharacterCountService intelligentHubCharacterCountService)
    {
        _kernel = kernel;
        _logger = logger;
        _azureFormRecognizerConfig = azureFormRecognizerConfig;
        _azureBlobClientFactory = azureBlobClientFactory;
        _documentStatisticsCalculatorFactory = documentStatisticsCalculatorFactory;
        _blobService = blobService;
        _fileDocumentService = fileDocumentService;
        _blobUploadHistoryService = blobUploadHistoryService;
        _summaryPlugin = summaryPlugin;
        _knowledgeBaseIngestionService = knowledgeBaseIngestionService;
        _intelligentHubConfigService = intelligentHubConfigService;
        _intelligentHubUsageService = intelligentHubUsageService;
        _intelligentHubCharacterCountService = intelligentHubCharacterCountService;
    }

    public async Task<FileDocument> CreateDocumentAsync(
        string sleekflowCompanyId,
        string blobId,
        string blobType,
        List<string> assignToAgentIds)
    {
        var (_, documentStatistics) = await PreProcessMetadata(sleekflowCompanyId, blobId, blobType);

        var intelligentHubConfig = await _intelligentHubConfigService.GetIntelligentHubConfigAsync(sleekflowCompanyId);

        if (intelligentHubConfig == null)
        {
            throw new Exception("Intelligent Hub config not found");
        }

        // Check character count limits for each agent
        var characterCountLimit = _intelligentHubUsageService.GetFeatureTotalUsageLimit(intelligentHubConfig, PriceableFeatures.AiAgentsKnowledgeBaseTotalCharacterCount);
        var characterCount = documentStatistics.TotalCharacters;

        foreach (var agentId in assignToAgentIds.Where(id => id != CompanyAgentTypes.SmartReply))
        {
            var currentCharacterCount = await _intelligentHubCharacterCountService.GetCharacterCountForAgent(sleekflowCompanyId, agentId);
            if (currentCharacterCount + characterCount > characterCountLimit)
            {
                throw new SfUserFriendlyException(
                    $"Agent {agentId} would exceed the character count limit of {characterCountLimit}. Current count: {currentCharacterCount}, Adding: {characterCount}");
            }
        }

        var blobUploadHistory = await _blobUploadHistoryService.GetBlobUploadHistory(sleekflowCompanyId, blobId);
        var fileName = blobUploadHistory.FileName;
        var uploadedBy = blobUploadHistory.UploadedBy;

        var fileDocument = await _fileDocumentService.CreateOrGetDocumentAsync(
            sleekflowCompanyId,
            blobId,
            blobType,
            string.Empty,
            documentStatistics,
            new Dictionary<string, object?>(),
            string.Empty,
            ProcessFileDocumentStatuses.NotConverted,
            fileName,
            uploadedBy,
            assignToAgentIds);

        if (assignToAgentIds.Count > 0)
        {
            await _knowledgeBaseIngestionService.StartKnowledgeBaseIngestion(sleekflowCompanyId, fileDocument.Id);
        }

        return fileDocument;
    }

    private async Task<(Stream BlobStream, DocumentStatistics DocumentStatistic)> PreProcessMetadata(
        string sleekflowCompanyId,
        string blobId,
        string blobType)
    {
        // retrieve blob stream from azure blob storage
        var blobStream = await LoadBlobAsStreamAsync(sleekflowCompanyId, blobId, "File");

        // calculate statistics
        var documentStatisticCalculator =
            _documentStatisticsCalculatorFactory.CreateDocumentStatisticsCalculator(blobType);
        var documentStatistics = documentStatisticCalculator.CalculateDocumentStatistics(blobStream);
        return (blobStream, documentStatistics);
    }

    public async Task<DocumentStatistics> CalculateDocumentStatistics(
        string sleekflowCompanyId,
        string blobId,
        string blobType)
    {
        var document = await _fileDocumentService.GetDocumentByBlobIdAsync(sleekflowCompanyId, blobId);
        if (document != null)
        {
            return document.DocumentStatistics;
        }

        // retrieve blob stream from azure blob storage
        var blobStream = await LoadBlobAsStreamAsync(sleekflowCompanyId, blobId, "File");
        var documentStatisticCalculator =
            _documentStatisticsCalculatorFactory.CreateDocumentStatisticsCalculator(blobType);

        var documentStatistics = documentStatisticCalculator.CalculateDocumentStatistics(blobStream);
        return documentStatistics;
    }

    private async Task<Stream> LoadBlobAsStreamAsync(
        string sleekflowCompanyId,
        string blobId,
        string blobType)
    {
        _logger.LogInformation(
            "LoadBlobAsStreamAsync {SleekflowCompanyId} {BlobId} {BlobType}",
            sleekflowCompanyId,
            blobId,
            blobType);

        var blobName = GetBlobNameFromId(blobId);
        var publicBlobs = await _blobService.CreateBlobDownloadSasUrls(
            sleekflowCompanyId,
            new List<string>
            {
                blobName
            },
            blobType);

        if (publicBlobs.Count != 1)
        {
            throw new Exception();
        }

        var blobUrl = publicBlobs[0].Url;
        var blobClient = _azureBlobClientFactory.GetBlobClient(blobUrl);
        var blobStream = new MemoryStream();
        await blobClient.DownloadToAsync(blobStream);
        blobStream.Position = 0;

        return blobStream;
    }

    private string GetBlobNameFromId(string blobId)
    {
        var splitId = blobId.Split('/');
        if (splitId.Length != 2)
        {
            throw new Exception();
        }

        return splitId[1];
    }

    private async Task<IReadOnlyList<(int Index, string Text)>> GetCsvDocumentAsSummarizedTextAsync(Stream stream)
    {
        List<(int Index, string Text)> pageTuples = new ();

        var reader = new StreamReader(stream);
        var fullCsv = await reader.ReadToEndAsync();
        var summarizedTable = await _summaryPlugin.TransformCsvToText(_kernel, fullCsv);

        pageTuples.Add((0, summarizedTable));
        return pageTuples.AsReadOnly();
    }

    private async Task<IReadOnlyList<(int Index, string Text)>> GetExcelDocumentAsSummarizedTextAsync(Stream stream)
    {
        _logger.LogInformation("GetExcelDocumentAsSummarizedTextAsync");
        List<(int Index, string Text)> pageTuples = new ();

        using var spreadsheetDocument = SpreadsheetDocument.Open(stream, false);
        var workbookPart = spreadsheetDocument.WorkbookPart;
        var worksheetPart = workbookPart!.WorksheetParts.First();
        var sheetData = worksheetPart.Worksheet.Elements<SheetData>().First();

        var csvStringBuilder = new StringBuilder();
        foreach (var row in sheetData.Elements<Row>())
        {
            var cellValues = row.Elements<Cell>().Select(c => ExcelStatisticsCalculator.GetCellValue(c, workbookPart))
                .ToArray();
            csvStringBuilder.AppendLine(string.Join(",", cellValues));
        }

        var fullCsv = csvStringBuilder.ToString();

        _logger.LogInformation("Passing csv to plugin: {FullCsv}", fullCsv);
        var summarizedTable = await _summaryPlugin.TransformCsvToText(_kernel, fullCsv);
        _logger.LogInformation("Summarized table: {SummarizedTable}", summarizedTable);

        pageTuples.Add((0, summarizedTable));
        return pageTuples.AsReadOnly();
    }

    private static string GetTableHtml(DocumentTable table)
    {
        var tableHtmlSb = new StringBuilder("<table>");

        var rows = new List<DocumentTableCell>[table.RowCount];
        for (var k = 0; k < table.RowCount; k++)
        {
            rows[k] = table.Cells
                .Where(c => c.RowIndex == k)
                .OrderBy(c => c.ColumnIndex)
                .ToList();
        }

        foreach (var rowCells in rows)
        {
            tableHtmlSb.Append("<tr>");

            foreach (var cell in rowCells)
            {
                var cellTag = (cell.Kind == "columnHeader" || cell.Kind == "rowHeader") ? "th" : "td";
                var colRowSpan =
                    string.Empty
                    + (cell.ColumnSpan > 1 ? $" colspan='{cell.ColumnSpan}'" : string.Empty)
                    + (cell.RowSpan > 1 ? $" rowspan='{cell.RowSpan}'" : string.Empty);

                tableHtmlSb.AppendFormat(
                    "<{0}{1}>{2}</{0}>",
                    cellTag,
                    colRowSpan,
                    WebUtility.HtmlEncode(cell.Content));
            }

            tableHtmlSb.Append("</tr>");
        }

        tableHtmlSb.Append("</table>");

        return tableHtmlSb.ToString();
    }

    private async Task<IReadOnlyList<(int Index, string Text)>> GetDocumentAsIndexAndTextTuplesAsync(
        Stream stream,
        bool ifTransformTableToText)
    {
        var documentClient = new DocumentAnalysisClient(
            new Uri(_azureFormRecognizerConfig.AzureFormRecognizerEndpoint),
            new AzureKeyCredential(_azureFormRecognizerConfig.AzureFormRecognizerKey),
            new DocumentAnalysisClientOptions
            {
                Diagnostics =
                {
                    IsLoggingContentEnabled = true
                }
            });

        var operation = await documentClient.AnalyzeDocumentAsync(WaitUntil.Started, "prebuilt-layout", stream);

        List<(int Index, string Text)> pageTuples = new ();

        var analyzeResponse = await operation.WaitForCompletionAsync();

        var documentPages = analyzeResponse.Value.Pages;

        await Parallel.ForEachAsync(
            Enumerable.Range(0, documentPages.Count).ToList(),
            async (i, ct) =>
            {
                _logger.LogInformation("Processing the page {Page}", i);

                var documentPage = documentPages[i];

                var spanIndex = documentPage.Spans[0].Index;
                var spanLength = documentPage.Spans[0].Length;

                // Mark all positions of the spans in the page
                var spanChars = Enumerable.Repeat(-1, spanLength).ToArray();

                IReadOnlyList<DocumentTable> tablesOnPage =
                    analyzeResponse.Value.Tables
                        .Where(t => t.BoundingRegions[0].PageNumber == i + 1)
                        .ToList();
                for (var tableId = 0; tableId < tablesOnPage.Count; tableId++)
                {
                    var documentTable = tablesOnPage[tableId];

                    foreach (var span in documentTable.Spans)
                    {
                        // Replace all table spans with "tableId" in tableChars array
                        for (var j = 0; j < span.Length; j++)
                        {
                            var index = span.Index - spanIndex + j;
                            if (index >= 0 && index < spanLength)
                            {
                                spanChars[index] = tableId;
                            }
                        }
                    }
                }

                StringBuilder textSb = new ();

                HashSet<int> processedTable = new ();
                for (var j = 0; j < spanChars.Length; j++)
                {
                    var isTableSpan = spanChars[j] != -1;

                    if (isTableSpan && ifTransformTableToText)
                    {
                        if (processedTable.Contains(spanChars[j]))
                        {
                            // Already processed this table
                            continue;
                        }

                        var table = tablesOnPage[spanChars[j]];

                        var tableHtmlStr = GetTableHtml(table);
                        var tableAsText = await _summaryPlugin.TransformHtmlTableToText(_kernel, tableHtmlStr);

                        textSb.Append(tableAsText);
                        processedTable.Add(spanChars[j]);
                    }
                    else if (!isTableSpan)
                    {
                        textSb.Append(analyzeResponse.Value.Content[spanIndex + j]);
                    }
                }

                textSb.Append(' ');

                lock (pageTuples)
                {
                    pageTuples.Add((i, textSb.ToString()));
                }

                _logger.LogInformation("Processed the page {Page}, output: {Output}", i, textSb.ToString());
            });

        return pageTuples.OrderBy(pd => pd.Index).ToList().AsReadOnly();
    }

    private static IEnumerable<FaqAgentContentSection> CreateSections(IReadOnlyList<FaqAgentPageDetail> pageDetails)
    {
        const int maxSectionLength = 1_000;
        const int sentenceSearchLimit = 100;
        const int sectionOverlap = 100;

        var sentenceEndings = new[]
        {
            '.',
            '。',
            '．',

            '!',
            '！',
            '﹗',

            '?',
            '﹖',
            '？',
        };
        var wordBreaks = new[]
        {
            ',',
            '﹐',
            '，',
            '､',
            '、',

            ';',
            '﹔',
            '；',

            ':',
            '﹕',
            '︰',

            ' ',
            '(',
            ')',
            '[',
            ']',
            '{',
            '}',

            '\t',
            '\n',
        };
        var allText = string.Concat(pageDetails.Select(p => p.Text));
        var length = allText.Length;
        var start = 0;
        var end = length;
        var pattern = "[^0-9a-zA-Z_-]";

        while (start + sectionOverlap < length)
        {
            var lastWord = -1;
            end = start + maxSectionLength;

            if (end > length)
            {
                end = length;
            }
            else
            {
                // Try to find the end of the sentence
                while (end < length
                       && (end - start - maxSectionLength) < sentenceSearchLimit
                       && !sentenceEndings.Contains(allText[end]))
                {
                    if (wordBreaks.Contains(allText[end]))
                    {
                        lastWord = end;
                    }

                    end++;
                }

                if (end < length && !sentenceEndings.Contains(allText[end]) && lastWord > 0)
                {
                    end = lastWord; // Fall back to at least keeping a whole word
                }
            }

            if (end < length)
            {
                end++;
            }

            // Try to find the start of the sentence or at least a whole word boundary
            lastWord = -1;
            while (start > 0
                   && start > end - maxSectionLength - (2 * sentenceSearchLimit)
                   && !sentenceEndings.Contains(allText[start]))
            {
                if (wordBreaks.Contains(allText[start]))
                {
                    lastWord = start;
                }

                start--;
            }

            if (!sentenceEndings.Contains(allText[start]) && lastWord > 0)
            {
                start = lastWord;
            }

            if (start > 0)
            {
                start++;
            }

            var sectionText = allText[start..end];

            yield return new FaqAgentContentSection(
                id: Regex.Replace($"-{start}", pattern, "_").TrimStart('_'),
                content: sectionText);

            var lastTableStart = sectionText.LastIndexOf("<table", StringComparison.Ordinal);
            if (lastTableStart > 2 * sentenceSearchLimit &&
                lastTableStart > sectionText.LastIndexOf("</table", StringComparison.Ordinal))
            {
                // If the section ends with an unclosed table, we need to start the next section with the table.
                // If table starts inside SentenceSearchLimit, we ignore it, as that will cause an infinite loop for tables longer than MaxSectionLength
                // If last table starts inside SectionOverlap, keep overlapping.
                start = Math.Min(end - sectionOverlap, start + lastTableStart);
            }
            else
            {
                start = end - sectionOverlap;
            }
        }

        if (start + sectionOverlap < end)
        {
            yield return new FaqAgentContentSection(
                id: Regex.Replace($"-{start}", pattern, "_").TrimStart('_'),
                content: allText[start..end]);
        }
    }
}