using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Utils;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Workers.Activities;

public class GetWebpageHead
{
    private readonly ILogger<GetWebpageHead> _logger;
    private readonly IHttpClientFactory _httpClientFactory;

    public GetWebpageHead(
        ILogger<GetWebpageHead> logger,
        IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _httpClientFactory = httpClientFactory;
    }

    public class GetWebpageHeadInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("url")]
        public string Url { get; set; }

        [JsonConstructor]
        public GetWebpageHeadInput(
            string sleekflowCompanyId,
            string url)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Url = url;
        }
    }

    public class GetWebpageHeadOutput
    {
        [JsonProperty("webpage_head")]
        public WebpageHead WebpageHead { get; set; }

        [JsonConstructor]
        public GetWebpageHeadOutput(WebpageHead webpageHead)
        {
            WebpageHead = webpageHead;
        }
    }

    [Function(nameof(GetWebpageHead))]
    public async Task<GetWebpageHeadOutput> Run(
        [ActivityTrigger]
        GetWebpageHeadInput input)
    {
        _logger.LogInformation(
            "GetWebpageHead processing URL {Url} for company {CompanyId}",
            input.Url,
            input.SleekflowCompanyId);

        var httpClient = _httpClientFactory.CreateClient("default-handler");
        var mimeType = await MimeTypeDetectionUtils.DetectMimeTypeAsync(httpClient, input.Url, _logger);
        var webpageHead = new WebpageHead(mimeType);

        _logger.LogInformation(
            "GetWebpageHead completed processing URL {Url}, ContentType: {ContentType}",
            input.Url,
            webpageHead.MimeType);

        return new GetWebpageHeadOutput(webpageHead);
    }
}

public class WebpageHead
{
    [JsonProperty("mime_type")]
    public string MimeType { get; set; }

    [JsonConstructor]
    public WebpageHead(string mimeType)
    {
        MimeType = mimeType;
    }
}