﻿using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.NeedConfigs;

public class NeedConfigOption
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("label")]
    public string Label { get; set; }

    [JsonProperty("value")]
    public string Value { get; set; }

    [JsonProperty("description")]
    public string? Description { get; set; }

    [JsonProperty("icon")]
    public string? Icon { get; set; }

    [JsonProperty("label_key")]
    public string? LabelKey { get; set; }

    [JsonConstructor]
    public NeedConfigOption(
        string id,
        string label,
        string value,
        string? description,
        string? icon,
        string? labelKey)
    {
        Id = id;
        Label = label;
        Value = value;
        Description = description;
        Icon = icon;
        LabelKey = labelKey;
    }
}