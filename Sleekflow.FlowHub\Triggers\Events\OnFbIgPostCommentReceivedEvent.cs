using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.FlowHubEvents;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.RateLimits;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Events;

[TriggerGroup(
    ControllerNames.Events,
    null,
    filterNames: new[]
    {
        "Sleekflow.FlowHub.Cores.IDepthFuncFilter",
    })]
public class OnFbIgPostCommentReceivedEvent : ITrigger
{
    private readonly IFlowHubEventRateLimitProducer _flowHubEventRateLimitProducer;


    public OnFbIgPostCommentReceivedEvent(
        IFlowHubEventRateLimitProducer flowHubEventRateLimitProducer)
    {
        _flowHubEventRateLimitProducer = flowHubEventRateLimitProducer;
    }

    public class OnFbIgPostCommentReceivedEventInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("contact_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string ContactId { get; set; }

        [ValidateObject]
        [JsonProperty("event_body")]
        [System.ComponentModel.DataAnnotations.Required]
        public OnFbIgPostCommentReceivedEventBody EventBody { get; set; }

        [JsonConstructor]
        public OnFbIgPostCommentReceivedEventInput(
            string sleekflowCompanyId,
            string contactId,
            OnFbIgPostCommentReceivedEventBody eventBody)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ContactId = contactId;
            EventBody = eventBody;
        }
    }

    public class OnFbIgPostCommentReceivedEventOutput
    {
    }

    public async Task<OnFbIgPostCommentReceivedEventOutput> F(
        OnFbIgPostCommentReceivedEventInput onFbIgPostCommentReceivedEventInput)
    {
        var onTriggerEventRequestedEvent = new OnTriggerEventRequestedEvent(
            onFbIgPostCommentReceivedEventInput.EventBody,
            onFbIgPostCommentReceivedEventInput.ContactId,
            "Contact",
            onFbIgPostCommentReceivedEventInput.SleekflowCompanyId);
        await _flowHubEventRateLimitProducer.PublishWithRateLimitAsync(
            onTriggerEventRequestedEvent,
            RateLimitCacheKeyBuilder<OnTriggerEventRequestedEvent>.BuildCacheKeyOnCompanyId(
                onFbIgPostCommentReceivedEventInput.SleekflowCompanyId));

        return new OnFbIgPostCommentReceivedEventOutput();
    }
}