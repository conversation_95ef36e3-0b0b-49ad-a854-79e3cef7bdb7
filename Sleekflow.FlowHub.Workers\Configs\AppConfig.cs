﻿using Microsoft.Extensions.Configuration;
using Sleekflow.Exceptions;

namespace Sleekflow.FlowHub.Workers.Configs;

public interface IAppConfig
{
    string FlowHubInternalsEndpoint { get; }

    string InternalsKey { get; }

    string CrmHubInternalsEndpoint { get; }

    string HubspotIntegratorKey { get; }

}

public class AppConfig : IAppConfig
{
    public string FlowHubInternalsEndpoint { get; }

    public string InternalsKey { get; }

    public string CrmHubInternalsEndpoint { get; }

    public string HubspotIntegratorKey { get; }
    public AppConfig(IConfiguration configuration)
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        FlowHubInternalsEndpoint =
            Environment.GetEnvironmentVariable("FLOW_HUB_INTERNALS_ENDPOINT", target)
            ?? configuration["FLOW_HUB_INTERNALS_ENDPOINT"];
        InternalsKey =
            Environment.GetEnvironmentVariable("INTERNALS_KEY", target)
            ?? throw new SfMissingEnvironmentVariableException("INTERNALS_KEY");

        CrmHubInternalsEndpoint =
            Environment.GetEnvironmentVariable("CRM_HUB_INTERNALS_ENDPOINT", target)
            ?? configuration["CRM_HUB_INTERNALS_ENDPOINT"];

        HubspotIntegratorKey = Environment.GetEnvironmentVariable("HUBSPOT_INTEGRATOR_KEY", target)
                                ?? configuration["HUBSPOT_INTEGRATOR_KEY"];

    }
}