﻿using Sharprompt;
using Sleekflow.DataMigrator.Configs;
using Sleekflow.DataMigrator.Constants;
using Sleekflow.DataMigrator.Executors;
using Sleekflow.DataMigrator.Utils;
using Sleekflow.TenantHub.Models.Constants;

namespace Sleekflow.DataMigrator;

public static class Program
{
    public record struct DbConfigSelection(
        string Name,
        DbConfig DbConfig,
        List<TravisBackendClientConfig?> TravisBackendClientConfigs);

    public static async Task<int> Main(string[] args)
    {
        var operation = Prompt.Select(
            "Select your operation",
            new[]
            {
                "Exit",
                "Back Up",
                "Restore",
                "Delete",
                "Entity Move",
                "Entity Multiple Move",
                "Entity Upgrade",
                "Id State Upgrade",
                "Contact Context Patch",
                "Contact Context Validate",
                "Convert To Soft Delete",
                "Migrate Objects Field",
                "Patch Audit Hub Staff Manual Added Logs",
                "Patch Webhook Hub MaxRetryCount",
                "Patch Webhook Hub Urls",
                "Patch Missing Role Ids",
                "Patch Flow Hub State and Step Execution TTL",
                "Patch IntelligentHub Config AiSettings",
                "Patch IntelligentHub Agent Config Low Confidence Exit Condition"
            });
        if (operation == "Exit")
        {
            return 0;
        }

        var dbConfigSelections = new List<DbConfigSelection>
        {
            // TODO: Merge global and east asia configs together.
            new DbConfigSelection(
                "dev",
                new DbConfig(
                    "https://sleekflow2bd1537b.documents.azure.com:443/",
                    "****************************************************************************************"),
                new List<TravisBackendClientConfig?>
                {
                    new (
                        "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
                        // Environment value :   TRAVIS_BACKEND_AUTH0_SECRET
                        new Dictionary<string, string>() { { HubNames.TenantHub, "6a9_nBt?)R#@_he=v2Eo!K3B3Ae0ao`x*I`m}ZSX*S~hsQ7bQD]k#gh8r38ad,9o" } },
                        ServerLocations.EastAsia)
                }),
            new DbConfigSelection(
                "dev-global",
                new DbConfig(
                    "https://sleekflow-global95ca408e.documents.azure.com:443/",
                    "****************************************************************************************"),
                new List<TravisBackendClientConfig?>
                {
                    new (
                        "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
                        // Environment value :   TRAVIS_BACKEND_AUTH0_SECRET
                        new Dictionary<string, string>() { { HubNames.TenantHub, "6a9_nBt?)R#@_he=v2Eo!K3B3Ae0ao`x*I`m}ZSX*S~hsQ7bQD]k#gh8r38ad,9o" } },
                        ServerLocations.EastAsia)
                })
        };

        var httpClient = new HttpClient();

        if (operation == "Back Up")
        {
            var dbConfigName = Prompt.Select(
                "Select your account",
                dbConfigSelections.Select(s => s.Name));
            var dbConfigSelection = dbConfigSelections.Find(s => s.Name == dbConfigName);

            var executor = new BackUp(dbConfigSelection.DbConfig);

            await executor.PrepareAsync();
            await executor.ExecuteAsync();
        }
        else if (operation == "Entity Move")
        {
            var sourceDbConfigName = Prompt.Select(
                "Select your source account",
                dbConfigSelections.Select(s => s.Name));
            var sourceDbConfigSelection = dbConfigSelections.Find(s => s.Name == sourceDbConfigName);

            var targetDbConfigName = Prompt.Select(
                "Select your target account",
                dbConfigSelections.Select(s => s.Name));
            var targetDbConfigSelection = dbConfigSelections.Find(s => s.Name == targetDbConfigName);

            var executor = new EntityMove(sourceDbConfigSelection.DbConfig, targetDbConfigSelection.DbConfig);

            await executor.PrepareAsync();
            await executor.ExecuteAsync();
        }
        else if (operation == "Entity Multiple Move")
        {
            var sourceDbConfigName = Prompt.Select(
                "Select your source account",
                dbConfigSelections.Select(s => s.Name));
            var sourceDbConfigSelection = dbConfigSelections.Find(s => s.Name == sourceDbConfigName);

            var targetDbConfigName = Prompt.Select(
                "Select your target account",
                dbConfigSelections.Select(s => s.Name));
            var targetDbConfigSelection = dbConfigSelections.Find(s => s.Name == targetDbConfigName);

            var executor = new EntityMultipleMove(sourceDbConfigSelection.DbConfig, targetDbConfigSelection.DbConfig);

            await executor.PrepareAsync();
            await executor.ExecuteAsync();
        }
        else if (operation == "Entity Upgrade")
        {
            var dbConfigName = Prompt.Select(
                "Select your account",
                dbConfigSelections.Select(s => s.Name));
            var dbConfigSelection = dbConfigSelections.Find(s => s.Name == dbConfigName);

            var executor = new EntityUpgrade(dbConfigSelection.DbConfig);

            await executor.PrepareAsync();
            await executor.ExecuteAsync();
        }
        else if (operation == "Restore")
        {
            var dbConfigName = Prompt.Select(
                "Select your account",
                dbConfigSelections.Select(s => s.Name));
            var dbConfigSelection = dbConfigSelections.Find(s => s.Name == dbConfigName);

            var executor = new Restore(dbConfigSelection.DbConfig);

            await executor.PrepareAsync();
            await executor.ExecuteAsync();
        }
        else if (operation == "Delete")
        {
            var dbConfigName = Prompt.Select(
                "Select your account",
                dbConfigSelections.Select(s => s.Name));
            var dbConfigSelection = dbConfigSelections.Find(s => s.Name == dbConfigName);

            var executor = new Delete(dbConfigSelection.DbConfig);

            await executor.PrepareAsync();
            await executor.ExecuteAsync();
        }
        else if (operation == "Id State Upgrade")
        {
            var dbConfigName = Prompt.Select(
                "Select your account",
                dbConfigSelections.Select(s => s.Name));
            var dbConfigSelection = dbConfigSelections.Find(s => s.Name == dbConfigName);

            var executor = new IdStateUpgrade(dbConfigSelection.DbConfig);

            await executor.PrepareAsync();
            await executor.ExecuteAsync();
        }
        else if (operation == "Contact Context Patch")
        {
            var dbConfigName = Prompt.Select(
                "Select your account",
                dbConfigSelections.Select(s => s.Name));
            var dbConfigSelection = dbConfigSelections.Find(s => s.Name == dbConfigName);

            var executor = new ContactContextPatch(dbConfigSelection.DbConfig);

            await executor.PrepareAsync();
            await executor.ExecuteAsync();
        }
        else if (operation == "Contact Context Validate")
        {
            var dbConfigName = Prompt.Select(
                "Select your account",
                dbConfigSelections.Select(s => s.Name));
            var dbConfigSelection = dbConfigSelections.Find(s => s.Name == dbConfigName);

            var executor = new ContactContextValidate(dbConfigSelection.DbConfig);

            await executor.PrepareAsync();
            await executor.ExecuteAsync();
        }
        else if (operation == "Convert To Soft Delete")
        {
            var dbConfigName = Prompt.Select(
                "Select your account",
                dbConfigSelections.Select(s => s.Name));
            var dbConfigSelection = dbConfigSelections.Find(s => s.Name == dbConfigName);

            var executor = new ConvertToSoftDelete(dbConfigSelection.DbConfig);

            await executor.PrepareAsync();
            await executor.ExecuteAsync();
        }
        else if (operation == "Migrate Objects Field")
        {
            var dbConfigName = Prompt.Select(
                "Select your account",
                dbConfigSelections.Select(s => s.Name));
            var dbConfigSelection = dbConfigSelections.Find(s => s.Name == dbConfigName);

            var executor = new MigrateObjectsField(dbConfigSelection.DbConfig);

            await executor.PrepareAsync();
            await executor.ExecuteAsync();
        }
        else if (operation == "Patch Audit Hub Staff Manual Added Logs")
        {
            var dbConfigName = Prompt.Select(
                "Select your account",
                dbConfigSelections.Select(s => s.Name));
            var dbConfigSelection = dbConfigSelections.Find(s => s.Name == dbConfigName);

            var executor = new PatchAuditHubStaffManualAddedLogs(dbConfigSelection.DbConfig);

            await executor.PrepareAsync();
            await executor.ExecuteAsync();
        }
        else if (operation == "Patch Webhook Hub MaxRetryCount")
        {
            var dbConfigName = Prompt.Select(
                "Select your account",
                dbConfigSelections.Select(s => s.Name));
            var dbConfigSelection = dbConfigSelections.Find(s => s.Name == dbConfigName);

            var executor = new PatchWebhookHubMaxRetryCount(dbConfigSelection.DbConfig);

            await executor.PrepareAsync();
            await executor.ExecuteAsync();
        }
        else if (operation == "Patch Webhook Hub Urls")
        {
            var dbConfigName = Prompt.Select(
                "Select your account",
                dbConfigSelections.Select(s => s.Name));
            var dbConfigSelection = dbConfigSelections.Find(s => s.Name == dbConfigName);

            var executor = new PatchWebhookHubUrls(dbConfigSelection.DbConfig);

            await executor.PrepareAsync();
            await executor.ExecuteAsync();
        }
        else if (operation == "Patch Missing Role Ids")
        {
            var dbConfigName = Prompt.Select(
                "Select your account",
                dbConfigSelections.Select(s => s.Name));
            var dbConfigSelection = dbConfigSelections.Find(s => s.Name == dbConfigName);

            var executor = new PatchMissingRoleIds(
                dbConfigSelection.DbConfig,
                dbConfigSelection.TravisBackendClientConfigs,
                httpClient);

            await executor.PrepareAsync();
            await executor.ExecuteAsync();
        }
        else if (operation == "Patch Flow Hub State and Step Execution TTL")
        {
            var dbConfigName = Prompt.Select(
                "Select your account",
                dbConfigSelections.Select(s => s.Name));
            var dbConfigSelection = dbConfigSelections.Find(s => s.Name == dbConfigName);

            var executor = new PatchFlowHubStateAndStepExecutionTtl(dbConfigSelection.DbConfig);

            await executor.PrepareAsync();
            await executor.ExecuteAsync();
        }
        else if (operation == "Patch IntelligentHub Config AiSettings")
        {
            var dbConfigName = Prompt.Select(
                "Select your account",
                dbConfigSelections.Select(s => s.Name));
            var dbConfigSelection = dbConfigSelections.Find(s => s.Name == dbConfigName);

            var executor = new PatchIntelligentHubConfigAiSettings(dbConfigSelection.DbConfig);

            await executor.PrepareAsync();
            await executor.ExecuteAsync();
        }
        else if (operation == "Patch IntelligentHub Agent Config Low Confidence Exit Condition")
        {
            var dbConfigName = Prompt.Select(
                "Select your account",
                dbConfigSelections.Select(s => s.Name));

            var dbConfigSelection = dbConfigSelections.Find(s => s.Name == dbConfigName);

            var executor = new PatchAgentConfigExitCondition(dbConfigSelection.DbConfig);

            await executor.PrepareAsync();
            await executor.ExecuteAsync();
        }

        return 0;
    }
}