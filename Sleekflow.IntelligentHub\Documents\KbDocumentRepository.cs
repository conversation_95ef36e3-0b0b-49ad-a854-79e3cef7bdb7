using Microsoft.Azure.Cosmos;
using Newtonsoft.Json.Linq;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Documents;

public interface IKbDocumentRepository : IDynamicFiltersRepository<KbDocument>
{
    Task<string> GetFileDocumentProcessStatusAsync(
        string sleekflowCompanyId,
        string documentId);

    Task<KbDocument> PatchFileDocumentProcessStatusAsync(
        string sleekflowCompanyId,
        string documentId,
        string newStatus,
        double percentage);

    Task PatchAgentAssignmentsAsync(
        string sleekflowCompanyId,
        string documentId,
        List<AgentAssignment> agentAssignments,
        TransactionalBatch? transactionalBatch = null);

    Task<List<KbDocument>> GetKbDocumentsByCompanyAsync(
        string sleekflowCompanyId,
        List<string>? filterTrainingStatus,
        List<string>? filterExcludeTrainingStatus,
        List<string>? filterAgent,
        List<string>? filterExcludeAgent);

    Task<List<KbDocument>> SearchDocumentAsync(
        string sleekflowCompanyId,
        string searchInput);

    Task UpdateDebugTimestampsAsync(
        string sleekflowCompanyId,
        string documentId,
        DateTimeOffset? conversionStarted,
        DateTimeOffset? conversionEnded,
        DateTimeOffset? uploadStarted,
        DateTimeOffset? uploadEnded);
}

public class KbDocumentRepository
    : DynamicFiltersBaseRepository<KbDocument>, IKbDocumentRepository, IScopedService
{
    private readonly ILogger<KbDocumentRepository> _logger;

    public KbDocumentRepository(
        ILogger<KbDocumentRepository> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
        _logger = logger;
    }

    public async Task<string> GetFileDocumentProcessStatusAsync(
        string sleekflowCompanyId,
        string documentId)
    {
        var fileDocument = await GetAsync(documentId, sleekflowCompanyId);
        return fileDocument.FileDocumentProcessStatus;
    }

    public async Task<KbDocument> PatchFileDocumentProcessStatusAsync(
        string sleekflowCompanyId,
        string documentId,
        string newStatus,
        double percentage)
    {
        return await PatchAndGetAsync(
            documentId,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace($"/{KbDocument.PropertyNameFileDocumentProcessStatus}", newStatus),
                PatchOperation.Set($"/{KbDocument.PropertyNameFileDocumentProcessPercentage}", percentage),
                PatchOperation.Replace($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow)
            });
    }

    public async Task PatchAgentAssignmentsAsync(
        string sleekflowCompanyId,
        string documentId,
        List<AgentAssignment> agentAssignments,
        TransactionalBatch? transactionalBatch = null)
    {
        var fileDocument = await GetAsync(documentId, sleekflowCompanyId);
        if (fileDocument == null)
        {
            throw new SfNotFoundObjectException("Document not found");
        }

        await PatchAsync(
            documentId,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace($"/{KbDocument.PropertyNameAgentAssignments}", agentAssignments),
            },
            transactionalBatch);
    }

    public async Task<List<KbDocument>> GetKbDocumentsByCompanyAsync(
        string sleekflowCompanyId,
        List<string>? filterTrainingStatus,
        List<string>? filterExcludeTrainingStatus,
        List<string>? filterAgent,
        List<string>? filterExcludeAgent)
    {
        return await GetObjectsAsync(
            d =>
                d.SleekflowCompanyId == sleekflowCompanyId &&

                // Filter by training status (include list)
                (filterTrainingStatus == null || filterTrainingStatus.Count == 0 ||
                 filterTrainingStatus.Contains(d.FileDocumentProcessStatus)) &&

                // Filter by training status (exclude list)
                (filterExcludeTrainingStatus == null || filterExcludeTrainingStatus.Count == 0 ||
                 !filterExcludeTrainingStatus.Contains(d.FileDocumentProcessStatus)) &&

                // Filter by agent (include list)
                (filterAgent == null || filterAgent.Count == 0 ||
                 (d.AgentAssignments != null && d.AgentAssignments.Any(a => filterAgent.Contains(a.AgentId)))) &&

                // Filter by agent (exclude list)
                (filterExcludeAgent == null || filterExcludeAgent.Count == 0 ||
                 d.AgentAssignments == null || !d.AgentAssignments.Any(a => filterExcludeAgent.Contains(a.AgentId))));
    }

    public async Task<List<KbDocument>> SearchDocumentAsync(
        string sleekflowCompanyId,
        string searchInput)
    {
        var input = searchInput.ToLower();

        return await GetObjectsAsync(
            new QueryDefinition(
                    $"""
                     SELECT *
                     FROM c
                     WHERE c.sleekflow_company_id = @sleekflowCompanyId
                     AND (contains(c.file_name, @fileName) OR contains(c.base_url, @baseUrl) OR contains(c.url, @url))
                     """)
                .WithParameter("@fileName", input)
                .WithParameter("@baseUrl", input)
                .WithParameter("@url", input)
                .WithParameter("@sleekflowCompanyId", sleekflowCompanyId));
    }

    public async Task UpdateDebugTimestampsAsync(
        string sleekflowCompanyId,
        string documentId,
        DateTimeOffset? conversionStarted,
        DateTimeOffset? conversionEnded,
        DateTimeOffset? uploadStarted,
        DateTimeOffset? uploadEnded)
    {
        // Get current document to merge with existing debug timestamps
        var document = await GetAsync(documentId, sleekflowCompanyId);
        if (document == null)
        {
            throw new SfNotFoundObjectException(documentId, sleekflowCompanyId);
        }

        // Merge the timestamps - only update non-null values
        var currentTimestamps = document.DebugTimestamps ?? new DebugTimestamps();
        var updatedTimestamps = new DebugTimestamps(
            conversionStarted ?? currentTimestamps.ConversionStarted,
            conversionEnded ?? currentTimestamps.ConversionEnded,
            uploadStarted ?? currentTimestamps.UploadStarted,
            uploadEnded ?? currentTimestamps.UploadEnded);

        await PatchAsync(
            documentId,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Set($"/{KbDocument.PropertyNameDebugTimestamps}", updatedTimestamps),
                PatchOperation.Replace($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow)
            });
    }

    protected override KbDocument ToTargetType(JObject jObject)
    {
        var sysTypeName = (string?) jObject[Entity.PropertyNameSysTypeName];

        KbDocument? targetObject = null;
        if (string.IsNullOrEmpty(sysTypeName))
        {
            targetObject = jObject.ToObject<FileDocument>();
        }

        switch (sysTypeName)
        {
            case SysTypeNames.FileDocument:
                targetObject = jObject.ToObject<FileDocument>();
                break;
            case SysTypeNames.WebsiteDocument:
                targetObject = jObject.ToObject<WebsiteDocument>();
                break;
            case SysTypeNames.WebpageDocument:
                targetObject = jObject.ToObject<WebpageDocument>();
                break;
        }

        return targetObject ??
               throw new Exception($"Unable to convert {jObject} to {nameof(FileDocument)}");
    }
}