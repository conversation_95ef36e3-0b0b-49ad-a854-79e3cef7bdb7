using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;

/// <summary>
/// Represents a snapshot of a CompanyAgentConfig at a specific point in time.
/// Used to track configuration changes and provide audit trail.
/// </summary>
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IIntelligentHubDbResolver))]
[ContainerId(ContainerNames.CompanyAgentConfigSnapshot)]
public class CompanyAgentConfigSnapshot : AuditEntity
{
    /// <summary>
    /// The operation that triggered this snapshot (e.g., "Patch", "Update").
    /// </summary>
    [JsonProperty("operation_type")]
    public string OperationType { get; set; }

    /// <summary>
    /// The complete CompanyAgentConfig at the time of snapshot.
    /// </summary>
    [JsonProperty("config")]
    public CompanyAgentConfig Config { get; set; }

    [JsonConstructor]
    public CompanyAgentConfigSnapshot(
        string id,
        string operationType,
        CompanyAgentConfig config,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff? createdBy = null,
        AuditEntity.SleekflowStaff? updatedBy = null)
        : base(
            id,
            SysTypeNames.CompanyAgentConfigSnapshot,
            createdAt,
            updatedAt,
            sleekflowCompanyId,
            createdBy,
            updatedBy)
    {
        OperationType = operationType;
        Config = config;
    }

    /// <summary>
    /// Creates a snapshot from an existing CompanyAgentConfig.
    /// </summary>
    /// <param name="snapshotId">The ID for the snapshot.</param>
    /// <param name="config">The configuration to snapshot.</param>
    /// <param name="operationType">The operation that triggered this snapshot.</param>
    /// <param name="createdBy">The staff member creating the snapshot.</param>
    /// <returns>A new CompanyAgentConfigSnapshot instance.</returns>
    public static CompanyAgentConfigSnapshot CreateFromConfig(
        string snapshotId,
        CompanyAgentConfig config,
        string operationType,
        AuditEntity.SleekflowStaff createdBy)
    {
        var now = DateTimeOffset.UtcNow;

        return new CompanyAgentConfigSnapshot(
            snapshotId,
            operationType,
            config,
            now,
            now,
            config.SleekflowCompanyId,
            createdBy,
            createdBy);
    }
}