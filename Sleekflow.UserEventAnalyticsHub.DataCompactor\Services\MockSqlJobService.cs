using Sleekflow.UserEventHub.Models.Cores;
using Sleekflow.UserEventHub.Models.SqlJobs;
using Microsoft.Extensions.Logging;

namespace Sleekflow.UserEventAnalyticsHub.DataCompactor.Services;

public class MockSqlJobService : ISqlJobService
{
    private readonly ILogger<MockSqlJobService> _logger;

    public MockSqlJobService(ILogger<MockSqlJobService> logger)
    {
        _logger = logger;
    }

    public Task<SqlJob> CreateAndGetSqlJobAsync(string sleekflowCompanyId, string sqlQuery, List<Condition> conditions)
    {
        _logger.LogInformation("Mock: Creating SQL job for company {CompanyId}", sleekflowCompanyId);

        var job = new SqlJob(
            "mock-job-id",
            "SqlJob",
            sleekflowCompanyId,
            sqlQuery,
            "Pending",
            DateTimeOffset.UtcNow,
            null,
            null,
            null,
            null,
            null,
            null,
            conditions);

        return Task.FromResult(job);
    }

    public Task<(List<SqlJob> Objs, string? NextContinuationToken)> GetSqlJobsAsync(string sleekflowCompanyId, string? continuationToken, int limit)
    {
        _logger.LogInformation("Mock: Getting SQL jobs for company {CompanyId}", sleekflowCompanyId);
        return Task.FromResult((new List<SqlJob>(), (string?)null));
    }

    public Task<List<Dictionary<string, object?>>> CreateAndGetShortSqlJobAsync(string sleekflowCompanyId, string sqlQuery, List<Condition> conditions)
    {
        _logger.LogInformation("Mock: Creating short SQL job for company {CompanyId}", sleekflowCompanyId);
        return Task.FromResult(new List<Dictionary<string, object?>>());
    }

    public Task<SqlJob?> GetOrDefaultAsync(string jobId, string sleekflowCompanyId)
    {
        _logger.LogInformation("Mock: Getting SQL job {JobId} for company {CompanyId}", jobId, sleekflowCompanyId);

        var job = new SqlJob(
            jobId,
            "SqlJob",
            sleekflowCompanyId,
            Environment.GetEnvironmentVariable("TEST_SQL_QUERY") ?? "SELECT COUNT(*) as total_events FROM events WHERE eventType = 'user_action'",
            "Pending",
            DateTimeOffset.UtcNow,
            null,
            null,
            null,
            null,
            null,
            null,
            new List<Condition>());

        return Task.FromResult<SqlJob?>(job);
    }

    public Task SetRunningAsync(SqlJob job)
    {
        _logger.LogInformation("Mock: Setting job {JobId} to running", job.Id);
        return Task.CompletedTask;
    }

    public Task CompleteJobAsync(SqlJob job, string? blobId)
    {
        _logger.LogInformation("Mock: Completing job {JobId} with blob {BlobId}", job.Id, blobId ?? "none");
        return Task.CompletedTask;
    }

    public Task FailJobAsync(SqlJob job, string errorMessage)
    {
        _logger.LogInformation("Mock: Failing job {JobId} with error: {Error}", job.Id, errorMessage);
        return Task.CompletedTask;
    }
}