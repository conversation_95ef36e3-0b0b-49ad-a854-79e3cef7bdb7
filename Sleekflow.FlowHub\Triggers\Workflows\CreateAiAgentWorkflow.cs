using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class CreateAiAgentWorkflow : ITrigger
{
    private readonly IAiAgentWorkflowService _aiAgentWorkflowService;

    public CreateAiAgentWorkflow(
        IAiAgentWorkflowService aiAgentWorkflowService)
    {
        _aiAgentWorkflowService = aiAgentWorkflowService;
    }

    public class CreateAiAgentWorkflowInput : IHasSleekflowCompanyId, IHasSleekflowStaff
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("channel_configs")]
        [Required]
        [Validations.ValidateArray]
        public List<Dictionary<string, object>> ChannelConfigs { get; set; }

        [JsonProperty("schemaful_object")]
        [Required]
        [Validations.ValidateObject]

        public Dictionary<string, object> SchemafulObject { get; set; }

        [JsonProperty("agents")]
        [Required]
        [Validations.ValidateArray]

        public List<Dictionary<string, object>> Agents { get; set; }

        [JsonProperty("contact_property")]
        [Required]
        [Validations.ValidateObject]
        public Dictionary<string, object> ContactProperty { get; set; }

        [JsonProperty("ai_step_id")]
        [Required]
        public string AiStepId { get; set; }

        [JsonProperty("dependency_workflow_id")]
        [Required]
        public string DependencyWorkflowId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [Validations.ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public CreateAiAgentWorkflowInput(
            string sleekflowCompanyId,
            List<Dictionary<string, object>> channelConfigs,
            Dictionary<string, object> schemafulObject,
            List<Dictionary<string, object>> agents,
            Dictionary<string, object> contactProperty,
            string sleekflowStaffId,
            string aiStepId,
            string dependencyWorkflowId,
            List<string>? sleekflowStaffTeamIds = null)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ChannelConfigs = channelConfigs;
            SchemafulObject = schemafulObject;
            Agents = agents;
            ContactProperty = contactProperty;
            SleekflowStaffId = sleekflowStaffId;
            AiStepId = aiStepId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
            DependencyWorkflowId = dependencyWorkflowId;
        }
    }

    public class CreateAiAgentWorkflowOutput
    {
        [JsonProperty("workflow")]
        public ProxyWorkflow Workflow { get; set; }

        [JsonConstructor]
        public CreateAiAgentWorkflowOutput(
            ProxyWorkflow workflow)
        {
            Workflow = workflow;
        }
    }

    public async Task<CreateAiAgentWorkflowOutput> F(CreateAiAgentWorkflowInput createAiAgentWorkflowInput)
    {
        var workflow = await _aiAgentWorkflowService.CreateAiAgentWorkflow(
            createAiAgentWorkflowInput.SleekflowCompanyId,
            createAiAgentWorkflowInput.ChannelConfigs,
            createAiAgentWorkflowInput.SchemafulObject,
            createAiAgentWorkflowInput.Agents,
            createAiAgentWorkflowInput.ContactProperty,
            createAiAgentWorkflowInput.SleekflowStaffId,
            createAiAgentWorkflowInput.AiStepId,
            createAiAgentWorkflowInput.DependencyWorkflowId);

        return new CreateAiAgentWorkflowOutput(workflow);
    }
}
