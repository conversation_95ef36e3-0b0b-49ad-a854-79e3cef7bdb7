using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Workers.Activities;

public class GetWebpageDocument
{
    private readonly IKbDocumentService _kbDocumentService;

    public GetWebpageDocument(IKbDocumentService kbDocumentService)
    {
        _kbDocumentService = kbDocumentService;
    }

    public class GetWebpageDocumentInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("document_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string DocumentId { get; set; }

        [JsonConstructor]
        public GetWebpageDocumentInput(
            string sleekflowCompanyId,
            string documentId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DocumentId = documentId;
        }
    }

    public class GetWebpageDocumentOutput
    {
        [JsonProperty("webpage_document")]
        public WebpageDocument WebpageDocument { get; set; }

        [JsonConstructor]
        public GetWebpageDocumentOutput(WebpageDocument webpageDocument)
        {
            WebpageDocument = webpageDocument;
        }
    }

    [Function(nameof(GetWebpageDocument))]
    public async Task<GetWebpageDocumentOutput> Run(
        [ActivityTrigger]
        GetWebpageDocumentInput input)
    {
        var webpageDocument = (WebpageDocument) await _kbDocumentService.GetDocumentAsync(
            input.SleekflowCompanyId,
            input.DocumentId);

        return new GetWebpageDocumentOutput(webpageDocument);
    }
}