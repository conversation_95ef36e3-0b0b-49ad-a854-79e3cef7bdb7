using System.Text.RegularExpressions;

namespace Sleekflow.UserEventAnalyticsHub.DataCompactor.Utils;

public static class BlobPathParser
{
    // Pattern: sleekflowCompanyId={id}/year={yyyy}/month={mm}/day={dd}/hour={hh}/filename.parquet
    private static readonly Regex BlobPathRegex = new(
        @"sleekflowCompanyId=(?<companyId>[^/]+)/year=(?<year>\d{4})/month=(?<month>\d{1,2})/day=(?<day>\d{1,2})/hour=(?<hour>\d{1,2})/(?<filename>.+\.parquet)",
        RegexOptions.Compiled | RegexOptions.IgnoreCase);

    public static bool TryParseBlobPath(string blobPath, out string sleekflowCompanyId, out int year, out int month, out int day, out int hour)
    {
        sleekflowCompanyId = string.Empty;
        year = 0;
        month = 0;
        day = 0;
        hour = 0;

        if (string.IsNullOrWhiteSpace(blobPath))
            return false;

        var match = BlobPathRegex.Match(blobPath);
        if (!match.Success)
            return false;

        sleekflowCompanyId = match.Groups["companyId"].Value;

        return int.TryParse(match.Groups["year"].Value, out year) &&
               int.TryParse(match.Groups["month"].Value, out month) &&
               int.TryParse(match.Groups["day"].Value, out day) &&
               int.TryParse(match.Groups["hour"].Value, out hour) &&
               IsValidDate(year, month, day) &&
               IsValidHour(hour);
    }

    public static bool IsParquetFile(string blobPath)
    {
        return !string.IsNullOrWhiteSpace(blobPath) &&
               blobPath.EndsWith(".parquet", StringComparison.OrdinalIgnoreCase);
    }

    public static bool IsValidBlobPath(string blobPath)
    {
        return IsParquetFile(blobPath) &&
               BlobPathRegex.IsMatch(blobPath);
    }

    public static string? ExtractCompanyId(string blobPath)
    {
        if (TryParseBlobPath(blobPath, out var companyId, out _, out _, out _, out _))
            return companyId;
        return null;
    }

    public static string BuildBlobPathPattern(string sleekflowCompanyId, int? year = null, int? month = null, int? day = null, int? hour = null)
    {
        var pattern = $"sleekflowCompanyId={sleekflowCompanyId}";

        if (year.HasValue)
        {
            pattern += $"/year={year}";
            if (month.HasValue)
            {
                pattern += $"/month={month}";
                if (day.HasValue)
                {
                    pattern += $"/day={day}";
                    if (hour.HasValue)
                    {
                        pattern += $"/hour={hour}";
                    }
                }
            }
        }

        return pattern;
    }

    private static bool IsValidDate(int year, int month, int day)
    {
        try
        {
            _ = new DateTime(year, month, day);
            return true;
        }
        catch
        {
            return false;
        }
    }

    private static bool IsValidHour(int hour)
    {
        return hour >= 0 && hour <= 23;
    }
}