using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents.WebsiteDocuments;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.IntelligentHub.Workers.Activities;

public class PatchWebsiteDocumentSelectedUrlStatuses
{
    private readonly ILogger<PatchWebsiteDocumentSelectedUrlStatuses> _logger;
    private readonly IWebsiteDocumentService _websiteDocumentService;

    public PatchWebsiteDocumentSelectedUrlStatuses(
        ILogger<PatchWebsiteDocumentSelectedUrlStatuses> logger,
        IWebsiteDocumentService websiteDocumentService)
    {
        _logger = logger;
        _websiteDocumentService = websiteDocumentService;
    }

    public class PatchWebsiteDocumentSelectedUrlStatusesInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("document_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string DocumentId { get; set; }

        [JsonProperty("urls_to_update")]
        [System.ComponentModel.DataAnnotations.Required]
        [ValidateArray]
        public List<string> UrlsToUpdate { get; set; }

        [JsonProperty("new_status")]
        [System.ComponentModel.DataAnnotations.Required]
        public string NewStatus { get; set; }

        [JsonConstructor]
        public PatchWebsiteDocumentSelectedUrlStatusesInput(
            string sleekflowCompanyId,
            string documentId,
            List<string> urlsToUpdate,
            string newStatus)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DocumentId = documentId;
            UrlsToUpdate = urlsToUpdate;
            NewStatus = newStatus;
        }
    }

    public class PatchWebsiteDocumentSelectedUrlStatusesOutput
    {
    }

    [Function(nameof(PatchWebsiteDocumentSelectedUrlStatuses))]
    public async Task<PatchWebsiteDocumentSelectedUrlStatusesOutput> Batch(
        [ActivityTrigger]
        PatchWebsiteDocumentSelectedUrlStatusesInput patchWebsiteDocumentSelectedUrlStatusesInput)
    {
        _logger.LogInformation(
            "PatchWebsiteDocumentSelectedUrlStatuses {SleekflowCompanyId} {DocumentId} {UrlCount} URLs to {Status}",
            patchWebsiteDocumentSelectedUrlStatusesInput.SleekflowCompanyId,
            patchWebsiteDocumentSelectedUrlStatusesInput.DocumentId,
            patchWebsiteDocumentSelectedUrlStatusesInput.UrlsToUpdate.Count,
            patchWebsiteDocumentSelectedUrlStatusesInput.NewStatus);

        await _websiteDocumentService.UpdateUrlStatusesAsync(
            patchWebsiteDocumentSelectedUrlStatusesInput.SleekflowCompanyId,
            patchWebsiteDocumentSelectedUrlStatusesInput.DocumentId,
            patchWebsiteDocumentSelectedUrlStatusesInput.UrlsToUpdate,
            patchWebsiteDocumentSelectedUrlStatusesInput.NewStatus);

        return new PatchWebsiteDocumentSelectedUrlStatusesOutput();
    }
}