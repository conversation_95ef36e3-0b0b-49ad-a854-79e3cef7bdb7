﻿using System.Text;
using System.Text.RegularExpressions;
using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Newtonsoft.Json;
using Sleekflow.Exceptions.IntelligentHub;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.KnowledgeBases;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Events;
using Sleekflow.IntelligentHub.Models.KnowledgeBaseEntries;
using Sleekflow.IntelligentHub.Models.Playgrounds;
using Sleekflow.IntelligentHub.Models.Snapshots;
using Sleekflow.IntelligentHub.Playgrounds;
using Sleekflow.JsonConfigs;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.Events;

public class GeneratePlaygroundRecommendedReplyEventConsumerDefinition
    : ConsumerDefinition<GeneratePlaygroundRecommendedReplyEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<GeneratePlaygroundRecommendedReplyEventConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class GeneratePlaygroundRecommendedReplyEventConsumer : IConsumer<GeneratePlaygroundRecommendedReplyEvent>
{
    private readonly ILogger<GeneratePlaygroundRecommendedReplyEventConsumer> _logger;
    private readonly IChatService _chatService;
    private readonly IPlaygroundService _playgroundService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;
    private readonly ITokenCountingService _tokenCountingService;
    private readonly IAcquiredKnowledgeContext _acquiredKnowledgeContext;

    public GeneratePlaygroundRecommendedReplyEventConsumer(
        ILogger<GeneratePlaygroundRecommendedReplyEventConsumer> logger,
        IChatService chatService,
        IPlaygroundService playgroundService,
        IIntelligentHubUsageService intelligentHubUsageService,
        ITokenCountingService tokenCountingService,
        IAcquiredKnowledgeContext acquiredKnowledgeContext)
    {
        _logger = logger;
        _chatService = chatService;
        _playgroundService = playgroundService;
        _intelligentHubUsageService = intelligentHubUsageService;
        _tokenCountingService = tokenCountingService;
        _acquiredKnowledgeContext = acquiredKnowledgeContext;
    }

    public async Task Consume(ConsumeContext<GeneratePlaygroundRecommendedReplyEvent> context)
    {
        var message = context.Message;
        var sleekflowCompanyId = message.SleekflowCompanyId;
        var sfChatEntries = message.ConversationContext;
        var knowledgeSource = string.Empty;
        string recommendedReply;
        var groupChatId = string.Empty;

        _logger.LogInformation(
            "Streaming Agent Recommended Reply {SleekflowCompanyId} {ConversationContext}",
            sleekflowCompanyId,
            JsonConvert.SerializeObject(sfChatEntries, JsonConfig.DefaultLoggingJsonSerializerSettings));

        var playground = await _playgroundService.GetAsync(message.SessionId, message.SleekflowCompanyId);

        var agentConfigDto = playground.AgentConfig;
        var agentConfig = new CompanyAgentConfig(agentConfigDto);

        try
        {
            var (asyncEnumerable, sourcesStr, groupChatIdStr) = await _chatService.StreamAgentAnswerAsync(
                sfChatEntries,
                sleekflowCompanyId,
                new ReplyGenerationContext(
                    sleekflowCompanyId,
                    playground.Id,
                    new Dictionary<string, string>(),
                    Guid.NewGuid().ToString(),
                    null),
                agentConfig);

            knowledgeSource = sourcesStr ?? string.Empty;
            groupChatId = groupChatIdStr;
            var answerSb = new StringBuilder();
            await foreach (var partialAnswer in asyncEnumerable.WithCancellation(CancellationToken.None))
            {
                if (partialAnswer is null)
                {
                    continue;
                }

                answerSb.Append(partialAnswer);
            }

            recommendedReply = Regex.Replace(answerSb.ToString(), @"\[source\]", string.Empty, RegexOptions.IgnoreCase);
        }
        catch (Exception ex) when (ex is SfKnowledgeBaseSourceNotFoundException
                                       or SfAgentUnableToFindRelevantSourceException)
        {
            recommendedReply = "***No related knowledge found. Will exit with low confidence score condition.***";
            _logger.LogError(ex, "Error occured while processing playground agent reply");
        }

        _logger.LogInformation("Agent Recommended reply: {RecommendedReply}", recommendedReply);

        await _intelligentHubUsageService.RecordUsageAsync(
            sleekflowCompanyId,
            PriceableFeatures.RecommendReply,
            null,
            new PlaygroundRecommendReplySnapshot(
                JsonConvert.SerializeObject(
                    sfChatEntries,
                    JsonConfig.DefaultLoggingJsonSerializerSettings),
                knowledgeSource ?? string.Empty,
                recommendedReply,
                _tokenCountingService.GetTokenCounts(groupChatId),
                agentConfig.EffectiveCollaborationMode,
                agentConfig.Id,
                agentConfig.AgentVersionedId,
                playground.Id));

        var knowledgeCitations = _acquiredKnowledgeContext.KnowledgeContexts
            .SelectMany(k => k.ExtractedChunks, (k, l) => new KnowledgeCitation(l))
            .ToList();

        var playgroundRecommendedReply = new PlaygroundRecommendedReply(
            message.MessageId,
            sfChatEntries,
            recommendedReply,
            knowledgeCitations);

        await _playgroundService.PatchAsync(
            playground.Id,
            playground.SleekflowCompanyId,
            playground.RecommendedReplies.Append(playgroundRecommendedReply).ToList(),
            playground.ETag);
    }
}